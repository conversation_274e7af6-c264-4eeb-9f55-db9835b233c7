package com.stt.android.domain.connectedservices

import io.reactivex.Single
import io.reactivex.schedulers.Schedulers
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class OAuth1AuthenticationUseCaseTest {

    @Mock
    private lateinit var repository: ConnectedServicesRepository
    private lateinit var useCase: OAuth1AuthenticationUseCase

    @Before
    fun setup() {
        useCase = OAuth1AuthenticationUseCase(
            repository,
            Schedulers.trampoline(),
            Schedulers.trampoline()
        )
    }

    @Test
    fun `should access the repository when fetching connection url`() {
        whenever(repository.fetchPartnerConnectionUrl(ArgumentMatchers.anyString()))
            .thenReturn(Single.just(""))

        useCase.fetchConnectionUrl(ArgumentMatchers.anyString())
            .test()
            .assertNoErrors()
            .assertComplete()

        verify(repository).fetchPartnerConnectionUrl(ArgumentMatchers.anyString())
    }

    @Test
    fun `should fail if repository fails inside and return an error`() {
        whenever(repository.fetchPartnerConnectionUrl(ArgumentMatchers.anyString()))
            .thenReturn(Single.error(Exception()))

        useCase.fetchConnectionUrl(ArgumentMatchers.anyString())
            .test()
            .assertError(Exception::class.java)
            .assertNotComplete()

        verify(repository).fetchPartnerConnectionUrl(ArgumentMatchers.anyString())
    }
}
