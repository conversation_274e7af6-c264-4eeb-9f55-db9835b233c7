package com.stt.android.domain.sportmodes

import io.reactivex.Completable
import io.reactivex.Single
import io.reactivex.schedulers.Schedulers
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class ChangeSportModesUseCaseTest {
    @Mock
    private lateinit var repository: SportModesRepository
    private lateinit var useCase: ChangeSportModesUseCase

    @Before
    fun setUp() {
        useCase = ChangeSportModesUseCase(
            repository,
            Schedulers.trampoline(),
            Schedulers.trampoline()
        )
    }

    @Test
    fun `should access the repository when setting sport mode fte completed`() {
        // prepare
        `when`(repository.setSportModeFteCompleted(anyBoolean()))
            .thenReturn(Completable.complete())
        // verify
        useCase.setSportModeFteCompleted(anyBoolean())
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(repository).setSportModeFteCompleted(anyBoolean())
    }

    @Test
    fun `should fail if repository fails inside and return an error while setting fte completed`() {
        // prepare
        `when`(repository.setSportModeFteCompleted(anyBoolean()))
            .thenReturn(Completable.error(Exception())) // verify
        useCase.setSportModeFteCompleted(anyBoolean())
            .test()
            .assertError(Exception::class.java)
            .assertNotComplete()
        verify(repository).setSportModeFteCompleted(anyBoolean())
    }

    @Test
    fun `should access the repository when adding display`() {
        // prepare
        `when`(repository.addDisplay(anyString(), anyString(), anyInt(), anyString()))
            .thenReturn(Single.just(WatchSportMode(Group(0, ""), emptyList())))
        // verify
        useCase.addDisplay(anyString(), anyString(), anyInt(), anyString())
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(repository).addDisplay(anyString(), anyString(), anyInt(), anyString())
    }

    @Test
    fun `should fail if repository fails inside and return an error while adding display`() {
        // prepare
        `when`(repository.addDisplay(anyString(), anyString(), anyInt(), anyString())).thenReturn(Single.error(Exception()))
        // verify
        useCase.addDisplay("", "", 1, "")
            .test()
            .assertError(Exception::class.java)
            .assertNotComplete()
        verify(repository).addDisplay(anyString(), anyString(), anyInt(), anyString())
    }

    @Test
    fun `should access the repository when changing display`() {
        // prepare
        `when`(repository.changeDisplay(anyString(), anyString(), anyInt(), anyString()))
            .thenReturn(Single.just(WatchSportMode(Group(0, ""), emptyList())))
        // verify
        useCase.changeDisplay(anyString(), anyString(), anyInt(), anyString())
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(repository).changeDisplay(anyString(), anyString(), anyInt(), anyString())
    }

    @Test
    fun `should fail if repository fails inside and return an error while changing display`() {
        // prepare
        `when`(repository.changeDisplay(anyString(), anyString(), anyInt(), anyString())).thenReturn(Single.error(Exception()))
        // verify
        useCase.changeDisplay("", "", 1, "")
            .test()
            .assertError(Exception::class.java)
            .assertNotComplete()
        verify(repository).changeDisplay(anyString(), anyString(), anyInt(), anyString())
    }

    @Test
    fun `should access the repository when deleting display`() {
        // prepare
        `when`(repository.deleteDisplay(anyString(), anyString(), anyInt()))
            .thenReturn(Single.just(WatchSportMode(Group(0, ""), emptyList())))
        // verify
        useCase.deleteDisplay("", "", 1)
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(repository).deleteDisplay(anyString(), anyString(), anyInt())
    }

    @Test
    fun `should fail if repository fails inside and return an error while deleting display`() {
        // prepare
        `when`(repository.deleteDisplay(anyString(), anyString(), anyInt())).thenReturn(Single.error(Exception()))
        // verify
        useCase.deleteDisplay("", "", 1)
            .test()
            .assertError(Exception::class.java)
            .assertNotComplete()
        verify(repository).deleteDisplay(anyString(), anyString(), anyInt())
    }

    @Test
    fun `should access the repository when changing field`() {
        // prepare
        `when`(repository.changeField(anyString(), anyInt(), anyInt(), anyString())).thenReturn(Single.just(""))
        // verify
        useCase.changeField(anyString(), anyInt(), anyInt(), anyString())
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(repository).changeField(anyString(), anyInt(), anyInt(), anyString())
    }

    @Test
    fun `should fail if repository fails inside and return an error while changing field`() {
        // prepare
        `when`(repository.changeField(anyString(), anyInt(), anyInt(), anyString())).thenReturn(Single.error(Exception()))
        // verify
        useCase.changeField(anyString(), anyInt(), anyInt(), anyString())
            .test()
            .assertError(Exception::class.java)
            .assertNotComplete()
        verify(repository).changeField(anyString(), anyInt(), anyInt(), anyString())
    }
}
