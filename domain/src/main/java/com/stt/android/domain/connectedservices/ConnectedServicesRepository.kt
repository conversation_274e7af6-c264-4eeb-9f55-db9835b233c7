package com.stt.android.domain.connectedservices

import io.reactivex.Completable
import io.reactivex.Single

interface ConnectedServicesRepository {
    suspend fun fetchAvailableServices(): PartnerConnectionData
    fun requestAuthForPartner(url: String, queryMap: Map<String, String>): Completable
    fun disconnectService(serviceName: String): Completable
    fun fetchPartnerConnectionUrl(url: String): Single<String>
    fun trackPartnerAutoSelect(serviceName: String?)
    fun getAutoSelectedPartner(): String?
    fun setShowPartnersListAfterConnect(showList: Boolean)
    fun getShowPartnersListAfterConnect(): Boolean
    fun setGuidePartnerListChanged(): Completable
    fun clearGuidePartnerListChanged()
    fun isGuidePartnerListChanged(): Boole<PERSON>
}
