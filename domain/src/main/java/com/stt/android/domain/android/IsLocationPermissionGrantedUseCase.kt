package com.stt.android.domain.android

import javax.inject.Inject

class IsLocationPermissionGrantedUseCase @Inject constructor(
    private val permissionStates: AppPermissionStates,
) {
    /**
     * Return foreground location permission granted state
     *
     * @return true if foreground location permission is granted, false otherwise
     */
    fun foregroundLocationPermissionGranted(): <PERSON><PERSON>an {
        return permissionStates.foregroundLocationPermissionGranted()
    }
}
