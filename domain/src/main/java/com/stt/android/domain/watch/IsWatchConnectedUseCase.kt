package com.stt.android.domain.watch

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import javax.inject.Inject

class IsWatchConnectedUseCase @Inject constructor(
    private val repository: WatchInfoRepository,
) {
    /**
     * Fetches the information whether the watch is connected to the phone and keeps listening
     * for state changes.
     */
    operator fun invoke(): Flow<Boolean> = repository.isConnected().distinctUntilChanged()
}
