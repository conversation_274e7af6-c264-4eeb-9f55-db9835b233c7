package com.stt.android.domain.session

enum class SessionStatus(val statusName: String) {
    /**
     * Valid session, user can proceed
     */
    VALID("VALID"),

    /**
     * Session is not valid, user has to login again
     */
    INVALID_NEED_LOGIN("INVALID_NEED_LOGIN"),

    /**
     * Session is not valid, user pwd has been reset and needs to generate a new password
     */
    INVALID_PWD_RESET("INVALID_PWD_RESET"),

    /**
     * Sessions is not valid, account info are not complete (ie. missing phone verification)
     */
    INVALID_ACCOUNT_INCOMPLETE("INVALID_ACCOUNT_INCOMPLETE"),

    /**
     * Sessions is not valid, account info are not complete because of missing phone verification
     */
    SMS_VERIFICATION_NEEDED("SMS_VERIFICATION_NEEDED"),

    /**
     * Couldn't fetch session status
     */
    UNKNOWN("UNKNOWN");

    companion object {
        private val statusByName: Map<String, SessionStatus> =
            entries.associateBy(SessionStatus::statusName)

        @JvmStatic
        fun fromStatusName(statusName: String): SessionStatus = statusByName[statusName] ?: UNKNOWN
    }
}
