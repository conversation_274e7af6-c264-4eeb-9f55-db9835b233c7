package com.stt.android.domain.routes

import com.stt.android.domain.Point
import java.util.UUID

data class RouteSegment(
    val startPoint: Point,
    val endPoint: Point,
    val position: Int,
    val routePoints: List<Point>,
    val ascent: Double? = null,
    val descent: Double? = null,
    // Used only for planner operations through equalsIgnorePosition and hashCodeIgnorePosition.
    val plannerUuid: String = UUID.randomUUID().toString()
) {
    fun isWaypointSegment(): Boolean = routePoints.isNotEmpty() && routePoints.last().type != null
    fun isTurnByTurnWaypointSegment(): Boolean {
        val type = routePoints.lastOrNull()?.type
        // Turn-by-turn waypoint types are from range 48..63. Check WaypointType.kt.
        return routePoints.isNotEmpty() && type != null && (type in 48..63)
    }

    fun waypointType(): Int? = routePoints.lastOrNull()?.type

    fun waypointName(): String? = routePoints.lastOrNull()?.name

    override fun hashCode(): Int {
        var result = startPoint.hashCode()
        result = 31 * result + endPoint.hashCode()
        result = 31 * result + position
        result = 31 * result + routePoints.hashCode()
        result = 31 * result + (ascent?.hashCode() ?: 0)
        result = 31 * result + (descent?.hashCode() ?: 0)
        // Ignore plannerUuid
        return result
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as RouteSegment

        if (startPoint != other.startPoint) return false
        if (endPoint != other.endPoint) return false
        if (position != other.position) return false
        if (routePoints != other.routePoints) return false
        if (ascent != other.ascent) return false
        if (descent != other.descent) return false
        // Ignore plannerUuid
        return true
    }

    fun hashCodeIgnorePosition(): Int {
        var result = startPoint.hashCode()
        result = 31 * result + endPoint.hashCode()
        result = 31 * result + routePoints.hashCode()
        result = 31 * result + (ascent?.hashCode() ?: 0)
        result = 31 * result + (descent?.hashCode() ?: 0)
        result = 31 * result + plannerUuid.hashCode()
        return result
    }

    fun equalsIgnorePosition(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as RouteSegment

        if (startPoint != other.startPoint) return false
        if (endPoint != other.endPoint) return false
        if (routePoints != other.routePoints) return false
        if (ascent != other.ascent) return false
        if (descent != other.descent) return false
        if (plannerUuid != other.plannerUuid) return false

        return true
    }

    override fun toString() =
        "RouteSegment(startPoint=$startPoint, endPoint=$endPoint, ascent=$ascent, descent=$descent, routePoints.size=${routePoints.size})"
}
