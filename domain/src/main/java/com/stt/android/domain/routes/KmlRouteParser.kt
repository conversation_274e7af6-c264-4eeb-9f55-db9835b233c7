package com.stt.android.domain.routes

import android.util.Xml
import org.xmlpull.v1.XmlPullParser
import java.io.InputStream

val NS = null

interface KmlRouteParser {

    companion object {
        const val KML = "kml"
        const val DOCUMENT = "Document"
        const val FOLDER = "Folder"
        const val PLACEMARK = "Placemark"
        const val LINE_STRING = "LineString"
        const val COORDINATES = "coordinates"
        const val NAME = "name"
        const val POINT = "Point"
        const val START_POINT = "Start"
        const val END_POINT = "End"
    }

    fun parse(inputStream: InputStream): KmlRoute?

    fun prepareXmlPullParser(inputStream: InputStream): XmlPullParser {
        val parser = Xml.newPullParser()
        parser.setFeature(XmlPullParser.FEATURE_PROCESS_NAMESPACES, false)
        parser.setInput(inputStream, null)
        parser.nextTag()
        return parser
    }

    fun skip(parser: XmlPullParser) {
        if (parser.eventType != XmlPullParser.START_TAG) {
            throw IllegalStateException()
        }
        var depth = 1
        while (depth != 0) {
            when (parser.next()) {
                XmlPullParser.END_TAG -> depth--
                XmlPullParser.START_TAG -> depth++
            }
        }
    }

    fun readString(parser: XmlPullParser, tag: String): String? {
        parser.require(XmlPullParser.START_TAG, NS, tag)
        val value = readText(parser)
        parser.require(XmlPullParser.END_TAG, NS, tag)
        return value
    }

    private fun readText(parser: XmlPullParser): String? {
        var result: String? = null
        if (parser.next() == XmlPullParser.TEXT) {
            result = parser.text
            parser.nextTag()
        }
        return result
    }

    fun readPoints(parser: XmlPullParser, tag: String): List<KmlPoint>? {
        val pointsString = readString(parser, tag)?.replace("\n", "")
        return if (pointsString.isNullOrEmpty()) {
            null
        } else {
            pointsString.split(" ")
                .filter {
                    it.isNotBlank()
                }.map { pointStr ->
                    val point = pointStr.trim().split(",").map { it.toDouble() }
                    val elevation = if (point.size < 3) 0.0 else point[2]
                    KmlPoint(longitude = point[0], latitude = point[1], elevation = elevation)
                }
        }
    }
}

inline fun readElement(parser: XmlPullParser, tag: String, readBlock: (XmlPullParser) -> Unit) {
    parser.require(XmlPullParser.START_TAG, NS, tag)
    while (parser.loopMustContinue()) {
        if (parser.eventType != XmlPullParser.START_TAG) {
            continue
        }
        readBlock.invoke(parser)
    }
}

fun XmlPullParser.loopMustContinue(): Boolean {
    val next = next()
    return next != XmlPullParser.END_TAG && next != XmlPullParser.END_DOCUMENT
}
