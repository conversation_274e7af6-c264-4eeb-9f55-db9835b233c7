package com.stt.android.domain.firstpairing

import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import io.reactivex.Completable
import io.reactivex.Single

interface FirstPairingRepository {
    fun isFirstTimePairingAttempt(): Single<Boolean>
    fun markFirstPairingAttemptAsDone(): Completable
    fun isOnboardingEverShown(deviceType: SuuntoDeviceType): Single<Boolean>
    fun markOnboardingShownAtLeastOnce(deviceType: SuuntoDeviceType): Completable
}
