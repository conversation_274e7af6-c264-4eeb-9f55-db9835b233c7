package com.stt.android.domain.routes

import androidx.annotation.VisibleForTesting
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.Point

internal const val AUTO_GENERATED_WAYPOINT_IDENTIFIER = "Auto-generated"
internal const val SEGMENT_MARKER_TYPE = 26
const val MAX_SNAP_TO_ROUTE_WAYPOINT_DISTANCE_IN_METERS = 50

abstract class BaseImportRouteUseCase(
    private val routeTool: RouteTool,
) {
    protected fun convertToRoute(
        routePoints: List<Point>,
        waypoints: List<Point>,
        username: String,
        routeName: String,
    ): ImportRouteResult {
        val (countWaypointsIgnored, segments) = addWaypoints(
            routePoints = sanitizeRoutePoints(routePoints),
            waypoints = waypoints,
        ).let { result ->
            result.countWaypointsIgnored to simplifyRouteSegments(segments = result.segments)
        }
        val startPoint = segments.first().startPoint
        val stopPoint = segments.last().endPoint
        val centerPoint = calculateCenterPoint(
            startLat = startPoint.latitude,
            startLon = startPoint.longitude,
            stopLat = stopPoint.latitude,
            stopLon = stopPoint.longitude
        )
        val verticalDelta = RouteVerticalDeltaCalc.calculateVerticalDelta(segments)
        return ImportRouteResult(
            route = Route(
                ownerUserName = username,
                name = routeName,
                startPoint = startPoint,
                centerPoint = centerPoint,
                stopPoint = stopPoint,
                activityIds = listOf(CoreActivityType.DEFAULT.id),
                segments = segments,
                locallyChanged = true,
                totalDistance = routeTool.calculateDistance(segments),
                ascent = verticalDelta?.ascent ?: 0.0,
                descent = verticalDelta?.descent ?: 0.0,
                watchEnabled = true,
            ),
            countWaypointsIgnored = countWaypointsIgnored,
        )
    }

    private fun addWaypoints(
        routePoints: List<Point>,
        waypoints: List<Point>,
    ): AddWaypointsResult {
        val result = addOnRouteWaypoints(routePoints, waypoints)
        val withAllWaypoints = result.routePoints.addLeftoverWaypoints(result.leftoverWaypoints)
        return AddWaypointsResult(
            // We require that a waypoint is always the last point in a segment.
            segments = withAllWaypoints.splitIntoSegments().toRouteSegments(),
            countWaypointsIgnored = result.countWaypointsIgnored
        )
    }

    private fun addOnRouteWaypoints(
        routePoints: List<Point>,
        waypoints: List<Point>,
    ): AddOnRouteWaypointsResult {
        // Will contain waypoints without matching route points
        val leftovers = waypoints.toMutableList()
        var previousPoint: Point? = null
        val withWaypoints = routePoints
            .mapNotNull { point ->
                if (point.isSamePosition(previousPoint)) {
                    return@mapNotNull null
                }
                previousPoint = point

                waypoints.firstOrNull { waypoint ->
                    waypoint.latitude == point.latitude && waypoint.longitude == point.longitude
                }?.let { waypoint ->
                    // Convert to on route waypoint if a waypoint exists at these coordinates
                    leftovers.remove(waypoint)

                    // if the waypoint doesn't have altitude, try to set it from the route point
                    if (waypoint.altitude == null && point.altitude != null) {
                        waypoint.copy(altitude = point.altitude)
                    } else {
                        waypoint
                    }
                } ?: point
            }

        val (leftoverWaypoints, waypointsIgnored) =
            leftoverWaypointIndexMap(leftovers, withWaypoints)
        return AddOnRouteWaypointsResult(
            routePoints = withWaypoints,
            leftoverWaypoints = leftoverWaypoints,
            countWaypointsIgnored = waypointsIgnored,
        )
    }

    private fun List<List<Point>>.toRouteSegments(): List<RouteSegment> =
        mapIndexed { index, points ->
            val verticalDelta = routeTool.calculateCumulativeVerticalDelta(points)
            RouteSegment(
                startPoint = points.first(),
                endPoint = points.last(),
                position = index,
                routePoints = points,
                ascent = verticalDelta?.ascent ?: 0.0,
                descent = verticalDelta?.descent ?: 0.0,
            )
        }

    private fun List<Point>.splitIntoSegments(): List<List<Point>> =
        foldIndexed(mutableListOf<MutableList<Point>>()) { index, acc, point ->
            if (acc.isEmpty()) acc.add(mutableListOf())
            acc.last().add(
                if (point.isSegmentMarker) {
                    point.copy(name = null, type = null)
                } else {
                    point
                }
            )
            if (point.type != null && index != lastIndex) {
                acc.add(mutableListOf(point.copy(name = null, type = null)))
            }
            acc
        }.filter(List<*>::isNotEmpty)

    private fun leftoverWaypointIndexMap(
        leftovers: List<Point>,
        routePoints: List<Point>
    ): Pair<Map<Int, Point>, Int> {
        var ignored = 0
        return leftovers.mapNotNull { waypoint ->
            val point = Point(
                longitude = waypoint.longitude,
                latitude = waypoint.latitude,
                altitude = waypoint.altitude
            )
            val nearestPoint = routeTool.findNearestPoint(point, routePoints)
            // Snap off route waypoints to route if distance is not over the allowed limit
            if (nearestPoint != null && nearestPoint.distanceInMeters <= MAX_SNAP_TO_ROUTE_WAYPOINT_DISTANCE_IN_METERS) {
                nearestPoint.index to nearestPoint.point.copy(
                    name = waypoint.name,
                    type = waypoint.type
                )
            } else {
                if (nearestPoint != null) {
                    // Waypoint too far from the route. Ignore it.
                    ignored += 1
                }
                null
            }
        }.toMap() to ignored
    }

    private fun List<Point>.addLeftoverWaypoints(
        leftoverWaypoints: Map<Int, Point>
    ): List<Point> = mapIndexed { index: Int, point: Point ->
        // Check if there is a leftover waypoint at this index and add it
        leftoverWaypoints.getOrDefault(index, point)
    }

    /*
     * Calculates the center point based on longitude and latitude of start and stop points
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun calculateCenterPoint(
        startLat: Double,
        startLon: Double,
        stopLat: Double,
        stopLon: Double,
    ): Point = Point((stopLon + startLon) / 2.0, (stopLat + startLat) / 2.0)

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun sanitizeRoutePoints(original: List<Point>): List<Point> {
        val (indexOfFirstValidPoint, firstValidPoint) = original.firstValidPoint()
            ?: return original
        val (indexOfLastValidPoint, lastValidPoint) = original.lastValidPoint()
            ?: return original
        if (indexOfFirstValidPoint == 0 && indexOfLastValidPoint == original.size - 1) {
            return original
        }

        return original.mapIndexed { index, point ->
            when {
                index < indexOfFirstValidPoint -> point.copy(altitude = firstValidPoint.altitude)
                index > indexOfLastValidPoint -> point.copy(altitude = lastValidPoint.altitude)
                else -> point
            }
        }
    }

    private fun List<Point>.firstValidPoint(): Pair<Int, Point>? {
        forEachIndexed { index, point ->
            if (point.altitude != null && point.altitude != 0.0) {
                return index to point
            }
        }
        return null
    }

    private fun List<Point>.lastValidPoint(): Pair<Int, Point>? {
        reversed().forEachIndexed { index, point ->
            // For last valid point, the altitude can be 0
            if (point.altitude != null) {
                return size - 1 - index to point
            }
        }
        return null
    }

    private val Point.isSegmentMarker: Boolean
        get() = type == SEGMENT_MARKER_TYPE && name == AUTO_GENERATED_WAYPOINT_IDENTIFIER

    data class AddWaypointsResult(
        val segments: List<RouteSegment>,
        val countWaypointsIgnored: Int,
    )

    data class AddOnRouteWaypointsResult(
        val routePoints: List<Point>,
        val leftoverWaypoints: Map<Int, Point>,
        val countWaypointsIgnored: Int,
    )
}

class ImportRouteException : RuntimeException {
    constructor(m: String) : super(m)
    constructor(e: Exception) : super(e)
    constructor()
}
