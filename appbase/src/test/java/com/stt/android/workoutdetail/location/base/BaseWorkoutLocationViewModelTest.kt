package com.stt.android.workoutdetail.location.base

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.google.android.gms.maps.model.LatLng
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.mapbox.FetchLocationNameUseCase
import com.stt.android.domain.mapbox.Place
import com.stt.android.models.MapSelectionModel
import com.stt.android.testutils.CoroutinesTestRule
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.anyString
import org.mockito.Mockito.verify
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class BaseWorkoutLocationViewModelTest {
    @Rule
    @JvmField
    val coroutinesTestRule = CoroutinesTestRule()

    @Rule
    @JvmField
    val archTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatchers = object : CoroutinesDispatchers {
        override val main = Dispatchers.Unconfined
        override val computation = Dispatchers.Unconfined
        override val io = Dispatchers.Unconfined
    }

    @Mock
    private lateinit var mapSelectionModel: MapSelectionModel

    @Mock
    private lateinit var fetchLocationNameUseCase: FetchLocationNameUseCase

    @Mock
    private lateinit var locationNameObserver: androidx.lifecycle.Observer<String>

    @Mock
    private lateinit var coordinateObserver: androidx.lifecycle.Observer<String>

    private lateinit var viewModel: BaseWorkoutLocationViewModel

    @Before
    fun setup() {
        viewModel = createViewModel(null)
    }

    @Test
    fun `test location name and coordinate are set`() {
        runTest {
            whenever(fetchLocationNameUseCase.invoke(any())).thenReturn(Place(fullAddress = LOCATION_NAME))

            viewModel.locationName.observeForever(locationNameObserver)
            viewModel.coordinate.observeForever(coordinateObserver)
            viewModel.setLocation(LatLng(60.170833, 24.9375))
            verify(locationNameObserver).onChanged(LOCATION_NAME)
            verify(coordinateObserver).onChanged(anyString())
        }
    }

    @Test
    fun `test location name request fails`() {
        runTest {
            whenever(fetchLocationNameUseCase.invoke(any())).thenThrow(RuntimeException("Error"))

            viewModel.locationName.observeForever(locationNameObserver)
            viewModel.coordinate.observeForever(coordinateObserver)
            viewModel.setLocation(LatLng(60.170833, 24.9375))
            verify(locationNameObserver).onChanged("")
            verify(coordinateObserver).onChanged(anyString())
        }
    }

    private fun createViewModel(
        latLng: LatLng?
    ): BaseWorkoutLocationViewModel = object : BaseWorkoutLocationViewModel(
        latLng = latLng,
        mapSelectionModel = mapSelectionModel,
        fetchLocationNameUseCase = fetchLocationNameUseCase,
        coroutinesDispatchers = testDispatchers,
    ) {}

    companion object {
        private const val LOCATION_NAME: String = "Helsinki"
    }
}
