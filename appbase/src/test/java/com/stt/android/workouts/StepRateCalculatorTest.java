package com.stt.android.workouts;

import com.stt.android.workouts.hardware.steps.StepRateCalculator;
import org.junit.Assert;
import org.junit.Test;

public class StepRateCalculatorTest {
    private final StepRateCalculator stepRateCalculator = new StepRateCalculator();

    @Test
    public void testInitialState() {
        StepRateCalculator stepRateCalculator = new StepRateCalculator();
        Assert.assertEquals(0, stepRateCalculator.calculateStepRate(0L));
        Assert.assertEquals(0, stepRateCalculator.calculateStepRate(1000L));
        Assert.assertEquals(0, stepRateCalculator.calculateStepRate(60000L));
    }

    @Test
    public void testStepRate() throws Exception {
        stepRateCalculator.addStepCount(1, 0L);

        // first step is ignored, so step rate is 0
        Assert.assertEquals(0, stepRateCalculator.calculateStepRate(500L));
        Assert.assertEquals(0, stepRateCalculator.calculateStepRate(1000L));

        stepRateCalculator.addStepCount(1, 1000L);

        // only 1 step so far
        Assert.assertEquals(60, stepRateCalculator.calculateStepRate(1000L));
        Assert.assertEquals(30, stepRateCalculator.calculateStepRate(2000L));
        Assert.assertEquals(12, stepRateCalculator.calculateStepRate(5000L));
        Assert.assertEquals(6, stepRateCalculator.calculateStepRate(10000L));
        Assert.assertEquals(2, stepRateCalculator.calculateStepRate(30000L));

        // the only available step is too old
        Assert.assertEquals(0, stepRateCalculator.calculateStepRate(30001L));
    }
}
