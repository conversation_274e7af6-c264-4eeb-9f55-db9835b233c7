package com.stt.android.workout.planner.common

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import com.google.common.truth.Truth.assertThat
import org.junit.Test

class OffsetMappingStringTest {
    private val spanStyle = SpanStyle(color = Color.Magenta)

    @Test
    fun `should correctly insert character at index 0`() {
        val mappedString = OffsetMappingString(AnnotatedString("abcd"), spanStyle)
        mappedString.insert(0, "!")

        assertThat(mappedString.string.text).isEqualTo("!abcd")
        assertThat(mappedString.originalToMapped).isInOrder()
        assertThat(mappedString.mappedToOriginal).isInOrder()
        // Note that the indices contain also one item past the last actual character
        assertThat(mappedString.originalToMapped).isEqualTo(listOf(1, 2, 3, 4, 5))
        assertThat(mappedString.mappedToOriginal).isEqualTo(listOf(0, 0, 1, 2, 3, 4))
    }

    @Test
    fun `should correctly insert character at end`() {
        val mappedString = OffsetMappingString(AnnotatedString("a"), spanStyle)
        mappedString.insert(1, "1")

        assertThat(mappedString.string.text).isEqualTo("a1")
        assertThat(mappedString.originalToMapped).isInOrder()
        assertThat(mappedString.mappedToOriginal).isInOrder()
        // Note that the indices contain also one item past the last actual character
        assertThat(mappedString.originalToMapped).isEqualTo(listOf(0, 2))
        assertThat(mappedString.mappedToOriginal).isEqualTo(listOf(0, 0, 1))
    }

    @Test
    fun `should correctly insert hh mm ss duration separators`() {
        val mappedString = OffsetMappingString(AnnotatedString("123456"), spanStyle)
        mappedString.insert(2, ":")
        mappedString.insert(5, "\'")

        assertThat(mappedString.string.text).isEqualTo("12:34'56")
        assertThat(mappedString.originalToMapped).isInOrder()
        assertThat(mappedString.mappedToOriginal).isInOrder()
        // Note that the indices contain also one item past the last actual character
        assertThat(mappedString.originalToMapped).isEqualTo(listOf(0, 1, 3, 4, 6, 7, 8))
        assertThat(mappedString.mappedToOriginal).isEqualTo(listOf(0, 1, 1, 2, 3, 3, 4, 5, 6))
    }

    @Test
    fun `should correctly insert and remove separators in the middle of the string`() {
        val mappedString = OffsetMappingString(AnnotatedString("abcdef"), spanStyle)
        mappedString.insert(2, ":") // ab:cdef
        mappedString.insert(5, "\'") // ab:cd'ef
        mappedString.removeAt(2) // abcd'ef
        mappedString.removeAt(4) // abcdef

        assertThat(mappedString.string.text).isEqualTo("abcdef")
        assertThat(mappedString.originalToMapped).isInOrder()
        assertThat(mappedString.mappedToOriginal).isInOrder()
        // Note that the indices contain also one item past the last actual character
        assertThat(mappedString.originalToMapped).isEqualTo(listOf(0, 1, 2, 3, 4, 5, 6))
        assertThat(mappedString.mappedToOriginal).isEqualTo(listOf(0, 1, 2, 3, 4, 5, 6))
    }

    @Test
    fun `should correctly remove first of two consecutive separators in the middle of the string`() {
        val mappedString = OffsetMappingString(AnnotatedString("abcd"), spanStyle)
        mappedString.insert(2, ":") // ab:cd
        mappedString.insert(3, "!") // ab:!cd
        mappedString.removeAt(2) // ab!cd

        assertThat(mappedString.string.text).isEqualTo("ab!cd")
        assertThat(mappedString.originalToMapped).isInOrder()
        assertThat(mappedString.mappedToOriginal).isInOrder()
        // Note that the indices contain also one item past the last actual character
        assertThat(mappedString.originalToMapped).isEqualTo(listOf(0, 1, 3, 4, 5))
        assertThat(mappedString.mappedToOriginal).isEqualTo(listOf(0, 1, 1, 2, 3, 4))
    }

    @Test
    fun `should correctly insert multiple characters`() {
        val mappedString = OffsetMappingString(AnnotatedString("abcd"), spanStyle)
        mappedString.insert(2, "1234") // ab1234cd

        assertThat(mappedString.string.text).isEqualTo("ab1234cd")
        assertThat(mappedString.originalToMapped).isInOrder()
        assertThat(mappedString.mappedToOriginal).isInOrder()
        // Note that the indices contain also one item past the last actual character
        assertThat(mappedString.originalToMapped).isEqualTo(listOf(0, 1, 6, 7, 8))
        assertThat(mappedString.mappedToOriginal).isEqualTo(listOf(0, 1, 1, 1, 1, 1, 2, 3, 4))
    }

    @Test
    fun `should correctly filter out characters`() {
        val mappedString = OffsetMappingString(AnnotatedString("a12b34c56"), spanStyle)
        mappedString.filter { it.isDigit() }

        assertThat(mappedString.string.text).isEqualTo("123456")
        assertThat(mappedString.originalToMapped).isInOrder()
        assertThat(mappedString.mappedToOriginal).isInOrder()
        // Note that the indices contain also one item past the last actual character
        assertThat(mappedString.originalToMapped).isEqualTo(listOf(0, 0, 1, 2, 2, 3, 4, 4, 5, 6))
        assertThat(mappedString.mappedToOriginal).isEqualTo(listOf(1, 2, 4, 5, 7, 8, 9))
    }

    @Test
    fun `should apply spanStyle to inserted character`() {
        val mappedString = OffsetMappingString(AnnotatedString("abcd"), spanStyle)
        mappedString.insert(2, ":") // ab:cd

        val spanStyles = mappedString.string.spanStyles
        assertThat(spanStyles).hasSize(1)
        val style = spanStyles.first()
        assertThat(style.item.color).isEqualTo(spanStyle.color)
        assertThat(style.start).isEqualTo(2)
        assertThat(style.end).isEqualTo(3)
    }
}
