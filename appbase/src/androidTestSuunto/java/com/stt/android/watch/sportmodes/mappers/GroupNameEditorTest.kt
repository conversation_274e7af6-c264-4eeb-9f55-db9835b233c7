package com.stt.android.watch.sportmodes.mappers

import com.google.common.truth.Truth.assertThat
import com.stt.android.domain.sportmodes.Group
import com.stt.android.moshi.buildBasicMoshi
import org.junit.Before
import org.junit.Test

class GroupNameEditorTest {
    private val moshi = buildBasicMoshi()
    private lateinit var groupNameEditor: SportModeJsonEditor

    @Before
    fun setup() {
        groupNameEditor = SportModeJsonEditor(moshi)
    }

    @Test
    fun checkThatEditorUpdatesGroupAndGetsNameProperly() {
        val json = """
            {
              "Settings": {
                "CustomModeGroups": [{
                  "ActivityID": 39,
                  "AutoTransition": false,
                  "CustomModeIDs": [560003],
                  "CustomModePackageID": 0,
                  "CustomModePackageVersion": 0,
                  "Name": "New sport mode",
                  "MoveType": 0,
                  "TransitionPhase": false
                }]
              }
            }
        """.trimIndent()
        val group = Group(0, json)
        val testValue = "test"
        val updatedGroup = groupNameEditor.getGroupWithUpdatedName(testValue, group)
        val updatedGroupName = groupNameEditor.getGroupNameAsString(updatedGroup)
        assertThat(updatedGroupName).isEqualTo(testValue)
    }

    @Test
    fun checkThatEditorDoesNotMessUpNumberFormatForIntegers() {
        val json = """
        {
          "Settings": {
            "CustomModeGroups": [
              {
                "ActivityID": 9,
                "AutoTransition": false,
                "CustomModeIDs": [
                  -1073741824
                ],
                "CustomModePackageID": 0,
                "CustomModePackageVersion": 0,
                "Name": "New sport mode",
                "MoveType": 0,
                "TransitionPhase": false
              }
            ]
          }
        }
        """.trimIndent()
        val group = Group(0, json)
        val updatedValue = groupNameEditor.getGroupWithUpdatedName("New sport mode", group).value
        assertThat(updatedValue).contains("\"CustomModeIDs\":[-1073741824]") // CustomModeIDs as integer
        assertThat(updatedValue).matches(".*\"ActivityID\":9[^.].*") // ActivityID without decimal point
        assertThat(updatedValue).matches(".*\"MoveType\":0[^.].*") // MoveType without decimal point
    }
}
