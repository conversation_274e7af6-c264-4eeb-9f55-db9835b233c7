package com.stt.android.di.timeline

import com.stt.android.data.timeline.TimelineResourceRepository
import com.stt.android.data.timeline.WeChatTimelineResourceRepository
import com.stt.android.di.ConnectivityModule
import com.stt.android.timeline.TimelineResourceLocalDataSource
import com.stt.android.timeline.WeChatTimelineResourceLocalDataSource
import dagger.Binds
import dagger.Module

@Module
@ConnectivityModule
abstract class TimelineResourceModule {

    @Binds
    abstract fun bindTimelineResourceDataSource(
        timelineResourceRepository: TimelineResourceRepository
    ): TimelineResourceLocalDataSource

    @Binds
    abstract fun bindWeChatTimelineResourceLocalDataSource(
        newTimelineResourceRepository: WeChatTimelineResourceRepository
    ): WeChatTimelineResourceLocalDataSource
}
