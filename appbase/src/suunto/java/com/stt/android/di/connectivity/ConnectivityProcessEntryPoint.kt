package com.stt.android.di.connectivity

import com.stt.android.di.initializer.SecondaryProcessInitializer
import com.suunto.connectivity.repository.AnalyticsRuntimeHook
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Qualifier

/**
 * Component used in the connectivity process
 */
@InstallIn(SingletonComponent::class)
@EntryPoint
interface ConnectivityProcessEntryPoint {
    fun analyticsRuntimeHook(): AnalyticsRuntimeHook

    @ConnectivityProcess
    fun connectivityProcessInitializers(): Set<@JvmSuppressWildcards SecondaryProcessInitializer>
}

@Qualifier
@MustBeDocumented
@Retention
annotation class ConnectivityProcess
