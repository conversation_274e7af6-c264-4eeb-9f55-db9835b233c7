package com.stt.android.billing

import com.stt.android.controllers.SubscriptionItemControllerDataSource
import com.stt.android.domain.UserSession
import com.stt.android.domain.user.SubscriptionItem
import com.stt.android.domain.user.UserSubscription
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SubscriptionItemControllerNoOp @Inject constructor() :
    SubscriptionItemControllerDataSource {
    override suspend fun store(subscriptionItem: SubscriptionItem) = Unit

    override suspend fun loadAllUserSubscriptions(): List<UserSubscription> = emptyList()

    override suspend fun loadValidSubscription(): UserSubscription? = null

    override fun validSubscriptionFlow(): Flow<UserSubscription?> = flowOf(null)

    override suspend fun empty() = Unit

    override suspend fun refreshUserSubscriptions() = Unit

    override suspend fun refreshUserSubscriptions(session: UserSession) = Unit
}
