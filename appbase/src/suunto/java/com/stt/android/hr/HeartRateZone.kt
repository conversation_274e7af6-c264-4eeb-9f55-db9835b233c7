package com.stt.android.hr

import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import com.stt.android.R
import kotlin.math.roundToInt
import com.stt.android.core.R as CR

enum class HeartRateZone(
    @field:StringRes val title: Int,
    @field:StringRes val description: Int,
    @field:ColorRes val color: Int,
    val lowPercentage: Int,
    val highPercentage: Int
) {
    PEAK(
        R.string.heart_rate_zone_peak_title,
        R.string.heart_rate_zone_peak_description,
        CR.color.heart_rate_5,
        87,
        100
    ),
    ANAEROBIC(
        R.string.heart_rate_zone_anaerobic_title,
        R.string.heart_rate_zone_anaerobic_description,
        CR.color.heart_rate_4,
        82,
        87
    ),
    AEROBIC(
        R.string.heart_rate_zone_aerobic_title,
        R.string.heart_rate_zone_aerobic_description,
        CR.color.heart_rate_3,
        76,
        82
    ),
    ENDURANCE(
        R.string.heart_rate_zone_endurance_title,
        R.string.heart_rate_zone_endurance_description,
        CR.color.heart_rate_2,
        72,
        76
    ),
    WARMUP(
        R.string.heart_rate_zone_warmup_title,
        R.string.heart_rate_zone_warmup_description,
        CR.color.heart_rate_1,
        0,
        72
    );

    fun getLowBpm(max: Int): Int = if (lowPercentage > 0) {
        (lowPercentage / 100.0f * max).roundToInt() + 1
    } else {
        0
    }

    fun getHighBpm(max: Int): Int = (highPercentage / 100.0f * max).roundToInt()
}
