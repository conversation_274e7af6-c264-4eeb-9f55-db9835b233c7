package com.stt.android.utils

import timber.log.Timber
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream

private const val BUFFER_SIZE = 1024 * 10

@Throws(IOException::class)
fun File.unzip(destination: String) {
    val stream = FileInputStream(this)
    unzip(stream, destination)
}

@Throws(IOException::class)
fun InputStream.unzipFromAssets(destination: String) {
    unzip(this, destination)
}

private fun unzip(stream: InputStream, destination: String) {
    val destinationDirectory = File(destination, "")
    makeDirectoryIfNeeded(destination, "")
    val buffer = ByteArray(BUFFER_SIZE)
    ZipInputStream(stream).use { zin ->
        var zipEntry: ZipEntry? = zin.nextEntry

        while (zipEntry != null) {
            Timber.v("Unzipping %s", zipEntry.name)
            ZipUtils.checkZipPathTraversal(destinationDirectory, zipEntry)

            if (zipEntry.isDirectory) {
                makeDirectoryIfNeeded(destination, zipEntry.name)
            } else {
                val file = File(destination, zipEntry.name)
                if (file.exists()) {
                    file.delete()
                }
                file.parentFile?.mkdirs()
                val success = file.createNewFile()
                if (!success) {
                    throw IOException("Failed to create file ${file.name}")
                }
                FileOutputStream(file).use {
                    var count = zin.read(buffer)
                    while (count != -1) {
                        it.write(buffer, 0, count)
                        count = zin.read(buffer)
                    }
                }
            }
            zin.closeEntry()
            zipEntry = zin.nextEntry
        }
    }
}

private fun makeDirectoryIfNeeded(destination: String, dir: String) {
    val file = File(destination, dir)

    if (!file.isDirectory) {
        val success = file.mkdirs()
        if (!success) {
            throw IOException("Failed to create folder ${file.name}")
        }
    }
}
