package com.stt.android.ui.activities.settings

import androidx.lifecycle.LiveData
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.RxViewModel
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.manage.ForgetWatchHelper
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.plusAssign
import io.reactivex.rxkotlin.subscribeBy
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class ManageConnectViewModel @Inject constructor(
    private val forgetWatchHelper: ForgetWatchHelper,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers
) : RxViewModel(ioThread, mainThread, coroutinesDispatchers) {

    private val _deviceTypeLD = SingleLiveEvent<SuuntoDeviceType>()
    val deviceTypeLD: LiveData<SuuntoDeviceType> = _deviceTypeLD

    private val _showLoading = SingleLiveEvent<Boolean>()
    val showLoading = _showLoading

    private val _unpairSuccess = SingleLiveEvent<SuuntoBtDevice>()
    val unpairSuccess = _unpairSuccess

    fun onForget() {
        disposables.add(
            forgetWatchHelper.unpair(
                onProgress = {
                    _showLoading.postValue(true)
                },
                onSuccess = {
                    _unpairSuccess.postValue(it)
                    _showLoading.postValue(false)
                },
                onFail = {
                    _showLoading.postValue(false)
                }
            )
        )
        disposables.add(forgetWatchHelper.sendEventToAnalytics(AnalyticsEvent.SUUNTO_FORGET_WATCH))
    }

    fun init() {
        disposables.add(forgetWatchHelper.sendEventToAnalytics(AnalyticsEvent.SUUNTO_WATCH_MANAGE_CONNECTION))
        disposables += forgetWatchHelper.getCurrentWatch()
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeBy(onSuccess = {
                val type = it.suuntoBtDevice.deviceType
                _deviceTypeLD.postValue(type)
            }, onError = {
                if (it is MissingCurrentWatchException) {
                    Timber.i(it, "Missing current watch")
                } else {
                    Timber.w(it, "Error during get current watch")
                }
            })
    }
}
