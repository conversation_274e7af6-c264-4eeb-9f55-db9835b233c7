package com.stt.android.ui.activities.settings.watch.notifications.domain

import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.ui.activities.settings.watch.notifications.datasource.NotificationsDataSource
import kotlinx.coroutines.withContext
import javax.inject.Inject

class FetchNotificationsEnabledUseCase @Inject constructor(
    private val dataSource: NotificationsDataSource,
    private val dispatcherProvider: CoroutinesDispatcherProvider
) {

    suspend fun run() = withContext(dispatcherProvider.io) {
        dataSource.fetchNotificationsEnabled()
    }
}
