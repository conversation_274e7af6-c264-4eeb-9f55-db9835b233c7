package com.stt.android.help

enum class HelpshiftTag(val tagName: String) {
    // Suunto app, partner related FAQs which are valid in all use cases
    SUU_COMMON(tagName = "SUU_common"),

    // FAQ that is deep linked from the APP hard coded
    SUU_HARDCODED(tagName = "SUU_hardcoded"),

    // FAQ for legacy suunto app users
    SUU_LEGACY_SUUNTO_APP(tagName = "SUU_legacy_suunto_app"),

    // FAQ to be displayed if no device is paired/connected to Suunto app
    SUU_NO_DEVICE(tagName = "SUU_no_device"),

    // product specific FAQ
    SUU_SUUNTO_9(tagName = "SUU_suunto_9"),

    // product specific FAQ
    SUU_SUUNTO_9_BARO(tagName = "SUU_suunto_9_baro"),

    // product specific FAQ
    SUU_SUUNTO_9_PEAK(tagName = "SUU_suunto_9_peak"),

    // product specific FAQ
    SUU_SUUNTO_9_PEAK_PRO(tagName = "SUU_suunto_9_peak_pro"),

    // product specific FAQ
    SUU_SUUNTO_3_FITNESS(tagName = "SUU_suunto_3_fitness"),

    // product specific FAQ
    SUU_SUUNTO_3(tagName = "SUU_suunto_3"),

    // product specific FAQ
    SUU_SPARTAN_TRAINER(tagName = "SUU_spartan_trainer"),

    // product specific FAQ
    SUU_SPARTAN_SPORT(tagName = "SUU_spartan_sport"),

    // product specific FAQ
    SUU_SPARTAN_SPORT_WHR(tagName = "SUU_spartan_sport_whr"),

    // product specific FAQ
    SUU_SPARTAN_SPORT_WHR_BARO(tagName = "SUU_spartan_sport_whr_baro"),

    // product specific FAQ
    SUU_SPARTAN_ULTRA(tagName = "SUU_spartan_ultra"),

    // product specific FAQ
    SUU_AMBIT3_PEAK(tagName = "SUU_ambit3_peak"),

    // product specific FAQ
    SUU_AMBIT3_SPORT(tagName = "SUU_ambit3_sport"),

    // product specific FAQ
    SUU_AMBIT3_RUN(tagName = "SUU_ambit3_run"),

    // product specific FAQ
    SUU_AMBIT3_VERTICAL(tagName = "SUU_ambit3_vertical"),

    // product specific FAQ
    SUU_TRAVERSE(tagName = "SUU_traverse"),

    // product specific FAQ
    SUU_TRAVERSE_ALPHA(tagName = "SUU_traverse_alpha"),

    // product specific FAQ
    SUU_SUUNTO_5(tagName = "SUU_suunto_5"),

    // product specific FAQ
    SUU_SUUNTO_5_PEAK(tagName = "SUU_suunto_5_peak"),

    // product specific FAQ
    SUU_SUUNTO_7(tagName = "SUU_suunto_7"),

    // product specific FAQ
    SUU_SUUNTO_D5(tagName = "SUU_suunto_d5"),

    // product specific FAQ
    SUU_EON_CORE(tagName = "SUU_eon_core"),

    // product specific FAQ
    SUU_EON_STEEL(tagName = "SUU_eon_steel"),

    // product specific FAQ
    SUU_EON_STEEL_BLACK(tagName = "SUU_eon_steel_black")
}
