package com.stt.android.watch.gearevent

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.remote.gearevent.GearEventRemoteApi
import timber.log.Timber
import javax.inject.Inject

class GearEventUnpairSender @Inject constructor(
    private val gearEventRemoteApi: GearEventRemoteApi,
) {
    suspend fun sendUnpairEventNow(serial: String) {
        runSuspendCatching {
            gearEventRemoteApi.sendUnpairEvent(serial)
        }.onFailure { e ->
            Timber.w(e, "Failed to send unpair event to gear API")
        }
    }
}
