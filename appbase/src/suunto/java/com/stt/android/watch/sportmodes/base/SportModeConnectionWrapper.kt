package com.stt.android.watch.sportmodes.base

import com.stt.android.R
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.sync.SyncState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class SportModeConnectionWrapper @Inject constructor(
    suuntoWatchModel: SuuntoWatchModel,
) {
    private val watchConnectedFlow: Flow<Boolean> = suuntoWatchModel
        .isConnectedFlow
        .catch { emit(false) }
        .distinctUntilChanged()

    private val watchSyncingFlow: Flow<Boolean> = suuntoWatchModel
        .watchStates()
        .map {
            it.syncState.state != SyncState.Companion.NOT_SYNCING && it.isConnected
        }
        .catch { emit(false) }
        .distinctUntilChanged()

    private val watchBusyFlow: Flow<Boolean> = suuntoWatchModel
        .watchStates()
        .map {
            it.isDeviceBusy
        }
        .catch { emit(false) }
        .distinctUntilChanged()

    val unableToEditReasonFlow: Flow<Int?> = combine(
        watchConnectedFlow,
        watchSyncingFlow,
        watchBusyFlow
    ) { connected, syncing, busy ->
        if (!connected) {
            R.string.watch_updates_watch_not_connected
        } else if (syncing) {
            R.string.sport_mode_watch_syncing_edit_display_screen
        } else if (busy) {
            R.string.watch_ui_status_watch_busy
        } else {
            null
        }
    }
}
