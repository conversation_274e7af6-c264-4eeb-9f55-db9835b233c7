package com.stt.android.watch.wifi

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.stt.android.R
import com.stt.android.compose.widgets.InfoDialog
import com.stt.android.watch.wifi.entity.WifiSecurity
import com.stt.android.watch.wifi.ui.AddWifiNetworkScreen
import com.stt.android.watch.wifi.ui.SavedWifiNetworkDetailsScreen
import com.stt.android.watch.wifi.ui.SavedWifiNetworksScreen
import com.stt.android.watch.wifi.ui.WifiPasswordScreen
import com.stt.android.watch.wifi.ui.dialogs.ConfirmConnectToOpenNetwork
import com.stt.android.watch.wifi.ui.dialogs.ConnectionSuccessDialog
import com.stt.android.watch.wifi.ui.dialogs.UnableToConnect
import com.stt.android.watch.wifi.ui.dialogs.WrongPassword
import com.suunto.connectivity.wifi.WifiResultReason
import kotlinx.coroutines.launch

object WifiNetworksDestinations {
    const val SAVED_NETWORKS = "savedNetworks"
    const val ADD_NETWORK = "addNetwork"
    const val PASSWORD = "password"
    const val NETWORK_DETAILS = "networkDetails"
}

@Composable
fun WifiNetworksNavHost(
    finishActivity: (didWifiSetup: Boolean) -> Unit,
    navigateToAddNetwork: Boolean,
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController(),
    startDestination: String = WifiNetworksDestinations.SAVED_NETWORKS,
    viewModel: WifiNetworksViewModel = hiltViewModel()
) {
    // Ignored in further composition rounds
    var openAddNetwork = rememberSaveable {
        navigateToAddNetwork
    }

    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier,
        enterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                tween(TRANSITION_DURATION_MS)
            )
        },
        exitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                tween(TRANSITION_DURATION_MS)
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                tween(TRANSITION_DURATION_MS)
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                tween(TRANSITION_DURATION_MS)
            )
        }
    ) {
        composable(WifiNetworksDestinations.SAVED_NETWORKS) {
            SavedWifiNetworksScreen(
                isWatchConnected = viewModel.isWatchConnected,
                isWatchSyncing = viewModel.isWatchSyncing,
                isWatchBusy = viewModel.isWatchBusy,
                isWifiEnabled = viewModel.isWifiEnabled,
                savedNetworks = viewModel.savedNetworks,
                loading = viewModel.loadingSavedNetworks,
                onSavedNetworkClick = { networkInfo ->
                    navController.navigate("${WifiNetworksDestinations.NETWORK_DETAILS}/${networkInfo.name}")
                },
                onWifiEnabledChange = { viewModel.setWifiEnabled(it) },
                onAddNetwork = {
                    viewModel.scanAvailableNetworks()
                    navController.navigate(WifiNetworksDestinations.ADD_NETWORK)
                },
                finishActivity = {
                    val didWifiSetup = !viewModel.savedNetworks.isNullOrEmpty()
                    finishActivity(didWifiSetup)
                }
            )

            if (viewModel.isConnectionSuccess) {
                ConnectionSuccessDialog(
                    ssid = viewModel.currentNetwork.name,
                    onDismissRequest = { viewModel.resetIsConnectionSuccess() }
                )
            }

            LaunchedEffect(Unit) {
                if (openAddNetwork) {
                    openAddNetwork = false
                    navController.navigate(WifiNetworksDestinations.ADD_NETWORK)
                }
            }
        }
        composable(WifiNetworksDestinations.ADD_NETWORK) {
            AddWifiNetworkScreen(
                isWatchConnected = viewModel.isWatchConnected,
                isWatchSyncing = viewModel.isWatchSyncing,
                isWatchBusy = viewModel.isWatchBusy,
                availableNetworks = viewModel.availableNetworks,
                loading = viewModel.scanningAvailableNetworks,
                batteryLevelCheckState = viewModel.batteryLevelCheckState,
                usbCableState = viewModel.usbCableState,
                onChargingPromptDismiss = viewModel::onChargingPromptDismissed,
                onSelectNetwork = { networkInfo ->
                    viewModel.currentNetwork = networkInfo
                    if (networkInfo.security == WifiSecurity.OPEN) {
                        viewModel.showConfirmConnectToOpenNetwork = true
                    } else {
                        navController.navigate(WifiNetworksDestinations.PASSWORD)
                    }
                },
                onRefresh = viewModel::scanAvailableNetworks,
                navigateBack = {
                    viewModel.onChargingPromptDismissed()
                    navController.navigateUp()
                }
            )

            if (viewModel.showConfirmConnectToOpenNetwork) {
                ConfirmConnectToOpenNetwork(
                    ssid = viewModel.currentNetwork.name,
                    onConfirmRequest = {
                        viewModel.saveNetwork(viewModel.currentNetwork, "")
                        if (viewModel.currentNetwork.security == WifiSecurity.OPEN) {
                            navController.navigateUp()
                        }
                        navController.navigate(WifiNetworksDestinations.PASSWORD)
                    },
                    onDismissRequest = { viewModel.showConfirmConnectToOpenNetwork = false }
                )
            }

            if (viewModel.showDeviceBusyDownloading) {
                InfoDialog(
                    title = stringResource(id = R.string.watch_busy_downloading_dialog_title),
                    text = stringResource(id = R.string.watch_busy_downloading_dialog_text),
                    confirmButtonText = stringResource(id = R.string.ok),
                    onDismissRequest = { viewModel.showDeviceBusyDownloading = false },
                    onConfirm = { viewModel.showDeviceBusyDownloading = false }
                )
            }

            val error = viewModel.savingNetworkError
            if (error != SaveNetworkError.NO_ERROR && viewModel.currentNetwork.security == WifiSecurity.OPEN) {
                val ssid = viewModel.currentNetwork.name
                UnableToConnect(
                    ssid,
                    resultCode = error.resultCode,
                    resultReason = error.resultReason.value,
                    onDismissRequest = {
                        viewModel.resetSavingNetworkError()
                    }
                )
            }
        }
        composable(WifiNetworksDestinations.PASSWORD) {
            WifiPasswordScreen(
                isWatchConnected = viewModel.isWatchConnected,
                networkInfo = viewModel.currentNetwork,
                savingNetwork = viewModel.savingNetwork,
                onSaveNetwork = { networkInfo, password ->
                    viewModel.saveNetwork(networkInfo, password)
                },
                navigateBack = {
                    viewModel.cancelSavingNetwork()
                    navController.navigateUp()
                }
            )

            if (viewModel.networkSaved) {
                LaunchedEffect(viewModel.networkSaved) {
                    navController.popBackStack(
                        WifiNetworksDestinations.SAVED_NETWORKS,
                        inclusive = false
                    )
                    viewModel.networkSaved = false
                }
            }

            val error = viewModel.savingNetworkError
            if (error != SaveNetworkError.NO_ERROR) {
                val ssid = viewModel.currentNetwork.name
                when (error.resultReason) {
                    WifiResultReason.WRONG_PWD -> WrongPassword(
                        ssid,
                        onDismissRequest = {
                            viewModel.resetSavingNetworkError()
                        }
                    )

                    else -> {
                        if (viewModel.currentNetwork.security == WifiSecurity.OPEN) {
                            LaunchedEffect(key1 = Unit) {
                                navController.navigateUp()
                                navController.navigate(WifiNetworksDestinations.ADD_NETWORK)
                            }
                        } else {
                            UnableToConnect(
                                ssid,
                                resultCode = error.resultCode,
                                resultReason = error.resultReason.value,
                                onDismissRequest = {
                                    viewModel.resetSavingNetworkError()
                                }
                            )
                        }
                    }
                }
            }
        }
        composable(
            "${WifiNetworksDestinations.NETWORK_DETAILS}/{$ROUTE_ARGUMENT_SSID}",
            arguments = listOf(navArgument(ROUTE_ARGUMENT_SSID) { type = NavType.StringType })
        ) { backStackEntry ->
            val ssid = backStackEntry.arguments?.getString(ROUTE_ARGUMENT_SSID)
            val networkInfo = viewModel.savedNetworks?.firstOrNull { it.name == ssid }
            if (networkInfo != null) {
                SavedWifiNetworkDetailsScreen(
                    isWatchConnected = viewModel.isWatchConnected,
                    loading = viewModel.forgettingNetwork,
                    networkInfo = networkInfo,
                    onForgetNetwork = {
                        with(viewModel) {
                            viewModelScope.launch {
                                if (viewModel.forgetNetwork(networkInfo)) {
                                    navController.popBackStack(
                                        WifiNetworksDestinations.SAVED_NETWORKS,
                                        inclusive = false
                                    )
                                }
                            }
                        }
                    },
                    navigateBack = { navController.navigateUp() }
                )
            }
        }
    }
}

private const val TRANSITION_DURATION_MS = 300
private const val ROUTE_ARGUMENT_SSID = "ssid"
