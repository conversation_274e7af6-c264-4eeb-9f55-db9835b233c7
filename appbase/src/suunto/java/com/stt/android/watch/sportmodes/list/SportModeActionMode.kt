package com.stt.android.watch.sportmodes.list

import android.view.Menu
import android.view.MenuItem
import androidx.annotation.MenuRes
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.view.ActionMode
import androidx.lifecycle.LiveData
import com.stt.android.R
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.scopes.ActivityRetainedScoped
import javax.inject.Inject

@ActivityRetainedScoped
class SportModeActionMode @Inject constructor() : ActionMode.Callback {

    private val _sportModeActionModeEvent = SingleLiveEvent<SportModeActionModeEvent>()

    val sportModeActionModeEvent: LiveData<SportModeActionModeEvent>
        get() = _sportModeActionModeEvent

    private var mode: ActionMode? = null

    @MenuRes
    private var menuResId: Int = 0
    var title: String? = null
        private set
    private var subtitle: String? = null

    override fun onCreateActionMode(mode: ActionMode, menu: Menu): Boolean {
        this.mode = mode
        mode.menuInflater.inflate(menuResId, menu)
        mode.title = title
        mode.subtitle = subtitle
        _sportModeActionModeEvent.value = SportModeActionModeEvent.OnActionModeToggled
        return true
    }

    override fun onPrepareActionMode(mode: ActionMode, menu: Menu): Boolean {
        return false
    }

    override fun onDestroyActionMode(mode: ActionMode) {
        this.mode = null
        _sportModeActionModeEvent.value = SportModeActionModeEvent.OnDestroyActionMode
    }

    override fun onActionItemClicked(mode: ActionMode, item: MenuItem): Boolean {
        return if (item.itemId == R.id.delete_sportmode) {
            _sportModeActionModeEvent.value = SportModeActionModeEvent.OnDeleteClicked
            true
        } else {
            false
        }
    }

    fun isActionModeActive() = mode != null

    fun updateTitleAndDeleteButton(selectedAmount: Int) {
        title?.let {
            mode?.title = String.format(it, selectedAmount)
        }

        val enableDelete = selectedAmount > 0
        mode?.menu?.findItem(R.id.delete_sportmode)?.isEnabled = enableDelete
    }

    fun startActionMode(
        activity: AppCompatActivity,
        @MenuRes menuResId: Int,
        title: String? = null,
        subtitle: String? = null
    ) {
        this.menuResId = menuResId
        this.title = title
        this.subtitle = subtitle
        activity.startSupportActionMode(this)
    }

    fun finishActionMode() {
        mode?.finish()
    }
}
