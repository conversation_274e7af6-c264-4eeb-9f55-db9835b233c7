package com.stt.android.watch.connecting

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.utils.STTConstants
import com.stt.android.watch.DeviceStateUpdate
import com.stt.android.watch.DeviceTextFormatter
import com.stt.android.watch.DeviceViewModel
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import javax.inject.Inject

@HiltViewModel
class DeviceConnectingViewModel
@Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val textFormatter: DeviceTextFormatter
) : DeviceViewModel(ioThread, mainThread) {

    val isRegistered = MutableLiveData<Boolean>().apply { value = false }

    var deviceType: SuuntoDeviceType? = null

    val connectingInstructionMessage: LiveData<String>
        get() = _connectingInstructionMessage
    private val _connectingInstructionMessage = MutableLiveData<String>().apply { value = "" }

    fun onNeedHelpClick() {
        when {
            deviceType?.isSuunto7 == true -> {
                sharedViewModel.onSuunto7HelpArticleClick(
                    STTConstants.HelpShiftPublishId.SUUNTO_7_NOT_CONNECTED,
                    AnalyticsPropertyValue.HelpshiftSource.CONNECTING_TO_EXISTING_WATCH,
                    AnalyticsPropertyValue.HelpshiftArticleOpenedSourceProperty.NOT_CONNECTED,
                    true
                )
            }
            deviceType?.isEonComputer == true -> {
                sharedViewModel.onHelpArticleClick(
                    STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_EON,
                    AnalyticsPropertyValue.HelpshiftSource.CONNECTING_TO_EXISTING_WATCH
                )
            }
            deviceType?.isSuuntoD5 == true -> {
                sharedViewModel.onHelpArticleClick(
                    STTConstants.HelpShiftPublishId.SUUNTO_WATCH_PAIRING_INSTRUCTIONS_SUUNTO_D5,
                    AnalyticsPropertyValue.HelpshiftSource.CONNECTING_TO_EXISTING_WATCH
                )
            }
            deviceType?.isAmbit == true || deviceType?.isTraverse == true -> {
                sharedViewModel.onHelpArticleClick(
                    STTConstants.HelpShiftPublishId.SUUNTO_WATCH_DISCONNECTED_HELP_LEGAGY,
                    AnalyticsPropertyValue.HelpshiftSource.PAIRING_FAILED
                )
            }
            else -> {
                sharedViewModel.onHelpArticleClick(
                    STTConstants.HelpShiftPublishId.SUUNTO_WATCH_SYNCING_OR_PAIRING_ISSUE,
                    AnalyticsPropertyValue.HelpshiftSource.CONNECTING_TO_EXISTING_WATCH
                )
            }
        }
    }

    override fun onDeviceStateUpdate(state: DeviceStateUpdate) {
        this.isRegistered.value = state.registered

        state.deviceType?.isDataLayerDevice?.let {
            deviceType = state.deviceType
            _connectingInstructionMessage.value = textFormatter.formatConnectingInstructionMessage(
                isDataLayerDevice = it
            )
        }
    }
}
