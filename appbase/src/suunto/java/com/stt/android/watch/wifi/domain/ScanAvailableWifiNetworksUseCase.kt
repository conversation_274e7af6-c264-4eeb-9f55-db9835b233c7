package com.stt.android.watch.wifi.domain

import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.watch.wifi.datasource.WifiNetworksDataSource
import com.stt.android.watch.wifi.entity.WifiNetworkInfo
import kotlinx.coroutines.withContext
import javax.inject.Inject

class ScanAvailableWifiNetworksUseCase @Inject constructor(
    private val dataSource: WifiNetworksDataSource,
    private val dispatcherProvider: CoroutinesDispatcherProvider,
) {
    suspend fun run(): List<WifiNetworkInfo> = withContext(dispatcherProvider.io) {
        dataSource.scanAvailableWifiNetworks()
    }
}
