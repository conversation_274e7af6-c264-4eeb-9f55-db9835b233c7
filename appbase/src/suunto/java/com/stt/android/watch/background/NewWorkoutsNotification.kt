package com.stt.android.watch.background

import android.annotation.SuppressLint
import android.app.PendingIntent
import android.content.Context
import androidx.core.app.NotificationManagerCompat
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceType.PUSH_MESSAGE
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceTypeDetail.ACTIVITIES_SYNCED
import com.stt.android.analytics.AppOpenAnalyticsActivity
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.notifications.CHANNEL_ID_ACTIVITY_SYNCED
import com.stt.android.notifications.STTNotificationUI
import timber.log.Timber

@SuppressLint("MissingPermission")
fun showNewWorkoutsNotification(
    context: Context,
    workoutHeaderController: WorkoutHeaderController,
    homeActivityNavigator: HomeActivityNavigator,
    userName: String,
    getNewWorkoutSyncedMessage: GetNewWorkoutSyncedMessage
) {
    val syncedMessage = getNewWorkoutSyncedMessage.getNewWorkoutSyncedMessage() ?: return

    val builder = STTNotificationUI.buildBasicNotificationBuilder(
        context,
        CHANNEL_ID_ACTIVITY_SYNCED
    )
    val intent = homeActivityNavigator.newStartIntentToDiaryWorkoutList(context)
    // Create a synthetic back stack for the activity that is launched from the notification
    val stackBuilder = homeActivityNavigator.getTaskStackBuilder(context, intent)
    stackBuilder.addNextIntent(AppOpenAnalyticsActivity.newStartIntent(context, PUSH_MESSAGE, ACTIVITIES_SYNCED))

    builder.setContentIntent(
        stackBuilder.getPendingIntent(
            0,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    )
    builder.setAutoCancel(true)

    val unseenWorkoutsCount = try {
        workoutHeaderController.loadUnseenWorkoutsCount(userName)
    } catch (e: Exception) {
        Timber.w(e, "Failed to show new workouts notification")
        0
    }
    if (unseenWorkoutsCount <= 0) {
        return
    }
    builder.setContentText(
        context.getString(syncedMessage)
    )
    val notificationManager = NotificationManagerCompat.from(context)
    notificationManager.notify(syncedMessage, builder.build())
}

fun NotificationManagerCompat.clearNewWorkoutsNotification() {
    GetNewWorkoutSyncedMessage.SYNCED_MESSAGE_IDS.forEach { cancel(it) }
}
