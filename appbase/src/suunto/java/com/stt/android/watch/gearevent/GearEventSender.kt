package com.stt.android.watch.gearevent

import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.work.ExistingWorkPolicy
import androidx.work.WorkManager
import com.stt.android.FirstPairedDeviceType
import com.stt.android.analytics.SyncFirstPairedDeviceEventJob
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.device.DeviceInfo
import com.stt.android.utils.STTConstants
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import javax.inject.Inject
import javax.inject.Named

/**
 * Class for coordinating sending pair and unpair events to the backend gear API
 */
class GearEventSender
@Inject constructor(
    @SuuntoSharedPrefs private val sharedPreferences: SharedPreferences,
    @Named(ENABLE_GEAR_EVENTS) private val gearEventEnabled: Boolean,
    private val workManager: WorkManager
) : HeadsetGearEventSender {
    private var lastPairEventSerial: String?
        get() = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_LAST_PAIR_EVENT_SERIAL,
            null
        )
        set(newValue) =
            sharedPreferences.edit {
                putString(
                    STTConstants.SuuntoPreferences.KEY_SUUNTO_LAST_PAIR_EVENT_SERIAL,
                    newValue
                )
            }

    private var lastHeadsetPairEventSerial: String?
        get() = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_LAST_HEADSET_PAIR_EVENT_SERIAL,
            null
        )
        set(newValue) =
            sharedPreferences.edit {
                putString(
                    STTConstants.SuuntoPreferences.KEY_SUUNTO_LAST_HEADSET_PAIR_EVENT_SERIAL,
                    newValue
                )
            }

    /**
     * Prepare for a new paired device. Once the device information is available, a pair event will be
     * scheduled to be sent to the backend. Pair even will be scheduled when [sendDevicePairedEventIfNeeded] is next
     * called.
     */
    fun setupForNewPairedDevice() {
        if (!gearEventEnabled) {
            return
        }

        // Send gear paired event later, when device info is known, e.g. when sendDevicePairedEventIfNotSent is called
        lastPairEventSerial = NEW_DEVICE_PENDING_PAIR_EVENT
    }

    /**
     * Schedule a pair event to be sent for this device. It is ok to call this multiple times for the same device.
     * In order to know how long the user has not used the watch, we need to use it every time we connect.
     */
    fun sendDevicePairedEventIfNeeded(deviceInfo: DeviceInfo) {
        if (!gearEventEnabled) {
            return
        }

        if (lastPairEventSerial == null) {
            // Last serial information is not available. This most likely means that the device was paired
            // with an older app version. Do not send event in this case.
            return
        }

        lastPairEventSerial = deviceInfo.serial
        val lastSyncDate = System.currentTimeMillis() / 1000
        val deviceType = SuuntoDeviceType.fromVariantName(deviceInfo.variantName)
        val firstPairedDeviceType = if (deviceType.isEon) {
            FirstPairedDeviceType.DIVING
        } else {
            FirstPairedDeviceType.WATCH
        }
        // need to send first paired device event to backend before sending gear paired event
        workManager.beginUniqueWork(
            "${SyncFirstPairedDeviceEventJob.TAG}_${deviceInfo.serial}",
            ExistingWorkPolicy.REPLACE,
            SyncFirstPairedDeviceEventJob.getWorkoutRequest(
                serialNumber = deviceInfo.serial,
                deviceName = SuuntoDeviceType.fromVariantName(deviceInfo.variantName).displayName,
                type = firstPairedDeviceType
            )
        ).then(
            GearEventSenderJob.getWorkoutRequest(
                GearEventSenderJob.EventType.PAIR,
                deviceInfo.serial,
                lastSyncDate
            )
        ).enqueue()
    }

    /**
     * Schedule an unpair event to be sent for this device. This should only be called once when unpairing.
     */
    fun sendDeviceUnpairedEvent(serial: String) {
        if (!gearEventEnabled) {
            return
        }

        lastPairEventSerial = null
        GearEventSenderJob.schedule(workManager, GearEventSenderJob.EventType.UNPAIR, serial)
    }

    /**
     * Cancel any scheduled events that have not yet been sent. This should be called when sending the events later
     * would no longer make sense (e.g. when the current user has logged out).
     */
    fun clear() {
        GearEventSenderJob.cancelAll(workManager)
    }

    // HeadsetGearEventSender implementation
    override fun setupForNewPairedHeadset() {
        if (!gearEventEnabled) {
            return
        }

        // Send gear paired event later, when device info is known
        lastHeadsetPairEventSerial = NEW_DEVICE_PENDING_PAIR_EVENT
    }

    override fun sendHeadsetPairedEventIfNotSent(
        serialNumber: String,
        manufacturer: String,
        model: String,
        firmwareVersion: String?,
        hardwareVersion: String?
    ) {
        if (!gearEventEnabled) {
            return
        }

        if (lastHeadsetPairEventSerial == null) {
            // Last serial information is not available. This most likely means that the device was paired
            // with an older app version. Do not send event in this case.
            return
        } else if (lastHeadsetPairEventSerial == serialNumber) {
            // Pair event already sent for this serial
            return
        }

        // Save headset device info to SharedPreferences
        sharedPreferences.edit {
            putString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_HEADSET_SERIAL_NUMBER, serialNumber)
            putString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_HEADSET_MANUFACTURER, manufacturer)
            putString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_HEADSET_MODEL, model)
            putString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_HEADSET_FW_VERSION, firmwareVersion ?: "")
            putString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_HEADSET_HW_VERSION, hardwareVersion ?: "")
        }

        lastHeadsetPairEventSerial = serialNumber
        val lastSyncDate = System.currentTimeMillis() / 1000

        // Send headset paired event
        workManager.enqueue(
            HeadsetGearEventSenderJob.getWorkoutRequest(
                HeadsetGearEventSenderJob.EventType.PAIR,
                serialNumber,
                lastSyncDate
            )
        )
    }

    override fun sendHeadsetUnpairedEvent(serial: String) {
        if (!gearEventEnabled) {
            return
        }

        lastHeadsetPairEventSerial = null
        HeadsetGearEventSenderJob.schedule(workManager, HeadsetGearEventSenderJob.EventType.UNPAIR, serial)
    }

    override fun clearHeadsetEvents() {
        HeadsetGearEventSenderJob.cancelAll(workManager)
    }

    companion object {
        /**
         * Magical value for lastPairEventSerial meaning that a new device was paired and that the paired
         * event should be sent once device information is available.
         */
        private const val NEW_DEVICE_PENDING_PAIR_EVENT = "<new device>"

        /**
         * Named injection qualifier for gear events enabled boolean flag.
         */
        const val ENABLE_GEAR_EVENTS = "GearEventEnabled"
    }
}
