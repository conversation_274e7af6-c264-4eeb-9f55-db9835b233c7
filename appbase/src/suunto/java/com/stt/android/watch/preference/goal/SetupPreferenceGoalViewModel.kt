package com.stt.android.watch.preference.goal

import android.content.SharedPreferences
import android.content.res.Resources
import androidx.lifecycle.viewModelScope
import com.stt.android.domain.activitydata.ActivityDataType
import com.stt.android.domain.activitydata.goals.FetchEnergyGoalUseCase
import com.stt.android.domain.activitydata.goals.FetchSleepGoalUseCase
import com.stt.android.domain.activitydata.goals.FetchStepsGoalUseCase
import com.stt.android.domain.activitydata.goals.SetEnergyGoalUseCase
import com.stt.android.domain.activitydata.goals.SetSleepGoalUseCase
import com.stt.android.domain.activitydata.goals.SetStepsGoalUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.sleep.FetchSleepTrackingModeUseCase
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.home.settings.goalsettings.FetchWeeklyTrainingGoalUseCase
import com.stt.android.home.settings.goalsettings.GoalSettingsViewModel
import com.stt.android.watch.SuuntoWatchModel
import com.stt.android.watch.device.Watch247DataSupport
import com.stt.android.watch.preference.SetupPreference
import com.stt.android.watch.preference.SetupPreferenceViewModel
import com.stt.android.watch.preference.UpdatePreferenceReducer
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SetupPreferenceGoalViewModel @Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    resources: Resources,
    fetchStepsGoalUseCase: FetchStepsGoalUseCase,
    fetchEnergyGoalUseCase: FetchEnergyGoalUseCase,
    fetchSleepGoalUseCase: FetchSleepGoalUseCase,
    setStepsGoalUseCase: SetStepsGoalUseCase,
    setEnergyGoalUseCase: SetEnergyGoalUseCase,
    setSleepGoalUseCase: SetSleepGoalUseCase,
    isWatchConnectedUseCase: IsWatchConnectedUseCase,
    fetchSleepTrackingModeUseCase: FetchSleepTrackingModeUseCase,
    fetchWeeklyTrainingGoalUseCase: FetchWeeklyTrainingGoalUseCase,
    sharedPreferences: SharedPreferences,
    suuntoWatchModel: SuuntoWatchModel,
) : GoalSettingsViewModel(
    ioThread,
    mainThread,
    resources,
    fetchStepsGoalUseCase,
    fetchEnergyGoalUseCase,
    fetchSleepGoalUseCase,
    setStepsGoalUseCase,
    setEnergyGoalUseCase,
    setSleepGoalUseCase,
    isWatchConnectedUseCase,
    fetchSleepTrackingModeUseCase,
    fetchWeeklyTrainingGoalUseCase,
    sharedPreferences,
    suuntoWatchModel
) {

    internal var setupPreferenceViewModel: SetupPreferenceViewModel? = null
        set(value) {
            field = value
            viewModelScope.launch {
                value?.stateFlow?.collect {
                    loadData()
                }
            }
        }
    internal var setupPreference: SetupPreference?
        get() = setupPreferenceViewModel?.stateFlow?.value
        set(value) {
            value?.let {
                setupPreferenceViewModel?.invokeReducer(UpdatePreferenceReducer(it))
            }
        }

    override fun loadData() {
        setupPreference?.goalSetting?.run {
            handleGoalData(
                GoalData(
                    weeklyTrainingGoal = weeklyTraining,
                    stepsGoal = dailySteps,
                    energyGoal = dailyKcal,
                    sleepGoal = dailySleep,
                    sleepTrackingEnabled = true,
                    watch247DataSupport = Watch247DataSupport(
                        supportsTrendData = true,
                        supportsSleepData = true,
                    ),
                    deviceType = SuuntoDeviceType.SuuntoRun, // Just for SuuntoRun for now
                )
            )
        }
    }

    override fun setActivityGoal(activityDataType: ActivityDataType) {
        when (activityDataType) {
            is ActivityDataType.Steps -> {
                setupPreference?.let {
                    setupPreference = it.copy(
                        goalSetting = it.goalSetting.copy(
                            dailySteps = activityDataType.goal
                        )
                    )
                }
            }

            is ActivityDataType.Energy -> {
                setupPreference?.let {
                    setupPreference = it.copy(
                        goalSetting = it.goalSetting.copy(
                            dailyKcal = activityDataType.goal
                        )
                    )
                }
            }

            is ActivityDataType.SleepDuration -> {
                setupPreference?.let {
                    setupPreference = it.copy(
                        goalSetting = it.goalSetting.copy(
                            dailySleep = activityDataType.goal
                        )
                    )
                }
            }

            else -> {
                // do nothing
            }
        }
    }
}
