package com.stt.android.watch.wifi.ui

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.LocalContentAlpha
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Switch
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.theme.switchColors

@Composable
fun WifiEnabledSetting(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    onWifiSettingInfo: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
) {
    Row(
        modifier = modifier.height(56.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // When sync is ongoing, dim icon and text and disable the switch
        val contentAlpha = if (enabled) 1.0f else 0.5f
        CompositionLocalProvider(LocalContentAlpha provides contentAlpha) {
            Icon(
                painter = painterResource(id = R.drawable.ic_watch_basic_outline),
                contentDescription = null
            )

            Row(
                modifier = Modifier
                    .fillMaxHeight()
                    .padding(horizontal = MaterialTheme.spacing.small)
                    .weight(1f),
            ) {
                Text(
                    text = stringResource(id = R.string.wireless_network_setting),
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.align(Alignment.CenterVertically)
                )
                IconButton(
                    onClick = { onWifiSettingInfo() },
                    modifier = Modifier.offset(x = (-12).dp, y = (-8).dp)
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_info_outline),
                        contentDescription = null,
                        modifier = Modifier.size(20.dp),
                        tint = MaterialTheme.colors.primaryVariant
                    )
                }
            }
        }
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            enabled = enabled,
            colors = MaterialTheme.colors.switchColors,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun WifiEnabledSettingPreview(
    @PreviewParameter(WifiEnabledSettingParamsProvider::class) params: WifiEnabledSettingParams
) {
    AppTheme {
        WifiEnabledSetting(
            checked = params.checked,
            onCheckedChange = {},
            onWifiSettingInfo = {},
            enabled = params.enabled,
        )
    }
}

private class WifiEnabledSettingParamsProvider :
    PreviewParameterProvider<WifiEnabledSettingParams> {
    override val values: Sequence<WifiEnabledSettingParams> = sequenceOf(
        WifiEnabledSettingParams(checked = false, enabled = false),
        WifiEnabledSettingParams(checked = false, enabled = true),
        WifiEnabledSettingParams(checked = true, enabled = true),
    )
}

private data class WifiEnabledSettingParams(
    val checked: Boolean,
    val enabled: Boolean,
)
