package com.stt.android.home.diary.spo2

import android.view.View
import com.stt.android.FontRefs
import com.stt.android.R
import com.stt.android.TestOpen
import com.stt.android.databinding.ExpandableItemDiarySpo2Binding
import com.stt.android.domain.activitydata.ActivityDataType
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.home.dayview.DayViewItemType
import com.stt.android.home.diary.BaseDiaryItem
import com.stt.android.home.diary.sleep.getStringWithStyledParameter
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.xwray.groupie.ExpandableGroup
import com.xwray.groupie.ExpandableItem
import timber.log.Timber
import java.time.Clock
import java.util.Locale
import kotlin.math.roundToInt

@TestOpen
data class ExpandableSpO2Item(
    val trendData: TrendData,
    val altitudeRange: IntRange?,
    val clock: Clock,
    val locale: Locale,
    val infoModelFormatter: InfoModelFormatter,
    val activityDataType: ActivityDataType.Spo2
) : BaseDiaryItem<ExpandableItemDiarySpo2Binding>(trendData.timestamp, clock, locale),
    ExpandableItem {
    override val itemType = DayViewItemType.SPO2

    private var expandableGroup: ExpandableGroup? = null

    override fun setExpandableGroup(onToggleListener: ExpandableGroup) {
        expandableGroup = onToggleListener
    }

    override fun getLayout() = R.layout.expandable_item_diary_spo2

    override fun getItemValue(): String = resources.getString(
        R.string.spo2_value_string,
        trendData.spo2?.let { (it * 100).roundToInt().toString() } ?: ""
    )

    override fun hasItemValue(): Boolean = trendData.spo2 != null

    override fun getItemValueUnit(): CharSequence {
        var unitString = ""
        val formattedAltitude = altitudeRange?.let { range ->
            runCatching {
                val minAltitudeValue =
                    infoModelFormatter.formatValue(SummaryItem.LOWALTITUDE, range.first)
                unitString = minAltitudeValue.unitString
                    ?: minAltitudeValue.unit?.let { context.getString(it) } ?: ""
                if (range.first >= range.last) {
                    minAltitudeValue.value
                } else {
                    val maxAltitudeValue =
                        infoModelFormatter.formatValue(SummaryItem.LOWALTITUDE, range.last)
                    val minValue = minAltitudeValue.value
                    val maxValue = maxAltitudeValue.value
                    if (minValue != null && maxValue != null) {
                        "$minValue - $maxValue"
                    } else {
                        null
                    }
                }
            }.onFailure {
                Timber.w(it, "Failed to format altitude $altitudeRange")
            }.getOrNull()
        }

        return if (formattedAltitude != null) {
            context.getStringWithStyledParameter(
                textRes = R.string.diary_item_spo2_unit_header_with_altitude_range_with_unit,
                parameter = formattedAltitude,
                styleRes = R.style.Body_Medium_Bold,
                fontRes = FontRefs.WORKOUT_VALUE_FONT_REF,
                additionalParameters = arrayOf(unitString)
            )
        } else {
            context.getString(R.string.diary_item_spo2_unit_header)
        }
    }

    override fun hasSubHeader(): Boolean = !hasItemValue()

    override fun getValueForAvgCalculation(): Int = trendData.spo2?.let { (it * 100).roundToInt() } ?: 0

    override fun bind(viewBinding: ExpandableItemDiarySpo2Binding, position: Int) {
        super.bind(viewBinding, position)

        setExpandedState(viewBinding)
        viewBinding.expandableSpO2Item.setOnClickListener {
            expandableGroup?.onToggleExpanded()
            setExpandedState(viewBinding)
        }
    }

    private fun setExpandedState(viewBinding: ExpandableItemDiarySpo2Binding) {
        viewBinding.expandableDivider.apply {
            visibility = if (expandableGroup?.isExpanded == true) {
                View.GONE
            } else {
                View.VISIBLE
            }
        }
        viewBinding.isExpanded = expandableGroup?.isExpanded == true
    }
}
