package com.stt.android.home.diary.analytics

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.data.TimeUtils
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.activitydata.dailyvalues.StressState
import com.stt.android.domain.sleep.FetchSleepHrvUseCase
import com.stt.android.domain.sleep.SleepHrv
import com.stt.android.extensions.capitalize
import com.stt.android.home.dayview.DayType
import com.stt.android.home.dayview.DayViewData
import com.stt.android.home.dayview.DayViewDataFetcher
import com.stt.android.home.dayview.DayViewSleep
import com.stt.android.utils.STTConstants
import com.stt.android.utils.lastNotNullOfOrNull
import com.suunto.algorithms.data.Energy
import com.suunto.algorithms.data.Energy.Companion.cal
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.withContext
import java.time.Clock
import java.time.DayOfWeek
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.Locale
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.time.Duration
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.seconds

/**
 * Handles 247 data collection between given dates and sends analytics to Amplitude.
 */
class Suunto247Analytics @Inject constructor(
    @SuuntoSharedPrefs private val sharedPreferences: SharedPreferences,
    private val dayViewDataFetcher: DayViewDataFetcher,
    private val fetchSleepHrvUseCase: FetchSleepHrvUseCase,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val datahubAnalyticsTracker: DatahubAnalyticsTracker,
    private val clock: Clock,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    suspend fun formAndSend247AnalyticsData(fromTime: Long, toTime: Long) = withContext(coroutinesDispatchers.io) {
        val zonedFromTime = TimeUtils.epochToZonedDateTime(fromTime)
        val zonedToTime = TimeUtils.epochToZonedDateTime(toTime)
        // Do not send analytics from today and yesterday because sleep data might not be ready yet.
        if (ChronoUnit.DAYS.between(zonedFromTime, zonedToTime).toInt() <= 2) {
            return@withContext
        }

        val fetchDataUntilDate = zonedToTime.minusDays(2)
        val dayViewData = dayViewDataFetcher.fetchDataForDateRange(
            firstDay = zonedFromTime.toLocalDate(),
            lastDay = fetchDataUntilDate.toLocalDate(),
        )
            .asFlow()
            .firstOrNull()
            ?: return@withContext

        val hrvData = fetchSleepHrvUseCase.fetchAvgHrv(
            from = zonedFromTime.toLocalDate(),
            to = fetchDataUntilDate.toLocalDate()
        ).firstOrNull() ?: emptyList()

        sendAnalyticsToAmplitude(dayViewData, hrvData)
    }

    /**
     * Go through the list of DayViewData and send the data to analytics with that day's timestamp.
     *
     * @param dayViewDataList List<DayViewData> contains sleep, trend data and workout data for different days
     */
    fun sendAnalyticsToAmplitude(dayViewDataList: List<DayViewData>, sleepHrvList: List<SleepHrv>) {
        val firmwareVersion: String? = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_FW_VERSION,
            "N/A"
        )
        val watchVariantName: String? = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
            "N/A"
        )
        val watchModel: String? = watchVariantName?.let {
            AnalyticsDevicePropertyHelper.getWatchModelNameForVariantName(it)
        }

        for (dayViewData in dayViewDataList) {
            val sleepHrv =
                sleepHrvList.firstOrNull { it.date == dayViewData.startOfDayISO8601.toLocalDate() }
            val properties: AnalyticsProperties = mapValuesToProperties(dayViewData, sleepHrv).apply {
                put(AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION, firmwareVersion)
                put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModel)
            }

            trackEvent(AnalyticsEvent.SUUNTO_247_DATA, properties)
        }

        saveAnalyticsSentTime()
    }

    private fun mapValuesToProperties(dayViewData: DayViewData, sleepHrv: SleepHrv?): AnalyticsProperties {
        val properties = AnalyticsProperties()

        // Steps
        val steps = dayViewData.trendDataAggregated.sumOf { it.steps }
        val stepsGoalAchieved = if (steps > 0) steps >= dayViewData.stepsGoal else null
        with(properties) {
            put(AnalyticsEventProperty.STEPS_GOAL_ACHIEVED, stepsGoalAchieved)
            put(AnalyticsEventProperty.STEPS_GOAL, dayViewData.stepsGoal)
            put(AnalyticsEventProperty.STEPS, if (steps != 0) steps else null)
        }

        // Calories
        val activeEnergy = dayViewData.trendDataAggregated
            .sumOf { it.energy.inCal }
            .cal
        val caloriesGoalAchieved = if (activeEnergy != Energy.ZERO) {
            activeEnergy >= dayViewData.energyGoal
        } else {
            null
        }
        with(properties) {
            put(AnalyticsEventProperty.CALORIES_GOAL_ACHIEVED, caloriesGoalAchieved)
            put(AnalyticsEventProperty.CALORIES_GOAL, dayViewData.energyGoal)
            put(
                AnalyticsEventProperty.CALORIES,
                if (activeEnergy != Energy.ZERO) {
                    formatWithDotAndTwoDecimals(activeEnergy.inKcal)
                } else {
                    null
                }
            )
        }

        // Sleep
        val sleepProperties: AnalyticsSleepProperties = formatSleepData(dayViewData.sleep, dayViewData.sleepGoal)
        with(sleepProperties) {
            properties
                .put(
                    AnalyticsEventProperty.SLEEP_GOAL_ACHIEVED,
                    sleepGoalAchieved?.toString()?.capitalize(
                        Locale.ROOT
                    )
                )
                .put(AnalyticsEventProperty.SLEEP, sleepHours)
                .put(AnalyticsEventProperty.SLEEP_DEEP_SLEEP, deepSleep)
                .put(AnalyticsEventProperty.SLEEP_AWAKE_TIME, awakeTime)
                .put(AnalyticsEventProperty.SLEEP_TIME_FELL_ASLEEP, fellAsleep)
                .put(AnalyticsEventProperty.SLEEP_TIME_WOKE_UP, wokeUp)
                .put(AnalyticsEventProperty.SLEEP_GOAL, sleepGoal)
                .put(AnalyticsEventProperty.SLEEP_QUALITY, quality)
                .put(AnalyticsEventProperty.SLEEP_AVERAGE_HR, averageHr)
        }

        // HRV
        if (sleepHrv != null) {
            val avgHrv = sleepHrv.avgHrv
            val avg7DayHrv = sleepHrv.avg7DayHrv
            val normalRange1 = sleepHrv.normalRange
            with(properties) {
                if (avgHrv != null) {
                    put(AnalyticsEventProperty.SLEEP_HRV, avgHrv.roundToInt())
                }
                if (avg7DayHrv != null) {
                    put(
                        AnalyticsEventProperty.SLEEP_HRV_7_DAYS_AVG,
                        avg7DayHrv.roundToInt()
                    )
                }
                normalRange1?.let { normalRange ->
                    put(
                        AnalyticsEventProperty.SLEEP_HRV_NORMAL_RANGE_MIN,
                        normalRange.start.roundToInt()
                    )
                    put(
                        AnalyticsEventProperty.SLEEP_HRV_NORMAL_RANGE_MAX,
                        normalRange.endInclusive.roundToInt()
                    )
                }
            }
        }

        // Workouts duration and calories
        val workoutsDuration = dayViewData.workouts.sumOf { it.totalTime }.seconds.inWholeMinutes
        val workoutsCalories = dayViewData.workouts.sumOf { it.energyConsumption }.roundToInt()
        with(properties) {
            put(AnalyticsEventProperty.WORKOUTS, dayViewData.workouts.size)
            put(AnalyticsEventProperty.WORKOUTS_DURATION, workoutsDuration)
            put(AnalyticsEventProperty.WORKOUTS_CALORIES, workoutsCalories)
        }

        // Stress & recovery
        for (stressState in StressState.entries.filter { it != StressState.INVALID }) {
            properties.put(
                stressState.analyticsProperty,
                dayViewData.recoveryData.calculateStressStateDurationForAnalytics(stressState)
            )
        }

        properties.put(
            AnalyticsEventProperty.STRESS_AND_RECOVERY_HIGHEST_VALUE,
            dayViewData.recoveryData.highestValuePerc()
        )

        properties.put(
            AnalyticsEventProperty.STRESS_AND_RECOVERY_HIGHEST_VALUE_TIME,
            dayViewData.recoveryData.highestValueTimeHHMM()
        )

        // Day type
        val dayType: String = when (dayViewData.getDayType(TimeUtils.getTodayStartTime(clock))) {
            is DayType.ActiveDay -> AnalyticsPropertyValue.DayType.ACTIVE
            is DayType.TrainingDay -> AnalyticsPropertyValue.DayType.TRAINING
            is DayType.RestDay -> AnalyticsPropertyValue.DayType.REST
            is DayType.IncompleteDay -> AnalyticsPropertyValue.DayType.INCOMPLETE
            else -> AnalyticsPropertyValue.DayType.UNKNOWN
        }

        properties.put(AnalyticsEventProperty.DAY_TYPE, dayType)

        // Day of week
        val weekday: String =
            when (TimeUtils.epochToLocalZonedDateTime(dayViewData.startOfDay).dayOfWeek) {
                DayOfWeek.MONDAY -> AnalyticsPropertyValue.WeekdayProperty.MONDAY
                DayOfWeek.TUESDAY -> AnalyticsPropertyValue.WeekdayProperty.TUESDAY
                DayOfWeek.WEDNESDAY -> AnalyticsPropertyValue.WeekdayProperty.WEDNESDAY
                DayOfWeek.THURSDAY -> AnalyticsPropertyValue.WeekdayProperty.THURSDAY
                DayOfWeek.FRIDAY -> AnalyticsPropertyValue.WeekdayProperty.FRIDAY
                DayOfWeek.SATURDAY -> AnalyticsPropertyValue.WeekdayProperty.SATURDAY
                DayOfWeek.SUNDAY -> AnalyticsPropertyValue.WeekdayProperty.SUNDAY
                else -> AnalyticsPropertyValue.WeekdayProperty.UNKNOWN
            }

        properties.put(AnalyticsEventProperty.WEEKDAY, weekday)

        return properties
    }

    private fun formatSleepData(sleepList: List<DayViewSleep>, sleepGoal: Duration): AnalyticsSleepProperties {
        val secondsInHour = 1.hours.inWholeSeconds
        val sleepDuration = sleepList.sumOf { it.sleep.longSleep?.sleepDuration?.inWholeSeconds?.toDouble() ?: 0.0 }.seconds
        val deepSleep = sleepList.sumOf { it.sleep.longSleep?.deepSleepDuration?.inWholeSeconds?.toDouble() ?: 0.0 } / secondsInHour
        val awakeTime = sleepList.sumOf { it.sleep.longSleep?.awakeDuration?.inWholeSeconds?.toDouble() ?: 0.0 } / secondsInHour
        val firstLongSleep = sleepList.firstNotNullOfOrNull { it.sleep.longSleep }
        val lastLongSleep = sleepList.lastNotNullOfOrNull { it.sleep.longSleep }
        val fellAsleep = firstLongSleep?.fellAsleep
        val wokeUp = lastLongSleep?.wokeUp
        val sleepGoalAchieved = sleepDuration >= sleepGoal

        return if (sleepDuration > Duration.ZERO) {
            AnalyticsSleepProperties(
                sleepHours = formatWithDotAndTwoDecimals(sleepDuration.inWholeSeconds.toDouble().div(secondsInHour)),
                deepSleep = formatWithDotAndTwoDecimals(deepSleep),
                awakeTime = formatWithDotAndTwoDecimals(awakeTime),
                fellAsleep = fellAsleep?.let { formatToTimestamp(it) },
                wokeUp = wokeUp?.let { formatToTimestamp(it) },
                sleepGoalAchieved = sleepGoalAchieved,
                sleepGoal = formatWithDotAndTwoDecimals(sleepGoal.inWholeSeconds.toDouble() / secondsInHour),
                quality = firstLongSleep?.qualityPerc,
                averageHr = firstLongSleep?.avgHr?.inBpm?.roundToInt(),
            )
        } else {
            AnalyticsSleepProperties(
                sleepGoal = formatWithDotAndTwoDecimals(sleepGoal.inWholeSeconds.toDouble() / secondsInHour)
            )
        }
    }

    /**
     * Formats Long epoch milli time value to a timestamp of format HH:mm.
     */
    private fun formatToTimestamp(value: Long): String {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(value), ZoneOffset.systemDefault())
            .format(DateTimeFormatter.ofPattern("HH:mm"))
    }

    /**
     * Formats values to fulfill analytics specification of having values with two decimals and a dot e.g. 7.45 instead
     * of 7,45.
     */
    private fun formatWithDotAndTwoDecimals(value: Double): String {
        return String.format(
            Locale.US,
            "%.2f",
            value
        )
    }

    fun trackEvent(eventName: String, properties: AnalyticsProperties) {
        datahubAnalyticsTracker.trackEvent(eventName, properties)
        emarsysAnalytics.trackEvent(eventName)
    }

    /**
     * Saves start of today as the time the 247 analytics were last sent.
     */
    fun saveAnalyticsSentTime() {
        sharedPreferences.edit {
            putLong(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_247_ANALYTICS_LAST_SENT_TIME,
                TimeUtils.getTodayStartTime(clock)
            )
        }
    }
}
