package com.stt.android.home.diary.analytics

import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.domain.activitydata.dailyvalues.StressState
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.home.diary.RecoveryBarGraphItem
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.roundToInt

val StressState.analyticsProperty: String?
    get() = when (this) {
        StressState.INVALID -> null
        StressState.RELAXING -> AnalyticsEventProperty.STRESS_AND_RECOVERY_DURATION_RELAXING
        StressState.ACTIVE -> AnalyticsEventProperty.STRESS_AND_RECOVERY_DURATION_ACTIVE
        StressState.PASSIVE -> AnalyticsEventProperty.STRESS_AND_RECOVERY_DURATION_PASSIVE
        StressState.STRESSFUL -> AnalyticsEventProperty.STRESS_AND_RECOVERY_DURATION_STRESSFUL
    }

fun List<RecoveryData>.calculateStressStateDurationForAnalytics(stressState: StressState): String {
    val count = count { it.stressState == stressState }

    return if (count > 0) {
        Duration.ofSeconds(count * RecoveryBarGraphItem.DATA_FREQUENCY_SECONDS.toLong())
            .toMinutes()
            .toString()
    } else {
        AnalyticsEventProperty.STRESS_AND_RECOVERY_NO_DATA
    }
}

fun List<RecoveryData>.highestValuePerc(): String =
    maxByOrNull { it.balance }?.balance?.times(100f)?.roundToInt()?.toString()
        ?: AnalyticsEventProperty.STRESS_AND_RECOVERY_NO_DATA

fun List<RecoveryData>.highestValueTimeHHMM(): String {
    val highestValue = maxByOrNull { it.balance }
    return if (highestValue != null) {
        ZonedDateTime.ofInstant(
            Instant.ofEpochMilli(highestValue.timestamp),
            ZoneOffset.systemDefault()
        )
            .format(DateTimeFormatter.ofPattern("HH:mm"))
    } else {
        AnalyticsEventProperty.STRESS_AND_RECOVERY_NO_DATA
    }
}
