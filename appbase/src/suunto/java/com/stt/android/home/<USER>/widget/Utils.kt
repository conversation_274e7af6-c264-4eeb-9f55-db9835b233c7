package com.stt.android.home.dayviewv2.widget

import android.content.Context
import com.stt.android.ui.utils.TextFormatter
import kotlin.math.abs
import com.stt.android.core.R as CR

internal fun Long.secondsToHourMinute(withRounding: Boolean = true): Pair<Int, Int> {
    val seconds = if (withRounding) this + 30 else this
    return (seconds / 3600).toInt() to ((seconds % 3600) / 60).toInt()
}

internal fun Long.formatDuration(context: Context, withRounding: Boolean = true): String {
    val (hours, minutes) = abs(this).secondsToHourMinute(withRounding)
    val hourUnit = context.getString(CR.string.hour)
    val minuteUnit = context.getString(CR.string.minute)
    val text = if (hours == 0) {
        "$minutes $minuteUnit"
    } else if (minutes == 0) {
        "$hours $hourUnit"
    } else {
        "$hours $hourUnit $minutes $minuteUnit"
    }
    return if (this >= 0L) text else "-$text"
}

internal fun Float.formatPercent(optimizeZero: Boolean = false) = times(100).let { value ->
    if (optimizeZero && 0f < value && value < 1f) {
        "<1%"
    } else {
        "${TextFormatter.formatPercentage(value.toDouble())}%"
    }
}
