package com.stt.android.home.settings.connectedservices.list

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.SavedStateHandle
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.sendConnectedServiceConnectionSuccessfulEvent
import com.stt.android.common.ui.LoadingViewModel
import com.stt.android.domain.connectedservices.FetchPartnerServiceListUseCase
import com.stt.android.domain.connectedservices.IntegratePartnerServiceUseCase
import com.stt.android.domain.connectedservices.PartnerCategory
import com.stt.android.domain.connectedservices.ServiceMetadata
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.home.settings.connectedservices.ConnectedServicesActivity
import com.stt.android.home.settings.connectedservices.WeChatConnectServiceHelper
import com.stt.android.home.settings.connectedservices.WeChatConstants
import com.stt.android.ui.utils.SingleLiveEvent
import com.xwray.groupie.Group
import com.xwray.groupie.Section
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Completable
import io.reactivex.Scheduler
import io.reactivex.rxkotlin.plusAssign
import kotlinx.coroutines.rx2.rxSingle
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class ConnectedServicesListViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val fetchPartnerServiceListUseCase: FetchPartnerServiceListUseCase,
    private val integratePartnerServiceUseCase: IntegratePartnerServiceUseCase,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val datahubAnalyticsTracker: DatahubAnalyticsTracker,
    private val weChatConnectServiceHelper: WeChatConnectServiceHelper,
) : LoadingViewModel(ioThread, mainThread) {

    // Always reload the data from the server
    override fun isDataLoaded() = false

    private val _dismissConnectedServicesList = SingleLiveEvent<Any>()
    val dismissConnectedServicesList: LiveData<Any> = _dismissConnectedServicesList

    private val _errorOnServiceConnectionEvent = SingleLiveEvent<Any>()
    val errorOnServiceConnectionEvent: LiveData<Any> = _errorOnServiceConnectionEvent

    private val _authPartnerAutoSelectEvent = SingleLiveEvent<ServiceMetadata>()
    val authPartnerAutoSelectEvent: LiveData<ServiceMetadata> = _authPartnerAutoSelectEvent

    private val _categoriesLiveData = SingleLiveEvent<List<PartnerCategory>>()
    val categoriesLiveData: LiveData<List<PartnerCategory>> = _categoriesLiveData

    // original list of all services kept in memory for filtering
    private var originalItems: List<ConnectedServiceItem> = mutableListOf()

    private val _selectedItemTagLiveData = SingleLiveEvent<String>()
    val selectedItemTagLiveData: LiveData<String> = _selectedItemTagLiveData

    // value is set after groupie calls on updateComplete and it's safe to call smoothScrollToPosition
    private val _categoriesSelected = SingleLiveEvent<Group?>()
    val categoriesSelected: LiveData<Group?> = _categoriesSelected

    // selected category header-item in services list
    private var categoriesGroup: Group? = null

    private var previouslySelectedCategory: PartnerCategory? = null

    var sourceFromDetailsView = false

    // click weRun,true:Connected false:Disconnect
    private val _weRunClickEvent = SingleLiveEvent<Boolean>()
    val weRunClickEvent: LiveData<Boolean> = _weRunClickEvent

    fun toWechatSportPage(connectStatus: Boolean, context: Context) {
        if (connectStatus) {
            weChatConnectServiceHelper.toWeRunConnectedPage(context)
        } else {
            weChatConnectServiceHelper.toWeRunConnectGuidePage(context)
        }
    }

    override fun loadData() {
        notifyDataLoading()

        val authPartner = getIntentAuthPartner()
        val query = getIntentQuery()

        // if we have both authPartner and query, we are coming back here from the auth flow
        // otherwise we open the specified partner if it is the first time the screen is opened;
        val autoSelectPartner = authPartner.isNotBlank() && query.isBlank()

        val requestCompletable = if (authPartner.isNotBlank() && query.isNotBlank()) {
            resetIntentExtras()
            integratePartnerServiceUseCase.requestAuthForPartner(authPartner, getQueryMap(query))
                .observeOn(mainThread)
                .doOnComplete {
                    val autoSelected = authPartner == integratePartnerServiceUseCase.getAutoSelectedPartner()
                    sendConnectedServiceConnectionSuccessfulEvent(
                        authPartner,
                        autoSelected,
                        emarsysAnalytics,
                        datahubAnalyticsTracker
                    )
                    integratePartnerServiceUseCase.resetPartnerAutoSelected()

                    // When connected services view was used to show a single service, and
                    // the user connected to that service, dismiss the list after connect
                    // to return to the previous task (i.e. SuuntoPlus Guide partners list).
                    if (!integratePartnerServiceUseCase.getShowPartnersListAfterConnect()) {
                        integratePartnerServiceUseCase.setShowPartnersListAfterConnect(true)
                        _dismissConnectedServicesList.postCall()
                    }
                }
                .onErrorComplete {
                    _errorOnServiceConnectionEvent.call()
                    Timber.w(it, "Failed to link the partner service %s", authPartner)
                    true
                }
                .observeOn(ioThread)
        } else {
            Completable.complete()
        }

        var categories: List<PartnerCategory> = emptyList()
        val availableServices = rxSingle { fetchPartnerServiceListUseCase.fetchAvailableServices() }
            .map { list ->
                categories = list.partnerCategories
                list.serviceMetadata.map { ConnectedServiceItem(it) }
            }
        disposables += requestCompletable.andThen(
            availableServices
        )
            .doOnSuccess { list ->
                // side effect to notify opening of specific partner
                if (autoSelectPartner) {
                    list.firstOrNull { it.serviceMetadata.name == authPartner }
                        ?.let {
                            // tracking which partner is being auto-selected for analytics purposes
                            integratePartnerServiceUseCase.trackPartnerAutoSelected(it.serviceMetadata.name)
                            // resetting authPartner in the query to avoid auto-selecting multiple times
                            resetIntentExtras()
                            _authPartnerAutoSelectEvent.postValue(it.serviceMetadata)
                        }
                } else {
                    integratePartnerServiceUseCase.resetPartnerAutoSelected()
                }
            }
            .map { combinedList ->
                originalItems = combinedList
                splitServiceListToSections(combinedList, null)
            }
            .retryWhen { errors -> handleError(errors) }
            .observeOn(mainThread)
            .subscribe({ serviceList ->
                if (serviceList.isEmpty()) {
                    notifyEmptyState()
                } else {
                    val previouslySelectedCategory = this.previouslySelectedCategory
                    if (previouslySelectedCategory != null && previouslySelectedCategory.tag != ALL_CATEGORIES) {
                        notifyCategoriesLoaded(categories, previouslySelectedCategory.tag)
                        val data = splitServiceListToSections(
                            originalItems,
                            previouslySelectedCategory
                        )
                        notifyDataLoaded(data)
                    } else {
                        notifyDataLoaded(serviceList)
                        notifyCategoriesLoaded(categories, null)
                    }
                }
            }, { error ->
                Timber.w(error, "An error has occurred while fetching services list")
            })
    }

    private fun notifyCategoriesLoaded(
        categories: List<PartnerCategory>,
        previousSelectedCategory: String?
    ) {
        _categoriesLiveData.value = categories
        _selectedItemTagLiveData.value = previousSelectedCategory ?: ALL_CATEGORIES
    }

    private fun getIntentAuthPartner(): String =
        savedStateHandle[ConnectedServicesActivity.AUTH_PARTNER] ?: ""

    private fun getIntentQuery(): String =
        savedStateHandle[ConnectedServicesActivity.QUERY] ?: ""

    private fun resetIntentExtras() {
        savedStateHandle.remove<String>(ConnectedServicesActivity.AUTH_PARTNER)
        savedStateHandle.remove<String>(ConnectedServicesActivity.QUERY)
    }

    private fun getIntentSource(): String =
        savedStateHandle[ConnectedServicesActivity.SOURCE] ?: ""

    private fun splitServiceListToSections(combinedList: List<ConnectedServiceItem>, category: PartnerCategory?): MutableList<Section> {
        categoriesGroup = null
        val connectedServices = mutableListOf<ConnectedServiceItem>()
        val availableServices = mutableListOf<ConnectedServiceItem>()
        // only Suunto china show weRun
        val filteredCombinedList = if (weChatConnectServiceHelper.showWeRunItem()) {
            combinedList
        } else {
            combinedList.filter { it.serviceMetadata.name != WeChatConstants.WECHAT_NAME }
        }
        filteredCombinedList.forEach {
            // set weRun click event
            val item = if (it.serviceMetadata.name == WeChatConstants.WECHAT_NAME) {
                it.copy(weRunClickEvent = _weRunClickEvent)
            } else {
                it
            }
            when {
                item.serviceMetadata.isConnected -> connectedServices.add(item)
                else -> availableServices.add(item)
            }
        }
        val sectionsList = mutableListOf<Section>()
        if (connectedServices.isNotEmpty()) {
            Section().apply {
                setHeader(ConnectedServiceSectionHeaderItem(ConnectedServiceSectionHeaderItem.ServiceSectionType.CONNECTED))
                addAll(connectedServices)
                sectionsList.add(this)
            }
        }
        if (availableServices.isNotEmpty()) {
            if (category != null) {
                val filteredList =
                    availableServices.filter { it.serviceMetadata.clientTags.contains(category.tag) && !it.serviceMetadata.isConnected }
                if (filteredList.isNotEmpty()) {
                    val categoriesGroup = Section().apply {
                        setHeader(ConnectedServiceSectionHeaderItem(ConnectedServiceSectionHeaderItem.ServiceSectionType.CATEGORY, category.localization.name))
                        addAll(filteredList)
                    }
                    sectionsList.add(categoriesGroup)
                    this.categoriesGroup = categoriesGroup
                }
            } else {
                // No category selected -> show all services
                Section().apply {
                    setHeader(ConnectedServiceSectionHeaderItem(ConnectedServiceSectionHeaderItem.ServiceSectionType.ALL_SERVICES))
                    addAll(availableServices)
                    sectionsList.add(this)
                }
            }
        }
        sendListScreenEvent(filteredCombinedList.size)
        return sectionsList
    }

    private fun getQueryMap(query: String): Map<String, String> {
        val params = query.split("&".toRegex()).toTypedArray()
        val map = HashMap<String, String>()
        for (param in params) {
            param.split("=".toRegex()).toTypedArray().let { currentParamsPair ->
                if (currentParamsPair.size >= 2) {
                    val name = currentParamsPair[0]
                    val value = currentParamsPair[1]
                    map[name] = value
                }
            }
        }
        return map
    }

    fun chipItemClicked(category: PartnerCategory) {
        if (category.tag == _selectedItemTagLiveData.value) {
            return
        }

        previouslySelectedCategory = category
        sendCategorySelectedEvent(category.tag)
        _selectedItemTagLiveData.value = category.tag
        if (category.tag == ALL_CATEGORIES) {
            notifyDataLoaded(splitServiceListToSections(originalItems, null))
        } else {
            notifyDataLoaded(splitServiceListToSections(originalItems, category))
        }
    }

    private fun sendListScreenEvent(amountOfConnectedServices: Int) {
        val source = if (sourceFromDetailsView) {
            AnalyticsPropertyValue.ConnectedServicesListSource.SERVICE_DETAILS_SCREEN
        } else {
            getIntentSource()
        }

        val analyticsProperties = AnalyticsProperties().apply {
            put(AnalyticsEventProperty.SUUNTO_CURRENTLY_CONNECTED_TO, amountOfConnectedServices)
            put(AnalyticsEventProperty.SOURCE, source)
        }

        datahubAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_CONNECTED_SERVICE_LIST_SCREEN,
            analyticsProperties
        )
    }

    private fun sendCategorySelectedEvent(category: String) {
        val analyticsProperties = AnalyticsProperties().put(
            AnalyticsEventProperty
                .CATEGORY,
            category
        )

        datahubAnalyticsTracker.trackEvent(
            AnalyticsEvent.PARTNER_SERVICES_LIST_FILTERED,
            analyticsProperties
        )
    }

    fun onUpdateComplete() {
        _categoriesSelected.value = categoriesGroup
    }

    companion object {
        const val ALL_CATEGORIES = "ALL"
    }
}
