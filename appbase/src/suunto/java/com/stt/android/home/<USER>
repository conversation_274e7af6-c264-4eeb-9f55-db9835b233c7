package com.stt.android.home

import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.lifecycle.LiveData
import com.stt.android.controllers.CurrentUserController
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.login.LoginHelper
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.STTConstants
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.btscanner.ScannedSuuntoBtDevice
import com.suunto.connectivity.btscanner.SuuntoDataLayerScanner
import com.suunto.connectivity.btscanner.SuuntoLeScanner
import hu.akarnokd.rxjava.interop.RxJavaInterop
import io.reactivex.Flowable
import io.reactivex.Scheduler
import io.reactivex.disposables.CompositeDisposable
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class SmoothPairingHelper
@Inject constructor(
    @IoThread private val ioThread: Scheduler,
    @MainThread private val mainThread: Scheduler,
    private val suuntoWatchModel: SuuntoWatchModel,
    private val currentUserController: CurrentUserController,
    private val suuntoLeScanner: SuuntoLeScanner,
    private val suuntoDlScanner: SuuntoDataLayerScanner,
    @SuuntoSharedPrefs private val suuntoSharedPrefs: SharedPreferences
) {
    val deviceFoundForSmoothPairing: LiveData<Any>
        get() = _deviceFoundForSmoothPairing

    private val _deviceFoundForSmoothPairing = SingleLiveEvent<Any>()
    private val disposables = CompositeDisposable()

    private var smoothPairingDoneDeviceNames: Set<String>
        get() = suuntoSharedPrefs.getStringSet(
            STTConstants.SuuntoPreferences.KEY_SMOOTH_PAIRING_TRANSITION_DONE_DEVICE_NAMES,
            null
        ) ?: setOf()
        set(value) {
            suuntoSharedPrefs.edit {
                putStringSet(
                    STTConstants.SuuntoPreferences.KEY_SMOOTH_PAIRING_TRANSITION_DONE_DEVICE_NAMES,
                    value
                )
            }
        }

    fun initiateSmoothPairingIfNeeded() {
        stopSmoothPairingScanning()

        if (currentUserController.isLoggedIn) {
            // Start scanning for smooth pairing
            val hasWatchSingle = RxJavaInterop.toV2Single(suuntoWatchModel.currentWatch)
                .map { true }
                .onErrorReturn {
                    if (it !is MissingCurrentWatchException) {
                        Timber.w(it)
                    }
                    false
                }

            val timeout = Flowable.timer(SMOOTH_PAIRING_SCANNING_TIMEOUT_SECONDS, TimeUnit.SECONDS, ioThread)
                .doOnComplete {
                    Timber.d("Smooth pairing scanning timed out")
                }

            // If the user has just logged in, scan also for BLE advertisements from data layer devices.
            // If not, only include Wear OS devices paired via Wear OS app.
            val bleScanningObservable =
                if (LoginHelper.justLoggedIn.get()) {
                    RxJavaInterop.toV2Flowable(suuntoLeScanner.scanBleDevices(false))
                        .filter { it.suuntoBtDevice.deviceType.isDataLayerDevice }
                        .doOnSubscribe {
                            Timber.d("Smooth pairing: scanning for BLE advertisements and data layer devices")
                        }
                } else {
                    Flowable.empty<ScannedSuuntoBtDevice>()
                        .doOnSubscribe {
                            Timber.d("Smooth pairing: scanning for data layer devices only")
                        }
                }

            val scanningObservable = bleScanningObservable
                .mergeWith(suuntoDlScanner.scanDataLayerDevices())

            val scanningDisposable = hasWatchSingle
                .flatMapPublisher { hasWatch ->
                    // Only start scanning for devices for smooth pairing if no watch is paired yet
                    if (hasWatch) {
                        Flowable.empty()
                    } else {
                        Timber.d("Start scanning for devices for smooth pairing")
                        scanningObservable
                    }
                }
                .takeUntil(timeout)
                .delaySubscription(SMOOTH_PAIRING_INITIAL_DELAY_MILLISECONDS, TimeUnit.MILLISECONDS, ioThread)
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .subscribe(
                    { device ->
                        if (!smoothPairingDoneDeviceNames.contains(device.name)) {
                            // Only trigger automatic transition once for each device
                            Timber.d("Smooth pairing: Found device '${device.name}'")
                            smoothPairingDoneDeviceNames = smoothPairingDoneDeviceNames + setOf(device.name)
                            _deviceFoundForSmoothPairing.call()
                        }
                    },
                    {
                        Timber.w(it, "Error in scanning for smooth pairing")
                    }
                )

            disposables.add(scanningDisposable)
        }
    }

    fun stopSmoothPairingScanning() {
        disposables.clear()
    }

    companion object {
        private const val SMOOTH_PAIRING_INITIAL_DELAY_MILLISECONDS = 800L
        private const val SMOOTH_PAIRING_SCANNING_TIMEOUT_SECONDS = 3L
    }
}
