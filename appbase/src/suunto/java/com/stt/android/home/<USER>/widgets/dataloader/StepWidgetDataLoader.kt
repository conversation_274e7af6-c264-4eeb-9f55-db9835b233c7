package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import com.stt.android.R
import com.stt.android.domain.activitydata.dailyvalues.ActivityDataDailyRepository
import com.stt.android.domain.activitydata.goals.ActivityDataGoalRepository
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.home.dashboardv2.ui.widgets.common.generateTargetSubtitle
import com.stt.android.home.dashboardv2.widgets.StepWidgetInfo
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.onEmpty
import java.time.LocalDate
import javax.inject.Inject

internal class StepWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val activityDataDailyRepository: ActivityDataDailyRepository,
    private val activityDataGoalRepository: ActivityDataGoalRepository,
    private val trendDataRepository: TrendDataRepository,
) : WidgetDataLoader<StepWidgetInfo>() {

    override suspend fun mockedWidgetInfo(type: WidgetType): StepWidgetInfo? = StepWidgetInfo(
        progress = 3653 / 8000F,
        title = AnnotatedString("3653"),
        subtitle = generateTargetSubtitle(
            context = context,
            completed = 3653,
            target = 8000,
            postfixRes = R.string.widget_step_subtitle,
        ),
    )

    override suspend fun realLoad(param: Param): WidgetData<StepWidgetInfo> = run {
        val todayMillis = LocalDate.now().atStartOfDay().toEpochMilli()
        WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = combine(
                activityDataGoalRepository.fetchStepsGoal(),
                activityDataDailyRepository.fetchSteps(),
                trendDataRepository.fetchTrendDataForDateRange(
                    todayMillis,
                    todayMillis,
                    true,
                ).onEmpty { emit(emptyList()) },
            ) { goal, dailySteps, todayTrend ->
                val steps = dailySteps.takeIf { it > 0 } ?: todayTrend.lastOrNull()?.steps ?: 0
                val progress = (steps.toFloat() / goal).coerceIn(0f, 1f)
                val title = AnnotatedString(steps.toString())
                val subtitle = generateTargetSubtitle(
                    context = context,
                    completed = steps,
                    target = goal,
                    postfixRes = R.string.widget_step_subtitle,
                )

                StepWidgetInfo(
                    progress = progress,
                    title = title,
                    subtitle = subtitle,
                )
            },
        )
    }
}
