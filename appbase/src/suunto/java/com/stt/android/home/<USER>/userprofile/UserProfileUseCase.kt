package com.stt.android.home.settings.userprofile

import android.content.SharedPreferences
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.utils.toV2
import com.stt.android.watch.GetWatchCapabilities
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.settings.UnitSystem
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.rx2.asFlow
import kotlinx.coroutines.rx2.await
import javax.inject.Inject

class UserProfileUseCase @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
    private val watchCapabilities: GetWatchCapabilities
) {

    @Inject
    @FeatureTogglePreferences
    lateinit var featureTogglePreferences: SharedPreferences

    /**
     * If supportsEditingUserProfile is true, users can edit and sync to the watch,
     * otherwise, the previous logic is retained.
     */
    fun supportsEditingUserProfile(): Flow<Boolean> {
        return suuntoWatchModel.stateChangeObservable.first()
            .map { it.isPaired }.toV2()
            .asFlow()
            .flatMapConcat { isPaired ->
                if (!isPaired) {
                    flowOf(true)
                } else {
                    watchCapabilities.getCurrentCapabilitiesAsFlow().map {
                        it.capabilities?.supportsUserProfileSetting ?: false
                    }
                }
            }
    }

    suspend fun updateUserBirthYear(birthYear: Int) {
        suuntoWatchModel.sendUserBirthYear(birthYear).toV2().await()
    }

    suspend fun updateUserGender(gender: String) {
        suuntoWatchModel.sendUserGender(gender).toV2().await()
    }

    suspend fun updateUserHeight(heightCentimeter: Int) {
        suuntoWatchModel.sendUserHeight(heightCentimeter)
    }

    suspend fun updateUserWeight(weightKilograms: Float) {
        suuntoWatchModel.sendUserWeight(weightKilograms).toV2().await()
    }

    suspend fun updateUserMaxHeart(maxHeart: Int) {
        suuntoWatchModel.sendUserMaxHeart(maxHeart)
    }

    suspend fun updateUserRestHeart(restHeart: Int) {
        suuntoWatchModel.sendUserRestHeart(restHeart)
    }

    suspend fun updateUserUnitSystem(unitSystem: UnitSystem) {
        suuntoWatchModel.sendUserUnitSystem(unitSystem)
    }
}
