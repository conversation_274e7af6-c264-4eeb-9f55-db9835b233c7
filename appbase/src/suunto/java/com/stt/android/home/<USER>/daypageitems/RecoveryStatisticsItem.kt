package com.stt.android.home.dayview.daypageitems

import android.content.res.Resources
import com.stt.android.R
import com.stt.android.common.ui.BaseBindableItem
import com.stt.android.data.TimeUtils
import com.stt.android.databinding.ItemDayViewRecoveryStatisticsBinding
import com.stt.android.home.diary.sleep.StatisticsValueItem
import com.stt.android.core.R as CR

class RecoveryStatisticsItem(
    private val activeSeconds: Long,
    private val passiveSeconds: Long,
    private val stressfulSeconds: Long,
    private val relaxingSeconds: Long
) : BaseBindableItem<ItemDayViewRecoveryStatisticsBinding>() {
    override fun getLayout() = R.layout.item_day_view_recovery_statistics

    lateinit var resources: Resources

    override fun bind(viewBinding: ItemDayViewRecoveryStatisticsBinding, position: Int) {
        super.bind(viewBinding, position)
        resources = viewBinding.root.resources
    }

    val activeItem: StatisticsValueItem
        get() = StatisticsValueItem(
            isVisible = true,
            value = TimeUtils.getDurationStringAsHmm(activeSeconds),
            unit = resources.getString(CR.string.hour),
            label = resources.getString(R.string.stress_state_active)
        )

    val passiveItem: StatisticsValueItem
        get() = StatisticsValueItem(
            isVisible = true,
            value = TimeUtils.getDurationStringAsHmm(passiveSeconds),
            unit = resources.getString(CR.string.hour),
            label = resources.getString(R.string.stress_state_inactive)
        )

    val stressfulItem: StatisticsValueItem
        get() = StatisticsValueItem(
            isVisible = true,
            value = TimeUtils.getDurationStringAsHmm(stressfulSeconds),
            unit = resources.getString(CR.string.hour),
            label = resources.getString(R.string.stress_state_stressful)
        )

    val relaxingItem: StatisticsValueItem
        get() = StatisticsValueItem(
            isVisible = true,
            value = TimeUtils.getDurationStringAsHmm(relaxingSeconds),
            unit = resources.getString(CR.string.hour),
            label = resources.getString(R.string.stress_state_recovering)
        )
}
