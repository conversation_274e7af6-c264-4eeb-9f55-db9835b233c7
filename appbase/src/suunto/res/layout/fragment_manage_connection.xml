<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/flManageConnectionContainer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/llManageConnectionContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/companion_device_association_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?suuntoBackground"
            android:orientation="vertical"
            android:paddingTop="@dimen/size_spacing_medium"
            android:paddingBottom="@dimen/size_spacing_medium">

            <TextView
                style="@style/Body.Small.Bold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="false"
                android:paddingStart="@dimen/size_spacing_medium"
                android:paddingEnd="@dimen/size_spacing_medium"
                android:text="@string/manage_connection_unpair_this_device"
                android:textAllCaps="true"
                tools:text="Applications" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="@dimen/size_spacing_medium"
            android:paddingRight="@dimen/size_spacing_medium"
            android:paddingBottom="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_large">

            <TextView
                android:id="@+id/tvManageConnectionTitle"
                style="@style/Body.Medium.Bold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/size_spacing_xsmall"
                android:paddingTop="@dimen/size_spacing_medium"
                android:paddingBottom="@dimen/size_spacing_medium"
                android:visibility="gone"
                tools:text="@string/manage_connection_title" />

            <TextView
                android:id="@+id/tvManageConnectionContent"
                style="@style/Body.Medium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/size_spacing_xsmall"
                tools:text="@string/manage_connection_forget_watch_steps" />

            <Button
                android:id="@+id/forgetBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:fontFamily="sans-serif"
                android:gravity="center_horizontal"
                android:padding="@dimen/size_spacing_medium"
                android:textAllCaps="false"
                android:textColor="@color/bright_red"
                android:textSize="@dimen/text_size_larger"
                android:textStyle="bold"
                tools:text="@string/manage_connection_forget_watch_btn_text"/>
        </LinearLayout>
    </LinearLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_large"
        android:padding="@dimen/size_spacing_xxxxlarge" />
</FrameLayout>

