<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="onClick"
            type="android.view.View.OnClickListener" />

        <variable
            name="title"
            type="String" />

        <variable
            name="enabled"
            type="boolean" />

        <variable
            name="description"
            type="String" />

        <variable
            name="selected"
            type="boolean" />
    </data>

    <LinearLayout
        style="@style/Button.Action"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:enabled="@{enabled}"
        android:onClick="@{onClick}"
        android:orientation="horizontal"
        android:padding="@dimen/size_spacing_medium"
        tools:showIn="@layout/device_action_list">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@null"
            android:orientation="vertical">

            <TextView
                android:id="@+id/watch_notification_settings_header"
                style="@style/Body.Action.Larger"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:enabled="@{enabled}"
                android:text="@{title}" />

            <TextView
                android:id="@+id/watch_notification_settings_contents"
                style="@style/Body.Action"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:enabled="@{enabled}"
                android:lineSpacingMultiplier="1.3"
                android:text="@{description}" />

        </LinearLayout>

        <ImageView
            android:id="@+id/notificationStateIconOff"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/size_spacing_small"
            android:layout_marginEnd="@dimen/size_spacing_small"
            android:layout_weight="0"
            android:background="@null"
            android:src="@drawable/ic_checkmark_orange"
            app:visible="@{selected}"
            tools:src="@drawable/ic_watch_notifications_off"
            tools:visibility="visible" />
    </LinearLayout>

</layout>
