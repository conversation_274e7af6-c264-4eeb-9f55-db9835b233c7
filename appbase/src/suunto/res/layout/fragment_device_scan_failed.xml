<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="viewModel"
            type="com.stt.android.watch.failed.ScanFailedViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/device_image"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/device_image_height"
            android:src="@drawable/watch_activity_ghost_white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/device_pair_animation_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@+id/device_image"
            app:layout_constraintEnd_toEndOf="@+id/device_image"
            app:layout_constraintStart_toStartOf="@+id/device_image"
            app:layout_constraintTop_toTopOf="@+id/device_image"
            app:lottie_autoPlay="true"
            app:lottie_fileName="@string/watch_activity_anim_error"
            app:lottie_loop="false" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingEnd="@dimen/padding"
                android:paddingStart="@dimen/padding">

                <Space
                    android:id="@+id/device_pairing_failed_top_space"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_image_height" />

                <TextView
                    android:id="@+id/device_pairing_failed_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/padding"
                    android:gravity="center"
                    android:lineSpacingMultiplier="1.3"
                    android:text="@string/device_scan_connect_failed_title"
                    style="@style/HeaderLabel.Large" />

                <TextView
                    android:id="@+id/device_pairing_failed_guidance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="@dimen/padding"
                    android:layout_marginTop="@dimen/padding"
                    android:gravity="center"
                    android:lineSpacingMultiplier="1.3"
                    android:text="@string/device_scan_connect_failed_tips"
                    style="@style/Body.Medium" />

                <Button
                    android:id="@+id/device_pairing_failed_restart"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding"
                    android:layout_marginEnd="@dimen/padding"
                    android:layout_marginStart="@dimen/padding"
                    android:layout_marginTop="@dimen/padding"
                    android:onClick="@{() -> viewModel.onRestartScanClick()}"
                    android:text="@string/try_again"
                    style="@style/Button.Primary" />

                <TextView
                    android:id="@+id/device_pairing_failed_need_help"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="@dimen/bigger_padding"
                    android:layout_marginEnd="@dimen/size_spacing_xlarge"
                    android:layout_marginStart="@dimen/size_spacing_xlarge"
                    android:layout_marginTop="@dimen/padding"
                    android:onClick="@{() -> viewModel.onNeedHelpClick()}"
                    android:text="@string/need_help"
                    android:textColor="@color/accent"
                    style="@style/ButtonFlat" />

            </LinearLayout>

        </ScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
