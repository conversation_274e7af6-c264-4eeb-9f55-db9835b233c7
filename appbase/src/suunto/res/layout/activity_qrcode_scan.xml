<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.stt.android.qrcode.QRCodeScanViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".qrcode.QRCodeScanActivity">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            style="@style/Toolbar.Native"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:theme="@style/Toolbar.Native" />

        <com.stt.android.ui.utils.WidthLimiterLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.camera.view.PreviewView
                    android:id="@+id/previewView"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.stt.android.qrcode.ScanOverlay
                    android:id="@+id/overlay"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/switchTorch"
                    android:layout_width="@dimen/size_icon_large"
                    android:layout_height="@dimen/size_icon_large"
                    android:layout_marginBottom="30dp"
                    android:importantForAccessibility="no"
                    android:onClick="@{() -> viewModel.onTorchSwitchClicked()}"
                    android:src="@{viewModel.torchOn ? @drawable/device_scan_qrcode_torch_on : @drawable/device_scan_qrcode_torch_off}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintBottom_toTopOf="@id/tips"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <LinearLayout
                    android:id="@+id/tips"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="54dp"
                    android:layout_marginBottom="30dp"
                    android:background="@drawable/shape_white_corner_8"
                    android:backgroundTint="#1AFFFFFF"
                    android:orientation="horizontal"
                    android:padding="@dimen/padding"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">

                    <ImageView
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:layout_gravity="center_vertical"
                        android:importantForAccessibility="no"
                        android:src="@drawable/device_scan_qrcode_tips" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/padding"
                        android:lineSpacingMultiplier="1.2"
                        android:src="@drawable/device_scan_qrcode_tips"
                        android:text="@string/device_scan_qrcode_tips"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_medium"
                        tools:text="QR code pairing is only available with Suunto [Dilu] for now" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.stt.android.ui.utils.WidthLimiterLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
