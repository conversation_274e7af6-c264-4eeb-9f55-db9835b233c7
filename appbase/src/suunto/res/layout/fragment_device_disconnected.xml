<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View"/>
        <variable
            name="viewModel"
            type="com.stt.android.watch.disconnected.DeviceDisconnectedViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinator"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/device_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_anchor="@+id/device_disconnected_scrollview"
            app:layout_behavior="com.stt.android.watch.VerticalFadeBehavior">

            <include
                layout="@layout/fragment_device_image"
                android:id="@+id/device_disconnected_image"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/device_image_height"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:viewModel="@{viewModel}" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_watch_state_not_connected"
                app:layout_constraintBottom_toBottomOf="@+id/device_disconnected_image"
                app:layout_constraintEnd_toEndOf="@+id/device_disconnected_image"
                app:layout_constraintStart_toStartOf="@+id/device_disconnected_image"
                app:layout_constraintTop_toTopOf="@+id/device_disconnected_image" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/device_disconnected_scrollview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_image_height" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/padding"
                    android:lineSpacingMultiplier="1.3"
                    android:text="@string/watch_ui_not_connected"
                    style="@style/HeaderLabel.Large" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="@dimen/padding"
                    android:layout_marginTop="@dimen/smaller_padding"
                    android:lineSpacingMultiplier="1.3"
                    android:text="@string/device_ui_status_reconnecting"
                    style="@style/Body.Medium.Gray" />

                <include
                    layout="@layout/device_connection_alert"
                    android:visibility="@{viewModel.connectionAlertAvailable ? View.VISIBLE : View.GONE}" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/padding"
                    android:gravity="center_horizontal"
                    android:lineSpacingMultiplier="1.3"
                    android:text="@{viewModel.connectingInstructionMessage}"
                    tools:text="@string/device_ui_status_connecting_message" />

                <Button
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="@dimen/padding"
                    android:layout_marginEnd="@dimen/size_spacing_xlarge"
                    android:layout_marginStart="@dimen/size_spacing_xlarge"
                    android:layout_marginTop="@dimen/padding"
                    android:onClick="@{() -> viewModel.onHelpClick()}"
                    android:text="@string/need_help"
                    style="@style/ButtonFlat" />

                <include
                    android:id="@+id/device_action_list"
                    layout="@layout/device_action_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/padding"
                    android:layout_marginTop="@dimen/padding"
                    app:sharedViewModel="@{viewModel.sharedViewModel}" />

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/device_image_height_half" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
