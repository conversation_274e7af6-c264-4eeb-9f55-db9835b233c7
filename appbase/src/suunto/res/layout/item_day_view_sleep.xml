<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="item"
            type="com.stt.android.home.dayview.daypageitems.DayViewSleepItem" />
        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/FeedCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:theme="@style/WhiteTheme">

        <TextView
            android:id="@+id/durationLabel"
            style="@style/Datalabel.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:maxLines="1"
            android:text="@{item.itemValue}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="7:36" />

        <TextView
            android:id="@+id/durationUnit"
            style="@style/Body.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_xsmall"
            android:maxLines="1"
            android:text="@string/hour"
            app:layout_constraintBaseline_toBaselineOf="@id/durationLabel"
            app:layout_constraintStart_toEndOf="@id/durationLabel"
            tools:text="h" />

        <TextView
            android:id="@+id/goalText"
            style="@style/Body.Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_xsmall"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:gravity="end"
            android:maxLines="1"
            android:text="@{item.getGoalText(context)}"
            app:layout_constraintBaseline_toBaselineOf="@id/durationLabel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/durationUnit"
            tools:text="-0:48 short of goal" />

        <com.stt.android.ui.components.MultipleProgressIndicator
            android:id="@+id/goalProgressBar"
            style="@style/NewSleepGoalProgressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            app:layout_constraintTop_toBottomOf="@id/durationLabel"
            app:progressValue="@{item.progressValue}"
            app:secondProgressValue="@{item.secondProgressValue}"
            tools:progress="80" />

        <com.airbnb.epoxy.EpoxyRecyclerView
            android:id="@+id/sleep_time_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_small"
            app:layout_constraintTop_toBottomOf="@+id/goalProgressBar"
            app:layout_constraintStart_toStartOf="@+id/goalProgressBar"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:sleepTimeItems="@{item.sleep}"
            />

        <View
            android:id="@+id/durationDivider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:background="@color/very_light_gray"
            android:visibility="@{item.showLongSleep ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@+id/sleep_time_rv" />

        <TextView
            android:id="@+id/control_show_detail"
            style="@style/Body.Medium.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{() -> item.toggleShowSleepStagesGraph() }"
            android:padding="@dimen/size_spacing_smaller"
            android:text="@string/show_sleep_detail"
            android:textColor="@color/suunto_blue"
            android:visibility="@{item.showSleepStages ? View.VISIBLE : View.GONE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/durationDivider" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/size_spacing_smaller"
            android:contentDescription="@null"
            android:rotation="@{item.showSleepStageGraph ? 0 : 180}"
            android:src="@drawable/ic_up_arrow_sleep_stages"
            android:visibility="@{item.showSleepStages ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@+id/control_show_detail"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/control_show_detail" />

        <com.stt.android.home.dayview.sleepstages.SleepStagesBarChart
            android:id="@+id/sleep_stages_chart"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginStart="@dimen/size_spacing_smaller"
            android:layout_marginEnd="@dimen/size_spacing_smaller"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:visibility="@{item.showSleepStageGraph ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@+id/control_show_detail" />

        <TextView
            android:id="@+id/resourcesLabel"
            style="@style/Datalabel.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:maxLines="1"
            android:text="@{item.balanceOnWakeUpPerc != null ? item.balanceOnWakeUpPerc.toString() : ``}"
            android:visibility="@{item.showBalanceGained ? View.VISIBLE : View.GONE}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/sleep_stages_chart"
            tools:text="82" />

        <TextView
            android:id="@+id/resourcesUnit"
            style="@style/Body.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_xsmall"
            android:maxLines="1"
            android:text="@string/TXT_PERCENT"
            android:visibility="@{item.showBalanceGained ? View.VISIBLE : View.GONE}"
            app:layout_constraintBaseline_toBaselineOf="@id/resourcesLabel"
            app:layout_constraintStart_toEndOf="@id/resourcesLabel" />

        <TextView
            android:id="@+id/resourcesGainedText"
            style="@style/Body.Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_xsmall"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:gravity="end"
            android:maxLines="1"
            android:text="@{item.getBalanceGainedText(context)}"
            android:visibility="@{item.showBalanceGained ? View.VISIBLE : View.GONE}"
            app:layout_constraintBaseline_toBaselineOf="@id/resourcesLabel"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="@string/day_view_resources_gained_during_sleep" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/resourcesProgressBar"
            style="@style/ResourcesGainedProgressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:progress="@{item.balanceOnWakeUpPerc != null ? item.balanceOnWakeUpPerc : 0}"
            android:visibility="@{item.showBalanceGained ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/resourcesLabel"
            tools:progress="40" />

        <TextView
            android:id="@+id/resourcesOnWakeUpLabel"
            style="@style/Body.Medium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_xsmall"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:maxLines="1"
            android:text="@string/day_view_resources_on_wake_up"
            android:visibility="@{item.showBalanceGained ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/resourcesProgressBar" />

        <View
            android:id="@+id/resourcesGainedDivider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:background="@color/very_light_gray"
            android:visibility="@{item.showBalanceGained ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/resourcesOnWakeUpLabel" />

        <TextView
            android:id="@+id/feedbackText"
            style="@style/Body.Medium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:lineSpacingMultiplier="1.2"
            android:text="@{item.getFeedbackText(context)}"
            android:visibility="@{item.getFeedbackText(context) != null ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/resourcesGainedDivider"
            tools:text="@string/sleep_summary_text_4" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/heartRateIconStart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_begin="28dp" />

        <ImageView
            android:id="@+id/averageHRIcon"
            android:layout_width="@dimen/size_icon_medium"
            android:layout_height="@dimen/size_icon_medium"
            android:layout_marginTop="@dimen/size_spacing_large"
            android:contentDescription="@null"
            android:scaleType="center"
            android:src="@drawable/ic_activity_data_daily_hr"
            android:visibility="@{item.showAverageHR ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@+id/minHRIcon"
            app:layout_constraintStart_toStartOf="@id/heartRateIconStart"
            app:layout_constraintTop_toBottomOf="@+id/feedbackText"
            app:layout_constraintVertical_chainStyle="packed"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/averageHRText"
            style="@style/Body.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:text="@{item.getAverageHRText(context)}"
            android:visibility="@{item.showAverageHR ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@id/averageHRIcon"
            app:layout_constraintStart_toEndOf="@id/averageHRIcon"
            app:layout_constraintTop_toTopOf="@id/averageHRIcon"
            tools:text="65 bpm\nAverage" />

        <ImageView
            android:id="@+id/minHRIcon"
            android:layout_width="@dimen/size_icon_medium"
            android:layout_height="@dimen/size_icon_medium"
            android:layout_marginTop="@dimen/size_spacing_xlarge"
            android:contentDescription="@null"
            android:scaleType="center"
            android:src="@drawable/ic_activity_data_daily_hr"
            android:visibility="@{item.showMinHR ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@+id/avgHRVIcon"
            app:layout_constraintStart_toStartOf="@id/heartRateIconStart"
            app:layout_constraintTop_toBottomOf="@+id/averageHRIcon" />

        <TextView
            android:id="@+id/minHRText"
            style="@style/Body.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:text="@{item.getMinimumHRText(context)}"
            android:visibility="@{item.showMinHR ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@id/minHRIcon"
            app:layout_constraintStart_toEndOf="@id/minHRIcon"
            app:layout_constraintTop_toTopOf="@id/minHRIcon"
            tools:text="51 bpm\nMinimum" />

        <ImageView
            android:id="@+id/avgHRVIcon"
            android:layout_width="@dimen/size_icon_medium"
            android:layout_height="@dimen/size_icon_medium"
            android:layout_marginTop="@dimen/size_spacing_xlarge"
            android:contentDescription="@null"
            android:scaleType="center"
            android:src="@drawable/ic_activity_data_daily_hrv"
            android:visibility="@{item.showAvgHrv ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@+id/maxSpO2Icon"
            app:layout_constraintStart_toStartOf="@id/heartRateIconStart"
            app:layout_constraintTop_toBottomOf="@+id/minHRIcon" />

        <TextView
            android:id="@+id/avgHRVText"
            style="@style/Body.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:text="@{item.getAverageHRVText(context)}"
            android:visibility="@{item.showAvgHrv ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@id/avgHRVIcon"
            app:layout_constraintStart_toEndOf="@id/avgHRVIcon"
            app:layout_constraintTop_toTopOf="@id/avgHRVIcon"
            tools:text="30 ms\nAverage" />

        <ImageView
            android:id="@+id/maxSpO2Icon"
            android:layout_width="@dimen/size_icon_medium"
            android:layout_height="@dimen/size_icon_medium"
            android:layout_marginTop="@dimen/size_spacing_xlarge"
            android:contentDescription="@null"
            android:scaleType="center"
            android:src="@drawable/ic_spo2_icon"
            android:visibility="@{item.showMaxSpO2 ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@+id/qualityIcon"
            app:layout_constraintStart_toStartOf="@id/heartRateIconStart"
            app:layout_constraintTop_toBottomOf="@+id/avgHRVIcon" />

        <TextView
            android:id="@+id/maxSpO2Text"
            style="@style/Body.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:text="@{item.getMaxSpO2Text(context)}"
            android:visibility="@{item.showMaxSpO2 ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@+id/maxSpO2AltitudeText"
            app:layout_constraintStart_toEndOf="@id/maxSpO2Icon"
            app:layout_constraintTop_toTopOf="@id/maxSpO2Icon"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="51 bpm\nMinimum" />

        <TextView
            android:id="@+id/maxSpO2AltitudeText"
            style="@style/Body.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:drawablePadding="@dimen/size_spacing_xxsmall"
            android:text="@{item.getAltitudeText(context)}"
            android:visibility="@{item.showMaxSpO2 &amp;&amp; item.showAltitude ? View.VISIBLE : View.GONE}"
            app:drawableStartCompat="@drawable/ic_altitude_outline_small"
            app:layout_constraintBottom_toBottomOf="@id/maxSpO2Icon"
            app:layout_constraintStart_toEndOf="@id/maxSpO2Icon"
            app:layout_constraintTop_toBottomOf="@id/maxSpO2Text"
            tools:drawableStart="@drawable/ic_altitude_outline_small"
            tools:text="300 m" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/qualityIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_xlarge"
            android:layout_marginBottom="@dimen/size_spacing_small"
            android:contentDescription="@null"
            android:src="@drawable/sleep_fill_32dp"
            android:tint="@color/activity_data_sleep_quality"
            android:visibility="@{item.showQuality ? View.VISIBLE : View.GONE}"
            app:layout_constraintStart_toStartOf="@id/heartRateIconStart"
            app:layout_constraintTop_toBottomOf="@+id/maxSpO2Icon"
            app:layout_constraintBottom_toTopOf="@+id/sleepDataWarningLabel"
            />

        <TextView
            android:id="@+id/qualityText"
            style="@style/Body.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:text="@{item.getQualityText(context)}"
            android:visibility="@{item.showQuality ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@id/qualityIcon"
            app:layout_constraintStart_toEndOf="@id/qualityIcon"
            app:layout_constraintTop_toTopOf="@id/qualityIcon"
            tools:text="87 %\nQuality" />

        <!-- Sleep stages are shown in the right hand half of the screen if HR or quality values
        are available. If not, then this anchor view is set to GONE visibility and sleep stages
        are shown in the left hand side. -->
        <View
            android:id="@+id/sleepStagesStartAnchor"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="@{item.shouldUseVerticalBarrier ? View.VISIBLE : View.GONE}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.50" />

        <TextView
            android:id="@+id/awakeText"
            style="@style/Body.Small"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:text="@{item.getAwakeDurationText(context)}"
            android:visibility="@{item.showAwakeDuration ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@id/awakeProgressBar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/sleepStagesStartAnchor"
            app:layout_constraintTop_toBottomOf="@+id/feedbackText"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="Awake 0:59 h" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/awakeProgressBar"
            style="@style/SleepStageProgressBar"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:progress="@{item.awakeProgressValue}"
            android:visibility="@{item.showAwakeDuration ? View.VISIBLE : View.GONE}"
            app:indicatorColor="@color/sleep_awake"
            app:layout_constraintBottom_toTopOf="@id/remText"
            app:layout_constraintStart_toEndOf="@id/sleepStagesStartAnchor"
            app:layout_constraintTop_toBottomOf="@id/awakeText"
            tools:progress="22" />

        <TextView
            android:id="@+id/remText"
            style="@style/Body.Small"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:layout_marginTop="@dimen/size_spacing_large"
            android:text="@{item.getREMSleepDurationText(context)}"
            android:visibility="@{item.showREMSleepDuration ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@id/remProgressBar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/sleepStagesStartAnchor"
            app:layout_constraintTop_toBottomOf="@id/awakeProgressBar"
            tools:text="REM 2:33 h" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/remProgressBar"
            style="@style/SleepStageProgressBar"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:progress="@{item.remSleepProgressValue}"
            android:visibility="@{item.showREMSleepDuration ? View.VISIBLE : View.GONE}"
            app:indicatorColor="@color/sleep_rem"
            app:layout_constraintBottom_toTopOf="@id/lightSleepText"
            app:layout_constraintStart_toEndOf="@id/sleepStagesStartAnchor"
            app:layout_constraintTop_toBottomOf="@id/remText"
            tools:progress="44" />

        <TextView
            android:id="@+id/lightSleepText"
            style="@style/Body.Small"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:layout_marginTop="@dimen/size_spacing_large"
            android:text="@{item.getLightSleepDurationText(context)}"
            android:visibility="@{item.showLightSleepDuration ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@id/lightSleepProgressBar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/sleepStagesStartAnchor"
            app:layout_constraintTop_toBottomOf="@id/remProgressBar"
            tools:text="Light 2:33 h" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/lightSleepProgressBar"
            style="@style/SleepStageProgressBar"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:progress="@{item.lightSleepProgressValue}"
            android:visibility="@{item.showLightSleepDuration ? View.VISIBLE : View.GONE}"
            app:indicatorColor="@color/sleep_core"
            app:layout_constraintBottom_toTopOf="@id/deepSleepText"
            app:layout_constraintStart_toEndOf="@id/sleepStagesStartAnchor"
            app:layout_constraintTop_toBottomOf="@id/lightSleepText"
            tools:progress="50" />

        <TextView
            android:id="@+id/deepSleepText"
            style="@style/Body.Small"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:layout_marginTop="@dimen/size_spacing_large"
            android:text="@{item.getDeepSleepDurationText(context)}"
            android:visibility="@{item.showDeepSleepDuration ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@id/deepSleepProgressBar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/sleepStagesStartAnchor"
            app:layout_constraintTop_toBottomOf="@id/lightSleepProgressBar"
            tools:text="Deep 1:36 h" />

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/deepSleepProgressBar"
            style="@style/SleepStageProgressBar"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:progress="@{item.deepSleepProgressValue}"
            android:visibility="@{item.showDeepSleepDuration ? View.VISIBLE : View.GONE}"
            app:indicatorColor="@color/sleep_deep"
            app:layout_constraintBottom_toTopOf="@id/sleepDataWarningLabel"
            app:layout_constraintStart_toEndOf="@id/sleepStagesStartAnchor"
            app:layout_constraintTop_toBottomOf="@id/deepSleepText"
            tools:progress="60" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrierBottom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="qualityIcon,deepSleepProgressBar" />

        <TextView
            android:id="@+id/sleepDataWarningLabel"
            style="@style/Body.Medium.Gray"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lineSpacingMultiplier="1.2"
            android:padding="@dimen/size_spacing_medium"
            android:text="@string/sleep_data_warning_label"
            android:visibility="@{item.showLongSleep ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/barrierBottom" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
