<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="sleepTime"
            type="String" />

        <variable
            name="isNap"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/sleep_time_legend"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:background="@{isNap? @drawable/nap_circle : @drawable/sleep_circle}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/sleep_time"
            app:layout_constraintBottom_toBottomOf="@+id/sleep_time"
            tools:background="@drawable/sleep_circle"
            />

        <TextView
            android:id="@+id/sleep_time"
            style="@style/Body.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_small"
            android:maxLines="1"
            app:layout_constraintStart_toEndOf="@+id/sleep_time_legend"
            app:layout_constraintTop_toTopOf="parent"
            android:text="@{sleepTime}"
            tools:text="1h 25min Nap" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
