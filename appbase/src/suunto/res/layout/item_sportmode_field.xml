<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="item"
            type="com.stt.android.watch.sportmodes.editfield.SportModeFieldItem" />
    </data>
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/size_divider_thin"
        style="@style/FeedCard.ripple.noelevation">
        <TextView
            android:id="@+id/data_field_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/size_spacing_medium"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_medium"
            android:text="@{item.fieldName}"
            tools:text="1. Pace (min/km)"
            style="@style/Body.Large" />
    </FrameLayout>
</layout>
