<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="versionLog"
            type="String" />

        <variable
            name="activity"
            type="android.app.Activity" />

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/near_white"
        android:visibility="@{versionLog.isEmpty() ? View.GONE : View.VISIBLE}"
        android:paddingVertical="10dp">

        <WebView
            android:id="@+id/versionLog"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:activity="@{activity}"
            app:versionLog="@{versionLog}"
            tools:ignore="WebViewLayout" />

    </FrameLayout>
</layout>
