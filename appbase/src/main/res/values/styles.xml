<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="ApplicationTheme" parent="@style/Theme.AppCompat.NoActionBar">
        <item name="android:textColorPrimary">@color/primary_text_color</item>
        <item name="android:textColorPrimaryInverse">@color/primary_text_color_inverse</item>
        <item name="android:textColorSecondary">@color/secondary_text_color</item>
        <item name="android:windowBackground">@drawable/theme_background</item>
        <item name="colorAccent">@color/newAccent</item>
        <item name="colorButtonNormal">?attr/newAccentColor</item>
        <item name="preferenceTheme">@style/AppPreferencesTheme</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>

    <style name="ApplicationTheme.NoBackground" parent="ApplicationTheme">
        <item name="android:windowBackground">@null</item>
    </style>

    <style name="ApplicationTheme.AlmostNoTransparent" parent="ApplicationTheme">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@color/almost_no_transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="ApplicationTheme.ActionBar" parent="@style/Theme.AppCompat">
        <item name="android:textColorPrimary">@color/primary_text_color</item>
        <item name="android:textColorPrimaryInverse">@color/primary_text_color_inverse</item>
        <item name="android:textColorSecondary">@color/secondary_text_color</item>
        <item name="android:windowBackground">@drawable/theme_background</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="actionBarStyle">@style/ActionBarStyle</item>
        <item name="android:itemTextAppearance">@style/MenuItemTextAppearance</item>
        <item name="colorAccent">@color/newAccent</item>
        <item name="preferenceTheme">@style/AppPreferencesTheme</item>
    </style>

    <style name="MenuItemTextAppearance">
        <item name="android:textColor">@color/color_text_menu_item_legacy</item>
    </style>

    <!-- Dummy style. See values-v19/styles.xml -->
    <style name="ApplicationTheme.TranslucentStatus" parent="ApplicationTheme"/>

    <style name="ApplicationTheme.FullyTranslucent" parent="android:Theme.Translucent.NoTitleBar">
        <item name="android:progressBarStyle">@style/InvisibleProgress</item>
    </style>

    <style name="InvisibleProgress">
        <item name="android:visibility">gone</item>
    </style>

    <!-- Dummy style. See values-v19/styles.xml -->
    <style name="ApplicationTheme.NoBackground.TranslucentStatus" parent="ApplicationTheme.NoBackground"/>

    <style name="ActionBarTitleStyle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="ActionBarSubtitleStyle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse">
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="ActionBarStyle" parent="@style/Widget.AppCompat.Light.ActionBar.Solid.Inverse">
        <item name="titleTextStyle">@style/ActionBarTitleStyle</item>
        <item name="subtitleTextStyle">@style/ActionBarSubtitleStyle</item>
    </style>

    <style name="basic_text">
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps" tools:ignore="NewApi">false</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="basic_text_normal" parent="basic_text">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="s12" parent="basic_text">
        <item name="android:textSize">@dimen/d8f</item>
    </style>

    <style name="s14" parent="basic_text">
        <item name="android:textSize">@dimen/d9_33f</item>
    </style>

    <style name="s15" parent="basic_text">
        <item name="android:textSize">@dimen/d10f</item>
    </style>

    <style name="s16" parent="basic_text">
        <item name="android:textSize">@dimen/d11f</item>
    </style>

    <style name="s18" parent="basic_text">
        <item name="android:textSize">@dimen/d12f</item>
    </style>

    <style name="s20" parent="basic_text">
        <item name="android:textSize">@dimen/d13f</item>
    </style>

    <style name="s21" parent="basic_text">
        <item name="android:textSize">@dimen/d14f</item>
    </style>

    <style name="s23" parent="basic_text">
        <item name="android:textSize">@dimen/d15_33f</item>
    </style>

    <style name="s26" parent="basic_text">
        <item name="android:textSize">@dimen/d17_33f</item>
    </style>

    <style name="s32" parent="basic_text">
        <item name="android:textSize">@dimen/d21_33f</item>
        <item name="android:typeface">sans</item>
    </style>

    <style name="s38" parent="basic_text">
        <item name="android:textSize">@dimen/d25_33f</item>
        <item name="android:typeface">sans</item>
    </style>

    <style name="s50" parent="basic_text_normal">
        <item name="android:textSize">@dimen/d33_33f</item>
        <item name="android:typeface">sans</item>
    </style>

    <style name="s72" parent="basic_text_normal">
        <item name="android:textSize">@dimen/d48f</item>
        <item name="android:typeface">sans</item>
    </style>

    <style name="s80" parent="basic_text_normal">
        <item name="android:textSize">@dimen/d53_33f</item>
        <item name="android:typeface">sans</item>
    </style>


    <style name="s90" parent="basic_text_normal">
        <item name="android:textSize">@dimen/d70f</item>
        <item name="android:typeface">sans</item>
    </style>

    <style name="s135" parent="basic_text_normal">
        <item name="android:textSize">@dimen/d90f</item>
        <item name="android:typeface">sans</item>
    </style>

    <style name="Label14" parent="s14">
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="Label15" parent="s15">
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="LabelDarker15" parent="s15">
        <item name="android:textColor">@color/label_darker</item>
    </style>

    <style name="Label15normal" parent="Label15">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="Label16" parent="s16">
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="Label18" parent="s18">
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="WidgetLabel" parent="basic_text">
        <item name="android:textColor">@color/label</item>
        <item name="android:textSize">@dimen/dWidgetLabelf</item>
    </style>

    <style name="Label18normal" parent="Label18">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="Label20" parent="s20">
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="Label20normal" parent="Label20">
        <item name="android:textStyle">normal</item>
        <item name="android:includeFontPadding">true</item>
    </style>

    <style name="Label21" parent="s21">
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="Label21normal" parent="Label21">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="Label21normalRed" parent="Label21normal">
        <item name="android:textColor">@color/red</item>
    </style>

    <style name="Label23" parent="s23">
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="Label26" parent="s26">
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="Label32" parent="s32">
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="Label32Normal" parent="Label32">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="Value12" parent="s12">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="Value15" parent="s15">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="Black15" parent="s15">
        <item name="android:textColor">@color/black</item>
        <item name="android:shadowColor">@color/white</item>
        <item name="android:shadowRadius">0.5</item>
        <item name="android:shadowDx">0.5</item>
        <item name="android:shadowDy">0.5</item>
    </style>

    <style name="Value15normal" parent="Value15">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="Value16" parent="s16">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="Value16normal" parent="s16">
        <item name="android:textColor">@color/value</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="Value18" parent="s18">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="Value18normal" parent="Value18">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="Value20" parent="s20">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="Value20normal" parent="Value20">
        <item name="android:textStyle">normal</item>
        <item name="android:includeFontPadding">true</item>
    </style>

    <style name="Value21" parent="s21">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="Value21normal" parent="Value21">
        <item name="android:textStyle">normal</item>
        <item name="android:includeFontPadding">true</item>
    </style>

    <style name="Value23" parent="s23">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="Value23normal" parent="Value23">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="Value26" parent="s26">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="Value26normal" parent="Value26">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="Value32" parent="s32">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="Value32normal" parent="Value32">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="Value50" parent="s50">
        <item name="android:textColor">@color/value</item>
        <item name="android:layout_marginTop">-3dp</item>
    </style>

    <style name="Value72" parent="s72">
        <item name="android:textColor">@color/value</item>
        <item name="android:layout_marginTop">-6dp</item>
    </style>


    <style name="Value80" parent="s80">
        <item name="android:textColor">@color/value</item>
        <item name="android:layout_marginTop">-6dp</item>
    </style>

    <style name="Value90" parent="s90">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="Value135" parent="s135">
        <item name="android:textColor">@color/value</item>
    </style>

    <style name="Accent26" parent="s26">
        <item name="android:textColor">@color/newAccent</item>
    </style>

    <style name="Accent23" parent="s23">
        <item name="android:textColor">@color/newAccent</item>
    </style>

    <style name="Accent16" parent="s16">
        <item name="android:textColor">@color/newAccent</item>
    </style>

    <style name="Superscript" parent="s16">
        <item name="android:textColor">@color/label</item>
        <item name="android:textStyle">normal</item>
        <item name="android:shadowColor">#000000</item>
        <item name="android:shadowRadius">2</item>
        <item name="android:drawableBottom">#000000</item>
    </style>

    <style name="SectionLayout">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">?android:attr/selectableItemBackground</item>
    </style>

    <style name="SectionLayoutHome">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/dash_separatorline</item>
    </style>

    <style name="VerticalSectionLayoutHome">
        <item name="android:layout_width">1dp</item>
        <item name="android:background">@drawable/dash_separatorline</item>
    </style>

    <style name="VerticalGroupLayout">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="Icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">2dp</item>
    </style>

    <style name="PageBullet">
        <item name="android:layout_width">@dimen/size_spacing_small</item>
        <item name="android:layout_height">@dimen/size_spacing_small</item>
        <item name="android:layout_margin">@dimen/size_spacing_xsmall</item>
    </style>

    <style name="ImageAd">
        <item name="android:layout_marginStart">@dimen/size_spacing_medium</item>
        <item name="android:layout_marginEnd">@dimen/size_spacing_medium</item>
        <item name="android:layout_marginBottom">@dimen/size_spacing_medium</item>
        <item name="android:layout_marginTop">@dimen/size_spacing_medium</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="Image">
        <item name="android:layout_margin">2dp</item>
    </style>

    <style name="Image.Thumbnail">
        <item name="android:layout_width">@dimen/thumbnailImageWidth</item>
        <item name="android:layout_height">@dimen/thumbnailImageHeight</item>
    </style>

    <style name="Image.Big">
        <item name="android:layout_width">@dimen/bigImageWidth</item>
        <item name="android:layout_height">@dimen/bigImageHeight</item>
    </style>

    <style name="Image.Small">
        <item name="android:layout_width">50dp</item>
        <item name="android:layout_height">50dp</item>
        <item name="android:layout_margin">0dp</item>
    </style>

    <style name="BigButton" parent="s21">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">@dimen/bigButton_height</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/value</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="SmallButton" parent="s14">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">@dimen/smallButton_height</item>
        <item name="android:textColor">@color/value</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="AccentButton" parent="BigButton">
        <item name="android:background">@drawable/accent_button</item>
    </style>

    <style name="LoginSignUpButton">
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/dark_gray_7</item>
        <item name="android:background">@color/white</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="FbSignUpBtn" parent="LoginSignUpButton">
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@color/facebook_blue</item>
    </style>

    <style name="TrackingWideButton" parent="Button.Wide">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="TrackingAccentButton" parent="Button.RoundedSecondaryAccent">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="AccentButtonSmall" parent="SmallButton">
        <item name="android:background">@drawable/accent_button</item>
    </style>

    <style name="GreenButton" parent="BigButton">
        <item name="android:background">@drawable/green_button</item>
    </style>

    <style name="RedButton" parent="BigButton">
        <item name="android:background">@drawable/red_button</item>
    </style>

    <style name="WorkoutCountersText" parent="basic_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">10sp</item>
        <item name="android:drawablePadding">2dp</item>
    </style>

    <style name="topNavBt" parent="Value18">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:paddingStart">@dimen/leftMargin</item>
        <item name="android:paddingEnd">@dimen/rightMargin</item>
        <item name="android:paddingTop">1dp</item>
        <item name="android:paddingBottom">1dp</item>
        <item name="android:minWidth">75dp</item>
        <item name="android:background">@drawable/top_nav_bt</item>
    </style>

    <style name="topNavBtIcon" parent="Value16">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:minWidth">44dp</item>
        <item name="android:paddingStart">5dp</item>
        <item name="android:paddingEnd">5dp</item>
        <item name="android:background">@drawable/round_ripple_bg</item>
    </style>

    <style name="topNavThinBt" parent="topNavBt">
        <item name="android:minWidth">50dp</item>
    </style>

    <style name="topNav" parent="Value26normal">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">@dimen/top_navigation_bar_height</item>
        <item name="android:background">@drawable/top_nav_background</item>
        <item name="android:paddingStart">8dp</item>
        <item name="android:paddingEnd">8dp</item>
    </style>

    <style name="topNavText" parent="topNav">
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="graphHrZoneHeader" parent="Value21">
        <item name="android:textStyle">normal</item>
    </style>

    <style name="graphHrZoneHeaderValue" parent="Value21">
        <item name="android:textStyle">normal</item>
        <item name="android:padding">10dp</item>
    </style>

    <!-- Use this style for elements that need to provide feedback when user taps -->
    <style name="SelectableItem">
        <item name="android:background">@drawable/menuitem_background</item>
    </style>

    <style name="SelectableItemNoDivider">
        <item name="android:background">@drawable/menuitem_background_no_divider</item>
    </style>

    <style name="graphHeader" parent="Value26">
        <item name="android:gravity">right</item>
    </style>

    <style name="graphHeaderUnit" parent="Value21">
        <item name="android:textStyle">normal</item>
        <item name="android:paddingStart">3dp</item>
    </style>

    <style name="graphCheckBox" parent="Label21">
        <item name="android:button">@drawable/graph_checkbox</item>
        <item name="android:textAllCaps" tools:ignore="NewApi">true</item>
    </style>

    <style name="FloatLabel" parent="android:TextAppearance.Small">
        <item name="android:textColor">@color/label</item>
        <item name="android:textSize">@dimen/d14f</item>
    </style>

    <style name="AccentButton.Material" parent="Widget.AppCompat.Button.Colored">
        <item name="android:colorButtonNormal">@color/newAccent</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="WhiteButton.Material" parent="Widget.AppCompat.Button.Colored">
        <item name="android:colorButtonNormal">@color/white</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="whatsNewTitle">
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">@color/black</item>
        <item name="android:shadowRadius">2</item>
        <item name="android:textSize">@dimen/whats_new_title_text_size</item>
    </style>

    <style name="whatsNewText" parent="whatsNewTitle">
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">@dimen/whats_new_text_size</item>
        <item name="android:lineSpacingExtra">@dimen/whats_new_line_spacing_extra</item>
    </style>

    <style name="whatsNewActionButton">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">@dimen/padding</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textSize">@dimen/whats_new_text_size</item>
        <item name="android:background">@drawable/start_workout_button_bg</item>
        <item name="android:minWidth">180dp</item>
    </style>

    <style name="hrZoneText" parent="Body">
        <item name="android:textSize" tools:ignore="SpUsage">14dp</item>
    </style>

    <style name="hrZoneText.Bold">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="FullscreenDialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="FullscreenDialog.Laps">
        <item name="android:windowBackground">?suuntoBackground</item>
        <item name="android:windowAnimationStyle">@style/EnterUpExitDownDialogAnimation</item>
    </style>

    <style name="EnterUpExitDownDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_in_up</item>
        <item name="android:windowExitAnimation">@anim/slide_out_down</item>
    </style>

    <style name="LapTable">
        <item name="android:ellipsize">end</item>
        <item name="android:gravity">left</item>
    </style>

    <style name="LapTable.Labels" parent="Body.Small.Bold">
        <item name="android:textAllCaps">true</item>
        <item name="android:textSize">10sp</item>
    </style>

    <style name="LapTable.Values" parent="Body.Medium">
        <item name="android:textSize">@dimen/text_size_medium_dp</item>
    </style>

    <style name="LapTable.Values.LapNumber"/>

    <style name="LapButtons" parent="ButtonFlat">
        <item name="android:textColor">@color/navbar_text_color</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:background">@drawable/laps_top_nav_bt</item>
    </style>

    <style name="AdvancedLapsTabTextAppearance" parent="HeaderLabel.Medium">
        <item name="android:textColor">@color/advanced_laps_tab_color</item>
    </style>

    <style name="WorkoutDetailCard" parent="FeedCard"/>

    <style name="WorkoutDetailCard.TopMargin">
        <item name="android:layout_marginTop">@dimen/size_spacing_medium</item>
    </style>

    <style name="WorkoutDetailSectionLayout" parent="WorkoutDetailCard">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/selectable_card_background</item>
    </style>

    <style name="WorkoutDetailCardTitle" parent="Body.Medium.Black">
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="WorkoutEditAddPicsButtonTitle">
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textAppearance">@style/Body2</item>
        <item name="android:textColor">@color/newAccent</item>
    </style>

    <style name="TextBadge" parent="@style/Body.Small">
        <item name="android:paddingLeft">@dimen/size_spacing_small</item>
        <item name="android:paddingRight">@dimen/size_spacing_small</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/badge_background</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:height">22dp</item>
    </style>

    <style name="WorkoutCommentUserName" parent="@style/Body.Medium.Bold"/>
    <style name="WorkoutCommentBody" parent="@style/Body.Medium"/>

    <style name="WorkoutSummaryValue" parent="@style/Body2">
        <item name="android:textSize">18sp</item>
    </style>

    <style name="WorkoutDetailsGridValue" parent="@style/Body.Medium.Black">
        <item name="android:textSize">@dimen/text_size_medium_dp</item>
    </style>

    <style name="WorkoutDetailsGridUnit" parent="@style/Body.Medium">
        <item name="android:textSize">@dimen/text_size_medium_dp</item>
    </style>

    <style name="GoalWheelBig">
        <item name="android:textSize" tools:ignore="SpUsage">66dp</item><!-- Intentionally use dp unit for text size -->
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">sans-serif-condensed</item>
    </style>

    <style name="DurationWidgetDuration">
        <item name="android:textSize">@dimen/dashboard_duration_max_text_size</item><!-- Intentionally use dp unit for text size -->
        <item name="android:fontFamily">@font/suunto_font</item>
    </style>

    <style name="CommuteWidgetSavedCO2">
        <item name="android:textSize">@dimen/text_size_big</item><!-- Intentionally use dp unit for text size -->
        <item name="android:textColor">@color/near_black</item>
        <item name="android:fontFamily">@font/suunto_font</item>
    </style>

    <style name="WorkoutDurationProgressBar" parent="Widget.MaterialComponents.LinearProgressIndicator">
        <item name="trackThickness">6dp</item>
        <item name="trackCornerRadius">3dp</item>
        <item name="trackColor">@color/transparent_white</item>
    </style>

    <style name="DiaryDurationSummaryProgressBar" parent="Widget.MaterialComponents.LinearProgressIndicator">
        <item name="trackThickness">6dp</item>
        <item name="trackCornerRadius">3dp</item>
        <item name="trackColor">?suuntoBackground</item>
    </style>

    <style name="DiarySummaryListItemProgressBar" parent="Widget.MaterialComponents.LinearProgressIndicator">
        <item name="trackThickness">4dp</item>
        <item name="trackCornerRadius">2dp</item>
        <item name="trackColor">?suuntoBackground</item>
        <item name="indicatorColor">@color/summaryAccentColor</item>
    </style>

    <style name="GoalWheel" parent="Body.Medium"/>

    <style name="DashboardViewPager" parent="Body.Small"/>

    <style name="Theme.SimpleAlertDialogActivityTheme" parent="android:Theme">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="Theme.TransparentActivityTheme" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="SimpleAlertDialog" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="colorAccent">@color/accent</item>
    </style>

    <style name="ZoneDurationProgressBar" parent="Base.Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:progressDrawable">@drawable/zone_duration_progress_bar</item>
    </style>

    <style name="ZonedChartGridLine">
        <item name="android:background">?analysisGridColor</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:layout_width">0dp</item>
    </style>

    <style name="ZonedChartThresholdLine">
        <item name="android:layout_height">3dp</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:background">@drawable/zoned_chart_threshold_line</item>
        <!-- No dashes on real device if hardware layer is used.
        See https://issuetracker.google.com/issues/36945767 -->
        <item name="android:layerType">software</item>
    </style>

    <style name="ZonedChartGridLineLabel" parent="Body.Small">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
    </style>

    <style name="ZonedChartThresholdLineLabel" parent="Body.Small.Bold">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
    </style>

    <style name="LandscapeGraph" parent="WhiteTheme">
        <item name="android:windowActionBarOverlay">true</item>
    </style>

    <style name="RouteAltitudeAxisLabel" parent="Body.Small">
        <item name="android:textSize">10sp</item>
    </style>

    <style name="CustomCalendarRangePickerTheme" parent="@style/ThemeOverlay.MaterialComponents.MaterialCalendar">
        <item name="colorPrimary">?colorAccent</item>
    </style>

    <style name="Theme.DiaryTSS" parent="WhiteTheme.DiaryTSS" />

    <!-- A style to be applied as bottomSheetStyle to BottomSheetDialogTheme used by default by WhiteTheme -->
    <style name="ModalBottomSheetDialogStyle" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="backgroundTint">@color/transparent</item>
    </style>

    <!-- A style to be applied as to the root view of the layout used inside BottomSheetDialogs
    when rounded top corners are needed -->
    <style name="RoundedCornerBottomSheetStyle">
        <item name="android:background">@drawable/bottom_sheet_rounded_top_corners_background</item>
        <item name="android:theme">@style/WhiteTheme</item>
    </style>

    <!-- A style to be applied as to the root view of the layout used inside BottomSheetDialogs
    when sharp top corners are needed -->
    <style name="SharpCornerBottomSheetStyle">
        <item name="android:background">?android:attr/colorBackground</item>
        <item name="android:theme">@style/WhiteTheme</item>
    </style>

    <style name="WorkoutDetailWatchModel" parent="@style/Body.Medium.Black">
        <item name="android:textSize">14sp</item>
        <item name="android:color">@color/near_black</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="BaseAutoTaggedDialogDescription">
        <item name="fontWeight">400</item>
        <item name="android:textSize">12sp</item>
        <item name="lineHeight">18sp</item>
        <item name="android:textColor">@color/near_black</item>
    </style>

    <style name="BaseExtraInfoDescription">
        <item name="android:textSize">@dimen/text_size_small</item>
        <item name="android:lineSpacingMultiplier">1.1</item>
    </style>

    <style name="FullScreenDialogStyle" parent="Theme.MaterialComponents.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@color/transparent</item>
    </style>

    <style name="FullScreenTransparentDialogStyle" parent="Theme.MaterialComponents.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>
</resources>
