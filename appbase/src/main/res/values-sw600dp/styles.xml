<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Image.Small">
        <item name="android:layout_width">90dp</item>
        <item name="android:layout_height">90dp</item>
        <item name="android:layout_margin">0dp</item>
    </style>

    <style name="topNavBt" parent="Value18">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:paddingTop">1dp</item>
        <item name="android:paddingBottom">1dp</item>
        <item name="android:minWidth">75dp</item>
        <item name="android:background">@drawable/top_nav_bt</item>
    </style>

</resources>
