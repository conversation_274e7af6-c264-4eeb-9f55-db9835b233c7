<?xml version="1.0" encoding="utf-8"?>
<!-- Override layout_width and layout_height on include tag using this layout-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <fragment
        android:id="@+id/map_snapshotter"
        class="com.stt.android.maps.MapSnapshotterFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/padding"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_max="@dimen/map_card_max_width"
        app:layout_constraintDimensionRatio="4:3"
        tools:ignore="FragmentTagUsage" />

</androidx.constraintlayout.widget.ConstraintLayout>
