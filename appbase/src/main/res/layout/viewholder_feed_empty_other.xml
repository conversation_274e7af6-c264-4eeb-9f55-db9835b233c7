<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?suuntoDividerColor">

        <ImageView
            android:id="@+id/iv_empty_follow"
            android:layout_width="134dp"
            android:layout_height="134dp"
            android:layout_marginTop="56dp"
            android:src="@drawable/ic_empty_other"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            style="@style/Body.Medium.DarkGray"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/d16v"
            android:layout_marginBottom="56dp"
            android:text="@string/no_tracked_activity"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_empty_follow" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
