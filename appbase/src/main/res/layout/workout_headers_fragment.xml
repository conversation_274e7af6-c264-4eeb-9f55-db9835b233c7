<?xml version="1.0" encoding="utf-8"?>
<TableLayout xmlns:android="http://schemas.android.com/apk/res/android"
             xmlns:tools="http://schemas.android.com/tools"
             android:id="@+id/workoutHeadersContainer"
             android:layout_width="match_parent"
             android:layout_height="wrap_content"
             android:divider="?suuntoDividerColor"
             android:paddingBottom="@dimen/size_spacing_small"
             android:paddingEnd="@dimen/size_spacing_xsmaller"
             android:paddingStart="@dimen/size_spacing_xsmaller"
             android:paddingTop="@dimen/size_spacing_small"
             android:showDividers="middle"
             android:stretchColumns="1, 2"
             style="@style/SectionLayout">

    <TableRow
        android:id="@+id/targetCurrentRow"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/targetDate"
            android:layout_width="0dp"
            android:layout_column="1"
            android:layout_gravity="center_vertical"
            android:includeFontPadding="true"
            tools:text="Yesterday at 13:40 PM"
            style="@style/Body.Medium"/>

        <TextView
            android:id="@+id/currentDate"
            android:layout_width="0dp"
            android:layout_column="2"
            android:layout_gravity="center_vertical"
            android:includeFontPadding="true"
            android:text="@string/current"
            style="@style/Body.Medium"/>
    </TableRow>

    <TableRow
        android:id="@+id/skiRuns"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:text="@string/ski_runs_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/skiRunsValue"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            tools:text="01:23:45"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        android:id="@+id/skiTime"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:text="@string/ski_time_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/skiTimeValue"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            tools:text="01:23:45"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/durationLabel"
            android:text="@string/duration_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetDurationValue"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            tools:text="01:23:45"
            style="@style/ActivitySummaryValue"/>

        <LinearLayout
            android:baselineAlignedChildIndex="0"
            android:paddingStart="@dimen/activity_summary_value_left_padding">

            <TextView
                android:id="@+id/durationValue"
                tools:text="01:23:45"
                style="@style/ActivitySummaryValue"/>

            <TextView
                android:id="@+id/startEndTime"
                android:paddingStart="@dimen/activity_summary_unit_padding"
                tools:text="(00:00-00:00)"
                style="@style/ActivitySummaryValue"/>
        </LinearLayout>
    </TableRow>

    <TableRow
        android:id="@+id/skiDistance"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:text="@string/ski_distance_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/skiDistanceValue"
            tools:text="123.45 km"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>

    </TableRow>

    <TableRow
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/distanceLabel"
            android:text="@string/distance_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetDistanceValue"
            tools:text="123.45 km"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>

        <TextView
            android:id="@+id/distanceValue"
            tools:text="123.45 km"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        android:id="@+id/skiRunDescent"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:text="@string/descent_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/skiRunDescentValue"
            tools:text="123.45 km"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        android:id="@+id/avgSpeedSection"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/avgSpeedLabel"
            android:text="@string/avg_speed_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetAvgSpeedValue"
            tools:text="12.4 km/h"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>

        <TextView
            android:id="@+id/avgSpeedValue"
            tools:text="12.4 km/h"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        android:id="@+id/avgPace"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/avgPaceLabel"
            android:text="@string/avg_pace_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetAvgPaceValue"
            tools:text="05:14 min/km"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>

        <TextView
            android:id="@+id/avgPaceValue"
            tools:text="05:14 min/km"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        android:id="@+id/maxSpeedSection"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/maxSpeedLabel"
            android:text="@string/max_speed_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetMaxSpeedValue"
            tools:text="16.4 km/h"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>

        <TextView
            android:id="@+id/maxSpeedValue"
            tools:text="16.4 km/h"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/energyLabel"
            android:text="@string/energy_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetEnergyValue"
            tools:text="163 kcal"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>

        <TextView
            android:id="@+id/energyValue"
            tools:text="163 kcal"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/avgHrLabel"
            android:text="@string/avg_hr_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetAvgHrValue"
            tools:text="163 bpm"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>

        <TextView
            android:id="@+id/avgHrValue"
            tools:text="163 bpm"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/maxHrLabel"
            android:text="@string/max_hr_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetMaxHrValue"
            tools:text="196 bpm"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>

        <TextView
            android:id="@+id/maxHrValue"
            tools:text="196 bpm"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        android:id="@+id/averageCadenceSection"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:text="@string/avg_cadence_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetAvgCadenceValue"
            tools:text="86 rpm"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>

        <TextView
            android:id="@+id/avgCadenceValue"
            tools:text="86 rpm"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        android:id="@+id/maxCadenceSection"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/maxCadenceLabel"
            android:text="@string/max_cadence_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetMaxCadenceValue"
            tools:text="86 rpm"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>

        <TextView
            android:id="@+id/maxCadenceValue"
            tools:text="86 rpm"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        android:id="@+id/stepCountSection"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/stepCountLabel"
            android:text="@string/step_count_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetStepCountValue"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            tools:text="12345"
            style="@style/ActivitySummaryValue"/>

        <TextView
            android:id="@+id/stepCountValue"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            tools:text="15187"
            style="@style/ActivitySummaryValue"/>
    </TableRow>

    <TableRow
        android:id="@+id/stepCadenceSection"
        style="@style/SaveActivitySummaryRow">
        <TextView
            android:id="@+id/stepCadenceLabel"
            android:text="@string/step_rate_capital"
            style="@style/ActivitySummaryHeader"/>

        <TextView
            android:id="@+id/targetStepCadenceValue"
            tools:text="86 /min"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>

        <TextView
            android:id="@+id/stepCadenceValue"
            tools:text="86 /min"
            android:paddingStart="@dimen/activity_summary_value_left_padding"
            style="@style/ActivitySummaryValue"/>
    </TableRow>
</TableLayout>
