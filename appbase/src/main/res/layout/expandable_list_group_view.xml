<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="?suuntoItemBackgroundColor"
    android:theme="@style/WhiteTheme"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/topDivider"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:background="?suuntoDividerColor"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAllCaps="true"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginBottom="@dimen/size_spacing_large"
        android:layout_marginTop="@dimen/size_spacing_large"
        app:layout_constraintBottom_toBottomOf="@+id/bottomDivider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topDivider"
        tools:text="February 2019"
        style="@style/Body.Small.Bold"/>

    <TextView
        android:id="@+id/activitiesSum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:layout_marginStart="@dimen/size_spacing_medium"
        app:layout_constraintBottom_toBottomOf="@+id/bottomDivider"
        app:layout_constraintStart_toEndOf="@id/date"
        app:layout_constraintTop_toTopOf="@id/topDivider"
        tools:text="( 3 activities )"
        style="@style/Body.Small"/>

    <ImageView
        android:id="@+id/openIndicator"
        android:layout_width="@dimen/size_icon_small"
        android:layout_height="@dimen/size_icon_small"
        android:contentDescription="@null"
        android:src="@drawable/ic_dropdown_arrow_fill"
        android:tint="?newAccentColor"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        app:layout_constraintBottom_toTopOf="@+id/bottomDivider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/topDivider"
        tools:src="@drawable/ic_dropdown_arrow_fill"/>

    <View
        android:id="@+id/bottomDivider"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:background="?suuntoDividerColor"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
