<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:clipChildren="false"
    android:background="?suuntoBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/askToApproveNewFollowersLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/selectable_card_background">

            <include
                android:id="@+id/askToApproveNewFollowers"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                layout="@layout/title_summary_toggle" />

            <TextView
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:layout_marginBottom="@dimen/size_spacing_medium"
                android:lineSpacingMultiplier="1.2"
                android:text="@string/settings_follow_approval_ask_summmary"
                style="@style/Body.Medium.DarkGray" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/size_divider"
            android:background="@drawable/selectable_card_background">

            <include
                android:id="@+id/private_account"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                layout="@layout/title_summary_toggle" />

            <TextView
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:layout_marginBottom="@dimen/size_spacing_medium"
                android:lineSpacingMultiplier="1.2"
                android:text="@string/settings_private_account"
                style="@style/Body.Medium.DarkGray" />

        </LinearLayout>

        <include
            android:id="@+id/defaultPrivacyHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            layout="@layout/preference_category_header" />

        <include
            android:id="@+id/sharePublic"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            layout="@layout/title_summary_toggle" />

        <include
            android:id="@+id/shareFollowers"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            layout="@layout/title_summary_toggle" />

        <include
            android:id="@+id/sharePrivate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            layout="@layout/title_summary_toggle" />

        <include
            android:id="@+id/shareLastUsed"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            layout="@layout/title_summary_toggle" />

        <TextView
            android:id="@+id/edit_past_activity_privacy"
            style="@style/Body.Larger"
            android:layout_width="match_parent"
            android:layout_height="@dimen/list_item_single_line_height"
            android:layout_marginTop="1dp"
            android:background="@drawable/selectable_card_background"
            android:duplicateParentState="true"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:paddingStart="@dimen/padding"
            android:paddingEnd="@dimen/size_spacing_xlarge"
            android:text="@string/privacy_edit_past_activity_title"
            android:textColor="@color/settings_item_color"
            app:drawableEndCompat="@drawable/chevron_right"
            app:drawableTint="@color/near_black" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_marginTop="1dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:id="@+id/layoutPassActivityPrivacy"
            android:paddingStart="@dimen/padding"
            android:paddingEnd="@dimen/padding"
            android:layout_width="match_parent"
            android:background="@drawable/selectable_card_background"
            android:layout_height="@dimen/list_item_single_line_height"
            >
            <TextView
                app:layout_constraintStart_toStartOf="parent"
                android:id="@+id/tvPastActivityPrivacyProcessing"
                app:layout_constraintTop_toTopOf="parent"
                style="@style/Body.Larger.Gray"
                android:layout_marginTop="@dimen/padding"
                android:text="@string/past_activity_privacy_processing"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <ProgressBar
                android:layout_marginTop="6dp"
                app:layout_constraintTop_toBottomOf="@id/tvPastActivityPrivacyProcessing"
                android:id="@+id/editPastPrivacyBar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/size_spacing_xsmall"
                android:layout_gravity="center_vertical"
                tools:progress="40"
                style="@style/ZoneDurationProgressBar" />

            <TextView
                android:id="@+id/tvEditPastPrivacyProgress"
                app:layout_constraintEnd_toEndOf="parent"
                style="@style/Body.Larger.Gray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="40.20%"
                app:layout_constraintTop_toTopOf="@id/tvPastActivityPrivacyProcessing" />


        </androidx.constraintlayout.widget.ConstraintLayout>


        <View android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?suuntoBackground"
            android:elevation="@dimen/elevation_separator"/>

    </LinearLayout>

</ScrollView>
