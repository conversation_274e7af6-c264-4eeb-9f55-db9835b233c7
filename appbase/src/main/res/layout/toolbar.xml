<?xml version="1.0" encoding="utf-8"?>

<!-- This layout can be included to activity layouts
    if you only need the basic tool bar functionality. -->
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        style="@style/Toolbar.Native"
        android:theme="@style/Toolbar.Native" />

</com.google.android.material.appbar.AppBarLayout>
