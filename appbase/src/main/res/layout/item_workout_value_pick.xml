<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/workoutValueText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/size_spacing_medium"
        android:lineSpacingExtra="1.5dp"
        android:layout_gravity="start|center_vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/selectedIcon"
        tools:text= "Ascent\n100 m"/>

    <ImageView
        android:id="@+id/selectedIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center|end"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:src="@drawable/ic_checkmark_fill"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:contentDescription="@string/done" />

    <View
        android:layout_width="match_parent"
        style="@style/HorizontalDivider"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
