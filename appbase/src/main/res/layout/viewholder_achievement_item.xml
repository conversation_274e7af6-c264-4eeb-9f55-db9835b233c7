<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="androidx.core.content.ContextCompat" />

        <import type="android.view.View" />

        <variable
            name="item"
            type="com.stt.android.newfeed.AchievementItem" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/WorkoutDetailCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/achievementTextView"
            style="@style/Body.Medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:drawableStart="@{ContextCompat.getDrawable(context, item.icon)}"
            android:drawablePadding="@dimen/size_spacing_xsmall"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_small"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:paddingBottom="@dimen/size_spacing_small"
            android:text="@{item.title}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

