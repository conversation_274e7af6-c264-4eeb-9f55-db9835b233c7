<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/chartViewContainer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:orientation="vertical"
    android:visibility="invisible"
    tools:showIn="@layout/static_workout_map_activity"
    tools:visibility="visible">

    <!-- We need this FrameLayout wrapping the ImageView so when we animate the ImageView the
    background doesn't look weird -->
    <FrameLayout
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="end"
        android:background="@color/barely_transparent">
        <ImageView
            android:id="@+id/chartViewController"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:contentDescription="@null"
            android:padding="4dp"
            app:srcCompat="@drawable/ic_chevron" />
    </FrameLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/barely_transparent"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/currentDuration"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:freezesText="true"
            android:gravity="center"
            android:paddingTop="@dimen/smaller_padding"
            android:textColor="@color/almost_white"
            android:lines="1"
            tools:text="11:22:33"
            style="@style/s21" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/currentDistance"
                android:layout_height="match_parent"
                android:gravity="bottom"
                android:freezesText="true"
                android:textColor="@color/almost_white"
                android:lines="1"
                tools:text="4.56"
                style="@style/s21" />

            <TextView
                android:id="@+id/currentDistanceUnit"
                android:layout_marginStart="@dimen/d2h"
                android:layout_height="match_parent"
                android:gravity="bottom"
                android:freezesText="true"
                android:textColor="@color/almost_white"
                android:lines="1"
                app:autoSizeTextType="uniform"
                app:autoSizeMaxTextSize="@dimen/d10f"
                app:autoSizeMinTextSize="@dimen/d8f"
                tools:text="km"
                style="@style/s15" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/currentSpeedPaceContainer"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/currentSpeedPace"
                android:layout_height="match_parent"
                android:gravity="bottom"
                android:freezesText="true"
                android:textColor="@color/graphlib_speed"
                android:lines="1"
                tools:text="7.8"
                style="@style/s21" />

            <TextView
                android:id="@+id/currentSpeedPaceUnit"
                android:layout_marginStart="@dimen/d2h"
                android:layout_height="match_parent"
                android:gravity="bottom"
                android:freezesText="true"
                android:textColor="@color/graphlib_speed"
                android:lines="1"
                app:autoSizeTextType="uniform"
                app:autoSizeMaxTextSize="@dimen/d10f"
                app:autoSizeMinTextSize="@dimen/d6f"
                tools:text="km/h"
                style="@style/s15" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/currentAltitudeContainer"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/currentAltitude"
                android:layout_height="match_parent"
                android:gravity="bottom"
                android:freezesText="true"
                android:textColor="@color/graphlib_altitude"
                android:lines="1"
                tools:text="90"
                style="@style/s21" />

            <TextView
                android:id="@+id/currentAltitudeUnit"
                android:layout_height="match_parent"
                android:gravity="bottom"
                android:layout_marginStart="@dimen/d2h"
                android:freezesText="true"
                android:textColor="@color/graphlib_altitude"
                android:lines="1"
                app:autoSizeTextType="uniform"
                app:autoSizeMaxTextSize="@dimen/d10f"
                app:autoSizeMinTextSize="@dimen/d8f"
                tools:text="m"
                style="@style/s15" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/currentHeartRateContainer"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/currentHeartRate"
                android:layout_height="match_parent"
                android:gravity="bottom"
                android:freezesText="true"
                android:textColor="@color/graphlib_hr"
                android:lines="1"
                tools:text="123"
                style="@style/s21" />

            <TextView
                android:id="@+id/currentHeartRateUnit"
                android:layout_marginStart="@dimen/d2h"
                android:layout_height="match_parent"
                android:gravity="bottom"
                android:freezesText="true"
                android:text="@string/bpm"
                android:textColor="@color/graphlib_hr"
                android:lines="1"
                app:autoSizeTextType="uniform"
                app:autoSizeMaxTextSize="@dimen/d10f"
                app:autoSizeMinTextSize="@dimen/d8f"
                style="@style/s15" />
        </LinearLayout>
    </LinearLayout>

    <com.stt.android.ui.components.charts.WorkoutDataChart
        android:id="@+id/workoutDataChartView"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@color/barely_transparent" />
</LinearLayout>
