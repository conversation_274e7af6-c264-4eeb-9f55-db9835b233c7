<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/padding">

        <View
            android:id="@+id/featureToggleArea"
            android:layout_width="@dimen/size_spacing_xxxlarge"
            android:layout_height="@dimen/size_spacing_xxxlarge"
            android:layout_marginStart="@dimen/size_spacing_small"
            app:layout_constraintBottom_toBottomOf="@+id/profileImage"
            app:layout_constraintStart_toEndOf="@+id/profileImage"
            app:layout_constraintTop_toTopOf="@+id/profileImage" />


        <ImageView
            android:id="@+id/addProfileImage"
            android:layout_width="@dimen/size_spacing_xxxlarge"
            android:layout_height="@dimen/size_spacing_xxxlarge"
            android:layout_gravity="center"
            android:background="@drawable/white_circle"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:elevation="@dimen/size_spacing_xsmaller"
            android:padding="@dimen/size_spacing_smaller"
            android:src="@drawable/ic_add_photo_outline"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <ImageView
            android:id="@+id/profileImage"
            android:layout_width="@dimen/size_spacing_xxxlarge"
            android:layout_height="@dimen/size_spacing_xxxlarge"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:scaleType="centerCrop"
            android:transitionName="@string/profile_image_transition_name"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="ContentDescription" />

        <ProgressBar
            android:id="@+id/profileImageLoadingSpinner"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/profileImage"
            app:layout_constraintEnd_toEndOf="@+id/profileImage"
            app:layout_constraintStart_toStartOf="@+id/profileImage"
            app:layout_constraintTop_toTopOf="@+id/profileImage"
            tools:visibility="visible"
            style="?android:attr/progressBarStyleSmall" />

        <View
            android:id="@+id/profileNameBackground"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/fullName"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/fullName" />

        <TextView
            android:id="@+id/fullName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:ellipsize="end"
            android:maxLines="2"
            android:gravity="center"
            android:paddingBottom="@dimen/size_spacing_small"
            android:paddingTop="@dimen/size_spacing_small"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/profileImage"
            tools:text="Antti Sorvari"
            style="@style/Body.Larger.Bold" />

        <View
            android:id="@+id/profileDescriptionBackground"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/profileDescription"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/profileDescription" />

        <TextView
            android:id="@+id/profileDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingBottom="@dimen/size_spacing_small"
            android:paddingTop="@dimen/size_spacing_small"
            android:ellipsize="end"
            android:maxLength="256"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fullName"
            app:layout_constraintStart_toStartOf="parent"
            style="@style/Body.Medium"
            tools:text="This is a description"/>

        <androidx.constraintlayout.widget.Group
            android:id="@+id/profileEditGroup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="profileDescriptionBackground,profileNameBackground" />

        <ImageView
            android:id="@+id/followIcon"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:visibility="gone"
            android:layout_marginEnd="@dimen/size_spacing_xsmall"
            app:layout_constraintBottom_toBottomOf="@+id/followText"
            app:layout_constraintEnd_toStartOf="@+id/followText"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintTop_toTopOf="@+id/followText"
            app:srcCompat="@drawable/ic_check_follow"
            tools:ignore="ContentDescription"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/followText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/profileDescription"
            tools:text="FOLLOW"
            style="@style/Profile.Following" />

        <ProgressBar
            android:id="@+id/followActionSpinner"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/smaller_padding"
            android:paddingStart="@dimen/activity_horizontal_margin"
            android:paddingEnd="@dimen/activity_horizontal_margin"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/followIcon"
            app:layout_constraintEnd_toEndOf="@+id/followText"
            app:layout_constraintStart_toStartOf="@+id/followIcon"
            app:layout_constraintTop_toTopOf="@+id/followIcon"
            tools:visibility="visible"
            style="?android:attr/progressBarStyleSmall" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
