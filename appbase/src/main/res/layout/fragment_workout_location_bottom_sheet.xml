<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <import type="com.stt.android.workoutdetail.location.bottomsheet.WorkoutLocationBottomSheetViewModel.SheetState" />

        <variable
            name="viewModel"
            type="com.stt.android.workoutdetail.location.bottomsheet.WorkoutLocationBottomSheetViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root"
        style="@style/RoundedCornerBottomSheetStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/workout_location_bottom_sheet_background"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clickable="true"
            android:elevation="@dimen/elevation_navbar"
            android:focusable="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:elevation="0dp"
            tools:ignore="KeyboardInaccessibleWidget" />

        <ImageView
            android:id="@+id/workout_location_bottom_sheet_drag_handle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_smaller"
            android:contentDescription="@null"
            android:elevation="@dimen/elevation_navbar"
            android:src="@drawable/ic_handle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/workout_location_bottom_sheet_background"
            tools:elevation="0dp" />

        <ImageView
            android:id="@+id/workout_location_bottom_sheet_marker"
            android:layout_width="@dimen/size_icon_medium"
            android:layout_height="@dimen/size_icon_medium"
            android:layout_marginTop="@dimen/size_spacing_smaller"
            android:contentDescription="@null"
            android:elevation="@dimen/elevation_navbar"
            android:src="@drawable/bottombar_map_outline"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workout_location_bottom_sheet_drag_handle"
            tools:elevation="0dp" />

        <!-- Shown when no location is selected -->
        <TextView
            android:id="@+id/workout_location_bottom_sheet_centered_instructions"
            style="@style/Body.Larger"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:elevation="@dimen/elevation_navbar"
            android:gravity="center"
            android:text="@string/tap_to_select_location_instructions"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workout_location_bottom_sheet_marker"
            tools:elevation="0dp" />

        <TextView
            android:id="@+id/workout_location_bottom_sheet_location_name"
            style="@style/Body.Medium.Bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:elevation="@dimen/elevation_navbar"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="@{viewModel.locationName}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workout_location_bottom_sheet_drag_handle"
            tools:elevation="0dp"
            tools:text="Montana del Filo, Santa Cruz de Tenerife" />

        <TextView
            android:id="@+id/workout_location_bottom_sheet_coordinates_text"
            style="@style/Body.Small"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:elevation="@dimen/elevation_navbar"
            android:maxLines="2"
            android:text="@{viewModel.coordinate}"
            android:textIsSelectable="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workout_location_bottom_sheet_location_name"
            tools:text="Coordinates: 28°25'08.1&quot;N 16°24'19.8&quot;W" />

        <!-- Shown when location is selected -->
        <TextView
            android:id="@+id/workout_location_bottom_sheet_start_aligned_instructions"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:elevation="@dimen/elevation_navbar"
            android:gravity="start"
            android:text="@string/tap_to_select_location_instructions"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workout_location_bottom_sheet_coordinates_text"
            tools:elevation="0dp" />

        <FrameLayout
            android:id="@+id/workout_location_bottom_sheet_save_location_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_smaller"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_smaller"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:elevation="@dimen/elevation_navbar"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workout_location_bottom_sheet_start_aligned_instructions">

            <Button
                android:id="@+id/workout_location_bottom_sheet_save_location"
                style="@style/Button"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:enabled="@{viewModel.viewState != SheetState.LOADING}"
                android:text="@string/select_location" />
        </FrameLayout>

        <ProgressBar
            android:id="@+id/workout_location_progress_bar"
            style="@style/Widget.AppCompat.ProgressBar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:elevation="@dimen/elevation_navbar"
            app:layout_constraintBottom_toTopOf="@id/workout_location_bottom_sheet_save_location_layout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/workout_location_bottom_sheet_drag_handle"
            app:visible="@{viewModel.viewState == SheetState.LOADING}" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/workout_location_group_instructions"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="workout_location_bottom_sheet_marker,workout_location_bottom_sheet_centered_instructions"
            app:visible="@{viewModel.viewState == SheetState.INSTRUCTIONS}"
            tools:visibility="gone" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/workout_location_location_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="workout_location_bottom_sheet_location_name,workout_location_bottom_sheet_coordinates_text,workout_location_bottom_sheet_start_aligned_instructions"
            android:visibility="@{viewModel.viewState == SheetState.LOCATION_INFO ? View.VISIBLE : View.INVISIBLE}"
            tools:visibility="invisible" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/workout_location_group_save_location"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="workout_location_bottom_sheet_save_location_layout"
            app:visible="@{viewModel.viewState != SheetState.INSTRUCTIONS}"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
