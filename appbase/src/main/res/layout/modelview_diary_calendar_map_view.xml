<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.stt.android.maps.SuuntoMapView
        android:id="@+id/diary_calendar_map"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:minZoomPreference="0.0"
        app:uiAttribution="false"
        app:uiCompass="false"
        app:uiLogo="false"
        app:uiMapToolbar="false"
        app:uiRotateGestures="false"
        app:uiScrollGestures="false"
        app:uiTiltGestures="false"
        app:uiZoomControls="false"
        app:uiZoomGestures="false" />

    <ImageView
        android:id="@+id/diary_calendar_mapbox_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start|bottom"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginBottom="@dimen/size_spacing_medium"
        android:contentDescription="@null"
        android:src="@drawable/mapbox_helmet" />

    <Button
        android:id="@+id/diary_calendar_touch_area"
        style="@style/ButtonFlat"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?selectableItemBackground"
        android:contentDescription="@string/map" />

    <TextView
        android:id="@+id/diary_calendar_map_credit"
        style="@style/Body.Small.Gray"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="@dimen/size_spacing_small"
        android:visibility="invisible" />

</merge>
