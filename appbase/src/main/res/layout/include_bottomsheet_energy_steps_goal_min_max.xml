<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="@layout/bottomsheet_daily_target_steps">

    <TextView
        android:id="@+id/goalMin"
        style="@style/Body.Larger"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:textColor="@color/dark_gray"
        app:layout_constraintEnd_toStartOf="@+id/goalMax"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/seekBar" />

    <TextView
        android:id="@+id/goalMax"
        style="@style/Body.Larger"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:textColor="@color/dark_gray"
        app:layout_constraintBottom_toBottomOf="@+id/goalMin"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/goalMin"
        tools:text="100000" />

</merge>
