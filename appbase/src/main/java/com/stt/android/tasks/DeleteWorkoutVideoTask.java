package com.stt.android.tasks;

import android.content.Intent;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.stt.android.STTApplication;
import com.stt.android.controllers.SessionController;
import com.stt.android.controllers.VideoModel;
import com.stt.android.domain.user.VideoInformation;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.exceptions.BackendException;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.utils.STTConstants;
import java.lang.ref.WeakReference;
import javax.inject.Inject;
import timber.log.Timber;

public class DeleteWorkoutVideoTask extends SimpleAsyncTask<Void, Void, Boolean> {
    public interface Listener {
        void onWorkoutVideoDeleted(boolean successful);
    }

    @Inject
    SessionController sessionController;

    @Inject
    LocalBroadcastManager localBroadcastManager;

    @Inject
    VideoModel videoModel;

    @Nullable
    private final WeakReference<Listener> listener;
    private final WorkoutHeader workoutHeader;
    private final VideoInformation video;

    public DeleteWorkoutVideoTask(@Nullable Listener listener, WorkoutHeader workoutHeader,
        VideoInformation video) {
        STTApplication.getComponent().inject(this);

        this.listener = listener != null ? new WeakReference<>(listener) : null;
        this.workoutHeader = workoutHeader;
        this.video = video;
    }

    @Override
    protected Boolean doInBackground(Void... params) {
        try {
            videoModel.deleteAndPushVideo(video, true);
            WorkoutHeader updatedWorkoutHeader =
                workoutHeader.toBuilder().locallyChanged(true).build();
            sessionController.storeWorkoutHeader(updatedWorkoutHeader);
            localBroadcastManager.sendBroadcast(
                new Intent(STTConstants.BroadcastActions.WORKOUT_UPDATED).putExtra(
                    STTConstants.ExtraKeys.WORKOUT_ID, updatedWorkoutHeader.getId())
                    .putExtra(STTConstants.ExtraKeys.WORKOUT_HEADER, updatedWorkoutHeader));
            return true;
        } catch (BackendException | InternalDataException e) {
            Timber.e(e, "Failed to delete video");
            return false;
        }
    }

    @Override
    protected void onPostExecute(Boolean result) {
        Listener listener = this.listener != null ? this.listener.get() : null;
        if (listener != null) {
            listener.onWorkoutVideoDeleted(result);
        }
    }
}
