package com.stt.android.tasks;

import android.text.TextUtils;
import com.stt.android.controllers.SessionController;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.domain.workout.WorkoutData;
import com.stt.android.exceptions.BackendException;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.workouts.binary.FsBinaryFileRepository;
import java.io.FileNotFoundException;
import javax.inject.Inject;
import timber.log.Timber;

public class WorkoutDataLoader extends SimpleAsyncTask<WorkoutHeader, Void, WorkoutData> {
    private final SessionController sessionController;
    private final FsBinaryFileRepository binaryFileRepository;

    @Inject
    public WorkoutDataLoader(
        SessionController sessionController,
        FsBinaryFileRepository binaryFileRepository) {
        this.sessionController = sessionController;
        this.binaryFileRepository = binaryFileRepository;
    }

    @Override
    protected WorkoutData doInBackground(WorkoutHeader... params) {
        WorkoutHeader workoutHeader = params[0];
        // The geo points have lost accuracy from the binary workout, so we need to fetch the WorkoutData from the backend first.
        if (binaryFileRepository.cachedFileExists(workoutHeader)) {
            try {
                return binaryFileRepository.readWorkoutDataFromDisk(workoutHeader);
            } catch (InternalDataException | FileNotFoundException e) {
                // fails to read the local copy, do nothing
            }
        }

        String workoutKey = workoutHeader.getKey();
        if (TextUtils.isEmpty(workoutKey)) {
            return null;
        }

        try {
            sessionController.fetchWorkoutDataAndSaveToDisk(workoutKey,
                workoutHeader.getFilename());
        } catch (BackendException | IllegalStateException e) {
            Timber.e(e, "Failed to fetch workout data");
            return null;
        }

        try {
            return binaryFileRepository.readWorkoutDataFromDisk(workoutHeader);
        } catch (InternalDataException | FileNotFoundException e) {
            Timber.e(e, "Failed to read workout data");
            return null;
        }
    }
}
