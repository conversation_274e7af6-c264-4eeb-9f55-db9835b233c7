package com.stt.android.home.dashboardv2.ui.widgets

import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonWeeklyLineChartWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.widgets.ActivitySpeedWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Period
import com.stt.android.home.dashboardv2.widgets.dataloader.todayForWidget
import java.time.DayOfWeek

@Composable
internal fun ActivitySpeedWidget(
    widgetInfo: ActivitySpeedWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    CommonWeeklyLineChartWidget(
        editMode = editMode,
        headerRes = if (widgetInfo.convertPace) {
            R.string.workout_values_headline_avg_pace
        } else {
            R.string.widget_activity_speed_header
        },
        subheaderText = stringResource(R.string.this_week),
        colorRes = widgetInfo.representativeActivityType.colorId,
        iconRes = widgetInfo.representativeActivityType.iconId,
        titleText = widgetInfo.title,
        subtitleText = widgetInfo.subtitle,
        period = widgetInfo.period,
        todayDate = (widgetInfo.period as? Period.CustomPeriod?)?.todayDate ?: todayForWidget,
        progresses = widgetInfo.progresses,
        onClick = onClick,
        onLongClick = onLongClick,
        onRemoveClick = onRemoveClick,
        modifier = modifier,
        iconCircleBg = true,
        subtitleIconRes = widgetInfo.subtitleIconRes,
    )
}

@Preview
@Composable
private fun ActivitySpeedWidgetPreview(
    @PreviewParameter(ActivitySpeedWidgetInfoProvider::class) widgetInfo: ActivitySpeedWidgetInfo
) {
    M3AppTheme {
        ActivitySpeedWidget(
            widgetInfo = widgetInfo,
            editMode = false,
            onClick = null,
            onLongClick = null,
            onRemoveClick = null,
            modifier = Modifier.size(170.dp),
        )
    }
}

private class ActivitySpeedWidgetInfoProvider :
    PreviewParameterProvider<ActivitySpeedWidgetInfo> {
    override val values: Sequence<ActivitySpeedWidgetInfo> = sequenceOf(
        ActivitySpeedWidgetInfo(
            period = Period.ThisWeek(DayOfWeek.MONDAY),
            representativeActivityType = ActivityType.RUNNING,
            convertPace = true,
            progresses = listOf(0.1f, 0.2f, 0.3f, -1f, 0.5f, 1.0f, 0.2f),
            title = generateWidgetTitle("6'30", "/km"),
            subtitle = "1'40 /km",
            subtitleIconRes = R.drawable.widget_up_arrow
        ),
        ActivitySpeedWidgetInfo(
            period = Period.ThisWeek(DayOfWeek.MONDAY),
            representativeActivityType = ActivityType.RUNNING,
            convertPace = true,
            progresses = (0..6).map { -1f },
            title = generateWidgetTitle("0", "/km"),
            subtitle = "No data"
        ),
        ActivitySpeedWidgetInfo(
            period = Period.ThisWeek(DayOfWeek.SUNDAY),
            representativeActivityType = ActivityType.CYCLING,
            convertPace = false,
            progresses = listOf(0.1f, 0.2f, 0.3f, -1f, 0.5f, 1.0f, 0.2f),
            title = generateWidgetTitle("23.2", "km/h"),
            subtitle = "1.2 km/h",
            subtitleIconRes = R.drawable.widget_up_arrow
        ),
        ActivitySpeedWidgetInfo(
            period = Period.ThisWeek(DayOfWeek.SUNDAY),
            representativeActivityType = ActivityType.HIKING,
            convertPace = true,
            progresses = listOf(0.1f, 0.2f, 0.3f, -1f, 0.5f, 1.0f, 0.2f),
            title = generateWidgetTitle("6'30", "/km"),
            subtitle = "1'40 /km",
            subtitleIconRes = R.drawable.widget_up_arrow
        ),
    )
}
