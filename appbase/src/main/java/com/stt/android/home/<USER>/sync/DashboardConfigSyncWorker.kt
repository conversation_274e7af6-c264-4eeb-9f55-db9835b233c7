package com.stt.android.home.dashboardv2.sync

import android.content.Context
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import androidx.work.ListenableWorker
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.utils.asWorkReportData
import com.stt.android.exceptions.remote.ServerError
import timber.log.Timber
import javax.inject.Inject

internal class DashboardConfigSyncWorker(
    context: Context,
    params: WorkerParameters,
    private val syncUseCase: DashboardConfigSyncUseCase
) : CoroutineWorker(context, params) {

    override suspend fun doWork(): Result {
        return runSuspendCatching {
            Timber.d("Starting dashboard config sync")
            
            val strategy = runCatching {
                enumValueOf<SyncStrategy>(inputData.getString(ARG_SYNC_STRATEGY) ?: "")
            }.getOrDefault(SyncStrategy.AUTO_RESOLVE)

            val syncResult = syncUseCase.sync(strategy)
            when (syncResult) {
                is DashboardConfigSyncResult.Success -> {
                    Timber.d("Dashboard config sync completed successfully")
                    Result.success()
                }
                is DashboardConfigSyncResult.ConflictResolved -> {
                    Timber.d("Dashboard config sync completed with conflict resolution")
                    Result.success()
                }
                is DashboardConfigSyncResult.NetworkError -> {
                    Timber.w("Dashboard config sync failed due to network error")
                    Result.retry()
                }
                is DashboardConfigSyncResult.Error -> {
                    Timber.w(syncResult.exception, "Dashboard config sync failed")
                    when (syncResult.exception) {
                        is ServerError.ServiceUnavailable -> Result.retry()
                        else -> Result.failure(syncResult.exception.asWorkReportData())
                    }
                }
            }
        }.getOrElse { e ->
            Timber.w(e, "Unexpected error during dashboard config sync")
            Result.failure(e.asWorkReportData())
        }
    }

    companion object {
        private const val WORK_NAME = "DashboardConfigSyncWorker"
        private const val ARG_SYNC_STRATEGY = "sync_strategy"
        private const val ARG_FORCE_SYNC = "force_sync"

        fun enqueue(
            workManager: WorkManager,
            strategy: SyncStrategy = SyncStrategy.AUTO_RESOLVE,
            forceSync: Boolean = false,
        ) {
            Timber.d("Enqueuing dashboard config sync with strategy: $strategy")

            val inputData = workDataOf(
                ARG_SYNC_STRATEGY to strategy.name,
                ARG_FORCE_SYNC to forceSync
            )

            val workRequest = OneTimeWorkRequestBuilder<DashboardConfigSyncWorker>()
                .setInputData(inputData)
                .setConstraints(
                    Constraints.Builder()
                        .setRequiredNetworkType(NetworkType.CONNECTED)
                        .build()
                )
                .build()

            workManager.enqueueUniqueWork(
                WORK_NAME,
                if (forceSync) ExistingWorkPolicy.REPLACE else ExistingWorkPolicy.KEEP,
                workRequest
            )
        }

        fun cancel(workManager: WorkManager) {
            workManager.cancelUniqueWork(WORK_NAME)
        }
    }
}

internal class DashboardConfigSyncWorkerFactory @Inject constructor(
    private val syncUseCase: DashboardConfigSyncUseCase
) : CoroutineWorkerAssistedFactory {
    override fun create(context: Context, params: WorkerParameters): ListenableWorker {
        return DashboardConfigSyncWorker(context, params, syncUseCase)
    }
}
