package com.stt.android.home.diary.diarycalendar.planner.core.datasources.remote.api

import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class GetTrainingPlannerProgramDetailsResponse(
    val header: TrainingPlannerProgramRemotePlan,
    val bannerUrl: String?,
    val description: String?,
    val richInfo: String?,
    val eventInfo: TrainingPlannerProgramDetailsRemoteEventInfo?,
    val questionnaire: TrainingPlannerProgramDetailsRemoteQuestionnaire?,
)

@JsonClass(generateAdapter = true)
data class TrainingPlannerProgramDetailsRemoteCoachNote(
    val text: String
)

@JsonClass(generateAdapter = true)
data class TrainingPlannerProgramDetailsRemoteEventInfo(
    val name: String,
    val date: String,
    val richInfo: String?,
    val distance: Int?,
    val ascent: Int?,
    val terrain: String?,
    val weather: String?,
)

@JsonClass(generateAdapter = true)
data class TrainingPlannerProgramDetailsRemoteOption(
    val id: String,
    val text: String?,
    val value: String?
)

@JsonClass(generateAdapter = true)
data class TrainingPlannerProgramDetailsRemoteQuestionnaire(
    val questions: List<TrainingPlannerProgramDetailsRemoteQuestion>,
    val updatedAt: String,
    val version: String
)

@JsonClass(generateAdapter = true)
data class TrainingPlannerProgramDetailsRemoteQuestion(
    val coachNote: TrainingPlannerProgramDetailsRemoteCoachNote?,
    val date: String?,
    val distanceInMeters: Int?,
    val durationInSeconds: Int?,
    val id: String,
    val maxSelectedCount: Int?,
    val maxValue: Int?,
    val minValue: Int?,
    val options: List<TrainingPlannerProgramDetailsRemoteOption>?,
    val subTitle: String?,
    val summaryTitle: String?,
    val title: String,
    val type: String,
    val value: Int?,
    val minDate: String?,
    val maxDate: String?,
)
