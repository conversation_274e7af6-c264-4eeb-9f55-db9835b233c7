package com.stt.android.home.diary.diarycalendar.planner.composables

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.R
import com.stt.android.compose.component.SuuntoDropDownMenu
import com.stt.android.compose.component.SuuntoDropDownMenuItem
import com.stt.android.compose.theme.spacing
import com.stt.android.home.diary.diarycalendar.planner.models.PlanUiState
import com.stt.android.home.diary.diarycalendar.planner.models.WeeklyChartType

@Composable
fun WeeklyChartTypeSelector(
    selectedType: WeeklyChartType,
    planUiState: PlanUiState,
    onItemClick: (WeeklyChartType) -> Unit,
    modifier: Modifier = Modifier,
) {
    SuuntoDropDownMenu(
        title = when (selectedType) {
            WeeklyChartType.TSS -> stringResource(R.string.workout_planner_chart_training_load)
            WeeklyChartType.DURATION -> stringResource(R.string.workout_planner_chart_duration)
            WeeklyChartType.RUNNING_KM -> stringResource(R.string.workout_planner_chart_running)
        },
        subtitle = when (selectedType) {
            WeeklyChartType.TSS -> stringResource(R.string.item_title_tss)
            WeeklyChartType.DURATION -> stringResource(com.stt.android.core.R.string.hour)
            WeeklyChartType.RUNNING_KM -> planUiState.weekTargetsUiState.distance?.unit?.let {
                stringResource(it)
            }.orEmpty()
        },
        enabled = true,
        modifier = modifier.padding(
            horizontal = MaterialTheme.spacing.medium,
            vertical = MaterialTheme.spacing.small,
        ),
        barColor = null,
    ) { onDismiss ->
        if (selectedType != WeeklyChartType.TSS) {
            SuuntoDropDownMenuItem(
                title = stringResource(R.string.workout_planner_target_training_load),
                onClick = { onItemClick(WeeklyChartType.TSS) },
                onDismiss = onDismiss,
            )
        }
        if (selectedType != WeeklyChartType.DURATION) {
            SuuntoDropDownMenuItem(
                title = stringResource(R.string.workout_planner_target_duration),
                onClick = { onItemClick(WeeklyChartType.DURATION) },
                onDismiss = onDismiss,
            )
        }
        if (selectedType != WeeklyChartType.RUNNING_KM &&
            planUiState.weekTargetsUiState.distance != null
        ) {
            SuuntoDropDownMenuItem(
                title = stringResource(
                    R.string.workout_planner_target_running,
                    planUiState.weekTargetsUiState.distance.unit?.let { stringResource(it) }.orEmpty()
                ),
                onClick = { onItemClick(WeeklyChartType.RUNNING_KM) },
                onDismiss = onDismiss,
            )
        }
    }
}
