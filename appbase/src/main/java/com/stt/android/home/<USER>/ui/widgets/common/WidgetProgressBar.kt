package com.stt.android.home.dashboardv2.ui.widgets.common

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing

@Composable
fun WidgetProgressBar(
    color: Color,
    progress: Float,
    modifier: Modifier = Modifier
) {
    val gray = MaterialTheme.colorScheme.mediumGrey
    val radius = 2.dp
    val progressHeight = 12.dp
    val textStyle =
        MaterialTheme.typography.bodySmall.merge(color = gray)
    val markWidth = 1.dp
    val markCount = 10
    Column(
        verticalArrangement = Arrangement.Bottom,
        modifier = modifier.fillMaxWidth().height(48.dp),
    ) {
        Box(
            Modifier.padding(horizontal = markWidth)
        ) {
            Box(
                Modifier
                    .fillMaxWidth()
                    .height(progressHeight)
                    .background(
                        MaterialTheme.colorScheme.lightGrey,
                        shape = RoundedCornerShape(radius)
                    )
            )
            Box(
                Modifier
                    .fillMaxWidth(fraction = progress)
                    .height(progressHeight)
                    .background(color, shape = RoundedCornerShape(radius))
            )
        }

        Spacer(modifier = Modifier.height(3.dp))

        Canvas(modifier = Modifier.fillMaxWidth().height(3.dp)) {
            val markWidthFloat = markWidth.toPx()
            val width = this.size.width - 2 * markWidthFloat
            val height = this.size.height
            (0..markCount).map {
                Offset(
                    x = 1f * it * width / markCount + markWidthFloat,
                    y = 0f
                )
            }.forEach { start ->
                drawLine(
                    color = gray,
                    start = start,
                    end = start.copy(y = height),
                    strokeWidth = density
                )
            }
        }

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))

        Row(
            Modifier.padding(horizontal = markWidth)
        ) {
            Text(
                text = "0${stringResource(R.string.percentage_sign)}",
                modifier = Modifier.weight(1f),
                style = textStyle
            )
            Text(
                text = "100${stringResource(R.string.percentage_sign)}",
                style = textStyle
            )
        }
    }
}

@Preview
@Composable
private fun WidgetProgressPreview() {
    WidgetProgressBar(
        color = Color.Red,
        progress = 0.3f,
        modifier = Modifier.width(width = 138.dp)
    )
}
