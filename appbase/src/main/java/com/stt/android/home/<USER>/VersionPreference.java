package com.stt.android.home.settings;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.util.AttributeSet;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.preference.Preference;
import androidx.preference.PreferenceViewHolder;
import com.stt.android.R;
import timber.log.Timber;

public class VersionPreference extends Preference {

    private View redDotView;
    private boolean hasNewVersion;

    public VersionPreference(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(context);
    }

    public VersionPreference(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public VersionPreference(Context context) {
        super(context);
        init(context);
    }

    @Override
    public void onBindViewHolder(@NonNull PreferenceViewHolder holder) {
        super.onBindViewHolder(holder);
        redDotView = holder.findViewById(R.id.readDotView);
        updateView();
    }

    private void init(Context context) {
        PackageManager packageManager = context.getPackageManager();
        String packageName = context.getPackageName();
        PackageInfo pInfo;
        try {
            pInfo = packageManager.getPackageInfo(packageName, 0);
            String versionName = pInfo.versionName;
            int versionCode = pInfo.versionCode;
            setSummary(context.getString(R.string.settings_other_app_update_summary, versionName,
                versionCode));
        } catch (PackageManager.NameNotFoundException e) {
            Timber.e(e, "Unable to retrieve current version information");
        }
    }

    public void updateAppVersionView(boolean hasNewVersion) {
        this.hasNewVersion = hasNewVersion;
        updateView();
    }

    private void updateView() {
        if (redDotView != null) {
            if (hasNewVersion) {
                redDotView.setVisibility(View.VISIBLE);
            } else {
                redDotView.setVisibility(View.GONE);
            }
        }
    }
}
