package com.stt.android.home.settings;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import androidx.annotation.CallSuper;
import androidx.appcompat.app.AlertDialog;
import androidx.preference.DialogPreference;
import androidx.preference.PreferenceViewHolder;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;
import com.stt.android.R;

public abstract class CustomDialogPreference extends DialogPreference
    implements DialogInterface.OnDismissListener, DialogInterface.OnClickListener {
    private AlertDialog.Builder builder;
    private int whichButtonClicked;
    private boolean needInputMethod = false;

    public CustomDialogPreference(Context context) {
        this(context, null);
    }

    public CustomDialogPreference(Context context, AttributeSet attrs) {
        this(context, attrs, android.R.attr.dialogPreferenceStyle);
    }

    public CustomDialogPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public CustomDialogPreference(Context context, AttributeSet attrs, int defStyleAttr,
        int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public void showDialog(Bundle state) {
        Context context = getContext();

        whichButtonClicked = DialogInterface.BUTTON_NEGATIVE;

        builder = new AlertDialog.Builder(context).setTitle(getDialogTitle())
            .setIcon(getDialogIcon())
            .setPositiveButton(getPositiveButtonText(), this)
            .setNegativeButton(getNegativeButtonText(), this);

        View contentView = onCreateDialogView();
        if (contentView != null) {
            onInitDialogView(contentView);
            onBindDialogView(contentView);
            builder.setView(contentView);
        } else {
            builder.setMessage(getDialogMessage());
        }

        onPrepareDialogBuilder(builder);

        //getPreferenceManager().registerOnActivityDestroyListener(this);

        // Create the dialog
        final Dialog dialog = builder.create();
        if (state != null) {
            dialog.onRestoreInstanceState(state);
        }
        if (needInputMethod()) {
            requestInputMethod(dialog);
        }
        dialog.setOnDismissListener(this);
        dialog.show();
    }

    private View onCreateDialogView() {
        if (getDialogLayoutResource() == 0) {
            return null;
        }

        LayoutInflater inflater = LayoutInflater.from(builder.getContext());
        return inflater.inflate(getDialogLayoutResource(), null);
    }

    @Override
    public void onBindViewHolder(PreferenceViewHolder holder) {
        super.onBindViewHolder(holder);

        onInitPreferenceView(holder);
    }

    @CallSuper
    private void onBindDialogView(View view) {
        View dialogMessageView = view.findViewById(android.R.id.message);

        if (dialogMessageView != null) {
            final CharSequence message = getDialogMessage();
            int newVisibility = View.GONE;

            if (!TextUtils.isEmpty(message)) {
                if (dialogMessageView instanceof TextView) {
                    ((TextView) dialogMessageView).setText(message);
                }

                newVisibility = View.VISIBLE;
            }

            if (dialogMessageView.getVisibility() != newVisibility) {
                dialogMessageView.setVisibility(newVisibility);
            }
        }

        onDialogOpened(view);
    }

    @Override
    public void onClick(DialogInterface dialog, int which) {
        whichButtonClicked = which;
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        //getPreferenceManager().unregisterOnActivityDestroyListener(this);
        onDialogClosed(whichButtonClicked == DialogInterface.BUTTON_POSITIVE);
    }

    private boolean needInputMethod() {
        return needInputMethod;
    }

    public void setNeedInputMethod(boolean needInputMethod) {
        this.needInputMethod = needInputMethod;
    }

    /**
     * Sets the required flags on the dialog window to enable input method window to show up.
     */
    private void requestInputMethod(Dialog dialog) {
        Window window = dialog.getWindow();
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE
            | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
    }

    protected abstract void onInitPreferenceView(PreferenceViewHolder viewHolder);

    /**
     * override this to set inputtype for custom dialogview
     */
    protected abstract void onInitDialogView(View dialogView);

    protected abstract void onPrepareDialogBuilder(
        androidx.appcompat.app.AlertDialog.Builder mBuilder);

    protected abstract void onDialogOpened(View rootView);

    protected abstract void onDialogClosed(boolean positiveResult);
}
