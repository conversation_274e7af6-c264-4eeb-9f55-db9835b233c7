package com.stt.android.home.dashboardv2.ui.activities

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboardv2.ui.DashboardScreenViewEvent
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.components.workout.WorkoutShareInfo

@Composable
internal fun Workout(
    workoutCard: WorkoutCardInfo,
    viewEvent: (DashboardScreenViewEvent) -> Unit,
    onReactionClick: (WorkoutHeader) -> Unit,
    onShareClick: (WorkoutHeader, WorkoutShareInfo) -> Unit,
    modifier: Modifier = Modifier,
    onCoverTouchEvent: ((Int) -> Unit)? = null,
    analyticsSource: String? = null,
) {
    WorkoutCard(
        viewData = workoutCard.workoutCardViewData,
        onClick = { viewEvent(DashboardScreenViewEvent.OpenWorkout(workoutCard.workoutHeader, analyticsSource)) },
        modifier = modifier.padding(
            horizontal = MaterialTheme.spacing.medium,
        ),
        onUserClick = { username -> viewEvent(DashboardScreenViewEvent.OpenUser(username)) },
        onAddPhotoClick = { viewEvent(DashboardScreenViewEvent.AddPhoto(workoutCard.workoutHeader)) },
        onPlayClick = {
            viewEvent(
                DashboardScreenViewEvent.PlayWorkout(
                    workoutHeader = workoutCard.workoutHeader,
                    hasPremiumSubscription = workoutCard.workoutCardViewData.isSubscribedToPremium,
                )
            )
        },
        onTagClicked = { tagName, isEditable ->
            viewEvent(
                DashboardScreenViewEvent.OpenTag(
                    tagName = tagName,
                    isEditable = isEditable,
                    hasPremiumSubscription = workoutCard.workoutCardViewData.isSubscribedToPremium,
                )
            )
        },
        onAddCommentClick = { viewEvent(DashboardScreenViewEvent.AddComment(workoutCard.workoutHeader)) },
        onReactionClick = { onReactionClick(workoutCard.workoutHeader) },
        onShareClick = { onShareClick(workoutCard.workoutHeader, it) },
        onEditClick = { viewEvent(DashboardScreenViewEvent.EditWorkout(workoutCard.workoutHeader)) },
        onCoverTouchEvent = onCoverTouchEvent,
    )
}
