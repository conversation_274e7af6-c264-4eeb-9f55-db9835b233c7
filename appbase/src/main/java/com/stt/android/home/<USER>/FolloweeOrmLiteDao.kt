package com.stt.android.home.people

import com.j256.ormlite.dao.Dao
import com.stt.android.common.coroutines.ORMLITE
import com.stt.android.domain.database.DatabaseHelper
import com.stt.android.domain.user.User
import com.stt.android.domain.user.UserDataSource
import com.stt.android.domain.user.followees.FolloweeDao
import com.stt.android.follow.FollowDirection
import com.stt.android.follow.FollowStatus
import com.stt.android.follow.UserFollowStatus
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class FolloweeOrmLiteDao
@Inject constructor(
    private val userDataSource: UserDataSource,
    databaseHelper: DatabaseHelper
) : FolloweeDao {
    // TODO: Use proper data source when follow status has been migrated to Room
    private val followStatusDao: Dao<UserFollowStatus, String> =
        databaseHelper.getDao(UserFollowStatus::class.java)

    override suspend fun isFollowee(username: String): Boolean {
        val followeeStatus: UserFollowStatus? = withContext(ORMLITE) {
            kotlin.runCatching {
                val ufsId = UserFollowStatus.createId(username, FollowDirection.FOLLOWING)
                followStatusDao.queryForId(ufsId)
            }.onFailure { Timber.w(it, "Failed to query UserFollowStatus for username=$username") }
                .getOrNull()
        }

        return followeeStatus != null && followeeStatus.status == FollowStatus.FOLLOWING
    }

    // Assume all stored users except currently logged in user are followees
    override suspend fun loadFollowees(): List<User> =
        userDataSource.getAllOtherUsers()

    // Assume all stored users except currently logged in user are followees
    override suspend fun loadFolloweeUsernames(): List<String> =
        userDataSource.getAllOtherUsernames()

    override suspend fun storeFollowees(fetchedFollowees: List<User>) =
        userDataSource.replaceOtherUsers(fetchedFollowees)
}
