package com.stt.android.home.settings;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.util.AttributeSet;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.preference.Preference;
import com.stt.android.STTApplication;
import com.stt.android.ui.activities.settings.NotificationSettingsActivity;
import com.stt.android.utils.STTConstants;
import javax.inject.Inject;

public class NotificationSettingsPreference extends Preference {

    @Inject
    LocalBroadcastManager localBM;

    private final BroadcastReceiver userStatusChangedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, final Intent intent) {
            update();
        }
    };

    public NotificationSettingsPreference(Context context) {
        this(context, null);
    }

    public NotificationSettingsPreference(Context context, AttributeSet attrs) {
        this(context, attrs, android.R.attr.preferenceStyle);
    }

    public NotificationSettingsPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, defStyleAttr);
    }

    public NotificationSettingsPreference(Context context, AttributeSet attrs, int defStyleAttr,
        int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    private void init() {
        STTApplication.getComponent().inject(this);
    }

    @Override
    public void onAttached() {
        localBM.registerReceiver(userStatusChangedReceiver,
            new IntentFilter(STTConstants.BroadcastActions.USER_STATUS_CHANGED));
        update();
        super.onAttached();
    }

    @Override
    public void onDetached() {
        localBM.unregisterReceiver(userStatusChangedReceiver);
        super.onDetached();
    }

    private void update() {
        setSummary(null);
        setEnabled(true);
    }

    @Override
    protected void onClick() {
        Context context = getContext();
        context.startActivity(NotificationSettingsActivity.newStartIntent(context));
    }
}
