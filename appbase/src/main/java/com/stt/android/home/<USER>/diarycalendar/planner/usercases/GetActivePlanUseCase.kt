package com.stt.android.home.diary.diarycalendar.planner.usercases

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.exceptions.remote.ClientError
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlan
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlanStatus
import com.stt.android.home.diary.diarycalendar.planner.domain.repositories.TrainingPlannerRepository
import timber.log.Timber
import java.io.IOException
import javax.inject.Inject

class GetActivePlanUseCase @Inject constructor(
    private val trainingPlannerRepository: TrainingPlannerRepository,
) {
    suspend operator fun invoke(): GetActivePlanUseCaseResult {
        return runSuspendCatching {
            val trainingPlan = trainingPlannerRepository.getActivePlan()
            if (trainingPlan == null) {
                GetActivePlanUseCaseResult.NoActivePlan
            } else {
                when (trainingPlan.status) {
                    TrainingPlanStatus.ACTIVE -> GetActivePlanUseCaseResult.ActivePlan(trainingPlan = trainingPlan)
                    TrainingPlanStatus.BEING_GENERATED -> GetActivePlanUseCaseResult.PlanIsBeingGenerated(
                        trainingPlan = trainingPlan
                    )

                    TrainingPlanStatus.GENERATION_FAILED -> GetActivePlanUseCaseResult.GenerationFailed(
                        trainingPlan = trainingPlan
                    )

                    else -> GetActivePlanUseCaseResult.UnknownError
                }
            }
        }.getOrElse {
            when {
                it is com.squareup.moshi.JsonEncodingException -> {
                    Timber.w(it, "JSON decoding failed")
                    GetActivePlanUseCaseResult.UnknownError
                }
                it is IOException -> {
                    Timber.w(it, "Network error when fetching the active plan")
                    GetActivePlanUseCaseResult.NetworkError
                }
                it is ClientError && it.code == 404 -> {
                    GetActivePlanUseCaseResult.NoActivePlan
                }
                else -> {
                    Timber.w(it, "Getting active plan failed")
                    GetActivePlanUseCaseResult.UnknownError
                }
            }
        }
    }
}

sealed class GetActivePlanUseCaseResult {
    data object NetworkError : GetActivePlanUseCaseResult()
    data object UnknownError : GetActivePlanUseCaseResult()
    data object NoActivePlan : GetActivePlanUseCaseResult()
    data class GenerationFailed(
        val trainingPlan: TrainingPlan,
    ) : GetActivePlanUseCaseResult()

    data class PlanIsBeingGenerated(
        val trainingPlan: TrainingPlan,
    ) : GetActivePlanUseCaseResult()

    data class ActivePlan(
        val trainingPlan: TrainingPlan,
    ) : GetActivePlanUseCaseResult()
}
