package com.stt.android.home.diary.diarycalendar.year

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy.DisposeOnLifecycleDestroyed
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.fragment.compose.AndroidFragment
import androidx.navigation.fragment.navArgs
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.data.RELEASED_YEAR
import com.stt.android.home.diary.diarycalendar.CalendarContainerViewModel
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE_CALENDAR
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE_MAP
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_SCROLL_TO_ACTIVITIES_LIST
import com.stt.android.home.diary.diarycalendar.DiaryCalendarPagerNavigation
import com.stt.android.home.diary.diarycalendar.DiaryCalendarPagerNavigation.Companion.INVALID_PAGE_INDEX
import com.stt.android.home.diary.diarycalendar.DiaryCalendarPagerNavigationImpl
import com.stt.android.home.diary.diarycalendar.DiaryCalendarPagerNavigationOwner
import com.stt.android.home.diary.diarycalendar.nextYear
import com.stt.android.home.diary.diarycalendar.parseYear
import com.stt.android.home.diary.diarycalendar.sendDiaryCalendarGranularityAnalytics
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.time.Year
import java.time.temporal.ChronoUnit
import javax.inject.Inject

@AndroidEntryPoint
class DiaryCalendarYearPagerFragment : Fragment(), DiaryCalendarPagerNavigationOwner {

    @Inject
    lateinit var datahubAnalyticsTracker: DatahubAnalyticsTracker

    private val args: DiaryCalendarYearPagerFragmentArgs by navArgs()

    override val pagerNavigation: DiaryCalendarPagerNavigation = DiaryCalendarPagerNavigationImpl()

    private val containerViewModel: CalendarContainerViewModel by viewModels(ownerProducer = {
        generateSequence(parentFragment) { it.parentFragment }.lastOrNull() ?: requireActivity()
    })

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(inflater.context).apply {
        setViewCompositionStrategy(DisposeOnLifecycleDestroyed(viewLifecycleOwner))

        val displayMode = arguments?.getInt(
            DIARY_CALENDAR_DISPLAY_MODE,
            DIARY_CALENDAR_DISPLAY_MODE_CALENDAR,
        ) ?: DIARY_CALENDAR_DISPLAY_MODE_CALENDAR
        val (maxYear, maxPages) = if (displayMode != DIARY_CALENDAR_DISPLAY_MODE_MAP) {
            val nextYear = nextYear()
            nextYear to ChronoUnit.YEARS.between(
                Year.of(RELEASED_YEAR),
                nextYear.plusYears(1) // Include the next year
            ).toInt()
        } else {
            val currentYear = Year.now()
            currentYear to ChronoUnit.YEARS.between(
                Year.of(RELEASED_YEAR),
                currentYear.plusYears(1),
            ).toInt()
        }

        fun calcYearIndex(year: Year): Int {
            val yearsBetween = maxYear.value - year.value.coerceAtLeast(0)
            return maxPages - 1 - yearsBetween
        }

        // Set initial year index based on the number of year between args.year
        // and the next year
        val currentYearIndex = calcYearIndex(getInitialYear())

        pagerNavigation.setDefaultPage(calcYearIndex(Year.now()), maxPages)

        setContentWithM3Theme {
            val pagerState = rememberPagerState(initialPage = currentYearIndex) { maxPages }

            val targetPage by pagerNavigation.currentPageIndex.collectAsState()

            HorizontalPager(
                state = pagerState,
                userScrollEnabled = displayMode != DIARY_CALENDAR_DISPLAY_MODE_MAP,
                beyondViewportPageCount = 1,
            ) { page ->
                if (!isRemoving) {
                    val currentYear = maxYear.minusYears(maxPages - page - 1L)
                    AndroidFragment<DiaryCalendarYearFragment>(
                        modifier = Modifier.fillMaxSize(),
                        arguments = bundleOf(
                            DiaryCalendarYearFragment.ARG_YEAR to currentYear.value,
                            DIARY_CALENDAR_SCROLL_TO_ACTIVITIES_LIST to args.showActivitiesList,
                            DIARY_CALENDAR_DISPLAY_MODE to displayMode,
                        ),
                    )
                }
            }

            val coroutineScope = rememberCoroutineScope()
            DisposableEffect(targetPage) {
                if (targetPage != INVALID_PAGE_INDEX && targetPage != pagerState.currentPage) {
                    coroutineScope.launch(Dispatchers.Main.immediate) {
                        if (displayMode == DIARY_CALENDAR_DISPLAY_MODE_MAP) {
                            pagerState.scrollToPage(targetPage)
                        } else {
                            pagerState.animateScrollToPage(targetPage)
                        }
                    }
                }
                onDispose {}
            }

            var firstTime by remember { mutableStateOf(true) }
            DisposableEffect(pagerState.currentPage) {
                val page = pagerState.currentPage
                containerViewModel.selectedYear = maxYear.minusYears(maxPages - page - 1L)
                pagerNavigation.updateCurrentPage(page)
                if (!firstTime) {
                    sendDiaryCalendarGranularityAnalytics(
                        datahubAnalyticsTracker,
                        AnalyticsPropertyValue.DiaryCalendarGranularity.YEARLY
                    )
                } else {
                    firstTime = false
                }
                onDispose {}
            }
        }
    }

    private fun getInitialYear(): Year {
        args.year?.run {
            return parseYear(this)
        }

        // If initial year not given, show current year
        return Year.now()
    }

    companion object {
        const val ARG_YEAR = "year"
    }
}
