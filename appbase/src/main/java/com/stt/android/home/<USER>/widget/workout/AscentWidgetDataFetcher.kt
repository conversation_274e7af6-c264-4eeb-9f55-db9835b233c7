package com.stt.android.home.dashboard.widget.workout

import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.infomodel.shouldNotCountAscentForActivity
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.toEpochMilli
import timber.log.Timber
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import javax.inject.Inject

class AscentWidgetDataFetcher @Inject constructor(
    private val workoutHeaderController: WorkoutHeaderController
) {
    fun fetchAscentWidgetData(username: String, lastDay: LocalDate): AscentWidgetData {
        val endOfLastDay = lastDay.atEndOfDay()
        val endOfLastDayMillis = endOfLastDay.toEpochMilli()

        val twoWeeksBeforeEnd = endOfLastDay.minusWeeks(2)
        val twoWeeksBeforeEndMillis = twoWeeksBeforeEnd.toEpochMilli()

        val workoutsForTwoWeeks = try {
            workoutHeaderController.findByUserAndStartTime(
                username,
                twoWeeksBeforeEndMillis,
                endOfLastDayMillis
            )
        } catch (e: Exception) {
            Timber.e(e, "Error fetching workouts")
            throw e
        }

        val startOfFirstDay = endOfLastDay.minusWeeks(1)
        return calculateAscentWidgetData(workoutsForTwoWeeks, startOfFirstDay, lastDay)
    }

    fun calculateAscentWidgetData(
        workoutsForTwoWeeks: List<WorkoutHeader>,
        startOfFirstDay: LocalDateTime,
        lastDay: LocalDate
    ): AscentWidgetData {
        val ascentCountedWorkoutsForTwoWeeks = workoutsForTwoWeeks.filterNot {
            shouldNotCountAscentForActivity(it.activityTypeId)
        }

        val startOfFirstDayMillis = startOfFirstDay.toEpochMilli()
        val (previousPeriodWorkouts, currentPeriodWorkouts) =
            ascentCountedWorkoutsForTwoWeeks.partition { it.startTime < startOfFirstDayMillis }

        val currentPeriodWorkoutsByDay = currentPeriodWorkouts.groupBy {
            Instant
                .ofEpochMilli(it.startTime)
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
        }

        val dailyAscents = mutableListOf<Double>()
        for (i in 0L..6L) {
            val day = startOfFirstDay.plusDays(i).toLocalDate()
            val workoutsForDay = currentPeriodWorkoutsByDay[day] ?: emptyList()
            dailyAscents.add(workoutsForDay.sumOf { it.totalAscent })
        }

        val previousPeriodTotalAscent = previousPeriodWorkouts.sumOf { it.totalAscent }
        val currentPeriodTotalAscent = currentPeriodWorkouts.sumOf { it.totalAscent }

        val changeSinceLastPeriod = if (previousPeriodTotalAscent > 0) {
            ((currentPeriodTotalAscent - previousPeriodTotalAscent) / previousPeriodTotalAscent) * 100
        } else {
            null
        }

        return AscentWidgetData(dailyAscents, changeSinceLastPeriod, lastDay)
    }
}
