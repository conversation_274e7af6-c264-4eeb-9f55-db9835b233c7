package com.stt.android.home.dashboardv2.edit.ui.components.editor

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyGridScope
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.component.SuuntoCard
import com.stt.android.compose.component.SuuntoSwitch
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.home.dashboardv2.edit.DashboardTabEditViewEvent
import com.stt.android.home.dashboardv2.edit.LatestWorkoutViewData
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.ui.components.workout.WorkoutCard

@Suppress("FunctionName")
internal fun LazyGridScope.LatestWorkoutSection(
    latestWorkout: LatestWorkoutViewData,
    onEvent: (event: DashboardTabEditViewEvent) -> Unit,
) {
    item(
        key = R.string.dashboard_latest_activity,
        span = { GridItemSpan(maxLineSpan) },
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            SectionTitle(
                title = R.string.dashboard_latest_activity,
                modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium),
            )

            Spacer(Modifier.weight(1.0F))

            SuuntoSwitch(
                checked = latestWorkout !is LatestWorkoutViewData.Hidden,
                onCheckedChange = { checked ->
                    onEvent(DashboardTabEditViewEvent.ToggleShowLatestWorkout(toShow = checked))
                },
                // Unfortunately, we can't change the size of Switch as of now, so use this hack
                // to make it look smaller.
                modifier = Modifier.scale(0.75F),
            )
        }
    }

    when (latestWorkout) {
        is LatestWorkoutViewData.Hidden -> Unit
        is LatestWorkoutViewData.Placeholder -> Placeholder()
        is LatestWorkoutViewData.LatestWorkout -> LatestWorkout(latestWorkout.latestWorkout)
    }
}

@Suppress("FunctionName")
private fun LazyGridScope.LatestWorkout(
    latestWorkout: WorkoutCardInfo,
    modifier: Modifier = Modifier,
) {
    item(
        key = latestWorkout.workoutHeader.id,
        span = { GridItemSpan(maxLineSpan) },
    ) {
        WorkoutCard(
            viewData = latestWorkout.workoutCardViewData,
            modifier = modifier
                .animateItem()
                .graphicsLayer { alpha = 0.6F },
        )
    }
}

@Suppress("FunctionName")
private fun LazyGridScope.Placeholder(
    modifier: Modifier = Modifier,
) {
    item(
        key = R.string.dashboard_latest_activity_placeholder_description,
        span = { GridItemSpan(maxLineSpan) },
    ) {
        SuuntoCard(
            modifier = modifier,
        ) {
            Column(
                modifier = Modifier.padding(MaterialTheme.spacing.large),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center,
                ) {
                    Row(
                        modifier = Modifier.widthIn(min = 296.dp),
                        horizontalArrangement = Arrangement.SpaceAround,
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        listOf(
                            CoreActivityType.RUNNING,
                            CoreActivityType.WALKING,
                            CoreActivityType.SWIMMING,
                            CoreActivityType.GYM,
                            CoreActivityType.CYCLING,
                            CoreActivityType.TRAIL_RUNNING,
                        ).forEach { activityType ->
                            SuuntoActivityIcon(
                                coreActivityType = activityType,
                            )
                        }
                    }
                }

                Text(
                    text = stringResource(R.string.dashboard_latest_activity_placeholder_description),
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center,
                )
            }
        }
    }
}

@Preview(widthDp = 320)
@Preview(widthDp = 640)
@Composable
private fun PlaceholderPreview() {
    M3AppTheme {
        LazyVerticalGrid(columns = GridCells.Fixed(1)) {
            Placeholder()
        }
    }
}
