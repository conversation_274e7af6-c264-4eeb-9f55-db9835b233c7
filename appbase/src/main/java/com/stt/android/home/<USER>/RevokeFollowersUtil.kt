package com.stt.android.home.people

import android.content.res.Resources
import android.graphics.Typeface
import android.text.SpannableString
import android.text.Spanned
import android.text.style.StyleSpan
import com.stt.android.R
import com.stt.android.follow.UserFollowStatus

fun formatRevokeFollowerMessage(resources: Resources, followStatus: UserFollowStatus): CharSequence {
    val name = followStatus.realNameOrUsername

    // Format the confirmation dialog message using a placeholder character for the name.
    // This enables the name to be replaced with a style span.
    val placeholder = "\uFFFC"
    var formattedMessage = resources.getString(R.string.revoke_single_follower_confirmation, placeholder)
    val index = formattedMessage.indexOf(placeholder)
    formattedMessage = formattedMessage.replace(placeholder, name)

    return SpannableString(formattedMessage).apply {
        setSpan(StyleSpan(Typeface.BOLD), index, index + name.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    }
}

fun formatRevokeMultipleFollowersMessage(resources: Resources): CharSequence =
    resources.getString(R.string.revoke_multiple_followers_confirmation)
