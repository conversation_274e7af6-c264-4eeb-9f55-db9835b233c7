package com.stt.android.home.people;

import androidx.annotation.Nullable;
import com.stt.android.domain.UserSession;
import com.stt.android.follow.UserFollowStatus;
import java.util.List;
import java.util.concurrent.Callable;
import rx.Completable;
import rx.Observable;
import rx.SingleSubscriber;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import timber.log.Timber;

class SuggestPeoplePresenter extends FollowStatusPresenter<SuggestPeopleView> {
    private final PeopleController peopleController;

    @Nullable
    private Subscription suggestionsSubscription;
    @Nullable
    private Subscription fbTokenValidSubscription;
    @Nullable
    private Subscription linkWithFbSubscription;

    SuggestPeoplePresenter(PeopleController peopleController,
        Observable<UserFollowStatus> followingObservable) {
        super(peopleController, followingObservable);
        this.peopleController = peopleController;
    }

    @Override
    protected void onUserFollowStatusUpdate(UserFollowStatus userFollowStatus) {
        SuggestPeopleView v = getView();
        if (v != null) {
            v.updateStatus(userFollowStatus);
        }
    }

    void findPeopleSuggestions() {
        if (suggestionsSubscription != null) {
            suggestionsSubscription.unsubscribe();
        }
        suggestionsSubscription = peopleController.fetchPeopleSuggestions()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(new SingleSubscriber<List<UserFollowStatus>>() {
                @Override
                public void onSuccess(List<UserFollowStatus> possiblePeople) {
                    SuggestPeopleView v = getView();
                    if (v != null) {
                        if (!possiblePeople.isEmpty()) {
                            v.onSuggestionsFound(possiblePeople);
                        } else {
                            v.showEmptyView();
                        }
                    }
                }

                @Override
                public void onError(Throwable throwable) {
                    switchToEmptyView();
                }
            });
        // Add to the compositeSubscription to delegate unsubscription to presenter
        subscription.add(suggestionsSubscription);
    }

    //facebook related stuff starts here
    boolean isFacebookReady() {
        UserSession userSession = peopleController.currentUserController.getSession();
        return userSession != null && userSession.isConnectedToFacebook();
    }

    void fbLoginSuccess(String token) {
        linkWithFacebook(token);
    }

    void fbLoginCancelled() {
    }

    void fbLoginError(Throwable exception) {
        Timber.w(exception, "Facebook login error");
        SuggestPeopleView v = getView();
        if (v != null) {
            v.onFacebookLoginFailed(exception);
        }
    }

    void checkFacebookTokenValid() {
        if (fbTokenValidSubscription != null) {
            fbTokenValidSubscription.unsubscribe();
        }
        fbTokenValidSubscription = peopleController.validateFacebookToken()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(new Subscriber<Boolean>() {
                @Override
                public void onCompleted() {
                }

                @Override
                public void onError(Throwable e) {
                    SuggestPeopleView v = getView();
                    if (v != null) {
                        // The Facebook token in backend is not valid ask user to login
                        // again so we get new token
                        v.loginWithFacebook();
                    }
                }

                @Override
                public void onNext(Boolean fbTokenValid) {
                    SuggestPeopleView v = getView();
                    if (v != null) {
                        if (fbTokenValid) {
                            v.onFacebookLoggedIn();
                        } else {
                            v.loginWithFacebook();
                        }
                    }
                }
            });
        // Add to the compositeSubscription to delegate unsubscription to presenter
        subscription.add(fbTokenValidSubscription);
    }

    private void linkWithFacebook(String accessToken) {
        if (linkWithFbSubscription != null) {
            linkWithFbSubscription.unsubscribe();
        }
        linkWithFbSubscription = Completable.fromCallable((Callable<Void>) () -> {
            peopleController.currentUserController.linkWithFacebook(accessToken);
            return null;
        })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(() -> {
                        SuggestPeopleView v = getView();
                        if (v != null) {
                            v.onFacebookLoggedIn();
                        }
                    }, e -> {
                        SuggestPeopleView v = getView();
                        if (v != null) {
                            v.onFacebookLoginFailed(e);
                        }
                    }
                );
        // Add to the compositeSubscription to delegate unsubscription to presenter
        subscription.add(linkWithFbSubscription);
    }
}
