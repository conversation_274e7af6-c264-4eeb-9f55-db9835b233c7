package com.stt.android.home.dashboard.widget.workout

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.annotation.Px
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updateMargins
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.stt.android.R
import com.stt.android.databinding.DashboardWidgetCommuteBinding
import com.stt.android.home.dashboard.widget.DashboardWidget
import com.stt.android.home.dashboard.widget.DashboardWidgetDelegate
import com.stt.android.home.dashboard.widget.DashboardWidgetView
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import kotlin.math.roundToInt

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_WRAP_HEIGHT)
class CommuteWidget @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr),
    DashboardWidgetView,
    DashboardWidget by DashboardWidgetDelegate() {
    private val binding =
        DashboardWidgetCommuteBinding.inflate(LayoutInflater.from(context), this, true)

    override val clickContainer: View
        get() = binding.root

    override val removeButton: View
        get() = binding.commuteWidgetRemoveButton

    private val underTenFormatter = DecimalFormat("0.00", DecimalFormatSymbols.getInstance(Locale.US))
    private val underHundredFormatter = DecimalFormat("#0.0", DecimalFormatSymbols.getInstance(Locale.US))

    @set:[ModelProp]
    var data: CommuteWidgetData? = null

    @AfterPropsSet
    fun bindProps() {
        data?.let {
            binding.commuteWidgetSavedCo2e.text = formatSavedEmissions(it.savedCo2eEmissions)
        } ?: run {
            binding.commuteWidgetSavedCo2e.text = "-"
        }

        bindDashboardWidgetView(this)
    }

    private fun formatSavedEmissions(savedCo2eEmissions: Double): String = when {
        savedCo2eEmissions < 10 -> underTenFormatter.format(savedCo2eEmissions)
        savedCo2eEmissions < 100 -> underHundredFormatter.format(savedCo2eEmissions)
        else -> savedCo2eEmissions.roundToInt().toString()
    }

    /**
     * Hides the title and description according to parameters, use for saving vertical space.
     * The primary saved CO2e text and the cloud image move depending on the configuration:
     *   - with only title removed, they switch their bottom constraint to top of the description text
     *   - with both texts removed, they center in the parent without vertical offset
     */
    fun setTextsHidden(
        hideTitle: Boolean,
        hideDescription: Boolean
    ) {
        binding.commuteWidgetTitle.isVisible = !hideTitle
        binding.commuteWidgetDescription.isVisible = !hideDescription

        val smallerSpacing = resources.getDimensionPixelSize(R.dimen.size_spacing_smaller)
        val largeSpacing = resources.getDimensionPixelSize(R.dimen.size_spacing_large)

        binding.commuteWidgetCloudImage.updateMargins(
            bottom = if (hideTitle) 0 else largeSpacing
        )

        binding.commuteWidgetSavedCo2e.updateMargins(
            top = if (hideTitle) {
                if (hideDescription) {
                    0
                } else {
                    largeSpacing
                }
            } else {
                smallerSpacing
            }
        )

        ConstraintSet().apply {
            clone(binding.commuteWidgetContainer)
            if (hideTitle && !hideDescription) {
                connect(
                    binding.commuteWidgetCloudImage.id,
                    ConstraintSet.BOTTOM,
                    binding.commuteWidgetDescription.id,
                    ConstraintSet.TOP
                )
                connect(
                    binding.commuteWidgetSavedCo2e.id,
                    ConstraintSet.BOTTOM,
                    binding.commuteWidgetDescription.id,
                    ConstraintSet.TOP
                )
            } else {
                connect(
                    binding.commuteWidgetCloudImage.id,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )
                connect(
                    binding.commuteWidgetSavedCo2e.id,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )
            }

            applyTo(binding.commuteWidgetContainer)
        }
    }

    fun unlockSize() {
        binding.commuteWidgetContainer.updateLayoutParams {
            width = LayoutParams.MATCH_PARENT
            height = LayoutParams.MATCH_PARENT
        }

        ConstraintSet().apply {
            clone(binding.commuteWidgetContainer)
            constrainMaxWidth(binding.commuteWidgetDescription.id, Int.MAX_VALUE)
            applyTo(binding.commuteWidgetContainer)
        }
    }
}

data class CommuteWidgetData(
    val savedCo2eEmissions: Double
)

private fun View.updateMargins(
    @Px left: Int? = null,
    @Px top: Int? = null,
    @Px right: Int? = null,
    @Px bottom: Int? = null
) = updateLayoutParams<ConstraintLayout.LayoutParams> {
    updateMargins(
        left = left ?: leftMargin,
        top = top ?: topMargin,
        right = right ?: rightMargin,
        bottom = bottom ?: bottomMargin
    )
}
