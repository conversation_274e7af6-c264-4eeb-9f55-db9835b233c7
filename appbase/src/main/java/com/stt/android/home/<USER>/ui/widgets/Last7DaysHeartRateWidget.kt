package com.stt.android.home.dashboardv2.ui.widgets

import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonWeeklyLineChartWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.widgets.Last7DaysHeartRateWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Period
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.home.dashboardv2.widgets.dataloader.todayForWidget

@Composable
internal fun Last7DaysHeartRateWidget(
    widgetInfo: Last7DaysHeartRateWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    val header = when (widgetInfo.widgetType) {
        WidgetType.MINIMUM_HEART_RATE -> R.string.widget_min_heart_rate
        WidgetType.MINIMUM_SLEEP_HEART_RATE -> R.string.widget_minimum_sleep_heart_rate_title
        WidgetType.RESTING_HEART_RATE -> R.string.widget_resting_heart_rate_title
        else -> throw IllegalArgumentException("${widgetInfo.widgetType} is not supported widget type for generating heart rate info")
    }
    CommonWeeklyLineChartWidget(
        editMode = editMode,
        headerRes = header,
        subheaderText = stringResource(R.string.today),
        colorRes = R.color.dashboard_widget_minimum_heart_rate,
        iconRes = R.drawable.ic_dashboard_widget_heart_rate,
        titleText = widgetInfo.title,
        subtitleText = widgetInfo.subtitle,
        period = widgetInfo.period,
        progresses = widgetInfo.progresses,
        todayDate = (widgetInfo.period as? Period.CustomPeriod?)?.todayDate ?: todayForWidget,
        onClick = onClick,
        onLongClick = onLongClick,
        onRemoveClick = onRemoveClick,
        modifier = modifier,
    )
}

@Preview
@Composable
private fun Last7DaysHeartRateWidgetPreview(
    @PreviewParameter(Last7DaysHeartRateWidgetInfoProvider::class) widgetInfo: Last7DaysHeartRateWidgetInfo
) {
    M3AppTheme {
        Last7DaysHeartRateWidget(
            widgetInfo = widgetInfo,
            editMode = false,
            onClick = {},
            onLongClick = {},
            onRemoveClick = {},
            modifier = Modifier.size(170.dp),
        )
    }
}

private class Last7DaysHeartRateWidgetInfoProvider :
    PreviewParameterProvider<Last7DaysHeartRateWidgetInfo> {
    override val values: Sequence<Last7DaysHeartRateWidgetInfo> = sequenceOf(
        Last7DaysHeartRateWidgetInfo(
            widgetType = WidgetType.MINIMUM_HEART_RATE,
            period = Period.Last7Days,
            progresses = listOf(0.1f, 0.2f, 0.3f, 0.0f, -1f, 1.0f, 0.2f),
            title = generateWidgetTitle("56", "bpm"),
            subtitle = "6-day avg 54 bpm"
        ),
        Last7DaysHeartRateWidgetInfo(
            widgetType = WidgetType.MINIMUM_SLEEP_HEART_RATE,
            period = Period.Last7Days,
            progresses = (0..6).map { -1f },
            title = AnnotatedString("--"),
            subtitle = "No data"
        )
    )
}
