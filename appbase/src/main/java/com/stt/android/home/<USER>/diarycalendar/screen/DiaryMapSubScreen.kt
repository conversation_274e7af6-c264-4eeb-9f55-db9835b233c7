package com.stt.android.home.diary.diarycalendar.screen

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onLayoutRectChanged
import androidx.compose.ui.platform.LocalWindowInfo
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.home.diary.diarycalendar.components.DiaryCalendarMap
import com.stt.android.home.diary.diarycalendar.components.DiaryMapActivityFilterTags
import com.stt.android.home.diary.diarycalendar.components.DiaryMapActivitySummary
import com.stt.android.home.diary.diarycalendar.components.DiaryMapNavigation
import com.stt.android.home.mytracks.MyTracksUtils
import com.stt.android.models.MapSelectionModel

@Composable
internal fun DiaryMapSubScreen(
    data: DiaryCalendarListContainer,
    mapSelectionModel: MapSelectionModel,
    datahubAnalyticsTracker: DatahubAnalyticsTracker,
    myTracksUtils: MyTracksUtils,
    previousPageEnabled: Boolean,
    nextPageEnabled: Boolean,
    onPreviousPage: () -> Unit,
    onNextPage: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val windowInfo = LocalWindowInfo.current

    // avoid multiple mapviews issue
    var visible by remember { mutableStateOf(false) }

    var selectedActivityType by remember { mutableStateOf<ActivityType?>(null) }

    Column(
        modifier = modifier
            .onLayoutRectChanged(debounceMillis = 0L) {
                val startX = it.positionInWindow.x
                val endX = it.positionInWindow.x + it.width
                visible = (0..windowInfo.containerSize.width).intersect(startX..endX).isNotEmpty()
            },
    ) {
        DiaryMapNavigation(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.small),
            timeRange = data.timeRange,
            previousPageEnabled = previousPageEnabled,
            nextPageEnabled = nextPageEnabled,
            onPreviousPage = onPreviousPage,
            onNextPage = onNextPage,
        )
        val locations = selectedActivityType?.id?.let {
            data.activityLocations[it] ?: emptyList()
        } ?: data.locations
        DiaryMapActivitySummary(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.medium),
            activityCount = locations.size,
        )
        AnimatedVisibility(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            visible = visible,
            enter = fadeIn(),
            exit = fadeOut(),
        ) {
            Box(modifier = Modifier.fillMaxSize()) {
                val routes = selectedActivityType?.id?.let {
                    data.activityRoutes[it] ?: emptyList()
                } ?: data.routes
                val bounds = selectedActivityType?.id?.let {
                    data.activityBounds[it] ?: data.bounds
                } ?: data.bounds
                DiaryCalendarMap(
                    modifier = Modifier.fillMaxSize(),
                    roundedCorners = false,
                    gestureEnabled = true,
                    locations = locations,
                    routes = routes,
                    bounds = bounds,
                    granularity = data.granularity,
                    mapSelectionModel = mapSelectionModel,
                    bubbleData = data.bubbleData,
                    myTracksUtils = myTracksUtils,
                    tracker = datahubAnalyticsTracker,
                )
                DiaryMapActivityFilterTags(
                    modifier = Modifier.fillMaxWidth(),
                    sortedActivities = data.mapActivities,
                    selectedActivityType = selectedActivityType,
                    onActivityTypeSelected = {
                        selectedActivityType = it
                    },
                )
            }
        }
    }
}
