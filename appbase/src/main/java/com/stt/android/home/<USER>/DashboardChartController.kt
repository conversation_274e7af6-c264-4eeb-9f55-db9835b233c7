package com.stt.android.home.dashboard

import android.content.Context
import androidx.annotation.DimenRes
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import com.airbnb.epoxy.EpoxyAsyncUtil
import com.airbnb.epoxy.TypedEpoxyController
import com.stt.android.R
import com.stt.android.di.FragmentContext
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.domain.workouts.ActivityGroupMapper
import com.stt.android.home.diary.diarycalendar.TotalValueItem
import com.stt.android.home.diary.diarycalendar.TotalValues
import com.stt.android.home.diary.diarycalendar.activitygroups.colorRes
import com.stt.android.mapping.InfoModelFormatter
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.core.R as CR

class DashboardChartController
@Inject constructor(
    private val infoModelFormatter: InfoModelFormatter,
    private val activityGroupMapper: ActivityGroupMapper,
    @FragmentContext private val fragmentContext: Context
) : TypedEpoxyController<DashboardChartContainer>(
    EpoxyAsyncUtil.getAsyncBackgroundHandler(),
    EpoxyAsyncUtil.getAsyncBackgroundHandler()
) {
    @DimenRes
    var itemHeight = R.dimen.dashboard_chart_item_height_no_header

    override fun buildModels(data: DashboardChartContainer?) {
        if (data?.activityStatsWithTotals == null || data.activityStatsWithTotals.isEmpty()) {
            buildActivityGroupPlaceholders()
        } else {
            val activityStatsWithTotals = data.activityStatsWithTotals
            val activityGroupRows = if (activityStatsWithTotals.size > 4) 3 else activityStatsWithTotals.size
            buildActivityGroups(activityStatsWithTotals, activityGroupRows, data.maxDuration)
            if (activityGroupRows == 3) {
                buildCombinedActivityGroup(activityStatsWithTotals, data.maxDuration)
            }
        }
    }

    private fun buildActivityGroupPlaceholders() {
        val maxDuration = 30000.0
        val durations = listOf(30000, 10000, 7000, 3000)
        val activityIcons = listOf(
            CR.drawable.ic_activity_cycling,
            CR.drawable.ic_activity_running,
            CR.drawable.ic_activity_swimming,
            CR.drawable.ic_activity_walking
        )
        for (i in 0..3) {
            val duration = durations[i]
            val totalValueItem = TotalValueItem("")
            val totalValueItems = listOf(totalValueItem)
            val totalValues = TotalValues(duration.toDouble(), 0.0, totalValueItems, listOf())
            val color = ContextCompat.getColor(
                fragmentContext,
                R.color.suunto_light_gray
            )
            val icon = AppCompatResources.getDrawable(fragmentContext, activityIcons[i])
            val progressValue = (100L * totalValues.duration / maxDuration).toInt()
            dashboardChartItem {
                id("activityGroupRow-$i")
                activityGroupColor(color)
                activityIcon(icon)
                totalValues(totalValues)
                progressValue(progressValue)
                heightDimenRes(itemHeight)
                openActivityList { _, _, _, _ ->
                    // Not implemented
                }
            }
        }
    }

    private fun buildActivityGroups(
        activityStatsWithTotals: List<Pair<ActivityType?, TotalValues>>,
        amountOfRows: Int,
        maxDuration: Double
    ) {
        activityStatsWithTotals.forEachIndexed { index, (activityType, totals) ->
            if (index < amountOfRows) {
                buildActivitySummary(
                    fragmentContext,
                    activityType,
                    totals,
                    maxDuration
                )
            } else {
                return@forEachIndexed
            }
        }
    }

    private fun buildCombinedActivityGroup(
        activityStatsWithTotals: List<Pair<ActivityType?, TotalValues>>,
        maxDuration: Double
    ) {
        val lastCombinedDuration = activityStatsWithTotals.subList(
            3,
            activityStatsWithTotals.size
        ).sumOf { pair: Pair<ActivityType?, TotalValues> -> pair.second.duration }

        val lastCombinedDistance = activityStatsWithTotals.subList(
            3,
            activityStatsWithTotals.size
        ).sumOf { pair: Pair<ActivityType?, TotalValues> -> pair.second.distance }

        if (lastCombinedDuration > 0) {
            val totalDurationlValueItem = getDurationTotal(lastCombinedDuration)
            val totalDistanceValueItem = getDistanceTotal(lastCombinedDistance)
            val totalValueItems = listOf(totalDurationlValueItem, totalDistanceValueItem)
            val combinedTotals = TotalValues(lastCombinedDuration, lastCombinedDistance, totalValueItems, listOf())
            buildActivitySummary(
                fragmentContext,
                null,
                combinedTotals,
                maxDuration
            )
        }
    }

    private fun buildActivitySummary(
        context: Context,
        activityType: ActivityType?,
        totalValues: TotalValues,
        maxDuration: Double
    ) {
        val icon = if (activityType == null) {
            AppCompatResources.getDrawable(context, R.drawable.ic_activity_unspecified)
        } else {
            AppCompatResources.getDrawable(context, activityType.iconId)
        }
        val progressValue = (100L * totalValues.duration / maxDuration).toInt()
        val color = if (activityType == null) {
            ContextCompat.getColor(
                context,
                ActivityGroup.Unspecified.colorRes
            )
        } else {
            ContextCompat.getColor(
                context,
                activityGroupMapper.activityTypeIdToGroup(activityType.id).colorRes
            )
        }
        val showDistanceGroup = totalValues.distance > 0.0
        val showQuantityLabel = activityType?.supportsDiveProfile == true

        dashboardChartItem {
            id("activityGroupRow-${activityType?.id ?: 0}")
            activityGroupColor(color)
            activityIcon(icon)
            totalValues(totalValues)
            showDistanceGroup(showDistanceGroup)
            showQuantityLabel(showQuantityLabel)
            progressValue(progressValue)
            heightDimenRes(itemHeight)
            openActivityList { _, _, _, _ ->
                // Not implemented
            }
        }
    }

    private fun getDurationTotal(seconds: Double): TotalValueItem {
        val value = try {
            infoModelFormatter.formatAccumulatedTotalDuration(seconds)
        } catch (e: Exception) {
            Timber.w(e, "Failed to format duration $seconds")
            "-"
        }

        return TotalValueItem(
            value = value,
            unitRes = CR.string.hour,
            labelRes = R.string.duration
        )
    }

    private fun getDistanceTotal(distance: Double): TotalValueItem {
        val value = try {
            infoModelFormatter.formatAccumulatedTotalDistance(distance, false)
        } catch (e: Exception) {
            Timber.w(e, "Failed to format distance $distance")
            "-"
        }

        return TotalValueItem(
            value = value,
            unitRes = infoModelFormatter.unit.distanceUnit,
            labelRes = R.string.distance
        )
    }
}
