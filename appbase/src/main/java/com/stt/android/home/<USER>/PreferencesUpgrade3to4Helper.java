package com.stt.android.home.settings;

import android.content.SharedPreferences;

import com.stt.android.domain.user.VoiceFeedbackSettings;
import com.stt.android.domain.user.VoiceFeedbackSettingsHelper;
import com.stt.android.utils.STTConstants;

public class PreferencesUpgrade3to4<PERSON><PERSON><PERSON> extends PreferencesUpgradeHelper {
    public PreferencesUpgrade3to4Helper(SharedPreferences preferences) {
        super(preferences);
    }

    @Override
    public void upgrade() {
        boolean voiceFeedbackEnabled = Boolean.parseBoolean(preferences.getString("voice_feedback", "false"));
        if (voiceFeedbackEnabled) {
            VoiceFeedbackSettings settings = VoiceFeedbackSettingsHelper
                    .getVoiceFeedbackSettings(preferences, VoiceFeedbackSettingsHelper.GENERIC_ACTIVITY_ID)
                    .updateEnabled(true);
            VoiceFeedbackSettingsHelper.saveVoiceFeedbackSettings(preferences, settings);
        }
        preferences.edit()
                .remove("voice_feedback")
                .putInt(STTConstants.DefaultPreferences.KEY_PREFERENCES_VERSION, 4)
                .apply();
    }
}
