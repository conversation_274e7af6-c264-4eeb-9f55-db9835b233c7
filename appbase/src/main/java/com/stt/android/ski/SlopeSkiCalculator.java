package com.stt.android.ski;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

public class SlopeSkiCalculator {
    private static class Observation {
        final double secondsInWorkout;
        final double totalDistance;
        final double altitude;
        final double speedMetersPerSecond;

        Observation(int millisecondsInWorkout, double totalDistance, double altitude, double
            speedMetersPerSecond) {
            this.secondsInWorkout = millisecondsInWorkout / 1000.0;
            this.totalDistance = totalDistance;
            this.altitude = altitude;
            this.speedMetersPerSecond = speedMetersPerSecond;
        }
    }

    @IntDef({ STATE_UNKNOWN, STATE_OTHER, STATE_POSSIBLE_LIFT, STATE_LIFT, STATE_POSSIBLE_RUN, STATE_RUN })
    @Retention(RetentionPolicy.SOURCE)
    public @interface State {
    }

    public static final int STATE_UNKNOWN = 0;
    public static final int STATE_OTHER = 1;
    public static final int STATE_POSSIBLE_LIFT = 2;
    public static final int STATE_LIFT = 3;
    public static final int STATE_POSSIBLE_RUN = 4;
    public static final int STATE_RUN = 5;

    private static final double COMBINE_SPLITS_TIME_ABSOLUTE_THRESHOLD = 60.0;
    private static final double COMBINE_SPLITS_TIME_THRESHOLD = 600.0;
    private static final double COMBINE_SPLITS_ALTITUDE_THRESHOLD = 25.0;

    private static final double UPHILL_VERTICAL_SPEED_THRESHOLD = 0.1;
    private static final double DOWNHILL_VERTICAL_SPEED_THRESHOLD = -0.3;

    private static final int UPHILL_OBSERVATION_THRESHOLD = 3;
    private static final int DOWNHILL_OBSERVATION_THRESHOLD = 3;
    private static final int OTHER_OBSERVATION_THRESHOLD = 3;

    private static final int LIFT_OBSERVATION_THRESHOLD = 5;
    private static final int RUN_OBSERVATION_THRESHOLD = 5;

    private static final double RUN_ALTITUDE_DIFF_THRESHOLD = 25.0;

    private static final int INITIAL_OBSERVATIONS_CAPACITY = 1024;

    private final ArrayList<Observation> observations =
        new ArrayList<>(INITIAL_OBSERVATIONS_CAPACITY);

    private final ArrayList<SlopeSki.Run> runs = new ArrayList<>();

    @State
    private int state = STATE_UNKNOWN;

    private int currentBottomPosition = 0;
    private int currentPeakPosition = 0;

    private int consecutiveUphillObservation = 0;
    private int consecutiveDownhillObservation = 0;
    private int consecutiveOtherObservation = 0;

    public void addObservation(int millisecondsInWorkout, double totalDistance, double altitude,
        double speedMetersPerSecond) {
        synchronized (observations) {
            Observation currentObservation =
                new Observation(millisecondsInWorkout, totalDistance, altitude, speedMetersPerSecond);
            observations.add(currentObservation);
            int size = observations.size();
            if (size == 1) {
                // only one observation, do nothing
                return;
            }

            int previousIndex = size - 2;
            Observation previousObservation = observations.get(previousIndex);
            double altitudeSpeed = (currentObservation.altitude - previousObservation.altitude)
                / (currentObservation.secondsInWorkout - previousObservation.secondsInWorkout);

            if (altitudeSpeed >= UPHILL_VERTICAL_SPEED_THRESHOLD) {
                if (++consecutiveUphillObservation >= UPHILL_OBSERVATION_THRESHOLD) {
                    // we have several consecutive uphill observations, reset other counters
                    // to 0
                    consecutiveDownhillObservation = 0;
                    consecutiveOtherObservation = 0;
                }
            } else if (altitudeSpeed <= DOWNHILL_VERTICAL_SPEED_THRESHOLD) {
                if (++consecutiveDownhillObservation >= DOWNHILL_OBSERVATION_THRESHOLD) {
                    // we have several consecutive downhill observations, reset other counters
                    // to 0
                    consecutiveUphillObservation = 0;
                    consecutiveOtherObservation = 0;
                }
            } else if (++consecutiveOtherObservation >= OTHER_OBSERVATION_THRESHOLD) {
                // we have several consecutive observations that are neither uphill nor
                // downhill, reset other counters to 0
                consecutiveUphillObservation = 0;
                consecutiveDownhillObservation = 0;
            }

            // TODO the checking should probably be more relaxed and more observations should
            // also probably be considered, in case we have bad data
            if (consecutiveUphillObservation >= LIFT_OBSERVATION_THRESHOLD) {
                if (state != STATE_POSSIBLE_LIFT && state != STATE_LIFT) {
                    double currentAltitude = currentObservation.altitude;
                    for (int j = previousIndex; j >= 0; --j) {
                        // goes back and find the latest bottom as start of current lift
                        double alt = observations.get(j).altitude;
                        if (currentAltitude < alt) {
                            currentBottomPosition = j + 1;
                            break;
                        }
                        currentAltitude = alt;
                    }

                    state = STATE_POSSIBLE_LIFT;
                } else if (state == STATE_POSSIBLE_LIFT) {
                    if (currentObservation.altitude - observations.get(
                        currentBottomPosition).altitude >= RUN_ALTITUDE_DIFF_THRESHOLD) {
                        state = STATE_LIFT;
                    }
                }
            } else if (consecutiveDownhillObservation >= RUN_OBSERVATION_THRESHOLD) {
                if (state != STATE_POSSIBLE_RUN && state != STATE_RUN) {
                    double currentAltitude = currentObservation.altitude;
                    for (int j = previousIndex; j >= 0; --j) {
                        // goes back and find the latest peak as start of current run
                        double alt = observations.get(j).altitude;
                        if (alt < currentAltitude) {
                            currentPeakPosition = j + 1;
                            break;
                        }
                        currentAltitude = alt;
                    }

                    state = STATE_POSSIBLE_RUN;
                } else if (state == STATE_POSSIBLE_RUN) {
                    if (observations.get(currentPeakPosition).altitude - currentObservation.altitude
                        >= RUN_ALTITUDE_DIFF_THRESHOLD) {
                        state = STATE_RUN;
                    }
                }
            } else {
                if (state != STATE_OTHER) {
                    if (state == STATE_RUN) {
                        currentPeakPosition =
                            createOrUpdateSplits(runs, observations, currentPeakPosition);
                    }

                    state = STATE_OTHER;
                }
            }
        }
    }

    /**
     * Creates a new split and appends to the given list, or updates it based on the observations.
     *
     * @return start position of current run
     */
    private static int createOrUpdateSplits(ArrayList<SlopeSki.Run> runs,
        ArrayList<Observation> observations, int start) {
        int end = findEndOfRun(observations, start);
        Observation endOfRun = observations.get(end);
        Observation startOfRun = observations.get(start);

        int splitCount = runs.size();
        if (splitCount > 0) {
            // let's look back and see if "current run" is actually a continuation of the
            // previous one:
            // 1) if "current one" was started shortly after previous one ended, we consider it
            // as a continuation
            // 2) if "current one" was started not so long after previous one ended, and the
            // altitude change is not that significant, we also consider it as a continuation
            SlopeSki.Run lastRun = runs.get(splitCount - 1);
            double timeDiff = startOfRun.secondsInWorkout - lastRun.getEndTimeInSeconds();
            if ((timeDiff < COMBINE_SPLITS_TIME_ABSOLUTE_THRESHOLD) || (timeDiff
                < COMBINE_SPLITS_TIME_THRESHOLD
                && startOfRun.altitude - lastRun.getAltitudes().get(lastRun.getAltitudes().size() - 1)
                < COMBINE_SPLITS_ALTITUDE_THRESHOLD)) {
                // goes back the observations to find start position of previous split
                double lastSplitStartTime = lastRun.getStartTimeInSeconds();
                for (int i = start - 1; i >= 0; --i) {
                    Observation observation = observations.get(i);
                    if (Double.compare(observation.secondsInWorkout, lastSplitStartTime) == 0) {
                        start = i;
                        startOfRun = observation;
                        break;
                    }
                }
                // removes previous split, because the current one is an "update" to that split
                runs.remove(splitCount - 1);
            }
        }

        double descents = 0.0;
        double distance = endOfRun.totalDistance - startOfRun.totalDistance;
        double previousAltitude = Double.MIN_VALUE;
        double maxSpeed = 0.0;
        List<Double> altitudes = new ArrayList<>(end - start + 1);
        for (int i = start; i <= end; ++i) {
            double altitude = observations.get(i).altitude;
            altitudes.add(altitude);
            if (altitude < previousAltitude) {
                descents += (previousAltitude - altitude);
            }
            previousAltitude = altitude;
            maxSpeed = Math.max(maxSpeed, observations.get(i).speedMetersPerSecond);
        }
        runs.add(new SlopeSki.Run(
            startOfRun.secondsInWorkout,
            endOfRun.secondsInWorkout,
            descents,
            distance,
            altitudes,
            maxSpeed)
        );

        return start;
    }

    private static int findEndOfRun(ArrayList<Observation> observations, int start) {
        int end = observations.size() - 1;
        Observation endOfRun = observations.get(end);
        for (int i = end - 1; i > start; --i) {
            Observation observation = observations.get(i);
            double altitudeSpeed =
                (endOfRun.altitude - observation.altitude) / (endOfRun.secondsInWorkout
                    - observation.secondsInWorkout);
            if (altitudeSpeed < DOWNHILL_VERTICAL_SPEED_THRESHOLD) {
                break;
            }
            end = i;
            endOfRun = observation;
        }
        return end;
    }

    @NonNull
    public SlopeSki calculateRuns() {
        synchronized (observations) {
            if (observations.size() == 0) {
                return new SlopeSki();
            }

            ArrayList<SlopeSki.Run> runs = new ArrayList<>(this.runs.size() + 1);
            runs.addAll(this.runs);
            if (state == STATE_RUN) {
                createOrUpdateSplits(runs, observations, currentPeakPosition);
            }

            return new SlopeSki().runsAndExtractTotals(runs);
        }
    }

    @SlopeSkiCalculator.State
    public int getCurrentState() {
        return state;
    }

    public int getRunCount() {
        synchronized (observations) {
            int count = runs.size();
            if (state == STATE_RUN) {
                ++count;
            }
            return count;
        }
    }

    public double getSkiDurationInSeconds() {
        synchronized (observations) {
            double duration = 0.0;
            for (int i = runs.size() - 1; i >= 0; --i) {
                duration += runs.get(i).duration();
            }
            if (state == STATE_RUN) {
                duration += getCurrentRunDurationInSeconds();
            }
            return duration;
        }
    }

    public double getSkiDistanceInMeters() {
        synchronized (observations) {
            double distance = 0.0;
            for (int i = runs.size() - 1; i >= 0; --i) {
                distance += runs.get(i).getDistance();
            }
            if (state == STATE_RUN) {
                distance += getCurrentRunDistanceInMeters();
            }
            return distance;
        }

    }

    public double getSkiDescentInMeters() {
        synchronized (observations) {
            double descent = 0.0;
            for (int i = runs.size() - 1; i >= 0; --i) {
                descent += runs.get(i).getDescents();
            }
            if (state == STATE_RUN) {
                descent += getCurrentRunDescentInMeters();
            }
            return descent;
        }
    }

    public double getSkiAngleDegree() {
        synchronized (observations) {
            return Math.toDegrees(Math.atan2(getSkiDescentInMeters(), getSkiDistanceInMeters()));
        }
    }

    public double getAverageSpeed() {
        synchronized (observations) {
            double distance = getSkiDistanceInMeters();
            double duration = getSkiDurationInSeconds();
            return duration > 0 ? distance / duration : 0.0;
        }
    }

    public double getMaximumSpeed() {
        synchronized (observations) {
            double totalMaxSpeed = 0.0;
            for (int i = runs.size() - 1; i >= 0; --i) {
                totalMaxSpeed = Math.max(totalMaxSpeed, runs.get(i).getMaxSpeedMetersPerSecond());
            }
            return Math.max(totalMaxSpeed, getCurrentRunMaximumSpeed());
        }
    }

    public double getCurrentRunDurationInSeconds() {
        synchronized (observations) {
            if (state == STATE_RUN) {
                int end = findEndOfRun(observations, currentPeakPosition);
                Observation endOfRun = observations.get(end);
                Observation startOfRun = observations.get(currentPeakPosition);
                return endOfRun.secondsInWorkout - startOfRun.secondsInWorkout;
            } else if (runs.size() > 0) {
                return runs.get(runs.size() - 1).duration();
            } else {
                return 0.0;
            }
        }
    }

    public double getCurrentRunDistanceInMeters() {
        synchronized (observations) {
            if (state == STATE_RUN) {
                int end = findEndOfRun(observations, currentPeakPosition);
                Observation endOfRun = observations.get(end);
                Observation startOfRun = observations.get(currentPeakPosition);
                return endOfRun.totalDistance - startOfRun.totalDistance;
            } else if (runs.size() > 0) {
                return runs.get(runs.size() - 1).getDistance();
            } else {
                return 0.0;
            }
        }
    }

    public double getCurrentRunDescentInMeters() {
        synchronized (observations) {
            if (state == STATE_RUN) {
                int end = findEndOfRun(observations, currentPeakPosition);
                Observation endOfRun = observations.get(end);
                Observation startOfRun = observations.get(currentPeakPosition);
                return startOfRun.altitude - endOfRun.altitude;
            } else if (runs.size() > 0) {
                SlopeSki.Run run = runs.get(runs.size() - 1);
                if (run.getAltitudes() != null && run.getAltitudes().size() > 1) {
                    return run.getAltitudes().get(0) - run.getAltitudes().get(run.getAltitudes().size() - 1);
                } else {
                    return 0.0;
                }
            } else {
                return 0.0;
            }
        }
    }

    public double getCurrentRunAverageSpeed() {
        synchronized (observations) {
            if (state == STATE_RUN) {
                int end = findEndOfRun(observations, currentPeakPosition);
                Observation endOfRun = observations.get(end);
                Observation startOfRun = observations.get(currentPeakPosition);
                double distance = endOfRun.totalDistance - startOfRun.totalDistance;
                double duration = endOfRun.secondsInWorkout - startOfRun.secondsInWorkout;
                return distance / duration;
            } else if (runs.size() > 0) {
                SlopeSki.Run run = runs.get(runs.size() - 1);
                return run.getDistance() / run.duration();
            } else {
                return 0.0;
            }
        }
    }

    public double getCurrentRunMaximumSpeed()  {
        synchronized (observations) {
            if (state == STATE_RUN) {
                int end = findEndOfRun(observations, currentPeakPosition);
                double maxSpeed = 0.0;
                for (int i = currentPeakPosition; i < end; i++) {
                    maxSpeed = Math.max(maxSpeed, observations.get(i).speedMetersPerSecond);
                }
                return maxSpeed;
            } else if (runs.size() > 0) {
                SlopeSki.Run run = runs.get(runs.size() - 1);
                return run.getMaxSpeedMetersPerSecond();
            } else {
                return 0.0;
            }
        }
    }
}
