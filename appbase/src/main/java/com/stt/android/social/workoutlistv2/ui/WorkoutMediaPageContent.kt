package com.stt.android.social.workoutlistv2.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.paging.compose.LazyPagingItems
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.stt.android.R
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.social.workoutlistv2.WorkoutMediaItem

@Composable
fun WorkoutMediaPageContent(
    pageItems: LazyPagingItems<WorkoutMediaItem>,
    modifier: Modifier = Modifier,
    onMediaIndexSelected: (Int) -> Unit = {},
    nestedScrollConnection: NestedScrollConnection? = null,
) {
    val appendState = pageItems.loadState.append
    val nestedScrollModifier = nestedScrollConnection?.let { Modifier.nestedScroll(it) } ?: Modifier
    Box(modifier = modifier.fillMaxSize()) {
        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
                .then(nestedScrollModifier),
            contentPadding = PaddingValues(MaterialTheme.spacing.small),
        ) {
            items(
                count = pageItems.itemCount,
                key = { index -> pageItems.peek(index)?.uri?.toString() ?: "placeholder_$index" }
            ) { index ->
                val mediaItem = pageItems[index]
                mediaItem?.let {
                    Box {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(it.uri)
                                .crossfade(true)
                                .build(),
                            modifier = Modifier
                                .padding(MaterialTheme.spacing.small)
                                .height(256.dp)
                                .fillMaxWidth()
                                .clip(MaterialTheme.shapes.large)
                                .clickableThrottleFirst {
                                    onMediaIndexSelected.invoke(index)
                                },
                            contentDescription = null,
                            contentScale = ContentScale.Crop,
                        )

                        if (it is WorkoutMediaItem.Video) {
                            Box(
                                modifier = Modifier
                                    .size(MaterialTheme.iconSizes.large)
                                    .align(Alignment.Center)
                                    .background(
                                        color = Color.Black.copy(alpha = 0.5f),
                                        shape = CircleShape,
                                    ),
                            ) {
                                Icon(
                                    painter = painterResource(R.drawable.ic_play_fill),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .align(Alignment.Center)
                                        .size(MaterialTheme.iconSizes.medium),
                                    tint = Color.White,
                                )
                            }
                        }
                    }
                }
            }

            item(span = { GridItemSpan(2) }) {
                LoadMoreFooter(
                    appendState = appendState,
                    fullLoadedTips = R.string.loaded_all,
                ) {
                    pageItems.retry()
                }
            }
        }
    }
}
