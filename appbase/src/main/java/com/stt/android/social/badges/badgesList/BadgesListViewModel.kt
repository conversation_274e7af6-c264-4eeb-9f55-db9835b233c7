package com.stt.android.social.badges.badgesList

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.eventtracking.EventTracker
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
internal class BadgesListViewModel @Inject constructor(
    private val badgesDataLoader: BadgesListDataLoader,
    private val eventTracker: EventTracker
) : ViewModel() {

    private val _uiState = MutableStateFlow<BadgesListViewData>(BadgesListViewData.Initial)
    val uiState: StateFlow<BadgesListViewData> = _uiState.asStateFlow()

    fun loadBadges(moduleName: String, moduleNameEn: String) {
        viewModelScope.launch {
            runSuspendCatching {
                val badgesList = badgesDataLoader.loadBadgesList(moduleName)
                _uiState.value = BadgesListViewData.Loaded(
                    moduleName = moduleName,
                    badgesList = badgesList
                )
                trackingBadgeListPage(moduleNameEn)
            }.onFailure {
                Timber.w(it, "Fail to load badges list.")
                _uiState.value = BadgesListViewData.Initial
            }
        }
    }

    fun trackingBadgeListPage(
        moduleName: String
    ) {
        eventTracker.trackEvent(
            AnalyticsEvent.BADGES_LIST_PAGE_START,
            mapOf(AnalyticsEventProperty.BADGES_MODULE_NAME to moduleName)
        )
    }
}
