package com.stt.android.workoutdetail.tags

import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.workouts.tag.SuuntoTag

data class DeviceTag(
    val displayName: String,
    val productType: String
) {
    companion object {
        fun createOrNull(
            displayName: String?,
            productType: String?
        ): DeviceTag? {
            if (!displayName.isNullOrBlank() && !productType.isNullOrBlank()) {
                return DeviceTag(displayName, productType)
            }
            return null
        }
    }
}

data class TagsData(
    val deviceTag: DeviceTag?,
    val suuntoTags: List<SuuntoTag>,
    val userTags: List<UserTag>,
    val isOwnWorkout: Boolean,
    val isPlannedWorkout: Boolean = false,
) {
    val hasSomethingToShow: Boolean
        get() = deviceTag != null || suuntoTags.isNotEmpty() || userTags.isNotEmpty()
}
