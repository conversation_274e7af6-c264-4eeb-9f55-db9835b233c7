package com.stt.android.common.ui

import android.os.Bundle
import androidx.annotation.MainThread
import androidx.lifecycle.AbstractSavedStateViewModelFactory
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.savedstate.SavedStateRegistryOwner

/**
 * Interface for constructing [ViewModel] that gets an instance of [SavedStateHandle]
 * as a parameter.
 *
 * Implement this interface passing all [ViewModel] dependencies to the implementation's
 * injectable constructor and return a new instance of the [ViewModel].
 *
 * Inject an instance of the implementation in a Fragment or Activity and pass it
 * to [withFactory] when using `by viewModels` like so:
 *
 *
 * ```
 * private val viewModel: WorkoutDetailsViewModelNew by viewModels {
 *     withFactory(workoutDetailsViewModelFactory, this)
 * }
 * ```
 *
 * @see withFactory
 */
interface SavedStateHandleViewModelFactory<out V : ViewModel> {
    fun create(handle: SavedStateHandle): V
}

interface AssistedSavedStateViewModelFactory<T : ViewModel> {
    fun create(savedStateHandle: SavedStateHandle): T
}

class GenericSavedStateViewModelFactory<out V : ViewModel>(
    private val savedStateHandleViewModelFactory: SavedStateHandleViewModelFactory<V>,
    owner: SavedStateRegistryOwner,
    defaultArgs: Bundle? = null
) : AbstractSavedStateViewModelFactory(owner, defaultArgs) {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(
        key: String,
        modelClass: Class<T>,
        handle: SavedStateHandle
    ): T {
        return savedStateHandleViewModelFactory.create(handle) as T
    }
}

/**
 * Convenience function to use with `by viewModels`.
 *
 * @see SavedStateHandleViewModelFactory
 */
@MainThread
inline fun <reified VM : ViewModel> withFactory(
    factory: SavedStateHandleViewModelFactory<VM>,
    owner: SavedStateRegistryOwner,
    defaultArgs: Bundle? = null
) = GenericSavedStateViewModelFactory(factory, owner, defaultArgs)
