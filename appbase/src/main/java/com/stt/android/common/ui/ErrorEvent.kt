package com.stt.android.common.ui

import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.exceptions.remote.AskoError
import com.stt.android.exceptions.remote.ServerError
import java.net.UnknownHostException
import java.util.concurrent.TimeoutException
import kotlin.reflect.KClass

/**
 * Data class to hold information regarding error events that occur while [LoadingViewModel.loadData]
 * is loading data. Instances of this class are used to determine whether the user should be notified
 * or not with a corresponding error message and an optional retry action button.
 *
 * @param shouldHandle True if this error should be handled. Setting this to False will make the
 * error handler in [LoadingViewModel.handleError] to propagate this error down the stream so that
 * onError of the RX chain would be invoked.
 * @param errorStringRes The string resource ID of the text to be shown to the user
 * @param canRetry True if the user be presented with a button to retry the loading operation
 * @param showCloseButton True (default) if the error note will have a close button.
 */
data class ErrorEvent(
    val shouldHandle: <PERSON>olean,
    @StringRes val errorStringRes: Int,
    val canRetry: <PERSON><PERSON>an,
    val showCloseButton: Boolean = true
) {
    companion object {
        private val unknownError = ErrorEvent(true, R.string.error_generic, false)

        private val map = mutableMapOf(
            /*
             *  No internet connection
             */
            UnknownHostException::class to ErrorEvent(true, R.string.no_network_error, true),
            /**
             *  Http 5XX errors: server errors
             */
            ServerError::class to ErrorEvent(true, R.string.error_generic, true),
            /**
             *  Timeout errors
             */
            TimeoutException::class to ErrorEvent(true, R.string.error_generic, true),

            AskoError.EmailAlreadyExists::class to ErrorEvent(true, R.string.error_101, false),

            AskoError.InvalidEmail::class to ErrorEvent(true, R.string.invalid_email, false)
        )

        fun get(type: KClass<out Throwable>): ErrorEvent {
            return map[type] ?: unknownError
        }

        fun registerErrorEvents(errorEvents: Map<KClass<out Exception>, ErrorEvent>) {
            map.putAll(errorEvents)
        }
    }
}
