package com.stt.android.ui.utils

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import androidx.annotation.ColorInt
import androidx.core.graphics.withSave
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyControllerAdapter
import kotlin.math.roundToInt

/**
 * Item decorator for showing dividers between list items in vertical lists using [EpoxyControllerAdapter]. Dividers may
 * optionally be filled with a solid color [dividerColor]. If [dividerColor] is null, the decorator only affects the
 * layout of the items and does not actively draw anything.
 *
 * The recycler view must be configured to use a [EpoxyControllerAdapter].
 *
 * By default dividers are drawn over the list items. This behavior may be changed by
 * setting [drawDividerOver] to `false`. Note that if [dividerColor] is specified and
 * [drawDividerOver] is `true`, this decoration will draw over the list items. This means that
 * the divider will draw over any shadows due to elevation, for example.
 *
 * Based on [androidx.recyclerview.widget.DividerItemDecoration], but this class allows item
 * specific divider heights by specifying [dividerHeightBetween]. When [dividerHeightBetween]
 * evaluates to `null`, no divider is drawn.
 */
class EpoxyConditionalDividerItemDecoration(
    @ColorInt private val dividerColor: Int? = null,
    private val drawDividerOver: Boolean = true,
    private val dividerHeightBetween: (item: Any?, nextItem: Any?) -> Int?
) : RecyclerView.ItemDecoration() {

    private val bounds = Rect()
    private val paint: Paint? by lazy {
        dividerColor?.let {
            Paint().apply {
                style = Paint.Style.FILL
                color = it
            }
        }
    }

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)

        dividerHeightAboveFirstItem(view, parent)?.let {
            outRect.top += it
        }
        dividerHeightBelow(view, parent)?.let {
            outRect.bottom += it
        }
    }

    override fun onDraw(canvas: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        if (!drawDividerOver) {
            draw(canvas, parent)
        }
    }

    override fun onDrawOver(canvas: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        if (drawDividerOver) {
            draw(canvas, parent)
        }
    }

    private fun draw(canvas: Canvas, parent: RecyclerView) {
        paint?.let { paint ->
            canvas.withSave {
                // Based on androidx.recyclerview.widget.DividerItemDecoration.onDraw
                val childCount = parent.childCount

                val left: Int
                val right: Int
                if (parent.clipToPadding) {
                    left = parent.paddingLeft
                    right = parent.width - parent.paddingRight
                    clipRect(
                        left,
                        parent.paddingTop,
                        right,
                        parent.height - parent.paddingBottom
                    )
                } else {
                    left = 0
                    right = parent.width
                }

                parent.getChildAt(0)?.let { firstChild ->
                    dividerHeightAboveFirstItem(firstChild, parent)?.let { dividerHeight ->
                        parent.getDecoratedBoundsWithMargins(firstChild, bounds)
                        val top = bounds.top + firstChild.translationY.roundToInt()
                        val bottom = top + dividerHeight
                        drawRect(
                            left.toFloat(),
                            top.toFloat(),
                            right.toFloat(),
                            bottom.toFloat(),
                            paint
                        )
                    }
                }
                for (i in 0 until childCount) {
                    val child = parent.getChildAt(i)
                    dividerHeightBelow(child, parent)?.let { dividerHeight ->
                        parent.getDecoratedBoundsWithMargins(child, bounds)
                        val bottom = bounds.bottom + child.translationY.roundToInt()
                        val top = bottom - dividerHeight
                        drawRect(
                            left.toFloat(),
                            top.toFloat(),
                            right.toFloat(),
                            bottom.toFloat(),
                            paint
                        )
                    }
                }
            }
        }
    }

    private fun dividerHeightBelow(view: View, parent: RecyclerView): Int? {
        val position = parent.getChildAdapterPosition(view)
        if (position == RecyclerView.NO_POSITION) return null

        val adapter = parent.adapter as EpoxyControllerAdapter
        val child = adapter.getModelAtPosition(position)

        val nextChild: Any? = if (position + 1 < adapter.itemCount) {
            adapter.getModelAtPosition(position + 1)
        } else {
            null
        }

        return dividerHeightBetween(child, nextChild)
    }

    private fun dividerHeightAboveFirstItem(view: View, parent: RecyclerView): Int? {
        val position = parent.getChildAdapterPosition(view)
        if (position != 0 || position == RecyclerView.NO_POSITION) return null

        val adapter = parent.adapter as EpoxyControllerAdapter
        val child = adapter.getModelAtPosition(position)
        return dividerHeightBetween(null, child)
    }
}
