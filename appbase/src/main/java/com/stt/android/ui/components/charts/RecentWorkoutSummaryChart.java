package com.stt.android.ui.components.charts;

import android.content.Context;
import androidx.core.content.ContextCompat;
import android.util.AttributeSet;

import com.github.mikephil.charting.charts.CombinedChart;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.stt.android.R;

public class RecentWorkoutSummaryChart extends CombinedChart {
    public RecentWorkoutSummaryChart(Context context) {
        super(context);
        initialize(context);
    }

    public RecentWorkoutSummaryChart(Context context, AttributeSet attrs) {
        super(context, attrs);
        initialize(context);
    }

    public RecentWorkoutSummaryChart(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        initialize(context);
    }

    private void initialize(Context context) {
        getDescription().setText("");
        setHighlightPerDragEnabled(false);
        setHighlightPerTapEnabled(true);
        setDrawGridBackground(false);
        setDrawBorders(false);
        setDrawBarShadow(false);

        YAxis axisLeft = getAxisLeft();
        axisLeft.setEnabled(true); // must be set as "true" that avg line can be drawn
        axisLeft.setDrawGridLines(false);
        axisLeft.setDrawAxisLine(false);
        axisLeft.setDrawLabels(false);
        axisLeft.setAxisMinimum(0f);
        getAxisRight().setEnabled(false);

        XAxis xAxis = getXAxis();
        xAxis.setDrawGridLines(false);
        xAxis.setDrawAxisLine(true);
        xAxis.setAxisLineColor(ContextCompat.getColor(context, com.stt.android.core.R.color.label));
        xAxis.setSpaceMin(0.5f);
        xAxis.setSpaceMax(0.5f);
    }
}
