package com.stt.android.ui.fragments.workout

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import com.stt.android.core.R as CR

/**
 * Custom view that draws y-axis of graph on shared photo
 */
open class WorkoutChartShareYAxisView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : View(context, attrs, defStyle) {

    /**
     * How much space there is below y-axis
     */
    var bottomOffset = 0f
        set(value) {
            if (field != value) {
                field = value
                calculateAxisStartAndStop(width, height)
                invalidate()
            }
        }

    /**
     * How much space there is above y-axis
     */
    var topOffset = 0f
        set(value) {
            if (field != value) {
                field = value
                calculateAxisStartAndStop(width, height)
                invalidate()
            }
        }

    /**
     * How big portion of the horizontal width the marker line takes. 1.0f means 100% of the width
     * of this view.
     */
    var markerLineWidthRatio: Float = DEFAULT_MARKER_LINE_WIDTH_PER_WIDTH
        set(value) {
            if (field != value) {
                field = value
                axisMarkerLineWidth = width * value
                invalidate()
            }
        }

    /**
     * How many marker lines to draw. The minimum is 2 (min and max marker lines).
     */
    var markerLineCount: Int = DEFAULT_MARKER_LINE_COUNT
        set(value) {
            if (field != value) {
                field = value
                invalidate()
            }
        }

    protected var xStart = 0f
    private var yStart = 0f
    private var yStop = 0f
    private var step = 0f
    protected var axisMarkerLineWidth = 0f

    private val linePaint = Paint().apply {
        color = ContextCompat.getColor(context, CR.color.near_black)
    }

    /**
     * Color used to draw the axis line
     */
    fun setColor(@ColorInt color: Int) {
        if (linePaint.color != color) {
            linePaint.color = color
            invalidate()
        }
    }

    /**
     * Line width of the axis line
     */
    fun setLineWidth(lineWidth: Float) {
        if (linePaint.strokeWidth != lineWidth) {
            linePaint.strokeWidth = lineWidth
            calculateAxisStartAndStop(width, height)
            invalidate()
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        calculateAxisStartAndStop(w, h)
        axisMarkerLineWidth = w * markerLineWidthRatio
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Vertical axis line
        canvas.drawLine(xStart, yStart, xStart, yStop, linePaint)

        // Marker lines
        for (i in 0 until markerLineCount) {
            val markerEnd = calculateMarkEndByMarkerLine(i)
            val y = yStart + linePaint.strokeWidth / 2 + i * step
            canvas.drawLine(xStart, y, markerEnd, y, linePaint)
        }
    }

    protected open fun calculateMarkEndByMarkerLine(i: Int): Float {
        return if (i == 0 || i == markerLineCount - 1) {
            xStart - axisMarkerLineWidth
        } else {
            xStart - axisMarkerLineWidth / 2
        }
    }

    protected open fun calculateAxisStart(w: Int, h: Int) {
        xStart = w - linePaint.strokeWidth
    }

    private fun calculateAxisStartAndStop(w: Int, h: Int) {
        calculateAxisStart(w, h)
        yStart = topOffset
        yStop = h - bottomOffset
        step = (yStop - yStart - linePaint.strokeWidth) / (markerLineCount - 1)
    }

    companion object {
        private const val DEFAULT_MARKER_LINE_WIDTH_PER_WIDTH = 0.4f
        private const val DEFAULT_MARKER_LINE_COUNT = 4
    }
}
