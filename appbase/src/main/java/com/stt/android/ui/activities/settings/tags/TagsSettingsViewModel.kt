package com.stt.android.ui.activities.settings.tags

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.autoCommuteTaggingEnabled
import com.stt.android.exceptions.InternalDataException
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.activities.settings.tags.TagsSettingsActivity.Companion.ARG_AUTO_TAGGED_WORKOUT_ID
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class TagsSettingsViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val userSettingsController: UserSettingsController,
    infoModelFormatter: InfoModelFormatter,
) : ViewModel() {

    var uiState by mutableStateOf(
        TagsSettingsViewState(
            measurementUnit = infoModelFormatter.unit
        )
    )
        private set

    // ARG_AUTO_TAGGED_WORKOUT_ID to detect whatever we navigated from auto tagged dialog or from settings (null, aka: id is missing)
    val autoTaggedWorkoutId = savedStateHandle.get<Int>(ARG_AUTO_TAGGED_WORKOUT_ID)

    init {
        loadData()
    }

    private fun loadData() {
        uiState = uiState.copy(
            isAutoTaggingCommuteEnabled = userSettingsController.settings.autoCommuteTaggingEnabled,
        )
    }

    fun onCheckedChange(isAutoTaggingCommuteEnabled: Boolean) {
        viewModelScope.launch {
            uiState = uiState.copy(
                isAutoTaggingCommuteEnabled = isAutoTaggingCommuteEnabled,
                isRemoveConfirmationDialogOpen = hasWorkoutId() && !isAutoTaggingCommuteEnabled // we show confirmation only when user navigated from auto tagged dialog
            )
            try {
                userSettingsController.storeSettings(
                    userSettingsController.settings.autoCommuteTaggingEnabled(isAutoTaggingCommuteEnabled)
                )
            } catch (e: InternalDataException) {
                Timber.w(e, "Error while storing if auto commute tagging is enabled")
            }
        }
    }

    private fun hasWorkoutId(): Boolean {
        return autoTaggedWorkoutId != null
    }

    fun dismissConfirmationDialog() {
        uiState = uiState.copy(
            isRemoveConfirmationDialogOpen = false
        )
    }
}
