package com.stt.android.ui.components

import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.annotation.IdRes
import androidx.core.view.get
import androidx.core.view.size
import com.google.android.material.bottomnavigation.BottomNavigationItemView
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.stt.android.R

class BottomNavigationBar
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : BottomNavigationView(context, attrs, defStyleAttr) {
    private var animator: ValueAnimator? = null

    fun selectTabAtPosition(index: Int) {
        selectedItemId = menu[index].itemId
    }

    fun getCurrentTabPosition(): Int {
        val id = selectedItemId
        for (i in 0 until menu.size) {
            if (menu[i].itemId == id) {
                return i
            }
        }
        return 0
    }

    fun show() {
        animator?.cancel()
        val targetHeight = resources.getDimensionPixelSize(R.dimen.navbar_height)
        if (layoutParams.height == targetHeight) {
            return
        }
        layoutParams.height = 0

        ValueAnimator.ofInt(0, targetHeight).apply {
            animator = this
            duration = 200
            addUpdateListener { animator ->
                layoutParams.height = animator.animatedValue as Int
                requestLayout()
            }
            start()
        }
    }

    fun hide() {
        animator?.cancel()
        val targetHeight = 0
        if (layoutParams.height == targetHeight) {
            return
        }
        val initialHeight = height

        ValueAnimator.ofInt(initialHeight, 0).apply {
            animator = this
            duration = 200
            addUpdateListener { animator ->
                layoutParams.height = animator.animatedValue as Int
                requestLayout()
            }
            start()
        }
    }

    fun setTabBadgeText(@IdRes itemId: Int, value: String?) {
        val itemView = findViewById<BottomNavigationItemView>(itemId)

        var textView = itemView.findViewById<TextView>(R.id.bottombarBadgeText)
        if (textView == null) {
            // The badge view has not been created yet, so do it now.
            val badge = LayoutInflater.from(context).inflate(R.layout.bottombar_badge, itemView, false)
            itemView.addView(badge)
            textView = badge.findViewById(R.id.bottombarBadgeText)
        }

        if (value.isNullOrEmpty()) {
            textView.text = ""
            textView.visibility = GONE
        } else {
            textView.text = value
            textView.visibility = VISIBLE
        }
    }

    // Show red circle without text
    fun setTabBadgeHighlight(@IdRes itemId: Int, enabled: Boolean) {
        val itemView = findViewById<BottomNavigationItemView>(itemId)

        var badgeView = itemView.findViewById<View>(R.id.bottomBarBadge)
        if (badgeView == null) {
            // The badge view has not been created yet, so do it now.
            val badge = LayoutInflater.from(context).inflate(R.layout.bottombar_badge_without_text, itemView, false)
            itemView.addView(badge)
            badgeView = badge.findViewById(R.id.bottomBarBadge)
        }
        badgeView.visibility = if (enabled) View.VISIBLE else View.GONE
    }
}

interface BottomNavigationBarDelegate {
    fun showBottomBar()
    fun hideBottomBar()
}
