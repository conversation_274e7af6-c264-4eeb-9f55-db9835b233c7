package com.stt.android.ui.components;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.HapticFeedbackConstants;
import android.view.MotionEvent;
import android.view.View;

import com.stt.android.R;

public class SlideLockView extends View {
    /**
     * How much we move to unlock
     */
    private static final float UNLOCK_POSITION = 0.8f;
    public static final int STATUS_IDLE = 1;
    public static final int STATUS_DRAGGED = 2;
    public static final int STATUS_CANCELING = 3;
    public static final int STATUS_DONE = 4;

    /**
     * This event listener can be used to listen event when the user moves the
     * sliding lock "thumb" around.
     */
    public static interface OnSlideEventListener {
        /**
         * Called when the user has started to slide the thumb.
         */
        public void onSlideStart();

        /**
         * Called when the user has successfully moved the thumb all the way to
         * the other end.
         */
        public void onSlideDone();

        /**
         * Called when the user has canceled the slide.
         */
        public void onSlideCancel();
    }

    private Bitmap backgroundBitmap;
    private Bitmap slideBitmap;
    private float slidePosition = 0.0f;
    private int slideStatus = STATUS_IDLE;
    private OnSlideEventListener slideEventListener;
    private final int gradientColors[] = {
            Color.TRANSPARENT, 0x40ef6f00, 0xffff7f00
    };
    private final float gradientPositions[] = {
            0.0f, 0.2f, 0.9f
    };

    private final Paint gradientPaint = new Paint();
    private final RectF backgroundRect = new RectF();
    private final RectF gradientRect = new RectF();
    private final RectF thumbRect = new RectF();

    public SlideLockView(Context context) {
        super(context);
        init();
    }

    public SlideLockView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        gradientPaint.setAlpha(128);

        backgroundBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.lock_slider_background);
    }

    /**
     * Sets the icon which will be used to draw the lock thumb.
     */
    public void setSlideIcon(int resourceId) {
        slideBitmap = BitmapFactory.decodeResource(getResources(), resourceId);
    }

    public void setOnSlideEventListener(OnSlideEventListener listener) {
        slideEventListener = listener;
    }

    /**
     * Resets the lock thumb and view state to default idle state.
     */
    public void resetLock() {
        if (slideStatus == STATUS_DRAGGED && slideEventListener != null) {
            slideEventListener.onSlideCancel();
        }
        slideStatus = STATUS_IDLE;
        setSlidePosition(0.0f);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        updateGradient();
        updateSlidePosition();
    }

    private void updateGradient() {
        float iconSize = getMeasuredHeight();
        float thumbHalfWidth = iconSize / 2.0f;
        LinearGradient gradientShader = new LinearGradient(thumbHalfWidth, 0, getWidth(), 0, gradientColors,
                gradientPositions, Shader.TileMode.CLAMP);
        gradientPaint.setShader(gradientShader);
    }

    private void setSlidePosition(float position) {
        if (position < 0) {
            position = 0;
        } else if (position > 1) {
            position = 1;
        }
        slidePosition = position;
        updateSlidePosition();
    }

    /**
     * Updates the position for the lock bitmap as well as the gradient
     */
    private void updateSlidePosition() {
        // We use the view height to figure out the icon size, therefore we
        // consider the icon to be equal width/height
        float iconSize = getMeasuredHeight();
        float thumbHalfWidth = iconSize / 2.0f;
        float xPos = slidePosition * (getMeasuredWidth() - iconSize);
        thumbRect.set(xPos, 0, xPos + iconSize, iconSize);
        gradientRect.set(thumbHalfWidth, 0, thumbRect.left + thumbHalfWidth, thumbRect.bottom);
        gradientRect.inset(0, 3);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        boolean handled = false;

        int action = event.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                if (thumbRect.contains(event.getX(), event.getY())) {
                    // User hits the thumb area, start dragging.
                    slideStatus = STATUS_DRAGGED;
                    handled = true;

                    performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY,
                            HapticFeedbackConstants.FLAG_IGNORE_GLOBAL_SETTING);

                    if (slideEventListener != null) {
                        slideEventListener.onSlideStart();
                    }
                }
                break;
            case MotionEvent.ACTION_MOVE:
                if (slideStatus == STATUS_DRAGGED) {
                    int iconSize = getHeight();
                    float newPosition = (event.getX() - iconSize / 2) / (getWidth() - iconSize);

                    setSlidePosition(newPosition);
                    handled = true;
                }
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                if (slidePosition >= UNLOCK_POSITION) {
                    // All the way to the right, sliding is done.
                    slideStatus = STATUS_DONE;

                    performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY,
                            HapticFeedbackConstants.FLAG_IGNORE_GLOBAL_SETTING);
                    if (slideEventListener != null) {
                        slideEventListener.onSlideDone();
                    }
                } else {
                    slideStatus = STATUS_CANCELING;
                    handled = true;

                    if (slideEventListener != null)
                        slideEventListener.onSlideCancel();
                }
                break;
        }

        if (handled) {
            invalidate();
        }
        return handled;
    }

    @Override
    public void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if (backgroundRect.right == 0) {
            int imageHeight = backgroundBitmap.getHeight();
            int top = (getMeasuredHeight() - imageHeight) / 2;
            backgroundRect.set(0, top, getMeasuredWidth(), top + imageHeight);
        }

        canvas.drawBitmap(backgroundBitmap, null, backgroundRect, null);

        if (slideStatus == STATUS_CANCELING) {
            // If the slider is cancelling, keep animating the
            // thumb until it has stopped completely.
            // TODO: use time to compute this.
            setSlidePosition(slidePosition - 0.05f);
            if (slidePosition <= 0.0f) {
                slideStatus = STATUS_IDLE;
            }
            invalidate();
        }

        if (slideStatus == STATUS_DRAGGED || slideStatus == STATUS_CANCELING || slideStatus == STATUS_DONE) {
            canvas.drawRect(gradientRect, gradientPaint);
        }

        if (slideBitmap != null) {
            canvas.drawBitmap(slideBitmap, null, thumbRect, null);
        }
    }
}
