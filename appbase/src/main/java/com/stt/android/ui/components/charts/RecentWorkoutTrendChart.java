package com.stt.android.ui.components.charts;

import android.content.Context;
import android.graphics.Typeface;
import android.util.AttributeSet;
import androidx.core.content.res.ResourcesCompat;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.components.AxisBase;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.formatter.ValueFormatter;
import com.stt.android.FontRefs;
import com.stt.android.ThemeColors;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.ui.utils.TextFormatter;

public class RecentWorkoutTrendChart extends LineChart {
    public RecentWorkoutTrendChart(Context context) {
        super(context);
        initialize(context);
    }

    public RecentWorkoutTrendChart(Context context, AttributeSet attrs) {
        super(context, attrs);
        initialize(context);
    }

    public RecentWorkoutTrendChart(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        initialize(context);
    }

    private void initialize(Context context) {
        clear();
        getDescription().setText("");
        setHighlightPerDragEnabled(false);
        setHighlightPerTapEnabled(true);
        setDrawGridBackground(false);
        setDrawBorders(false);
        getAxisLeft().setEnabled(false);
        getAxisLeft().setAxisMinimum(0.0F);
        getAxisRight().setEnabled(false);

        XAxis xAxis = getXAxis();
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
        xAxis.setDrawGridLines(false);
        xAxis.setDrawLabels(true);
        xAxis.setTextColor(ThemeColors.primaryTextColor(context));
        xAxis.setValueFormatter(new ValueFormatter() {
            /*
                x-axis should be linear even though x-axis labels do not progress linearly.
                Because of this, x-axis value should be index and label should be a date.
                Indexes are linear, but dates are not.
             */
            @Override
            public String getFormattedValue(float x) {
                if (getLineData() != null &&
                    getLineData().getDataSetByIndex(0) != null &&
                    getLineData().getDataSetByIndex(0).getEntryForXValue(x, 0) != null &&
                    getLineData().getDataSetByIndex(0).getEntryForXValue(x, 0).getData() != null
                ) {
                    Object data = getLineData().getDataSetByIndex(0).getEntryForXValue(x, 0).getData();
                    WorkoutHeader workoutHeader = data instanceof WorkoutHeader ? (WorkoutHeader)data : null;
                    if (workoutHeader == null) {
                        return "";
                    } else {
                        return TextFormatter.formatDate(context, workoutHeader.getStartTime(), false);
                    }
                } else {
                    return null;
                }
            }
        });

        Typeface typeface = ResourcesCompat.getFont(context, FontRefs.CHART_FONT_REF);
        if (typeface != null) {
            xAxis.setTypeface(typeface);
            xAxis.setTextSize(14f);
        }
    }
}
