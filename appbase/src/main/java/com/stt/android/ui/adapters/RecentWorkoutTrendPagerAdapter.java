package com.stt.android.ui.adapters;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import com.amersports.formatter.unit.jscience.JScienceUnitConverter;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.domain.user.workout.RecentWorkoutTrend;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.ui.components.charts.RecentWorkoutMarkerView;
import com.stt.android.ui.components.charts.RecentWorkoutTrendChart;
import com.stt.android.workoutcomparison.WorkoutComparisonActivity;
import com.stt.android.workoutdetail.recentsummary.RecentWorkoutItemHelper;
import com.stt.android.workoutdetail.trend.RecentTrendItemHelper;
import com.stt.android.workoutdetail.trend.RecentWorkoutTrendActivity;

public class RecentWorkoutTrendPagerAdapter extends RecentWorkoutPagerAdapter
    implements View.OnClickListener {
    @SuppressWarnings("WeakerAccess")
    final RecentWorkoutTrend recentWorkoutTrend;
    @SuppressWarnings("WeakerAccess")
    final boolean isSimilarWorkout;

    public RecentWorkoutTrendPagerAdapter(Context context, RecentWorkoutTrend recentWorkoutTrend,
        boolean isSimilarWorkout, @NonNull InfoModelFormatter infoModelFormatter,
        @NonNull JScienceUnitConverter unitConverter) {
        super(context, recentWorkoutTrend.currentWorkout, infoModelFormatter, unitConverter);
        this.recentWorkoutTrend = recentWorkoutTrend;
        this.isSimilarWorkout = isSimilarWorkout;
    }

    @Override
    public @NonNull Object instantiateItem(@NonNull ViewGroup container, int position) {
        View rootView = inflater.inflate(R.layout.trend_page, container, false);

        int page = RecentWorkoutItemHelper.INSTANCE.positionToPage(referenceWorkout.getActivityType(), position);
        RecentWorkoutTrendChart chart = rootView.findViewById(R.id.recentWorkoutTrendChart);
        chart.getXAxis().setLabelCount(4);
        chart.setMarker(new RecentWorkoutMarkerView(
            context,
            page,
            referenceWorkout.getActivityType(),
            infoModelFormatter,
            unitConverter));
        chart.setTouchEnabled(false);

        if (recentWorkoutTrend.previousWorkout != null) {
            TextView comparisonValue = rootView.findViewById(R.id.comparisonValue);
            TextView comparisonTitle = rootView.findViewById(R.id.comparisonTitle);

            RecentTrendItemHelper.INSTANCE.setPreviousData(
                position,
                recentWorkoutTrend,
                context.getResources(),
                isSimilarWorkout,
                chart,
                comparisonValue,
                comparisonTitle,
                infoModelFormatter,
                unitConverter
            );

            rootView.findViewById(R.id.comparisonContainer)
                .setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (isSimilarWorkout) {
                            context.startActivity(
                                WorkoutComparisonActivity.Companion.newStartIntent(context,
                                    recentWorkoutTrend.currentWorkout,
                                    recentWorkoutTrend.previousWorkout,
                                    AnalyticsPropertyValue.CompareWorkoutScreenSource.RANKINGLIST));
                        } else {
                            // If it's not in the same route then just open the trend
                            RecentWorkoutTrendPagerAdapter.this.onClick(v);
                        }
                    }
                });
        } else {
           RecentTrendItemHelper.INSTANCE.setCurrentData(
               position,
               recentWorkoutTrend,
               chart,
               context.getResources()
           );
            rootView.findViewById(R.id.comparisonContainer).setVisibility(View.GONE);
        }

        rootView.findViewById(R.id.recentWorkoutTrendChartTouchArea).setOnClickListener(this);

        container.addView(rootView, 0);
        return rootView;
    }

    @Override
    public void onClick(View v) {
        context.startActivity(
            RecentWorkoutTrendActivity.Companion.newStartIntent(context, recentWorkoutTrend.currentWorkout));
    }
}
