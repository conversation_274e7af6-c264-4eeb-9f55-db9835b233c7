package com.stt.android.ui.fragments.settings

import android.app.Dialog
import android.content.Context
import android.content.SharedPreferences
import android.content.res.ColorStateList
import android.graphics.PorterDuff
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.content.edit
import androidx.core.widget.ImageViewCompat
import androidx.fragment.app.DialogFragment
import com.stt.android.R
import com.stt.android.ThemeColors.primaryTextColor
import com.stt.android.ThemeColors.resolveColor
import com.stt.android.controllers.UserSettingsController
import com.stt.android.databinding.PrivacySettingDialogBinding
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.workout.SharingOption
import com.stt.android.exceptions.InternalDataException
import com.stt.android.utils.STTConstants
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.core.R as CR

/**
 * After the watch is connected, a dialog prompts the user to set privacy
 */
@AndroidEntryPoint
class PrivacySettingDialog : DialogFragment() {
    companion object {
        const val PRIVACY_SETTING_IN_DEVICE_ACTIVITY = "privacy_setting_in_device_activity"
    }

    @Inject
    lateinit var userSettingsController: UserSettingsController

    @Inject
    @SuuntoSharedPrefs
    lateinit var sharedPreferences: SharedPreferences

    private lateinit var sharePreferences: SharedPreferences
    private lateinit var binding: PrivacySettingDialogBinding
    private var userShareFlag: Int = SharingOption.FOLLOWERS.backendId

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        isCancelable = false
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        sharePreferences = requireContext().getSharedPreferences(
            STTConstants.DefaultWorkoutSharingPreferences.PREFS_NAME,
            Context.MODE_PRIVATE
        )
        this.userShareFlag = userSettingsController.settings.sharingFlagPreference
        binding = PrivacySettingDialogBinding.inflate(layoutInflater)
        binding.description.setText(R.string.sharing_dialog_description)
        updatePrivacySettingViews()

        return AlertDialog.Builder(requireContext())
            .setTitle(R.string.sharing_dialog_title)
            .setView(binding.root)
            .setCancelable(false)
            .setPositiveButton(R.string.done) { _, _ -> setPrivacyAlreadyPrompted() }
            .create()
    }

    private fun updatePrivacySettingViews() {
        val sharePrivateView = binding.sharePrivate.root
        val shareFollowersView = binding.shareFollowers.root
        val sharePublicView = binding.sharePublic.root

        setPrivacyItem(
            sharePrivateView,
            R.drawable.ic_sharing_private_fill,
            R.string.sharing_private,
            R.string.privacy_private_summary,
            STTConstants.DefaultWorkoutSharingPreferences.SHARE_PRIVATE,
            SharingOption.NOT_SHARED.backendId,
            userShareFlag
        )
        setPrivacyItem(
            shareFollowersView,
            R.drawable.ic_sharing_social_fill,
            R.string.followers,
            R.string.privacy_followers_summary,
            STTConstants.DefaultWorkoutSharingPreferences.SHARE_FOLLOWERS,
            SharingOption.FOLLOWERS.backendId,
            userShareFlag
        )
        setPrivacyItem(
            sharePublicView,
            R.drawable.ic_explore_outline,
            R.string.sharing_public,
            R.string.privacy_public_summary,
            STTConstants.DefaultWorkoutSharingPreferences.SHARE_PUBLIC,
            SharingOption.EVERYONE.backendId,
            userShareFlag
        )
    }

    private fun setPrivacyItem(
        root: View,
        @DrawableRes iconRes: Int,
        @StringRes titleRes: Int,
        @StringRes summaryRes: Int,
        preferencesKey: String,
        shareSettingFlag: Int,
        currentShareFlag: Int
    ) {
        val icon =
            root.findViewById<ImageView>(android.R.id.icon).apply { setImageResource(iconRes) }
        val title = root.findViewById<TextView>(android.R.id.title).apply { setText(titleRes) }
        val summary =
            root.findViewById<TextView>(android.R.id.summary).apply { setText(summaryRes) }

        val textColor: Int
        val iconColor: Int
        val background: Int
        if (shareSettingFlag == currentShareFlag) {
            textColor = resolveColor(root.context, R.attr.newAccentColor)
            iconColor = ContextCompat.getColor(root.context, CR.color.white)
            background = R.drawable.sharing_option_background_selected
        } else {
            textColor = primaryTextColor(root.context)
            iconColor = textColor
            background = R.drawable.sharing_option_background
        }

        title.setTextColor(textColor)
        summary.setTextColor(textColor)
        ImageViewCompat.setImageTintMode(icon, PorterDuff.Mode.SRC_IN)
        ImageViewCompat.setImageTintList(icon, ColorStateList.valueOf(iconColor))
        icon.setBackgroundResource(background)

        root.setOnClickListener {
            this.userShareFlag = shareSettingFlag
            try {
                userSettingsController.storeSettings(
                    userSettingsController.settings.setSharingFlagPreference(this.userShareFlag)
                )

                sharePreferences.edit {
                    putString(
                        STTConstants.DefaultWorkoutSharingPreferences.KEY_SHARE,
                        preferencesKey
                    )
                }
            } catch (e: InternalDataException) {
                Timber.d(e, "store user settings failed.")
            }

            updatePrivacySettingViews()
        }
    }

    private fun setPrivacyAlreadyPrompted() = sharedPreferences.edit {
        putBoolean(PRIVACY_SETTING_IN_DEVICE_ACTIVITY, true)
    }
}
