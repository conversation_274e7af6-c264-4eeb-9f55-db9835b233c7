package com.stt.android.ui.adapters;

import android.content.Context;
import android.content.res.Resources;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;
import com.amersports.formatter.unit.jscience.JScienceUnitConverter;
import com.stt.android.ThemeColors;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.workoutdetail.recentsummary.RecentWorkoutItemHelper;
import java.util.List;

public abstract class RecentWorkoutPagerAdapter extends PagerAdapter {

    protected final Context context;
    protected final Resources resources;
    protected final LayoutInflater inflater;
    protected final WorkoutHeader referenceWorkout;

    protected final int labelColor;

    @NonNull
    protected final InfoModelFormatter infoModelFormatter;

    @NonNull
    protected final JScienceUnitConverter unitConverter;

    private final List<String> titles;

    protected RecentWorkoutPagerAdapter(Context context, WorkoutHeader referenceWorkout,
        @NonNull InfoModelFormatter infoModelFormatter, @NonNull JScienceUnitConverter unitConverter) {
        this.context = context;
        this.resources = context.getResources();
        this.inflater = LayoutInflater.from(context);
        this.referenceWorkout = referenceWorkout;

        labelColor = ThemeColors.primaryTextColor(context);

        this.infoModelFormatter = infoModelFormatter;
        this.unitConverter = unitConverter;

        titles = RecentWorkoutItemHelper.INSTANCE.getTitle(resources, referenceWorkout);
    }

    @Override
    public int getCount() {
        return titles.size();
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        View view = (View) object;
        view.setTag(null);
        container.removeView((View) object);
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view == object;
    }

    @Override
    public CharSequence getPageTitle(int position) {
        return titles.get(position);
    }

}
