package com.stt.android.ui.components.charts

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.github.mikephil.charting.charts.LineChart
import com.stt.android.databinding.ZonedChartBinding
import kotlin.math.roundToInt

class ZonedChartView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    val chart: LineChart

    private val zoneBars: List<View>
    private val limitLines: List<View>
    private val limitLabels: List<TextView>
    private val thresholdLines: List<View>
    private val thresholdLabels: List<TextView>
    private val xLabels: List<TextView>

    private val binding = ZonedChartBinding.inflate(LayoutInflater.from(context), this)

    init {
        with(binding) {
            chart = zonedLineChart
            zoneBars = listOf(zone1Bar, zone2Bar, zone3Bar, zone4Bar, zone5Bar)
            limitLines = listOf(
                minLine,
                zone2LowerLine,
                zone3LowerLine,
                zone4LowerLine,
                zone5LowerLine,
                maxLine
            )
            limitLabels = listOf(
                minLabel,
                zone2LowerLabel,
                zone3LowerLabel,
                zone4LowerLabel,
                zone5LowerLabel,
                maxLabel
            )
            thresholdLines = listOf(thresholdLine1, thresholdLine2)
            thresholdLabels = listOf(thresholdLabel1, thresholdLabel2)
            xLabels = listOf(xLabel1, xLabel2, xLabel3, xLabel4, xLabel5)

            with(chart) {
                setTouchEnabled(false)
                setNoDataText("")
                description.text = ""
                setViewPortOffsets(0f, 0f, 0f, 0f)

                legend.isEnabled = false
                axisLeft.isEnabled = false
                axisRight.isEnabled = false
                xAxis.isEnabled = false
            }
        }
    }

    fun setShowXLabels(showXLabels: Boolean) {
        xLabels.forEach { it.visibility = if (showXLabels) View.VISIBLE else View.GONE }
    }

    fun setXLabels(xLabelStrings: List<String>) {
        xLabels.forEachIndexed { i, label ->
            label.text = xLabelStrings.getOrNull(i) ?: ""
        }
    }

    fun setLimitsAndThresholds(limits: List<Float>, thresholds: List<Float>) {
        require(limits.size == zoneBars.size + 1)
        require(thresholds.size <= thresholdLines.size)

        val range = limits.last() - limits.first()

        limits.mapIndexed { index, value ->
            limitLabels[index].text = formatLabel(value)
            if (index > 0) {
                val min = limits[index - 1]
                val max = limits[index]
                val weight = if (range > 0) (max - min) / range else 0f
                setZoneWeight(index, weight)
                limitLines[index].visibility = if (weight > 0) View.VISIBLE else View.INVISIBLE
                limitLabels[index].visibility = if (weight > 0) View.VISIBLE else View.INVISIBLE
            }
        }

        for (i in thresholdLines.indices) {
            if (i < thresholds.size &&
                thresholds[i] >= limits.first() && thresholds[i] <= limits.last()
            ) {
                thresholdLabels[i].text = formatLabel(thresholds[i])
                setThresholdLinePosition(thresholdLines[i], thresholds[i], limits.first(), range)
                thresholdLabels[i].visibility = View.VISIBLE
                thresholdLines[i].visibility = View.VISIBLE
            } else {
                thresholdLabels[i].visibility = View.GONE
                thresholdLines[i].visibility = View.GONE
            }
        }

        requestLayout()
    }

    private fun setZoneWeight(zone: Int, weight: Float) {
        with(zoneBars[zone - 1]) {
            (layoutParams as? LayoutParams)?.verticalWeight = weight
            visibility = if (weight > 0) View.VISIBLE else View.GONE
        }
    }

    private fun setThresholdLinePosition(
        thresholdLine: View,
        thresholdValue: Float,
        min: Float,
        range: Float
    ) {
        (thresholdLine.layoutParams as? LayoutParams)?.verticalBias =
            1 - (thresholdValue - min) / range
    }

    private fun formatLabel(value: Float) = value.roundToInt().toString()
}
