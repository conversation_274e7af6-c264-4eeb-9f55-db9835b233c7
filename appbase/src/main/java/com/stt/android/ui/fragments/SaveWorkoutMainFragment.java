package com.stt.android.ui.fragments;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import com.stt.android.R;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.databinding.SaveWorkoutMainFragmentBinding;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.ui.fragments.map.StaticWorkoutMiniMapComparisonFragment;
import com.stt.android.ui.fragments.map.StaticWorkoutMiniMapFragment;
import com.stt.android.ui.fragments.medialist.WorkoutEditMediaPickerFragment;
import com.stt.android.ui.fragments.workout.BaseWorkoutHeaderFragment;
import com.stt.android.ui.fragments.workout.RecentWorkoutSummaryFragment;
import com.stt.android.utils.STTConstants;
import com.stt.android.workoutdetail.trend.RecentWorkoutTrendFragment;
import com.stt.android.workouts.edit.SaveWorkoutHeaderService;
import dagger.hilt.android.AndroidEntryPoint;
import timber.log.Timber;

@AndroidEntryPoint
public class SaveWorkoutMainFragment extends BaseWorkoutHeaderFragment {
    public static final String FRAGMENT_TAG =
        "com.stt.android.ui.fragments.SaveWorkoutMainFragment.FRAGMENT_TAG";
    // Delay for throttling unnecessary updates (while description is still updating)
    private static final int DESCRIPTION_UPDATE_DELAY = 750;

    private SaveWorkoutMainFragmentBinding binding;

    public static SaveWorkoutMainFragment newInstance(WorkoutHeader workoutHeader,
        WorkoutHeader targetWorkout) {
        SaveWorkoutMainFragment fragment = new SaveWorkoutMainFragment();

        Bundle args = new Bundle();
        args.putParcelable(STTConstants.ExtraKeys.WORKOUT_HEADER, workoutHeader);
        if (targetWorkout != null) {
            args.putParcelable(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER, targetWorkout);
        }
        fragment.setArguments(args);

        return fragment;
    }

    private final Runnable saveWorkoutForDescriptionRunnable = new Runnable() {
        @Override
        public void run() {
            //SharingOptionsFragment inside getWorkoutHeader may be null.
            //Catch it until we find better solution. We catch it here because
            //SharingOptionsFragment seems to be null only when called inside this runnable for
            //descriptionTextchanges.
            try {
                WorkoutHeader workoutHeader = getWorkoutHeader();
                SaveWorkoutHeaderService.enqueueWork(getContext(), workoutHeader, false);
            } catch (NullPointerException e) {
                Timber.w(e, "Workout header saving failed" );
            }
        }
    };

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
        Bundle savedInstanceState) {
        binding = SaveWorkoutMainFragmentBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding.descriptionEditText.removeCallbacks(saveWorkoutForDescriptionRunnable);
        binding = null;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        binding.descriptionEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                descriptionChanged();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        final WorkoutHeader workoutHeader = super.getWorkoutHeader();
        final WorkoutHeader targetWorkout =
            getArguments().getParcelable(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER);

        FragmentManager fm = getChildFragmentManager();
        FragmentTransaction ft = fm.beginTransaction();

        boolean hasWorkoutPoints = workoutHeader.getStartPosition() != null;
        if (hasWorkoutPoints) {
            if (fm.findFragmentById(R.id.miniMapContainer) == null) {
                Fragment fragment;
                String tag;
                if (targetWorkout != null) {
                    fragment = StaticWorkoutMiniMapComparisonFragment.newInstance(
                        workoutHeader,
                        targetWorkout,
                        AnalyticsPropertyValue.BuyPremiumPopupShownSource.SAVE_RECORDED_WORKOUT_MAP
                    );
                    tag = StaticWorkoutMiniMapComparisonFragment.FRAGMENT_TAG;
                } else {
                    fragment = StaticWorkoutMiniMapFragment.newInstance(
                        workoutHeader,
                        AnalyticsPropertyValue.BuyPremiumPopupShownSource.SAVE_RECORDED_WORKOUT_MAP
                    );
                    tag = StaticWorkoutMiniMapFragment.FRAGMENT_TAG;
                }
                ft.add(R.id.miniMapContainer, fragment, tag);
            }
            binding.miniMapContainer.setVisibility(View.VISIBLE);
        }

        int imagePickerContainerId =
            targetWorkout == null ? R.id.picturePickerContainerWithoutComparison
                : R.id.picturePickerContainerWithComparison;
        getView().findViewById(imagePickerContainerId).setVisibility(View.VISIBLE);
        if (fm.findFragmentByTag(WorkoutEditMediaPickerFragment.FRAGMENT_TAG) == null) {
            ft.add(imagePickerContainerId, WorkoutEditMediaPickerFragment.newInstance(workoutHeader, true),
                WorkoutEditMediaPickerFragment.FRAGMENT_TAG);
        }

        // Only show the comparison if there was a target workout and if there's at least a point
        // recorded
        if (targetWorkout != null && hasWorkoutPoints) {
            getView().findViewById(R.id.workoutComparisonContainer).setVisibility(View.VISIBLE);
            if (fm.findFragmentByTag(WorkoutABGraphFragment.FRAGMENT_TAG) == null) {
                ft.add(R.id.workoutComparisonContainer,
                    WorkoutABGraphFragment.newInstance(workoutHeader, targetWorkout),
                    WorkoutABGraphFragment.FRAGMENT_TAG);
            }
        }

        if (fm.findFragmentByTag(WorkoutHeadersFragment.FRAGMENT_TAG) == null) {
            ft.add(R.id.workoutSummaryContainer,
                WorkoutHeadersFragment.newInstance(workoutHeader, targetWorkout),
                WorkoutHeadersFragment.FRAGMENT_TAG);
        }

        if (fm.findFragmentByTag(SharingOptionsFragment.FRAGMENT_TAG) == null) {
            ft.add(R.id.workoutSharingOptionsContainer,
                SharingOptionsFragment.newInstance(workoutHeader, false, false, false),
                SharingOptionsFragment.FRAGMENT_TAG);
        }

        boolean showTrend =
            !workoutHeader.getActivityType().isIndoor() && !workoutHeader.isPolylineEmpty();
        if (fm.findFragmentByTag(RecentWorkoutTrendFragment.FRAGMENT_TAG) == null && showTrend) {
            ft.add(R.id.recentWorkoutTrendContainer,
                RecentWorkoutTrendFragment.newInstance(workoutHeader),
                RecentWorkoutTrendFragment.FRAGMENT_TAG);
        } else if (!showTrend) {
            getView().findViewById(R.id.recentWorkoutTrendContainer).setVisibility(View.GONE);
        }

        boolean showSimilar =
            !workoutHeader.isPolylineEmpty() || workoutHeader.getTotalDistance() > 0.0;
        if (fm.findFragmentByTag(SimilarWorkoutsFragment.FRAGMENT_TAG) == null && showSimilar) {
            ft.add(R.id.similarWorkoutsContainer,
                SimilarWorkoutsFragment.newInstance(workoutHeader),
                SimilarWorkoutsFragment.FRAGMENT_TAG);
        } else if (!showSimilar) {
            getView().findViewById(R.id.similarWorkoutsContainer).setVisibility(View.GONE);
        }

        if (fm.findFragmentByTag(RecentWorkoutSummaryFragment.FRAGMENT_TAG) == null) {
            ft.add(R.id.recentWorkoutSummaryContainer,
                RecentWorkoutSummaryFragment.newInstance(workoutHeader),
                RecentWorkoutSummaryFragment.FRAGMENT_TAG);
        }

        ft.commit();
    }

    /**
     * @return WorkoutHeader
     * May throw NullPointerException if SharingOptionsFragment is not found
     */
    @Override
    public WorkoutHeader getWorkoutHeader() {
        String description = binding != null ? binding.descriptionEditText.getText().toString() : "";
        String owner = currentUserController.getUsername();
        int hrMaximum = userSettingsController.getSettings().getHrMaximum();
        SharingOptionsFragment sharingOptionsFragment =
            (SharingOptionsFragment) getChildFragmentManager().findFragmentByTag(
                SharingOptionsFragment.FRAGMENT_TAG);

        WorkoutHeader workoutHeader = super.getWorkoutHeader();

        int pictureCount = workoutHeader.getPictureCount();
        WorkoutEditMediaPickerFragment pickerFragment = getWorkoutEditMediaPickerFragment();
        if (pickerFragment != null) {
            pictureCount = pickerFragment.getPictureCount();
        }

        return workoutHeader
            .toBuilder()
            .sharingFlags(sharingOptionsFragment.getSharingFlags())
            .userName(owner)
            .description(description)
            .heartRateUserSetMax(hrMaximum)
            .locallyChanged(true)
            .pictureCount(pictureCount)
            .build();
    }

    @Override
    protected void onWorkoutHeaderUpdated(WorkoutHeader updateWorkoutHeader) {
        // do nothing
    }

    @Override
    public void onStop() {
        super.onStop();
        binding.descriptionEditText.removeCallbacks(saveWorkoutForDescriptionRunnable);
    }

    @Nullable
    private WorkoutEditMediaPickerFragment getWorkoutEditMediaPickerFragment() {
        FragmentManager fm = getChildFragmentManager();
        return (WorkoutEditMediaPickerFragment) fm.findFragmentByTag(
            WorkoutEditMediaPickerFragment.FRAGMENT_TAG);
    }

    // We need to save description changes before we create a share. Otherwise the description
    // will be empty/default from backend
    private void descriptionChanged() {
        if (binding != null) {
            binding.descriptionEditText.removeCallbacks(saveWorkoutForDescriptionRunnable);
            binding.descriptionEditText.postDelayed(saveWorkoutForDescriptionRunnable,
                DESCRIPTION_UPDATE_DELAY);
        }
    }
}
