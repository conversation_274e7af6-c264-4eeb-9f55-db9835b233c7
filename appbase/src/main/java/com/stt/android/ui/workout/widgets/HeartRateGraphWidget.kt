package com.stt.android.ui.workout.widgets

import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.stt.android.R
import com.stt.android.controllers.UserSettingsController
import com.stt.android.hr.HeartRateZone
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.components.ZoneDurationsView
import com.stt.android.ui.components.charts.ZonedChartView
import com.stt.android.ui.utils.HeartRateChartColorUtil
import com.stt.android.ui.utils.getZoneDurationsFromHrEvents
import com.stt.android.utils.ZoneDurationUtils
import javax.inject.Inject
import kotlin.time.Duration.Companion.milliseconds
import com.stt.android.core.R as CR

const val NO_VALUE_TEXT = "- -"

class HeartRateGraphWidget @Inject constructor(
    localBM: LocalBroadcastManager,
    userSettingsController: UserSettingsController,
    private val infoModelFormatter: InfoModelFormatter
) : UiUpdateWorkoutWidget(localBM) {

    lateinit var avgHeartRateView: TextView
    lateinit var currentHeartRateView: TextView
    lateinit var hrZonedChartView: ZonedChartView
    lateinit var hrZoneDurationsView: ZoneDurationsView

    private val colorLimits: IntArray
    private val chartLimits: List<Float>
    private val durationLimits: List<Float>
    private var durations: List<Long>

    private lateinit var colors: IntArray

    private var currentHeartRateColor = 0
    private var avgHeartRateColor = 0

    private var processedCount = 0

    init {
        val hrMax = userSettingsController.settings.hrMaximum

        colorLimits = intArrayOf(
            HeartRateZone.WARMUP.getHighBpm(hrMax),
            HeartRateZone.ENDURANCE.getHighBpm(hrMax),
            HeartRateZone.AEROBIC.getHighBpm(hrMax),
            HeartRateZone.ANAEROBIC.getHighBpm(hrMax)
        )

        chartLimits = intArrayOf(hrMax / 2)
            .plus(colorLimits)
            .plus(hrMax)
            .toList()
            .map { it.toFloat() }

        // Count everything below zone 2 to first zone duration
        durationLimits = listOf(0f).plus(chartLimits.subList(1, chartLimits.size))

        durations = getZoneDurationsFromHrEvents(emptyList(), durationLimits)
    }

    override fun getLayoutId() = R.layout.heart_rate_graph_widget

    override fun onViewInflated() {
        avgHeartRateView = view.findViewById(R.id.avgHeartRate)
        currentHeartRateView = view.findViewById(R.id.currentHeartRate)
        hrZonedChartView = view.findViewById(R.id.hrZonedChart)
        hrZoneDurationsView = view.findViewById(R.id.hrZoneDurations)

        colors = intArrayOf(
            ContextCompat.getColor(context, HeartRateZone.WARMUP.color),
            ContextCompat.getColor(context, HeartRateZone.ENDURANCE.color),
            ContextCompat.getColor(context, HeartRateZone.AEROBIC.color),
            ContextCompat.getColor(context, HeartRateZone.ANAEROBIC.color),
            ContextCompat.getColor(context, HeartRateZone.PEAK.color)
        )

        hrZonedChartView.setLimitsAndThresholds(chartLimits, emptyList())
        hrZonedChartView.setShowXLabels(false)

        val zoneDurationData = ZoneDurationUtils.createZoneDurations(
            durations = durations.map { it.milliseconds },
            infoModelFormatter = infoModelFormatter,
        )
        hrZoneDurationsView.setZoneDurations(zoneDurationData)

        initializeChart()
    }

    override fun onInit() {
        // do nothing
    }

    override fun onStarted() {
        // do nothing
    }

    override fun onStopped() {
        // do nothing
    }

    override fun onUpdate() {
        updateHeartRateTexts()
        processHrEvents()
    }

    private fun updateHeartRateTexts() {
        val rws = serviceConnection.recordWorkoutService ?: return

        val avgHeartRate = rws.averageHeartRate
        if (avgHeartRate > 0) {
            val heartRateColor = getColorForHeartRate(avgHeartRate)
            if (heartRateColor != avgHeartRateColor) {
                avgHeartRateColor = heartRateColor
                avgHeartRateView.setTextColor(avgHeartRateColor)
            }
            avgHeartRateView.text = avgHeartRate.toString()
        } else {
            avgHeartRateView.text = NO_VALUE_TEXT
        }

        val currentHeartRate = rws.currentHeartRate
        if (currentHeartRate > 0) {
            val heartRateColor = getColorForHeartRate(currentHeartRate)
            if (heartRateColor != currentHeartRateColor) {
                currentHeartRateColor = heartRateColor
                currentHeartRateView.setTextColor(currentHeartRateColor)
            }
            currentHeartRateView.text = currentHeartRate.toString()
        } else {
            currentHeartRateView.text = NO_VALUE_TEXT
        }
    }

    private fun processHrEvents() {
        val hrEvents = serviceConnection.recordWorkoutService?.currentHeartRateEvents ?: return

        val hrColors = mutableListOf<Int>()
        val lineData = hrZonedChartView.chart.lineData
        val lineDataSet = lineData.getDataSetByIndex(0)

        // hrEvents is a synchronizedList that must be synchronized when iterating over it.
        synchronized(hrEvents) {
            val newHrEvents = hrEvents.subList(Math.max(processedCount - 1, 0), hrEvents.size)
            val newDurations = getZoneDurationsFromHrEvents(newHrEvents, durationLimits)
            durations = durations.zip(newDurations).map { it.first + it.second }

            for (i in processedCount..hrEvents.lastIndex) {
                val hrEvent = hrEvents[i]
                val hr = hrEvent.heartRate
                lineDataSet.addEntry(Entry(i.toFloat(), hr.toFloat()))
                hrColors.add(HeartRateChartColorUtil.getColorForValue(hr, colorLimits, colors))
            }

            processedCount = hrEvents.size
        }

        lineDataSet.colors.addAll(hrColors)

        lineData.notifyDataChanged()

        val zoneDurationData = ZoneDurationUtils.createZoneDurations(
            durations = durations.map { it.milliseconds },
            infoModelFormatter = infoModelFormatter,
        )
        hrZoneDurationsView.setZoneDurations(zoneDurationData)
        hrZonedChartView.chart.notifyDataSetChanged()
        hrZonedChartView.chart.invalidate()
    }

    private fun initializeChart() {
        val resources = context.resources
        val lineData = LineData()
        val lineDataSet = LineDataSet(
            null,
            String.format(
                "%s (%s)",
                resources.getString(CR.string.heart_rate_capital),
                resources.getString(CR.string.bpm_capital)
            )
        )
            .apply {
                setDrawFilled(false)
                setDrawValues(false)
                setDrawCircles(false)
                setDrawHorizontalHighlightIndicator(false)
                isHighlightEnabled = false
            }

        lineData.addDataSet(lineDataSet)

        with(hrZonedChartView.chart) {
            axisLeft.axisMinimum = chartLimits.first()
            axisLeft.axisMaximum = chartLimits.last()
            data = lineData
        }
    }

    @ColorInt
    private fun getColorForHeartRate(heartRate: Int): Int {
        return colorLimits.indices
            .firstOrNull { heartRate < colorLimits[it] }
            ?.let { colors[it] }
            ?: colors.last()
    }
}
