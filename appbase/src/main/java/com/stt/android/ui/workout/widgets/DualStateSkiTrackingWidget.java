package com.stt.android.ui.workout.widgets;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.stt.android.R;
import com.stt.android.controllers.UserSettingsController;

public abstract class DualStateSkiTrackingWidget extends DualStateWorkoutWidget {

    protected final UserSettingsController userSettingsController;

    DualStateSkiTrackingWidget(LocalBroadcastManager localBM,
        UserSettingsController userSettingsController) {
        super(localBM);
        this.userSettingsController = userSettingsController;
    }

    protected abstract double getPrimaryValue();

    protected abstract double getSecondaryValue();

    protected abstract int getLabelResource();

    protected abstract String formatValue(double value);

    protected abstract String getUnit();

    @Override
    protected void onViewInflated() {
        super.onViewInflated();
        label.setText(getLabelResource());
        setIsPrimaryAndUpdate(true);
    }

    @Override
    protected void onUpdatePrimary() {
        int textColor = getTextColor();
        value.setTextColor(textColor);
        value.setText(formatValue(getPrimaryValue()));
        unit.setTextColor(textColor);
        unit.setText(getUnit());
    }

    @Override
    protected void onUpdateSecondary() {
        int textColor = getTextColor();
        value.setTextColor(textColor);
        value.setText(formatValue(getSecondaryValue()));
        unit.setTextColor(textColor);
        unit.setText(getUnit());
    }

    @Override
    protected int getLayoutId() {
        return R.layout.tracking_widget_with_unit;
    }

    @Override
    public void onInit() {
        onUpdate();
    }

    @Override
    public void onStarted() {
        onUpdate();
    }

    @Override
    public void onStopped() {
        onUpdate();
    }

    @Override
    protected int getWidgetLabelId() {
        return R.id.label;
    }
}
