package com.stt.android.maps

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import androidx.annotation.MainThread
import androidx.appcompat.content.res.AppCompatResources
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.maps.android.PolyUtil
import com.stt.android.R
import com.stt.android.cardlist.PolylineType
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.Point
import com.stt.android.domain.routes.GetRouteUseCase
import com.stt.android.domain.routes.Route
import com.stt.android.domain.routes.TopRoute
import com.stt.android.domain.routes.WaypointTools
import com.stt.android.domain.workouts.GetWorkoutHeaderByIdUseCase
import com.stt.android.domain.workouts.isDiving
import com.stt.android.maps.delegate.MapSnapshotterDelegate
import com.stt.android.maps.mapbox.MapboxMapsProvider
import com.stt.android.maps.snapshotter.SuuntoMapSnapshot
import com.stt.android.maps.snapshotter.SuuntoMapSnapshotterOptions
import com.stt.android.routes.toLatLng
import com.stt.android.ui.map.MapCacheHelper
import com.stt.android.ui.map.MapHelper
import com.stt.android.utils.STTConstants
import com.stt.android.utils.traceSuspend
import com.suunto.algorithms.geo.DistanceCalculator
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.supervisorScope
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.lang.ref.WeakReference
import javax.inject.Inject
import javax.inject.Singleton
import com.stt.android.core.R as CoreR
import com.suunto.algorithms.geo.LatLng as SuuntoLatLng

/**
 * A singleton [MapSnapshotter] object for orchestrating generating map snapshots.
 *
 * In order to integrate this into an UI, the [runSnapshotterEngine] method needs to be called from
 * the UI code. Typically this would be called by launching a coroutine using a fragment's view
 * lifecycle scope.
 *
 * [runSnapshotterEngine] intentionally never completes and keeps listening for any new map snapshot
 * requests when it runs out of work to do. It should be then cancelled by the coroutine scope when
 * the fragment's view is destroyed.
 *
 * Notice that [MapSnapshotBinder] has a similar execution flow with its own tasks.
 * [MapSnapshotBinder] will add a map snapshot request to here when it needs to bind a snapshot
 * and it is not found in cache.
 */
@Singleton
class MapSnapshotter @Inject constructor(
    private val getWorkoutHeaderByIdUseCase: GetWorkoutHeaderByIdUseCase,
    private val getRouteUseCase: GetRouteUseCase,
    private val waypointTools: WaypointTools,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    internal val binder = MapSnapshotBinder()

    init {
        instance = this
    }

    private val cache = MapCacheHelper()
    private val requestQueue = MapSnapshotRequestQueue(cache)
    private val mapViews = MutableStateFlow<List<WeakReference<SuuntoMapView>>>(emptyList())

    private var logoDrawable: Drawable? = null
    private var logoMargin = 0
    private var routePadding = 0
    private var routeStrokeWidth = 0.0f

    /**
     * Add a [SuuntoMapView] to be used for snapshot generation. This is only required when
     * [MapsProvider.requiresMapViewForSnapshotting] is true (e.g. Google Maps).
     */
    @MainThread
    fun addMapView(mapView: SuuntoMapView) {
        // Add weak reference to given view
        if (!mapViews.tryEmit(mapViews.value + WeakReference(mapView))) {
            Timber.w("Failed to emit new map view list when adding map view")
        }
    }

    @MainThread
    fun removeMapView(mapView: SuuntoMapView) {
        // Remove cleared weak references and given SuuntoMapView
        val views = mapViews.value.filter {
            val ref = it.get()
            ref != null && ref != mapView
        }
        if (!mapViews.tryEmit(views)) {
            Timber.w("Failed to emit new map view list when removing map view")
        }
    }

    /**
     * Let the snapshotter know that [SuuntoMapView] size has changed and it is now serving
     * snapshots in a different size. This is only required when
     * [MapsProvider.requiresMapViewForSnapshotting] is true (e.g. Google Maps).
     */
    @MainThread
    fun updateMapViewSize(mapView: SuuntoMapView) {
        // Remove cleared weak references and given SuuntoMapView
        val views = mapViews.value.filter {
            val ref = it.get()
            ref != null && ref != mapView
        }
        // Add the same view back
        // It may be that the list content is exactly the same as before, but [mapViews] flow emits
        // a new item which wakes up any requests waiting for a map view with the correct size.
        if (!mapViews.tryEmit(views + WeakReference(mapView))) {
            Timber.w("Failed to emit new map view list when map view size changed")
        }
    }

    // Don't cache, the default provider can change in ST with Premium
    private val defaultProvider: MapsProvider
        get() = SuuntoMaps.defaultProvider!!

    private fun MapSnapshotSpec.getProvider(): MapsProvider =
        explicitProviderName?.let {
            SuuntoMaps.getProvider(it)
        } ?: defaultProvider

    private suspend fun createSnapshot(spec: MapSnapshotSpec, context: Context): SuuntoMapSnapshot {
        Timber.d("Create snapshot: $spec")

        val provider = spec.getProvider()

        val mapView: SuuntoMapView? = if (provider.requiresMapViewForSnapshotting) {
            // Find a SuuntoMapView with correct size. Suspend until found.
            mapViews
                .map { views ->
                    views.firstOrNull { mapViewRef ->
                        mapViewRef.get()
                            ?.run { width == spec.width && height == spec.height }
                            ?: false
                    }?.get()
                }
                .firstOrNull()
                ?: throw IllegalArgumentException("No map views can be used to create snapshot for '$spec'")
        } else {
            null
        }

        val options = MapSnapshotOptionsUtils.createMapSnapshotterOptions(
            spec = spec,
            mapView = mapView
        )

        return traceSuspend("createSnapshot") {
            when (spec) {
                is MapSnapshotSpec.Sample ->
                    createSampleMapSnapshot(provider, spec, options, context)

                is MapSnapshotSpec.Workout ->
                    createWorkoutMapSnapshot(provider, spec, options, context)

                is MapSnapshotSpec.Route ->
                    createRouteMapSnapshot(provider, spec, options, context)

                is MapSnapshotSpec.WorkoutPolyline ->
                    createWorkoutPolylineMapSnapshot(provider, spec, options, context)

                is MapSnapshotSpec.Polylines ->
                    createCoordinatesMapSnapshot(provider, spec, options, context)

                is MapSnapshotSpec.ColorfulPolylines ->
                    createColorfulWorkoutMapSnapshot(provider, spec, options, context)

                is MapSnapshotSpec.DashboardMapWidget ->
                    createDashboardMapSnapshot(provider, spec, options, context)

                is MapSnapshotSpec.PopularRoute ->
                    createPopularRouteMapSnapshot(provider, spec, options, context)
            }.apply {
                if (provider.requiresLogoOverlay && spec.requiresLogoOverlay) {
                    addLogo(this, logoDrawable)
                }
            }
        }
    }

    private suspend fun createSampleMapSnapshot(
        provider: MapsProvider,
        spec: MapSnapshotSpec.Sample,
        options: SuuntoMapSnapshotterOptions,
        context: Context,
    ): SuuntoMapSnapshot = withContext(coroutinesDispatchers.io) {
        options.shouldHideMap = spec.shouldHideMap

        createPolylineSnapshot(
            provider = provider,
            route = SAMPLE_ROUTE,
            options = options,
            context = context,
            explicitMapType = spec.explicitMapType,
            explicitRoutePadding = spec.explicitRoutePadding,
            indicateHiddenRoute = true,
            hiddenDistanceStartEnd = spec.hiddenDistanceStartEnd,
        )
    }

    private suspend fun createWorkoutMapSnapshot(
        provider: MapsProvider,
        spec: MapSnapshotSpec.Workout,
        options: SuuntoMapSnapshotterOptions,
        context: Context,
    ): SuuntoMapSnapshot = withContext(coroutinesDispatchers.io) {
        Timber.d("Creating map snapshot for workout ID ${spec.workoutId}, size=${spec.width}x${spec.height}")
        val workoutHeader = getWorkoutHeaderByIdUseCase(spec.workoutId)
            ?: throw IllegalArgumentException("Workout (id=${spec.workoutId}) doesn't exist")

        options.disableZoomToBounds = workoutHeader.isDiving

        val route = workoutHeader.polyline
            ?.takeUnless(String::isEmpty)
            ?.let(PolyUtil::decode)
            ?: emptyList()

        createPolylineSnapshot(
            provider = provider,
            route = route,
            options = options,
            context = context,
            explicitMapType = spec.explicitMapType,
            explicitRoutePadding = spec.explicitRoutePadding
        )
    }

    private suspend fun createColorfulWorkoutMapSnapshot(
        provider: MapsProvider,
        spec: MapSnapshotSpec.ColorfulPolylines,
        options: SuuntoMapSnapshotterOptions,
        context: Context,
    ): SuuntoMapSnapshot {
        val workoutHeader = getWorkoutHeaderByIdUseCase(spec.workoutId)
            ?: throw IllegalArgumentException("Workout (id=${spec.workoutId}) doesn't exist")

        options.disableZoomToBounds = workoutHeader.isDiving

        val bounds = spec.colorfulTrackMapData.bounds
        if (options.disableZoomToBounds || bounds.northeast == bounds.southwest) {
            options.cameraPosition(
                SuuntoCameraOptions.fromLatLngZoom(
                    bounds.northeast,
                    STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
                )
            )
        } else {
            options.latLngBounds(bounds, spec.explicitRoutePadding ?: routePadding)
        }

        val partitionedTracksPoints = spec.colorfulTrackMapData.activityRoutesWithColor
        if (partitionedTracksPoints.isNotEmpty()) {
            partitionedTracksPoints.forEach {
                options.addColorfulRoute(
                    it.points.map { point -> point.latLng },
                    it.color,
                    context,
                )
            }
            spec.colorfulTrackMapData.nonActivityRoutesWithColor.forEach {
                options.addColorfulRoute(
                    it.points.map { point -> point.latLng },
                    it.color,
                    context,
                )
            }
        } else {
            spec.colorfulTrackMapData.activityRoutes.forEach {
                options.addRoute(
                    points = it,
                    polylineType = PolylineType.WORKOUT,
                    context = context
                )
            }
            spec.colorfulTrackMapData.nonActivityRoutes.forEach {
                options.addRoute(
                    points = it,
                    polylineType = PolylineType.WORKOUT,
                    context = context
                )
            }
        }

        options.addStartEndMarkers(
            startPoint = spec.colorfulTrackMapData.startPoint,
            endPoint = spec.colorfulTrackMapData.endPoint,
            polylineType = PolylineType.WORKOUT,
            context = context,
        )

        spec.explicitMapType?.name
            ?.let(options::mapType)

        val snapshotter = provider.createMapSnapshotterDelegate(context, options)
        return snapshotter.snapshot()
    }

    private suspend fun createWorkoutPolylineMapSnapshot(
        provider: MapsProvider,
        spec: MapSnapshotSpec.WorkoutPolyline,
        options: SuuntoMapSnapshotterOptions,
        context: Context
    ): SuuntoMapSnapshot {
        Timber.d("Creating map snapshot for workout polylineHash=${spec.polylineHash}, size=${spec.width}x${spec.height}")
        options.disableZoomToBounds = spec.disableZoomToBounds

        val points = if (!spec.polyline.isNullOrEmpty()) {
            PolyUtil.decode(spec.polyline).filterNot { point ->
                point.latitude == 0.0 && point.longitude == 0.0
            }
        } else {
            emptyList()
        }

        return createPolylineSnapshot(
            provider = provider,
            route = points,
            options = options,
            context = context,
            explicitMapType = spec.explicitMapType,
            explicitRoutePadding = null
        )
    }

    private suspend fun createPolylineSnapshot(
        provider: MapsProvider,
        route: List<LatLng>,
        options: SuuntoMapSnapshotterOptions,
        context: Context,
        explicitMapType: MapType?,
        explicitRoutePadding: Int?,
        indicateHiddenRoute: Boolean = false,
        hiddenDistanceStartEnd: Int = 0,
    ): SuuntoMapSnapshot {
        fun SuuntoMapSnapshotterOptions.updateSettings(
            bounds: LatLngBounds,
            startRoute: List<LatLng>,
            middleRoute: List<LatLng>,
            endRoute: List<LatLng>,
            startPoint: LatLng,
            endPoint: LatLng,
        ) {
            if (options.disableZoomToBounds || bounds.northeast == bounds.southwest) {
                options.cameraPosition(
                    SuuntoCameraOptions.fromLatLngZoom(
                        bounds.northeast,
                        STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
                    )
                )
            } else {
                latLngBounds(bounds, explicitRoutePadding ?: routePadding)
            }

            if (startRoute.size >= 2) {
                addRoute(
                    points = startRoute,
                    colorRes = CoreR.color.darkest_grey,
                    context = context,
                )
            }

            if (middleRoute.size >= 2) {
                addRoute(
                    points = middleRoute,
                    polylineType = PolylineType.WORKOUT,
                    context = context
                )
            }

            if (endRoute.size >= 2) {
                addRoute(
                    points = endRoute,
                    colorRes = CoreR.color.darkest_grey,
                    context = context,
                )
            }

            addStartEndMarkers(
                startPoint = startPoint,
                endPoint = endPoint,
                polylineType = PolylineType.WORKOUT,
                context = context
            )
        }

        if (route.isNotEmpty()) {
            if (hiddenDistanceStartEnd > 0) {
                val (startRoute, middleRoute, endRoute) = splitRouteByHiddenDistanceStartEnd(
                    route,
                    hiddenDistanceStartEnd,
                    hiddenDistanceStartEnd,
                )
                if (indicateHiddenRoute) {
                    options.updateSettings(
                        bounds = route.toBounds(),
                        startRoute = startRoute,
                        middleRoute = middleRoute,
                        endRoute = endRoute,
                        startPoint = route.first(),
                        endPoint = route.last(),
                    )
                } else {
                    options.updateSettings(
                        bounds = middleRoute.toBounds(),
                        startRoute = emptyList(),
                        middleRoute = middleRoute,
                        endRoute = emptyList(),
                        startPoint = middleRoute.first(),
                        endPoint = middleRoute.last(),
                    )
                }
            } else {
                options.updateSettings(
                    bounds = route.toBounds(),
                    startRoute = emptyList(),
                    middleRoute = route,
                    endRoute = emptyList(),
                    startPoint = route.first(),
                    endPoint = route.last(),
                )
            }
        }

        explicitMapType?.name
            ?.let(options::mapType)

        val snapshotter = provider.createMapSnapshotterDelegate(context, options)
        return snapshotter.snapshot()
    }

    private fun LatLng.toSuuntoLatLng() = SuuntoLatLng(latitude, longitude)

    private fun List<LatLng>.toBounds() = LatLngBounds.builder().apply {
        forEach { include(it) }
    }.build()

    /**
     * @param route must not be empty
     *
     * @return all the three route are all not empty.
     * If middle route is less than 2 points, the center point of the route is returned for map pin.
     */
    private fun splitRouteByHiddenDistanceStartEnd(
        route: List<LatLng>,
        hiddenDistanceStart: Int,
        hiddenDistanceEnd: Int,
    ): Triple<List<LatLng>, List<LatLng>, List<LatLng>> {
        var remainingRoute = route

        var startDistanceSum = 0.0
        var current = remainingRoute.first()
        val startRoute = remainingRoute.takeWhile {
            startDistanceSum += DistanceCalculator.distanceBetween(
                current.toSuuntoLatLng(),
                it.toSuuntoLatLng(),
            ).inMeters
            current = it
            startDistanceSum <= hiddenDistanceStart
        }

        remainingRoute = remainingRoute.drop(startRoute.size - 1)

        var endDistanceSum = 0.0
        current = remainingRoute.last()
        val endRoute = remainingRoute.takeLastWhile {
            endDistanceSum += DistanceCalculator.distanceBetween(
                current.toSuuntoLatLng(),
                it.toSuuntoLatLng(),
            ).inMeters
            current = it
            endDistanceSum <= hiddenDistanceEnd
        }

        remainingRoute = remainingRoute.dropLast(endRoute.size - 1)

        val middleRoute = if (remainingRoute.size < 2) {
            listOf(route.toBounds().center)
        } else remainingRoute

        return Triple(startRoute, middleRoute, endRoute)
    }

    private suspend fun createRouteMapSnapshot(
        provider: MapsProvider,
        spec: MapSnapshotSpec.Route,
        options: SuuntoMapSnapshotterOptions,
        context: Context
    ): SuuntoMapSnapshot {
        Timber.d("Creating map snapshot for route ID ${spec.routeId}, modified=${spec.segmentsModifiedDateMillis}, size=${spec.width}x${spec.height}")
        val route: Route = getRouteUseCase.getRoute(spec.routeId)
            ?: throw IllegalArgumentException("No route for id: ${spec.routeId}")
        val points: List<Point> = route.segments
            .fold(emptyList()) { list, segment ->
                list + segment.routePoints
            }

        val bounds = if (points.size > 1) {
            LatLngBounds.builder().apply {
                for (segment in route.segments) {
                    for (point in segment.routePoints) {
                        include(point.toLatLng())
                    }
                }
            }.build()
        } else {
            null
        }

        if (bounds != null) {
            if (options.disableZoomToBounds || bounds.northeast == bounds.southwest) {
                options.cameraPosition(
                    SuuntoCameraOptions.fromLatLngZoom(
                        bounds.northeast,
                        STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
                    )
                )
            } else {
                options.latLngBounds(bounds, routePadding)
            }

            val latLngs = points.map(Point::toLatLng)

            options.addRoute(
                points = latLngs,
                polylineType = PolylineType.ROUTE,
                context = context
            )

            if (provider.snapshottingSupportsWaypoints) {
                options.addWaypoints(
                    route = points,
                    showTurnByTurnWaypoints = spec.showTurnByTurnWaypoints,
                    waypointTools = waypointTools,
                    context = context
                )
            }

            options.addStartEndMarkers(
                startPoint = latLngs.firstOrNull(),
                endPoint = latLngs.lastOrNull(),
                polylineType = PolylineType.ROUTE,
                context = context,
            )
        }

        val snapshotter = provider.createMapSnapshotterDelegate(context, options)
        return snapshotter.snapshot()
    }

    private suspend fun createCoordinatesMapSnapshot(
        provider: MapsProvider,
        spec: MapSnapshotSpec.Polylines,
        options: SuuntoMapSnapshotterOptions,
        context: Context
    ): SuuntoMapSnapshot {
        Timber.d("Creating map snapshot for polylines ID ${spec.id}, count=${spec.coordinates.size}, size=${spec.width}x${spec.height}")
        val polylines: List<List<LatLng>> = spec.coordinates
        val allPoints = polylines.flatten()
        val bounds = if (allPoints.size > 1) {
            LatLngBounds.builder().apply {
                for (point in allPoints) {
                    include(point)
                }
            }.build()
        } else {
            null
        }

        if (bounds != null) {
            if (options.disableZoomToBounds || bounds.northeast == bounds.southwest) {
                options.cameraPosition(
                    SuuntoCameraOptions.fromLatLngZoom(
                        bounds.northeast,
                        STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
                    )
                )
            } else {
                options.latLngBounds(bounds, routePadding)
            }

            polylines.forEach { points ->
                options.addPolyline(
                    points = points,
                    colorRes = spec.colorRes,
                    lineWidthPx = spec.lineWidthPx,
                    context = context
                )
            }
        }

        return provider.createMapSnapshotterDelegate(context, options).snapshot()
    }

    private suspend fun createDashboardMapSnapshot(
        provider: MapsProvider,
        spec: MapSnapshotSpec.DashboardMapWidget,
        options: SuuntoMapSnapshotterOptions,
        context: Context,
    ): SuuntoMapSnapshot {
        val snapshotInfo = spec.snapshotInfo
        snapshotInfo.mapType?.name
            ?.let { options.mapType = it }
        snapshotInfo.latLngBounds?.let {
            options.latLngBounds(it, snapshotInfo.padding)
        }
        snapshotInfo.cameraOptions?.let {
            options.cameraPosition(it)
        }
        options.addMarkers(snapshotInfo.markers)
        options.addPolyline(snapshotInfo.polylines)

        val snapshotter = provider.createMapSnapshotterDelegate(context, options)
        return snapshotter.snapshot()
    }

    private suspend fun createPopularRouteMapSnapshot(
        provider: MapsProvider,
        spec: MapSnapshotSpec.PopularRoute,
        options: SuuntoMapSnapshotterOptions,
        context: Context
    ): SuuntoMapSnapshot {
        Timber.d("Creating map snapshot for popular route id = ${spec.route.id} ,modified=${spec.segmentsModifiedDateMillis}, size=${spec.width}x${spec.height}")
        val route: TopRoute = spec.route
        val points = route.segments.flatMap { it.routePoints }

        val bounds = if (points.size > 1) {
            LatLngBounds.builder().apply {
                for (segment in route.segments) {
                    for (point in segment.routePoints) {
                        include(point.toLatLng())
                    }
                }
            }.build()
        } else {
            null
        }

        if (bounds != null) {
            if (options.disableZoomToBounds || bounds.northeast == bounds.southwest) {
                options.cameraPosition(
                    SuuntoCameraOptions.fromLatLngZoom(
                        bounds.northeast,
                        STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
                    )
                )
            } else {
                val popularRoutePadding =
                    context.resources.getDimensionPixelSize(R.dimen.map_popular_route_padding)
                options.latLngBounds(bounds, popularRoutePadding)
            }

            val latLngs = points.map(Point::toLatLng)

            options.addRoute(
                points = latLngs,
                polylineType = PolylineType.ROUTE,
                context = context
            )
        }

        val snapshotter = provider.createMapSnapshotterDelegate(context, options)
        return snapshotter.snapshot()
    }

    // Wrap callback based snapshot creation as a suspending function
    internal suspend fun MapSnapshotterDelegate.snapshot(): SuuntoMapSnapshot =
        withContext(coroutinesDispatchers.main) {
            suspendCancellableCoroutine { cont ->

                start(
                    { cont.resumeWith(Result.success(it)) },
                    { cont.resumeWith(Result.failure(RuntimeException("Map snapshot failed: $it"))) }
                )

                cont.invokeOnCancellation {
                    cancel()
                }
            }
        }

    internal suspend fun getFromCache(spec: MapSnapshotSpec, context: Context): Drawable? =
        getBitmapFromCache(spec)?.toDrawable(context)

    suspend fun getBitmapFromCache(spec: MapSnapshotSpec): Bitmap? =
        cache.load(spec)

    internal suspend fun requestSnapshot(spec: MapSnapshotSpec, context: Context): Drawable =
        requestSnapshotBitmap(spec).toDrawable(context)

    suspend fun requestSnapshotBitmap(spec: MapSnapshotSpec): Bitmap {
        requestQueue.add(spec)
        val res = waitUntilReady(spec)

        return if (res is SnapshotResult.Success) {
            res.bitmap
        } else {
            Timber.w("requestSnapshotBitmap failed: $res")
            throw (res as? SnapshotResult.Failure)?.throwable
                ?: NullPointerException("requestSnapshotBitmap failed with null error")
        }
    }

    internal suspend fun cancelPendingRequest(spec: MapSnapshotSpec) {
        requestQueue.remove(spec)
    }

    internal suspend fun demoteToBackgroundPriority(spec: MapSnapshotSpec) {
        requestQueue.demoteToBackgroundPriority(spec)
    }

    private sealed class SnapshotResult(open val spec: MapSnapshotSpec) {
        data class Success(override val spec: MapSnapshotSpec, val bitmap: Bitmap) :
            SnapshotResult(spec)

        data class Failure(override val spec: MapSnapshotSpec, val throwable: Throwable) :
            SnapshotResult(spec)
    }

    private val results = MutableStateFlow<SnapshotResult?>(null)
    private var engineJob: Job? = null

    private suspend fun waitUntilReady(spec: MapSnapshotSpec): SnapshotResult {
        return results.firstOrNull { it?.spec == spec } ?: SnapshotResult.Failure(
            spec,
            NullPointerException("Internal error: got null SnapshotResult")
        )
    }

    /**
     * Main entry point to run map snapshotter logic. This should be called from a fragment using
     * fragment's view lifecycle scope.
     *
     * This method never completes but keeps listening for new map snapshot requests.
     */
    @MainThread
    suspend fun runSnapshotterEngine(context: Context) = supervisorScope {
        // Launch snapshot-to-view binding handling
        launch { binder.runBinderExecutor() }

        // Initialize values from resources
        if (context.resources.getBoolean(R.bool.maps_logo_enabled)) {
            logoDrawable = AppCompatResources.getDrawable(context, R.drawable.mapbox_helmet)
        }

        logoMargin = context.resources.getDimensionPixelSize(R.dimen.map_snapshot_logo_margin)
        routePadding = context.resources.getDimensionPixelSize(R.dimen.map_route_padding)
        routeStrokeWidth = context.resources.getDimension(R.dimen.route_map_stroke_width)

        engineJob?.cancelAndJoin() // Prevent multiple jobs
        engineJob = launch(coroutinesDispatchers.io) {
            Timber.d("Map snapshotter execution started")
            // If we don't clear the queue here, spec from other screen with different size
            // would block snapshotter since the map view size is different
            requestQueue.clear()
            try {
                while (true) {
                    // Suspend until we get a map snapshot request
                    val spec = requestQueue.popNextInPriority()

                    // Generate snapshot and save to cache
                    runSuspendCatching {
                        val snap = createSnapshot(spec, context)
                        results.tryEmit(SnapshotResult.Success(spec, snap.bitmap))
                        runSuspendCatching {
                            cache.save(spec, snap.bitmap)
                        }.onFailure { e ->
                            Timber.w(e, "Failed to save to cache")
                        }
                    }.onFailure { e ->
                        results.tryEmit(SnapshotResult.Failure(spec, e))
                    }
                }
            } catch (e: CancellationException) {
                Timber.d("Map snapshotter execution cancelled")
                throw e
            } catch (e: Exception) {
                Timber.w(e, "Map snapshotter execution failed")
            } finally {
                // Clear variables to avoid memory leaks.
                results.tryEmit(null)
                engineJob = null
            }
        }
    }

    fun setMapSnapshotSpecsForBackgroundGeneration(
        specs: List<MapSnapshotSpec>,
        lifecycleScope: CoroutineScope,
    ) = lifecycleScope.launch(coroutinesDispatchers.io) {
        runSuspendCatching {
            requestQueue.setSpecsForBackgroundGeneration(specs)
        }.onFailure { e ->
            Timber.w(e, "setMapSnapshotSpecsForBackgroundGeneration failed")
        }
    }

    fun clearCache() {
        cache.clearCache()
    }

    suspend fun mapSnapshotExists(spec: MapSnapshotSpec): Boolean {
        return cache.contains(spec)
    }

    private fun addLogo(snapshot: SuuntoMapSnapshot, logoDrawable: Drawable?): SuuntoMapSnapshot {
        logoDrawable?.run {
            if (intrinsicWidth > 0 && intrinsicHeight > 0) {
                val canvas = Canvas(snapshot.bitmap)
                bounds = Rect(0, 0, intrinsicWidth, intrinsicHeight)
                bounds.offset(snapshot.bitmap.width - intrinsicWidth - logoMargin, logoMargin)
                draw(canvas)
            }
        }

        return snapshot
    }

    companion object {
        const val ROUTE_MIN_WIDTH_PIXELS = 2f
        const val MARKER_ANCHOR_X = 0.5f
        const val MARKER_ANCHOR_Y = 0.5f

        @JvmStatic
        var instance: MapSnapshotter? = null

        private val SAMPLE_ROUTE = buildList {
            add(LatLng(60.18825, 24.912403))
            add(LatLng(60.187706, 24.911417))
            add(LatLng(60.18747, 24.911045))
            add(LatLng(60.18673, 24.910017))
            add(LatLng(60.18606, 24.909267))
            add(LatLng(60.186054, 24.909285))
            add(LatLng(60.185783, 24.910467))
            add(LatLng(60.18582, 24.91056))
            add(LatLng(60.18597, 24.911085))
            add(LatLng(60.186176, 24.911816))
            add(LatLng(60.186516, 24.912971))
            add(LatLng(60.186924, 24.914454))
            add(LatLng(60.1869, 24.914377))
            add(LatLng(60.18642, 24.914927))
            add(LatLng(60.186237, 24.91515))
            add(LatLng(60.185604, 24.915907))
            add(LatLng(60.185448, 24.916073))
            add(LatLng(60.185413, 24.915964))
            add(LatLng(60.18536, 24.915804))
            add(LatLng(60.185307, 24.915827))
            add(LatLng(60.18524, 24.915752))
            add(LatLng(60.18511, 24.915634))
            add(LatLng(60.185097, 24.915693))
            add(LatLng(60.185036, 24.915794))
            add(LatLng(60.18493, 24.915907))
            add(LatLng(60.184917, 24.916014))
            add(LatLng(60.184723, 24.91602))
            add(LatLng(60.184273, 24.915848))
            add(LatLng(60.183468, 24.91555))
            add(LatLng(60.183395, 24.91632))
            add(LatLng(60.183422, 24.9164))
            add(LatLng(60.18336, 24.916494))
            add(LatLng(60.183685, 24.917425))
            add(LatLng(60.18369, 24.91742))
            add(LatLng(60.183685, 24.917425))
            add(LatLng(60.183804, 24.917778))
            add(LatLng(60.18375, 24.917866))
            add(LatLng(60.18382, 24.918251))
            add(LatLng(60.183796, 24.919779))
            add(LatLng(60.183792, 24.919779))
            add(LatLng(60.18379, 24.920105))
            add(LatLng(60.18379, 24.922024))
            add(LatLng(60.183727, 24.922037))
            add(LatLng(60.183727, 24.922224))
            add(LatLng(60.183685, 24.922493))
            add(LatLng(60.183685, 24.923134))
            add(LatLng(60.18372, 24.923134))
            add(LatLng(60.183685, 24.923134))
            add(LatLng(60.18369, 24.923534))
            add(LatLng(60.18371, 24.923626))
            add(LatLng(60.183765, 24.923685))
            add(LatLng(60.183197, 24.924528))
            add(LatLng(60.183254, 24.924688))
            add(LatLng(60.18318, 24.92474))
            add(LatLng(60.18318, 24.924856))
            add(LatLng(60.183163, 24.92489))
            add(LatLng(60.183125, 24.924818))
            add(LatLng(60.183163, 24.92489))
            add(LatLng(60.182358, 24.926163))
        }
    }
}

private fun Bitmap.toDrawable(context: Context) = BitmapDrawable(context.resources, this)

// Google Maps requires a map view for creating snapshots. With Mapbox, no view is required.
private val MapsProvider.requiresMapViewForSnapshotting: Boolean
    get() = name != MapboxMapsProvider.NAME

// With Mapbox, logos are always shown except when R.bool.maps_logo_enabled is false (China builds)
private val MapsProvider.requiresLogoOverlay: Boolean
    get() = name == MapboxMapsProvider.NAME

// Icon scaling is not supported with Google maps so waypoints are not supported because with their
// default size it makes the snapshot look crowded.
private val MapsProvider.snapshottingSupportsWaypoints: Boolean
    get() = MapHelper.snapshottingWaypointsSupports(name)
