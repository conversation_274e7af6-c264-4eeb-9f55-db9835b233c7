package com.stt.android.maps

import android.widget.ImageView
import androidx.annotation.MainThread

/**
 * Bind a map snapshot to an [ImageView]. If the map snapshot already exists in cache, it is shown
 * as quickly as possible without any transition animation. If the snapshot is not in cache, then
 * a new map snapshot is requested from [MapSnapshotter] and it shown with a fade-in transition
 * when available.
 */
@MainThread
fun ImageView.bindMapSnapshot(spec: MapSnapshotSpec) {
    MapSnapshotter.instance?.binder?.bindMapSnapshot(spec, this)
}

/** Abort loading, reset current drawable */
@MainThread
fun ImageView.clearMapSnapshot() {
    MapSnapshotter.instance?.binder?.clearMapSnapshot(this)
}

/** Abort any ongoing loading operations, but keep showing current snapshot if there is one */
@MainThread
fun ImageView.detach() {
    MapSnapshotter.instance?.binder?.detachMapSnapshot(this)
}
