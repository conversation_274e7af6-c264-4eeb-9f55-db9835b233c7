package com.stt.android.maps

import android.content.ComponentCallbacks
import android.content.res.Configuration
import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LifecycleEventEffect
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.maps.extensions.awaitMap
import com.stt.android.ui.map.MapHelper

@Composable
fun SuuntoMap(
    modifier: Modifier = Modifier,
    mapOptions: SuuntoMapOptions = SuuntoMapOptions(),
    onMapReady: suspend (SuuntoMap) -> Unit = {}
) = Box(modifier = modifier) {
    // When in preview, early return a Box with the received modifier preserving layout
    if (LocalInspectionMode.current) {
        Image(
            painter = painterResource(id = R.drawable.maps_suomi_terrain),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        return
    }

    var key by remember { mutableLongStateOf(0) }

    key(key) {
        val context = LocalContext.current
        val mapView = remember { SuuntoMapView(context, mapOptions) }

        mapOptions.mapType?.let { type ->
            mapView.setInitialMapTypeHint(type)
        }

        AndroidView(factory = { mapView })
        MapLifecycle(mapView)

        LaunchedEffect(Unit) {
            val map = mapView.awaitMap()
            onMapReady(map)
        }
    }

    LifecycleEventEffect(Lifecycle.Event.ON_STOP) {
        // fix amap anr
        if (MapHelper.isDefaultProviderAMap()) {
            key += 1
        }
    }
}

/*
Lifecycle implementation copied from
https://github.com/googlemaps/android-maps-compose/blob/main/maps-compose/src/main/java/com/google/maps/android/compose/GoogleMap.kt
 */

@Composable
private fun MapLifecycle(mapView: SuuntoMapView) {
    val context = LocalContext.current
    val lifecycle = LocalLifecycleOwner.current.lifecycle
    val previousState = remember { mutableStateOf(Lifecycle.Event.ON_CREATE) }

    DisposableEffect(context, lifecycle, mapView) {
        val mapLifecycleObserver = mapView.lifecycleObserver(previousState)
        val callbacks = mapView.componentCallbacks()

        lifecycle.addObserver(mapLifecycleObserver)
        context.registerComponentCallbacks(callbacks)

        onDispose {
            lifecycle.removeObserver(mapLifecycleObserver)
            context.unregisterComponentCallbacks(callbacks)
            mapView.onDestroy()
            mapView.removeAllViews()
        }
    }
}

private fun SuuntoMapView.lifecycleObserver(previousState: MutableState<Lifecycle.Event>): LifecycleEventObserver =
    LifecycleEventObserver { _, event ->
        when (event) {
            Lifecycle.Event.ON_CREATE -> {
                // Skip calling mapView.onCreate if the lifecycle did not go through onDestroy - in
                // this case the map composable also doesn't leave the composition. So,
                // recreating the map does not restore state properly which must be avoided.
                if (previousState.value != Lifecycle.Event.ON_STOP) {
                    this.onCreate(Bundle())
                }
            }

            Lifecycle.Event.ON_START -> this.onStart()
            Lifecycle.Event.ON_RESUME -> this.onResume()
            Lifecycle.Event.ON_PAUSE -> this.onPause()
            Lifecycle.Event.ON_STOP -> this.onStop()
            Lifecycle.Event.ON_DESTROY -> {
                // Handled in onDispose
            }

            else -> throw IllegalStateException()
        }
        previousState.value = event
    }

private fun SuuntoMapView.componentCallbacks(): ComponentCallbacks =
    object : ComponentCallbacks {
        override fun onConfigurationChanged(config: Configuration) {}

        override fun onLowMemory() {
            <EMAIL>()
        }
    }

@Preview(showBackground = true)
@Composable
private fun SuuntoMapPreview() {
    M3AppTheme {
        SuuntoMap()
    }
}
