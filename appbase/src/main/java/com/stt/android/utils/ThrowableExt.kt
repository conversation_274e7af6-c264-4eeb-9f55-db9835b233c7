package com.stt.android.utils

import android.content.Context
import com.stt.android.exceptions.BackendException
import com.stt.android.R
import com.stt.android.network.interfaces.ANetworkProvider

fun Throwable.getLocalizedErrorMessage(context: Context): String {
    return when {
        !ANetworkProvider.isOnline() -> context.getString(R.string.no_network_error)
        this is BackendException -> error.getLocalizedMessage(context.resources, context.packageName)
        else -> context.getString(R.string.error_generic)
    }
}
