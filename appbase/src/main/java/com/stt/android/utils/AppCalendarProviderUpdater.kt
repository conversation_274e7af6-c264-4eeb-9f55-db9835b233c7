package com.stt.android.utils

import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.core.content.ContextCompat
import com.stt.android.controllers.UserSettingsController
import com.stt.android.di.initializer.AppInitializer
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AppCalendarProviderUpdater
@Inject constructor(
    private val userSettingsController: UserSettingsController,
    private val calendarProvider: FixedFirstDayOfTheWeekCalendarProvider,
    private val context: Context
) : AppInitializer, UserSettingsController.UpdateListener {
    override fun init(app: Application) {
        updateProvider()
        userSettingsController.addUpdateListener(this)
        val deviceSettingsUpdatedIntentFilter = IntentFilter().apply {
            addAction(Intent.ACTION_LOCALE_CHANGED)
        }
        ContextCompat.registerReceiver(
            app,
            deviceSettingsUpdatedReceiver,
            deviceSettingsUpdatedIntentFilter,
            ContextCompat.RECEIVER_EXPORTED
        )
    }

    override fun onSettingsStoredToPreferences(didLocalChanges: Boolean) {
        updateProvider()
    }

    private fun updateProvider() {
        calendarProvider.updateFixedFields(
            DeviceUtils.getSystemLevelLocale(context),
            userSettingsController.settings.firstDayOfTheWeek
        )
    }

    private val deviceSettingsUpdatedReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            updateProvider()
        }
    }
}
