package com.stt.android.utils

import android.content.Context
import com.stt.android.R
import java.util.Locale

object LocaleUtils {
    private const val TAIWAN_ISO3166 = "TW"
    private const val HONG_KONG_ISO3166 = "HK"
    private const val MACAO_ISO3166 = "MO"
    val localeMap: Map<String, Locale> by lazy {
        Locale.getISOCountries().associate {
            val locale = Locale("", it)
            locale.isO3Country.uppercase(Locale.US) to locale
        }
    }

    fun getDisplayCountry(locale: Locale, context: Context): String {
        return when (locale.country) {
            TAIWAN_ISO3166 -> context.getString(R.string.taiwan_display_china)
            HONG_KONG_ISO3166 -> context.getString(R.string.hong_kong_display_china)
            MACAO_ISO3166 -> context.getString(R.string.macao_display_china)
            else -> locale.displayCountry
        }
    }

    fun getDisplayCountry(code: String, context: Context): String {
        val locale = fromCountryCode(code) ?: return ""
        return when (locale.country) {
            TAIWAN_ISO3166 -> context.getString(R.string.taiwan_display_china)
            HONG_KONG_ISO3166 -> context.getString(R.string.hong_kong_display_china)
            MACAO_ISO3166 -> context.getString(R.string.macao_display_china)
            else -> locale.displayCountry
        }
    }

    @JvmStatic
    fun fromIso3CountryCode(iso3CountryCode: String): Locale? {
        return localeMap[iso3CountryCode]
    }

    fun fromCountryCode(countryCode: String): Locale? =
        localeMap.mapKeys { it.value.country }[countryCode]

    @JvmStatic
    fun getDefaultCountrySubdivision(countryCode: String): String {
        return when (countryCode) {
            Locale.US.country -> return countryUSSubdivisionLocaleMap.toList().first().second
            else -> ""
        }
    }

    @JvmStatic
    fun getUSStateFromIso3CountrySubdivisionCode(iso3CountrySubdivisionCode: String): String? =
        countryUSSubdivisionLocaleMap
            .filterValues { it == iso3CountrySubdivisionCode }
            .keys
            .firstOrNull()
            ?.toString()

    /**
     * ISO 3166-2:US
     * See also: https://en.wikipedia.org/wiki/ISO_3166-2:US
     */
    val countryUSSubdivisionLocaleMap: Map<String, String> =
        mapOf(
            "Alabama" to "US-AL",
            "Alaska" to "US-AK",
            "Arizona" to "US-AZ",
            "Arkansas" to "US-AR",
            "California" to "US-CA",
            "Colorado" to "US-CO",
            "Connecticut" to "US-CT",
            "Delaware" to "US-DE",
            "District Of Columbia" to "US-DC",
            "Florida" to "US-FL",
            "Georgia" to "US-GA",
            "Hawaii" to "US-HI",
            "Idaho" to "US-ID",
            "Illinois" to "US-IL",
            "Indiana" to "US-IN",
            "Iowa" to "US-IA",
            "Kansas" to "US-KS",
            "Kentucky" to "US-KY",
            "Louisiana" to "US-LA",
            "Maine" to "US-ME",
            "Maryland" to "US-MD",
            "Massachusetts" to "US-MA",
            "Michigan" to "US-MI",
            "Minnesota" to "US-MN",
            "Mississippi" to "US-MS",
            "Missouri" to "US-MO",
            "Montana" to "US-MT",
            "Nebraska" to "US-NE",
            "Nevada" to "US-NV",
            "New Hampshire" to "US-NH",
            "New Jersey" to "US-NJ",
            "New Mexico" to "US-NM",
            "New York" to "US-NY",
            "North Carolina" to "US-NC",
            "North Dakota" to "US-ND",
            "Ohio" to "US-OH",
            "Oklahoma" to "US-OK",
            "Oregon" to "US-OR",
            "Pennsylvania" to "US-PA",
            "Rhode Island" to "US-RI",
            "South Carolina" to "US-SC",
            "South Dakota" to "US-SD",
            "Tennessee" to "US-TN",
            "Texas" to "US-TX",
            "Utah" to "US-UT",
            "Vermont" to "US-VT",
            "Virginia" to "US-VA",
            "Washington" to "US-WA",
            "West Virginia" to "US-WV",
            "Wisconsin" to "US-WI",
            "Wyoming" to "US-WY"
        )
}
