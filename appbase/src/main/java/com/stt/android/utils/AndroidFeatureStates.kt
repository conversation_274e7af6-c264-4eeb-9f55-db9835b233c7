package com.stt.android.utils

import android.app.Application
import android.bluetooth.BluetoothAdapter
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.location.LocationManager
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import com.jakewharton.rxrelay2.BehaviorRelay
import com.stt.android.domain.android.DeviceFeatureStates
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AndroidFeatureStates implements DeviceFeatureStates and provides
 * enabled states for device features, like location and bluetooth
 * as flowables.
 */
@Singleton
class AndroidFeatureStates
@Inject constructor(
    private val app: Application
) : DeviceFeatureStates {
    private val locationManager = app.getSystemService(Context.LOCATION_SERVICE) as LocationManager

    // Bluetooth
    private val bluetoothReceiver = BluetoothStateReceiver()
    private val bluetoothEnabledSubject = BehaviorRelay.create<Boolean>()
    private val bluetoothEnabledFlowable = createBluetoothEnabledFlowable()

    // Location
    private val locationReceiver = LocationStateReceiver()
    private val locationEnabledSubject = BehaviorRelay.create<Boolean>()
    private val locationEnabledFlowable = createLocationEnabledFlowable()

    private fun createBluetoothEnabledFlowable(): Flowable<Boolean> {
        // Return bluetooth feature state flowable.
        return bluetoothEnabledSubject
            .doOnSubscribe {
                // Update initial value and register receiver with the first subscriber
                if (!bluetoothEnabledSubject.hasObservers()) {
                    // Update initial value to subject
                    val nearbyDevicesGranted = app.isNearbyDevicesPermissionGranted()

                    val enabled = nearbyDevicesGranted &&
                        BluetoothAdapter.getDefaultAdapter() != null &&
                        BluetoothAdapter.getDefaultAdapter().isEnabled
                    bluetoothEnabledSubject.accept(enabled)
                    // Register receiver
                    ContextCompat.registerReceiver(
                        app,
                        bluetoothReceiver,
                        IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED),
                        ContextCompat.RECEIVER_EXPORTED
                    )
                } else {
                    bluetoothEnabledSubject.accept(false)
                }
            }
            .doFinally {
                // Unregister receiver when all subscribers are detached
                if (!bluetoothEnabledSubject.hasObservers()) {
                    try {
                        app.unregisterReceiver(bluetoothReceiver)
                    } catch (exception: IllegalArgumentException) {
                        Timber.w(exception, "Bluetooth receiver was not registered.")
                    }
                }
            }
            // Use BackpressureStrategy.LATEST to make sure that only
            // the latest state is provided if downstream cannot keep up
            .toFlowable(BackpressureStrategy.LATEST)
    }

    private fun createLocationEnabledFlowable(): Flowable<Boolean> {
        // Return location feature state flowable
        return locationEnabledSubject
            .doOnSubscribe {
                // Update initial value and register receiver with the first subscriber
                if (!locationEnabledSubject.hasObservers()) {
                    // Update initial value to subject
                    val enabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER) ||
                        locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
                    locationEnabledSubject.accept(enabled)
                    // Register receiver
                    ContextCompat.registerReceiver(
                        app,
                        locationReceiver,
                        IntentFilter(LocationManager.PROVIDERS_CHANGED_ACTION),
                        ContextCompat.RECEIVER_EXPORTED
                    )
                }
            }
            .doFinally {
                // Unregister receiver when all subscribers are detached
                if (!locationEnabledSubject.hasObservers()) {
                    app.unregisterReceiver(locationReceiver)
                }
            }
            // Use BackpressureStrategy.LATEST to make sure that only
            // the latest state is provided if downstream cannot keep up
            .toFlowable(BackpressureStrategy.LATEST)
    }

    /**
     * Once permissions for bluetooth SCAN and CONNECT have been granted, we can now check if the
     * bluetooth adapter is enabled.
     */
    override fun recheckBluetoothEnabled(permissionsGranted: Boolean) {
        if (permissionsGranted) {
            val enabled =
                BluetoothAdapter.getDefaultAdapter() != null && BluetoothAdapter.getDefaultAdapter().isEnabled
            bluetoothEnabledSubject.accept(enabled)
        } else {
            bluetoothEnabledSubject.accept(false)
        }
    }

    override fun bluetoothSupported(): Boolean = BluetoothUtils.isBluetoothSupported(app)

    override fun bluetoothEnabled(): Flowable<Boolean> {
        return bluetoothEnabledFlowable
    }

    override fun locationEnabled(): Flowable<Boolean> {
        return locationEnabledFlowable
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    override fun isBackgroundLocationGranted(): Boolean {
        return app.applicationContext?.let {
            return it.isBackgroundLocationPermissionGranted()
        } ?: false
    }

    override fun areNotificationsEnabled(): Boolean {
        return app.applicationContext?.let {
            return NotificationManagerCompat.from(it).areNotificationsEnabled()
        } ?: false
    }

    /**
     * Listen to bluetooth state changes
     */
    private inner class BluetoothStateReceiver : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                BluetoothAdapter.ACTION_STATE_CHANGED -> {
                    val enabled = intent.getIntExtra(
                        BluetoothAdapter.EXTRA_STATE,
                        BluetoothAdapter.ERROR
                    ) == BluetoothAdapter.STATE_ON
                    bluetoothEnabledSubject.accept(enabled)
                }
            }
        }
    }

    /**
     * Listen to location state changes
     */
    private inner class LocationStateReceiver : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                LocationManager.PROVIDERS_CHANGED_ACTION -> {
                    val enabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER) ||
                        locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
                    locationEnabledSubject.accept(enabled)
                }
            }
        }
    }
}
