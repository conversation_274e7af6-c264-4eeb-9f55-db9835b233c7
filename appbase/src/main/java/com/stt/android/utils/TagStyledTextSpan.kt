package com.stt.android.utils

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.text.style.ReplacementSpan
import androidx.annotation.ColorRes
import androidx.core.content.res.ResourcesCompat
import com.stt.android.FontRefs
import com.stt.android.R

/**
 * Draws the spanned text with slightly smaller but bolded font in a rounded box
 */
class TagStyledTextSpan(
    context: Context,
    @ColorRes textColorRes: Int,
    @ColorRes backgroundColorRes: Int
) : ReplacementSpan() {
    private val textColor = context.getColor(textColorRes)
    private val backgroundColor = context.getColor(backgroundColorRes)
    private val backgroundRoundingRadius = context.resources.getDimension(R.dimen.radius_corners_generic)
    private val horizontalPadding = context.resources.getDimension(R.dimen.size_spacing_xsmaller)

    private val boldTypeface = ResourcesCompat.getFont(context, FontRefs.PREMIUM_NOTE_FONT_REF)

    override fun getSize(paint: Paint, text: CharSequence?, start: Int, end: Int, fm: Paint.FontMetricsInt?): Int {
        // Fixes 0 height when the Span is used for the whole text
        if (fm != null) {
            val fontMetricsFromPaint = paint.fontMetricsInt
            fm.leading = fontMetricsFromPaint.leading
            fm.top = fontMetricsFromPaint.top
            fm.bottom = fontMetricsFromPaint.bottom
            fm.ascent = fontMetricsFromPaint.ascent
            fm.descent = fontMetricsFromPaint.descent
        }

        return (horizontalPadding + paint.measureText(text, start, end) + horizontalPadding).toInt()
    }

    override fun draw(
        canvas: Canvas,
        text: CharSequence?,
        start: Int,
        end: Int,
        x: Float,
        top: Int,
        y: Int,
        bottom: Int,
        paint: Paint
    ) {
        if (text == null) return

        val originalTextSize = paint.textSize
        val originalTypeface = paint.typeface

        paint.textSize = originalTextSize * 0.75f
        paint.typeface = boldTypeface

        paint.color = backgroundColor

        val textWidth = paint.measureText(text, start, end)
        val backgroundRect = RectF(x, top.toFloat(), x + textWidth + 2 * horizontalPadding, bottom.toFloat())
        canvas.drawRoundRect(backgroundRect, backgroundRoundingRadius, backgroundRoundingRadius, paint)

        paint.color = textColor
        canvas.drawText(
            text,
            start,
            end,
            x + horizontalPadding,
            y.toFloat() - (0.5f * paint.fontMetrics.descent), // Slightly raise from baseline to compensate smaller font size
            paint
        )

        paint.textSize = originalTextSize
        paint.typeface = originalTypeface
    }
}
