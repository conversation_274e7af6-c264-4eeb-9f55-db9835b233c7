package com.stt.android.utils

import android.graphics.BitmapFactory
import android.text.TextUtils
import android.util.Size
import com.stt.android.STTApplication
import timber.log.Timber

object ImageSizeUtils {

    fun getImageSize(imageResId: Int, filePath: String?): Size {
        val bitmapOptions = BitmapFactory.Options()
        bitmapOptions.inJustDecodeBounds = true
        try {
            if (TextUtils.isEmpty(filePath)) {
                BitmapFactory.decodeResource(
                    STTApplication.getComponent().appContext().resources,
                    imageResId,
                    bitmapOptions
                )
            } else {
                STTApplication.getComponent().appContext().resources.assets.open(filePath!!).use {
                    BitmapFactory.decodeStream(it, null, bitmapOptions)
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "get resource image size fail")
        }
        return Size(bitmapOptions.outWidth, bitmapOptions.outHeight)
    }
}
