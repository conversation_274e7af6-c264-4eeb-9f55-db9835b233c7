package com.stt.android.utils

import android.content.Context
import android.os.Bundle
import androidx.core.net.toUri
import com.stt.android.launcher.ProxyActivity
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

// https://developer.huawei.com/consumer/en/doc/20602
class HuaweiUtils @Inject constructor(
    @ApplicationContext private val context: Context,
) {
    fun setIconBadge(count: Int) {
        if (!BrandUtils.isHuawei()) return

        runCatching {
            val extra = Bundle().apply {
                putString("package", context.packageName)
                putString("class", ProxyActivity::class.java.name)
                putInt("badgenumber", count)
            }
            context.contentResolver.call(
                "content://com.huawei.android.launcher.settings/badge/".toUri(),
                "change_badge",
                null,
                extra,
            )
        }
    }
}
