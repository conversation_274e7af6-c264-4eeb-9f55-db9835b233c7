package com.stt.android.utils

import android.Manifest
import android.app.Application
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat
import com.stt.android.domain.android.AppPermissionStates
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AndroidPermissionStates implements AppPermissionStates and provides
 * easy access to runtime permission states.
 */
@Singleton
class AndroidPermissionStates
@Inject constructor(
    private val app: Application
) : AppPermissionStates {
    override fun foregroundLocationPermissionGranted(): <PERSON><PERSON><PERSON> {
        return isPermissionGranted(Manifest.permission.ACCESS_COARSE_LOCATION) ||
            isPermissionGranted(Manifest.permission.ACCESS_FINE_LOCATION)
    }

    override fun nearbyDevicesPermissionGranted(): <PERSON>olean {
        return app.isNearbyDevicesPermissionGranted()
    }

    private fun isPermissionGranted(permission: String): <PERSON>olean {
        return ContextCompat.checkSelfPermission(app, permission) == PackageManager.PERMISSION_GRANTED
    }
}
