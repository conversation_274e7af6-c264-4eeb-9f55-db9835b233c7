package com.stt.android.utils

import androidx.annotation.StringRes
import com.stt.android.R

object AddImagePermissionsUtil {
    // only suunto app need to request camera permission
    val chooseImagePermission: Array<String> =
        PermissionUtils.STORAGE_PERMISSIONS + PermissionUtils.CAMERA_PERMISSION

    @StringRes
    val requestImagePermissionRationalResId = R.string.camera_storage_permission_rationale_picker

    @StringRes
    fun getRationalPurposeResIdAfterRefused(permissions: List<String>): Int {
        return when {
            permissions.containsAll(PermissionUtils.CAMERA_STORAGE_PERMISSIONS.toList()) -> R.string.camera_storage_permission_rationale_picker
            permissions.containsAll(PermissionUtils.STORAGE_PERMISSIONS.toList()) -> R.string.storage_permission_rationale_picker
            permissions.contains(PermissionUtils.CAMERA_PERMISSION) -> R.string.camera_permission_rational
            else -> R.string.storage_permission_rationale_picker
        }
    }

    fun imagePermissionsGranted(permissions: List<String>): Boolean = permissions.size == chooseImagePermission.size
}
