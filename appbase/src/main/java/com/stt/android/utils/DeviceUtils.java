package com.stt.android.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.os.ConfigurationCompat;
import androidx.core.util.Pair;
import com.stt.android.BuildConfig;
import com.stt.android.controllers.UserSettingsController;
import static com.stt.android.usecases.startup.LowPriorityStartupUseCase.STORE_NAME_CHINA;
import java.lang.reflect.Method;
import java.util.Locale;
import timber.log.Timber;

/**
 * Utility library to provide common methods to perform and get information about the device
 */
public class DeviceUtils {
    /**
     * @param telephonyManager TelephonyManager
     * @return a tuple with MCC and MNC. Might be empty strings if sim is not available.
     */
    @NonNull
    public static Pair<String, String> getMccAndMnc(@Nullable TelephonyManager telephonyManager) {
        if (telephonyManager == null) {
            return new Pair<>("", "");
        }

        String mcc = "";
        String mnc = "";
        String simOperator = telephonyManager.getSimOperator();
        if (telephonyManager.getSimState() == TelephonyManager.SIM_STATE_READY
                && simOperator != null && simOperator.length() > 3) {
            mcc = simOperator.substring(0, 3);
            mnc = simOperator.substring(3);
        }
        return new Pair<>(mcc, mnc);
    }

    @Nullable
    private static TelephonyManager getTelephonyManager(Context context) {
        return (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
    }

    /**
     * @param context
     * @return The SIM country code LOWERCASE, or fallback to current country code.
     */
    @NonNull
    public static String getSimCountryCode(Context context) {
        TelephonyManager telephonyManager = getTelephonyManager(context);
        if (telephonyManager == null) {
            return "";
        }
        String countryCode = telephonyManager.getSimCountryIso();
        if (TextUtils.isEmpty(countryCode)) {
            // Some devices don't have SIM but have access to a network
            // Result may be unreliable on CDMA networks.
            countryCode = telephonyManager.getNetworkCountryIso();
        }
        if (TextUtils.isEmpty(countryCode) &&
            telephonyManager.getPhoneType() == TelephonyManager.PHONE_TYPE_CDMA &&
            Build.VERSION.SDK_INT < Build.VERSION_CODES.P) {
            countryCode = extractCDMACountryCode();
        }
        // we don't try to second-guess china
        if (BuildConfig.FLAVOR_store.equals(STORE_NAME_CHINA)) {
            countryCode = UserSettingsController.COUNTRY_CHINA.toLowerCase(Locale.US);
        }
        return TextUtils.isEmpty(countryCode) ? "" : countryCode.toLowerCase(Locale.US);
    }

    /**
     * Do not call if API level is >= P as it uses reflections on system APIs.
     * Returns CDMA country code for countries like China or US.
     * Not always available but sometimes it is more trustworthy then querying {@link TelephonyManager}.
     * Any exception is handled and empty string is returned instead.
     * https://stackoverflow.com/questions/8292287/how-to-get-the-country-code-for-cdma-android-devices/11618774#11618774
     */
    @NonNull
    private static String extractCDMACountryCode() {
        try {
            // try to get country code from SystemProperties private class
            @SuppressLint("PrivateApi")
            Class systemProperties = Class.forName("android.os.SystemProperties");
            @SuppressWarnings("unchecked")
            Method get = systemProperties.getMethod("get", String.class);

            // get homeOperator that contain MCC + MNC
            String homeOperator = (String) get.invoke(
                systemProperties,
                "ro.cdma.home.operator.numeric"
            );

            // first 3 chars (MCC) from homeOperator represents the country code
            int mcc = Integer.parseInt(homeOperator.substring(0, 3));

            // mapping just countries that actually use CDMA networks
            switch (mcc) {
                case 330: return "PR";
                case 310: return "US";
                case 311: return "US";
                case 312: return "US";
                case 316: return "US";
                case 283: return "AM";
                case 460: return "CN";
                case 455: return "MO";
                case 414: return "MM";
                case 619: return "SL";
                case 450: return "KR";
                case 634: return "SD";
                case 434: return "UZ";
                case 232: return "AT";
                case 204: return "NL";
                case 262: return "DE";
                case 247: return "LV";
                case 255: return "UA";
            }
        } catch (Exception e) {
            Timber.w(e, "Error in extractCDMACountryCode()");
        }
        return "";
    }

    public static Locale getSystemLevelLocale(Context context) {
        return ConfigurationCompat.getLocales(context.getResources().getConfiguration()).get(0);
    }
}
