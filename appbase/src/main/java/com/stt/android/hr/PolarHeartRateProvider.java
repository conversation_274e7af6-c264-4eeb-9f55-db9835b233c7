
package com.stt.android.hr;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import timber.log.Timber;

/* package */class PolarHeartRateProvider implements HeartRateProvider {

    private final int packetSize;
    private final int packetMarker;
    private final int bufferSize;

    private final ExecutorService executor = Executors.newSingleThreadExecutor();
    private Receiver receiver;

    public PolarHeartRateProvider(HeartRateMonitorType type) {
        if (type != HeartRateMonitorType.POLAR)
            throw new IllegalArgumentException(type + " is not supported on this provider");
        packetSize = type.getPacketSize();
        packetMarker = type.getPacketMarker();
        bufferSize = type.getBufferSize();
    }

    @Override
    public synchronized void start(InputStream stream, HeartRateManager.Callbacks listener) {
        Timber.d("Polar heart rate parsing starting");
        receiver = new Receiver(stream, packetSize, packetMarker, bufferSize, listener);
        executor.execute(receiver);
    }

    @Override
    public synchronized void stop() {
        Timber.d("Polar heart rate parsing stopping");
        if (receiver != null) {
            receiver.cancel();
        }
    }

    /**
     * Byte pattern:
     * <p/>
     * <pre>
     * [MARKER][LEN_1][LEN_2][COUNTER][STATUS_1][CALCULATED HR][RR x 8]
     * [   0  ][  1  ][  2  ][   3   ][    4   ][      5      ][6 - 15]
     * </pre>
     */
    private static class Receiver implements Runnable {
        private static final int HEART_RATE_IN_BPM_INDEX = 5;
        private static final int BATTERY_STATUS_INDEX = 4;

        private enum PacketState {
            FIND_MARKER,
            PROCESS_HEADER,
            PROCESS_DATA
        }

        private boolean running = false;
        private final InputStream is;
        private final int packetSize;
        private final int packetMarker;
        private final int bufferSize;
        private final HeartRateManager.Callbacks listener;

        private int packetIndex = 0;
        private PacketState packetState = PacketState.FIND_MARKER;

        private long lastAcceptedHrTimestamp = System.currentTimeMillis();

        private Receiver(InputStream is, int packetSize, int packetMarker, int bufferSize,
                         HeartRateManager.Callbacks listener) {
            this.is = is;
            this.packetSize = packetSize;
            this.packetMarker = packetMarker;
            this.bufferSize = bufferSize;
            this.listener = listener;
        }

        @Override
        public void run() {
            Timber.d("Polar heart rate parsing started");
            int bytesRead;
            // prepare for roll-over / out of order packet boundaries.
            byte[] inputBuffer = new byte[bufferSize];
            int[] packet = new int[packetSize * 2];
            packetIndex = 0;
            running = true;

            try {
                while (running) {
                    bytesRead = is.read(inputBuffer);

                    // End of stream -> connection lost.
                    if (bytesRead == 0) {
                        listener.onStreamEnd();
                        break;
                    }

                    for (int i = 0; i < bytesRead; i++) {
                        byte in = inputBuffer[i];
                        int byteIn = in & 0xff;

                        if (packetState == PacketState.FIND_MARKER) {
                            packetIndex = 0;

                            // skip to next byte until marker is found.
                            if (byteIn != packetMarker) {
                                continue;
                            }

                            /*
                             * was marker, copy first length byte. packet index
                             * 0 -> 1.
                             */
                            packet[packetIndex++] = byteIn;
                            /*
                             * marker found, continue processing header in next
                             * loop.
                             */
                            packetState = PacketState.PROCESS_HEADER;
                            continue;
                        }

                        if (packetState == PacketState.PROCESS_HEADER) {
                            // rest of header. increase packet index.
                            packet[packetIndex++] = byteIn;

                            /*
                             * packet index == 6 -> we've processed header data
                             * and hr value. see protocol.
                             */
                            if (packetIndex >= 6) {
                                packetState = PacketState.PROCESS_DATA;
                            }
                            // continue -> process RR data in next loop.
                            continue;
                        }

                        if (packetState == PacketState.PROCESS_DATA) {
                            // How many bytes we need to read before processing?
                            int dataLength = packet[1];

                            // Keep reading bytes. It will stop once packetIndex
                            // == dataLength or is bigger than dataLength
                            packet[packetIndex++] = byteIn;

                            /*
                             * packet data counter exceeds packet length given
                             * in header -> skip packet.
                             */
                            if (packetIndex > dataLength || packetIndex >= (packetSize * 2)) {
                                // start processing new packet block.
                                packetState = PacketState.FIND_MARKER;
                                continue;
                            }

                            // We have read all the data
                            if (packetIndex == dataLength) {
                                // continue to next packet.
                                packetState = PacketState.FIND_MARKER;
                                long timestamp = System.currentTimeMillis();
                                /*
                                 * drop if packet is not older than 0.7 seconds
                                 */
                                if (timestamp - lastAcceptedHrTimestamp > 700) {
                                    listener.onHeartRate(BluetoothHeartRateEvent.newUpdateEvent(
                                            readBatteryStatus(packet[BATTERY_STATUS_INDEX]),
                                            timestamp, packet[HEART_RATE_IN_BPM_INDEX], packet));
                                    lastAcceptedHrTimestamp = timestamp;
                                }
                            }
                        }
                    }
                }

            } catch (IOException | NullPointerException e) {
                listener.onError(e);
            }
            Timber.d("Polar heart rate parsing stopped");
        }

        /**
         * <pre>
         * [    7 (MSB)    ][      6     ][5][4][3][2][1][         0 (LSB)       ]
         * [ext/bat powered][charging/not][battery level][bat level available/not]
         * [ 7 (MSB) ][   6  ][  5  ][       3       ][2][1][0 (LSB)]
         * [Nokia bit][battery level][Contact quality][ Sensor type ]
         * </pre>
         *
         * @param in
         * @return
         */
        private BatteryStatus readBatteryStatus(int in) {
            Timber.d("Battery status bits: %s", Integer.toBinaryString(in));
            // We're only interested in bits 5 and 6
            int batteryStatus = (in >> 5) & 0x03;

            int batteryLevelPercent = 5;
            if (batteryStatus == 3) {
                batteryLevelPercent = 70;
            } else if (batteryStatus == 2) {
                batteryLevelPercent = 40;
            } else if (batteryStatus == 1) {
                batteryLevelPercent = 20;
            }
            Timber.d("Battery level: %d %%", batteryLevelPercent);
            return new BatteryStatus(false, false, batteryLevelPercent);
        }

        public void cancel() {
            Timber.d("Canceling polar heart rate parsing");
            running = false;
        }
    }
}
