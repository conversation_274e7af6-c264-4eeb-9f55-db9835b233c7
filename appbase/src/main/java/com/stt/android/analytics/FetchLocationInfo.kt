package com.stt.android.analytics

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.mapbox.FetchLocationNameUseCase
import com.stt.android.domain.mapbox.Place
import com.stt.android.usecases.location.LastKnownLocationUseCase
import timber.log.Timber
import java.util.Locale
import kotlin.time.Duration.Companion.minutes

abstract class FetchLocationInfo(
    private val locationUseCase: LastKnownLocationUseCase,
    private val fetchLocationNameUseCase: FetchLocationNameUseCase,
    private val fetchRemoteLocationInfoRepository: FetchRemoteLocationInfoRepository,
) {
    suspend fun fetchLocationInfo(): Place? {
        // the place info from local is different from phone brand, so get it from cloud
        return runSuspendCatching {
            val remoteLocationInfo = fetchRemoteLocationInfoRepository.fetchLocationInfo()
            Place(
                city = remoteLocationInfo.city,
                province = remoteLocationInfo.region,
                country = remoteLocationInfo.country
            )
        }.onFailure {
            Timber.i("fail to fetch locaion from remote")
        }.getOrNull()
    }

    private suspend fun fetchLocationInfoFromGeo(): Place? {
        // 30 min
        val timeAgo = System.currentTimeMillis() - 30.minutes.inWholeMilliseconds
        val lastKnownLocation = locationUseCase.getLastKnownLocation(
            skipPassiveProvider = false,
            timeInMilliSecondsSinceEpoch = timeAgo
        )
        return convertPlace(
            lastKnownLocation?.let {
                fetchLocationNameUseCase.invoke(
                    FetchLocationNameUseCase.Params(
                        latitude = it.latitude,
                        longitude = it.longitude,
                        useCoarseAccuracy = true,
                        locale = Locale.US
                    )
                )
            }
        )
    }

    private suspend fun tryFetchLocationInfo(): Place? {
        var tryTimes = TRY_TIMES
        var place: Place? = null
        while (tryTimes > 0) {
            place = fetchLocationInfoFromGeo()
            if (place != null) break
            tryTimes--
        }
        return place
    }

    abstract fun convertPlace(place: Place?): Place?

    companion object {
        private const val TRY_TIMES = 3
    }
}
