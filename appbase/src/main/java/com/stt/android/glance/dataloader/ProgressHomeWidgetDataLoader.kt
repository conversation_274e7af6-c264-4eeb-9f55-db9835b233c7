package com.stt.android.glance.dataloader

import androidx.compose.ui.graphics.Color
import androidx.glance.color.ColorProvider
import com.stt.android.R
import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.diary.GetTrainingProgressDataUseCase
import com.stt.android.domain.diary.models.FormPhase
import com.stt.android.glance.data.ProgressHomeWidgetInfo
import java.time.LocalDate
import javax.inject.Inject
import kotlin.math.roundToInt

class ProgressHomeWidgetDataLoader @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val getTrainingProgressDataUseCase: GetTrainingProgressDataUseCase,
) {
    suspend fun load() = getTrainingProgressDataUseCase
        .invoke(
            GetTrainingProgressDataUseCase.Params(
                username = currentUserController.currentUser.username,
                firstDay = LocalDate.now(),
                lastDay = LocalDate.now(),
                addZeroValuesBeforeFirstRecordedTssDate = false,
            )
        ).lastOrNull()
        ?.let { progressData ->
            ProgressHomeWidgetInfo(
                contentRes = when (FormPhase.fromFormValue(progressData.form)) {
                    FormPhase.TOO_EASY -> R.string.tss_phase_description_losing_fitness_or_recovering_title
                    FormPhase.MAINTAINING_FITNESS -> R.string.tss_phase_description_maintaining_fitness_title
                    FormPhase.PRODUCTIVE_TRAINING -> R.string.tss_phase_description_productive_training_title
                    FormPhase.TOO_HARD -> R.string.tss_phase_description_going_too_hard_title
                },
                contentColor = when (FormPhase.fromFormValue(progressData.form)) {
                    FormPhase.TOO_EASY -> ColorProvider(
                        day = Color(0xFFF1ADBD),
                        night = Color(0xFFF1ADBD),
                    )

                    FormPhase.MAINTAINING_FITNESS -> ColorProvider(
                        day = Color(0xFFE9839C),
                        night = Color(0xFFE9839C),
                    )

                    FormPhase.PRODUCTIVE_TRAINING -> ColorProvider(
                        day = Color(0xFFDB315A),
                        night = Color(0xFFDB315A),
                    )

                    FormPhase.TOO_HARD -> ColorProvider(
                        day = Color(0xFF831D36),
                        night = Color(0xFFE85F68),
                    )
                },
                contentFontSize = when (FormPhase.fromFormValue(progressData.form)) {
                    FormPhase.TOO_EASY -> 18f
                    FormPhase.MAINTAINING_FITNESS -> 24f
                    FormPhase.PRODUCTIVE_TRAINING -> 24f
                    FormPhase.TOO_HARD -> 24f
                },
                ctl = progressData.fitness.roundToInt(),
                atl = progressData.fatigue.roundToInt(),
                tsb = progressData.form.roundToInt(),
            )
        }
        ?: ProgressHomeWidgetInfo(
            contentRes = R.string.tss_form_insight_no_activity_data,
            contentColor = null,
            contentFontSize = 14f,
            ctl = null,
            atl = null,
            tsb = null,
        )
}
