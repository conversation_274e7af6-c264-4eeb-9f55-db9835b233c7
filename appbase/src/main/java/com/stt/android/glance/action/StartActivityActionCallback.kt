package com.stt.android.glance.action

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import androidx.glance.GlanceId
import androidx.glance.action.ActionParameters
import androidx.glance.action.ActionParameters.Key
import androidx.glance.action.actionParametersOf
import androidx.glance.appwidget.action.ActionCallback
import androidx.glance.appwidget.action.actionRunCallback
import androidx.glance.appwidget.action.actionStartActivity
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceType.APP_WIDGET
import com.stt.android.analytics.AppOpenAnalytics
import com.stt.android.utils.BrandUtils
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import timber.log.Timber

/**
 * I don't know why, it just don't work on Huawei devices.
 */
fun actionStartActivityWithAnalytics(
    intent: Intent,
    widgetName: String,
) = if (BrandUtils.isHuawei()) {
    actionStartActivity(intent)
} else {
    actionRunCallback<StartActivityActionCallback>(
        actionParametersOf(
            StartActivityActionCallback.KEY_INTENT to intent,
            StartActivityActionCallback.KEY_WIDGET_NAME to widgetName,
        )
    )
}

internal class StartActivityActionCallback : ActionCallback {
    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface AnalyticsEntryPoint {
        fun appOpenAnalytics(): AppOpenAnalytics
    }

    override suspend fun onAction(
        context: Context,
        glanceId: GlanceId,
        parameters: ActionParameters,
    ) {
        val entryPoint = EntryPointAccessors.fromApplication(
            context.applicationContext,
            AnalyticsEntryPoint::class.java,
        )

        parameters[KEY_INTENT]?.let { intent ->
            try {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                context.startActivity(intent)
                val widgetName = parameters[KEY_WIDGET_NAME] ?: ""
                entryPoint.appOpenAnalytics().trackEvent(APP_WIDGET, widgetName)
            } catch (e: ActivityNotFoundException) {
                Timber.e(e, "Failed to start activity")
            }
        }
    }

    companion object {
        val KEY_INTENT = Key<Intent>("intent")
        val KEY_WIDGET_NAME = Key<String>("widget_name")
    }
}
