package com.stt.android.di.rankings;

import com.stt.android.data.ranking.RankingRepository;
import com.stt.android.data.source.local.DaoFactory;
import com.stt.android.data.source.local.RankingDao;
import com.stt.android.domain.ranking.RankingDataSource;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;

@Module
public abstract class RankingsModule {
    @Provides
    static RankingDao provideRankingDao(DaoFactory daoFactory) {
        return daoFactory.getRankingDao();
    }

    @Binds
    abstract RankingDataSource bindRankingDataSource(RankingRepository repository);
}
