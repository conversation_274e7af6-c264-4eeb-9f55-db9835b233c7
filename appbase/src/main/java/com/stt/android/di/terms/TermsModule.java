package com.stt.android.di.terms;

import com.squareup.moshi.Moshi;
import com.stt.android.remote.AuthProvider;
import com.stt.android.remote.BaseUrl;
import com.stt.android.remote.BaseUrlV2;
import com.stt.android.remote.SharedOkHttpClient;
import com.stt.android.remote.UserAgent;
import com.stt.android.remote.di.BrandOkHttpConfigFactory;
import com.stt.android.remote.di.RestApiFactory;
import com.stt.android.remote.terms.TermsRestApi;
import com.stt.android.remote.terms.TermsRestApiV2;
import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.components.SingletonComponent;
import okhttp3.OkHttpClient;

@SuppressWarnings("WeakerAccess")
@Module
@InstallIn(SingletonComponent.class)
public abstract class TermsModule {
    @Provides
    public static TermsRestApi provideTermsRestApi(
        @SharedOkHttpClient OkHttpClient sharedClient,
        AuthProvider authProvider,
        @BaseUrl String baseUrl,
        @UserAgent String userAgent,
        Mo<PERSON> moshi
    ) {
        return RestApiFactory.buildRestApi(
            sharedClient,
            baseUrl,
            TermsRestApi.class,
            BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
            moshi);
    }

    @Provides
    public static TermsRestApiV2 provideTermsRestApiV2(
        @SharedOkHttpClient OkHttpClient sharedClient,
        AuthProvider authProvider,
        @BaseUrlV2 String baseUrl,
        @UserAgent String userAgent,
        Moshi moshi
    ) {
        return RestApiFactory.buildRestApi(
            sharedClient,
            baseUrl,
            TermsRestApiV2.class,
            BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
            moshi);
    }
}
