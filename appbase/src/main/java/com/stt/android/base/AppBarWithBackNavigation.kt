package com.stt.android.base

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.AppBarDefaults
import androidx.compose.material.ContentAlpha
import androidx.compose.material.LocalContentAlpha
import androidx.compose.material.MaterialTheme
import androidx.compose.material.ProvideTextStyle
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.contentColorFor
import androidx.compose.material.primarySurface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.nearBlack
import java.util.Locale
import com.stt.android.R as BaseR

@Composable
fun AppBarWithBackNavigation(
    fragment: Fragment,
    modifier: Modifier = Modifier,
    actionText: String = "",
    showBackBtn: Boolean = true,
    onBackClicked: (() -> Unit)? = null,
    onActionClicked: (() -> Unit)? = null,
) {
    AppBarWithBackNavigation(
        onNavigationClick = {
            with(fragment.findNavController()) {
                onBackClicked?.invoke() ?: kotlin.run {
                    if (currentBackStackEntry == null) {
                        fragment.requireActivity().finish()
                    } else {
                        popBackStack()
                    }
                }
            }
        },
        modifier = modifier,
        actionText = actionText,
        showBackBtn = showBackBtn,
        onActionClick = onActionClicked
    )
}

@Preview
@Composable
private fun AppBarWithBackNavigationPreview() {
    AppBarWithBackNavigation(onNavigationClick = {})
}

@Composable
fun AppBarWithBackNavigation(
    title: @Composable () -> Unit,
    onNavigationClick: () -> Unit,
    modifier: Modifier = Modifier,
    actionText: String = "",
    onActionClick: (() -> Unit)? = null,
    showBackBtn: Boolean = true,
) {
    TopAppBar(
        title = title,
        modifier = modifier,
        contentColor = MaterialTheme.colors.onSurface,
        backgroundColor = MaterialTheme.colors.surface, // default background is Primary
        navigationIcon = {
            if (showBackBtn) {
                SuuntoIconButton(
                    icon = SuuntoIcons.ActionBack,
                    onClick = onNavigationClick,
                    contentDescription = stringResource(BaseR.string.back),
                )
            }
        },
        actions = {
            if (actionText.isNotEmpty()) {
                Text(
                    actionText.uppercase(Locale.getDefault()),
                    color = MaterialTheme.colors.nearBlack,
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.clickableThrottleFirst(onClick = {
                        onActionClick?.invoke()
                    })
                )
            }
        }
    )
}

@Composable
fun AppBarWithBackNavigation(
    onNavigationClick: () -> Unit,
    modifier: Modifier = Modifier,
    actionText: String = "",
    onActionClick: (() -> Unit)? = null,
    showBackBtn: Boolean = true,
) {
    AppBarWithBackNavigation(
        title = {
            Image(
                painter = painterResource(id = R.drawable.app_logo_small),
                contentDescription = "logo"
            )
        },
        onNavigationClick = onNavigationClick,
        modifier = modifier,
        actionText = actionText,
        onActionClick = onActionClick,
        showBackBtn = showBackBtn
    )
}

@Composable
fun TopAppBar(
    title: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    navigationIcon: @Composable (RowScope.() -> Unit)? = null,
    actions: @Composable RowScope.() -> Unit = {},
    backgroundColor: Color = MaterialTheme.colors.primarySurface,
    contentColor: Color = contentColorFor(backgroundColor),
    elevation: Dp = AppBarDefaults.TopAppBarElevation
) {
    AppBar(
        backgroundColor,
        contentColor,
        elevation,
        AppBarDefaults.ContentPadding,
        RectangleShape,
        modifier
    ) {
        if (navigationIcon == null) {
            Spacer(TitleInsetWithoutIcon)
        } else {
            Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                CompositionLocalProvider(
                    LocalContentAlpha provides ContentAlpha.high,
                ) {
                    Row(
                        Modifier
                            .fillMaxHeight()
                            .weight(1f),
                        horizontalArrangement = Arrangement.Start,
                        verticalAlignment = Alignment.CenterVertically,
                        content = navigationIcon
                    )
                }

                Row(
                    Modifier
                        .fillMaxHeight()
                        .weight(1f),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center,
                ) {
                    ProvideTextStyle(value = MaterialTheme.typography.h6) {
                        CompositionLocalProvider(
                            LocalContentAlpha provides ContentAlpha.high,
                            content = title
                        )
                    }
                }

                CompositionLocalProvider(LocalContentAlpha provides ContentAlpha.medium) {
                    Row(
                        Modifier
                            .fillMaxHeight()
                            .weight(1f)
                            .padding(end = 10.dp),
                        horizontalArrangement = Arrangement.End,
                        verticalAlignment = Alignment.CenterVertically,
                        content = actions
                    )
                }
            }
        }
    }
}

@Composable
private fun AppBar(
    backgroundColor: Color,
    contentColor: Color,
    elevation: Dp,
    contentPadding: PaddingValues,
    shape: Shape,
    modifier: Modifier = Modifier,
    content: @Composable RowScope.() -> Unit
) {
    Surface(
        color = backgroundColor,
        contentColor = contentColor,
        elevation = elevation,
        shape = shape,
        modifier = modifier
    ) {
        CompositionLocalProvider(LocalContentAlpha provides ContentAlpha.medium) {
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(contentPadding)
                    .height(AppBarHeight),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically,
                content = content
            )
        }
    }
}

private val AppBarHeight = 56.dp
private val AppBarHorizontalPadding = 4.dp
private val TitleInsetWithoutIcon = Modifier.width(16.dp - AppBarHorizontalPadding)
