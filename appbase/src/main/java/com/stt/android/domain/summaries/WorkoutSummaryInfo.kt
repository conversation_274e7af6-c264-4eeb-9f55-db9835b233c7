package com.stt.android.domain.summaries

import android.content.res.Resources
import com.stt.android.domain.Filterable
import com.stt.android.domain.workouts.WorkoutHeader

class WorkoutSummaryInfo(val workout: WorkoutHeader) :
    Filterable {
    override fun applyFilter(
        constraints: Array<out CharSequence>?,
        resources: Resources
    ): Boolean {
        return workout.applyFilter(constraints, resources)
    }
}
