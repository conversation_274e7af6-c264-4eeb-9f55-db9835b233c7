package com.stt.android.domain.user;

import androidx.annotation.Nullable;
import com.google.gson.annotations.SerializedName;
import com.stt.android.workoutdetail.comments.WorkoutComment;

/**
 * Convenient class that represents a workout comment coming from the backend
 */
public class BackendWorkoutComment {
    @SerializedName("key")
    @Nullable
    private final String key;
    @SerializedName("timestamp")
    private final long timestamp;
    @SerializedName("username")
    private final String username;
    @SerializedName("realname")
    private final String realName;
    @SerializedName("profilePictureUrl")
    private final String profilePictureUrl;
    @SerializedName("comment")
    private final String message;

    private BackendWorkoutComment(@Nullable String key, long timestamp, String username, String realName,
                                  String profilePictureUrl, String message) {
        this.key = key;
        this.timestamp = timestamp;
        this.username = username;
        this.realName = realName;
        this.profilePictureUrl = profilePictureUrl;
        this.message = message;
    }

    public WorkoutComment getWorkoutComment(String workoutKey) {
        return new WorkoutComment(key, workoutKey, message, username, realName, profilePictureUrl, timestamp);
    }
}
