package com.stt.android.domain.workout;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;
import com.stt.android.hr.HeartRateEvent;

import java.util.Locale;

/**
 * Holds all the information provided by {@link HeartRateEvent} and adds the
 * information related to a workout.
 * <p/>
 * <pre>
 * Note: it implements its own {@link Parcelable} methods and does not
 *       rely on {@link HeartRateEvent} ones.
 * </pre>
 */
public class WorkoutHrEvent extends HeartRateEvent implements Parcelable {
    public static final Parcelable.Creator<HeartRateEvent> CREATOR = new Creator<HeartRateEvent>() {
        @Override
        public HeartRateEvent[] newArray(int size) {
            return new HeartRateEvent[size];
        }

        @Override
        public HeartRateEvent createFromParcel(Parcel source) {
            long timestamp = source.readLong();
            int heartRateInBpm = source.readInt();
            int[] frame = new int[source.readInt()];
            source.readIntArray(frame);
            long millisecondsInWorkout = source.readLong();
            return new WorkoutHrEvent(timestamp, heartRateInBpm, frame, millisecondsInWorkout);
        }
    };
    /**
     * Refers to the duration in the workout
     */
    @SerializedName("millisecondsInWorkout")
    private final long millisecondsInWorkout;

    public WorkoutHrEvent(long timestamp, int heartRateInBpm, int[] binaryData, long millisecondsInWorkout) {
        super(timestamp, heartRateInBpm, binaryData);
        this.millisecondsInWorkout = millisecondsInWorkout;
    }

    public WorkoutHrEvent(long workoutTimestamp, int heartRateInBpm, long millisecondsInWorkout) {
        this(workoutTimestamp, heartRateInBpm, DUMMY_BINARY_DATA, millisecondsInWorkout);
    }

    public long getMillisecondsInWorkout() {
        return millisecondsInWorkout;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(getTimestamp());
        dest.writeInt(getHeartRate());
        int[] rawBinaryData = getRawData();
        dest.writeInt(rawBinaryData.length);
        dest.writeIntArray(rawBinaryData);
        dest.writeLong(millisecondsInWorkout);
    }

    @Override
    public String toString() {
        return String.format(Locale.US, "[timestamp: %d, ms in workout: %d, bpm: %d]",
                getTimestamp(), getMillisecondsInWorkout(), getHeartRate());
    }
}
