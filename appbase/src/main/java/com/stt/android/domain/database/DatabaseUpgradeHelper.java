
package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import java.sql.SQLException;

public abstract class DatabaseUpgradeHelper {
    protected final SQLiteDatabase db;
    protected final ConnectionSource connectionSource;
    protected final DatabaseHelper databaseHelper;

    public DatabaseUpgradeHelper(SQLiteDatabase db, ConnectionSource connectionSource, DatabaseHelper databaseHelper) {
        this.db = db;
        this.connectionSource = connectionSource;
        this.databaseHelper = databaseHelper;
    }

    public abstract void upgrade() throws SQLException;

}
