package com.stt.android.domain.user

import com.stt.android.domain.goaldefinition.GoalDefinition

data class GoalSummary(
    val goalDefinition: GoalDefinition,
    val goals: List<Goal>,
    val goalsBeforeTimeBegins: List<Goal>
) {
    val averageAchieved: Double
    val maxAchieved: Int
    val totalAchieved: Int
    val successfulGoals: Int
    val currentStreak: Int
    val longestStreak: Int
    val total: Int = goals.size

    init {
        if (goalDefinition.period === GoalDefinition.Period.CUSTOM) {
            var achieved = 0
            var maxAchieved = 0
            for (goal in goals) {
                achieved += goal.achieved
                if (goal.achieved > maxAchieved) {
                    maxAchieved = goal.achieved
                }
            }
            this.maxAchieved = maxAchieved
            totalAchieved = achieved

            averageAchieved = if (goals.isEmpty()) 0.0 else achieved.toDouble() / goals.size

            successfulGoals = 0
            currentStreak = 0
            longestStreak = 0
        } else {
            var maxAchieved = 0
            var successfulGoals = 0
            var longestStreak = 0
            var streak = 0
            var totalAchieved = 0.0
            val size = goals.size
            for (goal in goals) {
                totalAchieved += goal.achieved
                if (goal.achieved > maxAchieved) {
                    maxAchieved = goal.achieved
                }

                if (goal.status == Goal.STATUS_REACHED) {
                    ++successfulGoals
                    ++streak

                    if (streak > longestStreak) {
                        longestStreak = streak
                    }
                } else {
                    streak = 0
                }
            }
            this.maxAchieved = maxAchieved
            this.successfulGoals = successfulGoals
            this.longestStreak = longestStreak
            this.totalAchieved = totalAchieved.toInt()
            averageAchieved = if (goals.isEmpty()) totalAchieved else totalAchieved / size

            var currentStreak = 0
            var isFirst = true
            for (goal in goals) {
                if (goal.status == Goal.STATUS_REACHED) {
                    ++currentStreak
                } else if (!isFirst) {
                    // when counting the current streak, we exclude the current period if the user
                    // hasn't achieved the goal yet, otherwise the current streak is almost always 0
                    break
                }
                isFirst = false
            }
            this.currentStreak = currentStreak
        }
    }
}
