@file:Suppress("DEPRECATION")

package com.stt.android.domain.database

import android.content.ContentValues
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import com.google.gson.Gson
import com.google.gson.JsonParseException
import com.google.gson.stream.JsonReader
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.domain.database.deprecated.OldRouteTable
import com.stt.android.home.explore.routes.RouteSegment
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.utils.FileUtils
import com.stt.android.utils.STTConstants
import okio.buffer
import okio.source
import timber.log.Timber
import java.io.File
import java.io.IOException
import java.io.InputStreamReader
import java.sql.SQLException

class DatabaseUpgrade33To34Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper,
    val fileUtils: FileUtils,
    val gson: <PERSON>son
) : DatabaseUpgradeHelper(
    db,
    connectionSource,
    databaseHelper
) {

    @Throws(SQLException::class)
    override fun upgrade() {
        // This migration does the following:
        // In order to delete totalAscent column, we have to rename the old table
        // and create a new table. SQLite doesn't support dropping of a single column.
        // At the same time, we also migrate route segments stored previously on the filesystem
        // as separate files into a new column - segments - in the new table.
        // We also convert start, center and stop points and activity IDs from byte array to json strings

        db.apply {
            val oldRoutesTable = "old_routes"

            execSQL("DROP INDEX IF EXISTS routes_key_idx;")
            execSQL("ALTER TABLE ${OldRouteTable.TABLE_NAME} RENAME TO $oldRoutesTable;")
            execSQL(
                """
                CREATE TABLE IF NOT EXISTS `${OldRouteTable.TABLE_NAME}` (
                    `_id` TEXT NOT NULL,
                    `key` TEXT NOT NULL,
                    `ownerUserName` TEXT NOT NULL,
                    `name` TEXT NOT NULL,
                    `visibility` TEXT NOT NULL,
                    `activityIds` TEXT NOT NULL,
                    `avgSpeed` REAL NOT NULL,
                    `totalDistance` REAL NOT NULL,
                    `startPoint` TEXT NOT NULL,
                    `centerPoint` TEXT NOT NULL,
                    `stopPoint` TEXT NOT NULL,
                    `locallyChanged` INTEGER NOT NULL,
                    `deleted` INTEGER NOT NULL,
                    `created` INTEGER NOT NULL,
                    `watchSyncState` TEXT NOT NULL,
                    `watchSyncResponseCode` INTEGER NOT NULL,
                    `segments` TEXT NOT NULL,
                    `watchRouteId` INTEGER DEFAULT 0 NOT NULL,
                    `watchEnabled` INTEGER DEFAULT 0 NOT NULL,
                    PRIMARY KEY(`_id`))
                """.trimIndent()
            )

            query(oldRoutesTable, null, null, null, null, null, null).use { cursor ->
                if (cursor.moveToFirst()) {
                    Timber.v("Loaded routes from old database table")
                    do {
                        val values = createContentValues(cursor)
                        Timber.d("Migrating route segments from files to database")
                        val routeId = cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.ID))
                        val routeFile = fileUtils.getInternalFilePath(STTConstants.DIRECTORY_ROUTES, routeId)
                        val segmentsJson = gson.toJson(loadSegmentsFromDisk(routeId, routeFile))
                        values.put(OldRouteTable.DbFields.SEGMENTS, segmentsJson)
                        // We set locally changed flag to true so that this route will be synced to
                        // the server with the new route segments structure without polyline.
                        values.put(OldRouteTable.DbFields.LOCALLY_CHANGED, true)
                        val count = insert(OldRouteTable.TABLE_NAME, null, values)
                        if (count > 0) {
                            FileUtils.delete(routeFile)
                            Timber.v("Route segments for route ID %s were successfully migrated to database", routeId)
                        }
                    } while (cursor.moveToNext())
                }
            }
            execSQL("DROP TABLE $oldRoutesTable;")
        }
        Timber.d("Successfully migrated route segments to database")
    }

    @Throws(IOException::class, JsonParseException::class)
    private fun loadSegmentsFromDisk(routeId: String, routeFile: File): List<RouteSegment> {
        Timber.v("Loading route segments for ID: %s from file: %s", routeId, routeFile.absolutePath)
        val source = routeFile.source().buffer()
        val reader = JsonReader(
            InputStreamReader(
                source.inputStream(),
                ANetworkProvider.UTF_8_CHARSET
            )
        )
        val segments = ArrayList<RouteSegment>()
        reader.beginArray()
        var i = 0
        while (reader.hasNext()) {
            try {
                val segment = gson.fromJson<RouteSegment>(reader, RouteSegment::class.java)
                segments.add(segment)
            } catch (e: Exception) {
                Timber.w(e, "Error parsing route segment, route id:$routeId, segment index:$i")
            }
            i++
        }
        reader.endArray()
        reader.close()

        return segments
    }

    private fun createContentValues(cursor: Cursor): ContentValues {
        val values = ContentValues()
        values.put(OldRouteTable.DbFields.ID, cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.ID)))
        values.put(OldRouteTable.DbFields.WATCH_ROUTE_ID, 0)
        values.put(OldRouteTable.DbFields.KEY, cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.KEY)))
        values.put(OldRouteTable.DbFields.OWNER_USER_NAME, cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.OWNER_USER_NAME)))
        values.put(OldRouteTable.DbFields.NAME, cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.NAME)))
        values.put(OldRouteTable.DbFields.VISIBILITY, cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.VISIBILITY)))
        values.put(OldRouteTable.DbFields.ACTIVITY_IDS, convertIntByteArrayToJsonString(cursor.getBlob(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.ACTIVITY_IDS))))
        values.put(OldRouteTable.DbFields.AVERAGE_SPEED, cursor.getDouble(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.AVERAGE_SPEED)))
        values.put(OldRouteTable.DbFields.TOTAL_DISTANCE, cursor.getDouble(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.TOTAL_DISTANCE)))
        values.put(OldRouteTable.DbFields.START_POINT, convertPointByteArrayToJsonString(cursor.getBlob(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.START_POINT))))
        values.put(OldRouteTable.DbFields.CENTER_POINT, convertPointByteArrayToJsonString(cursor.getBlob(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.CENTER_POINT))))
        values.put(OldRouteTable.DbFields.STOP_POINT, convertPointByteArrayToJsonString(cursor.getBlob(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.STOP_POINT))))
        values.put(OldRouteTable.DbFields.LOCALLY_CHANGED, cursor.getInt(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.LOCALLY_CHANGED)))
        values.put(OldRouteTable.DbFields.DELETED, cursor.getInt(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.DELETED)))
        values.put(OldRouteTable.DbFields.CREATED, cursor.getLong(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.CREATED)))
        values.put(OldRouteTable.DbFields.WATCH_SYNC_STATE, cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.WATCH_SYNC_STATE)))
        values.put(OldRouteTable.DbFields.WATCH_SYNC_RESPONSE_CODE, cursor.getInt(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.WATCH_SYNC_RESPONSE_CODE)))
        return values
    }

    private fun convertPointByteArrayToJsonString(pointByteArray: ByteArray): String {
        val point = MigrationUtil.bytesToPoint(pointByteArray)
        return JsonPointPersister.pointToString(point)
    }

    private fun convertIntByteArrayToJsonString(intByteArray: ByteArray): String {
        val ints = MigrationUtil.byteArrayToIntList(intByteArray)
        return JsonIntegerListPersister.intListToString(ints)
    }
}
