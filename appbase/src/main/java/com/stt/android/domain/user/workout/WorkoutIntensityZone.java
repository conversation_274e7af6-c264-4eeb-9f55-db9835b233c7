package com.stt.android.domain.user.workout;

import java.io.Serializable;

/**
 * @deprecated  this class should only be used in DatabaseUpgrade58To59Helper migration.
 * Do not change class name or package or fields name.
 */
@Deprecated
public class WorkoutIntensityZone implements Serializable {

    private static final long serialVersionUID = 877349660248105118L;

    // these fields are scrambled exactly the same way that pro<PERSON> did when we saved this serializable in legacy DB BLOBs
    private final float b; // zone1Duration;
    private final float c; // zone2Duration;
    private final float d; // zone3Duration;
    private final float e; // zone4Duration;
    private final float f; // zone5Duration;
    private final float g; // zone2LowerLimit;
    private final float h; // zone3LowerLimit;
    private final float i; // zone4LowerLimit;
    private final float j; // zone5LowerLimit;

    public WorkoutIntensityZone(
        float zone1Duration,
        float zone2Duration,
        float zone3Duration,
        float zone4Duration,
        float zone5Duration,
        float zone2LowerLimit,
        float zone3LowerLimit,
        float zone4LowerLimit,
        float zone5LowerLimit
    ) {
        this.b = zone1Duration;
        this.c = zone2Duration;
        this.d = zone3Duration;
        this.e = zone4Duration;
        this.f = zone5Duration;
        this.g = zone2LowerLimit;
        this.h = zone3LowerLimit;
        this.i = zone4LowerLimit;
        this.j = zone5LowerLimit;
    }

    public float getZone1Duration() {
        return b;
    }

    public float getZone2Duration() {
        return c;
    }

    public float getZone3Duration() {
        return d;
    }

    public float getZone4Duration() {
        return e;
    }

    public float getZone5Duration() {
        return f;
    }

    public float getZone2LowerLimit() {
        return g;
    }

    public float getZone3LowerLimit() {
        return h;
    }

    public float getZone4LowerLimit() {
        return i;
    }

    public float getZone5LowerLimit() {
        return j;
    }
}
