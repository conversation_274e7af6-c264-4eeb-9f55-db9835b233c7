package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.stt.android.domain.user.PendingPurchase;
import com.stt.android.domain.user.SubscriptionInfo;
import com.stt.android.domain.user.SubscriptionItem;
import java.sql.SQLException;

public class DatabaseUpgrade5To6Helper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade5To6Helper(SQLiteDatabase db, ConnectionSource connectionSource,
                                     DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        // SubscriptionInfo table was added
        TableUtils.createTableIfNotExists(connectionSource, SubscriptionInfo.class);
        // PendingPurchase table was added
        TableUtils.createTableIfNotExists(connectionSource, PendingPurchase.class);
        // SubscriptionItem table was added
        TableUtils.createTableIfNotExists(connectionSource, SubscriptionItem.class);
    }
}
