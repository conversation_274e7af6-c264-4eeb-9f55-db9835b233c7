@file:Suppress("DEPRECATION")

package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.domain.database.deprecated.OldRouteTable
import java.sql.SQLException

class DatabaseUpgrade31To32Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper
) :
    DatabaseUpgradeHelper(
        db,
        connectionSource,
        databaseHelper
    ) {

    @Throws(SQLException::class)
    override fun upgrade() {
        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.WATCH_SYNC_STATE)
        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.WATCH_SYNC_RESPONSE_CODE)
    }
}
