package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import java.sql.SQLException

class DatabaseUpgrade36To37Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {

    @Throws(SQLException::class)
    override fun upgrade() {
        DatabaseHelper.addColumnIfNotExist(
            db,
            DatabaseUpgrade49To50Helper.SUMMARY_EXTENSION_TABLE_NAME,
            DatabaseUpgrade49To50Helper.DbFields.EXERCISE_ID
        )
    }
}
