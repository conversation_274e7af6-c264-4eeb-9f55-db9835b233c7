package com.stt.android.domain.user.workout;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;
import com.stt.android.domain.workouts.logbookentry.LogbookEntry;

@DatabaseTable(tableName = SuuntoLogbookEntry.TABLE_NAME)
public class SuuntoLogbookEntry implements Parcelable {
    public static final String TABLE_NAME = "logbookentry";

    public abstract class DbFields {

        public static final String WORKOUT_ID = "workoutId";
        public static final String ENTRY_ID = "entryId";
        public static final String DEVICE_NAME = "deviceName";
        public static final String SERIAL_NUMBER = "serialNumber";
        public static final String HW_NAME = "hwName";
        public static final String SW_VERSION = "swVersion";
    }
    @DatabaseField(columnName = DbFields.WORKOUT_ID, id = true)
    private final int workoutId;

    @DatabaseField(columnName = DbFields.ENTRY_ID)
    private final long entryId;

    @DatabaseField(columnName = DbFields.DEVICE_NAME)
    private final String deviceName;

    @DatabaseField(columnName = DbFields.SERIAL_NUMBER)
    private final String serialNumber;

    @DatabaseField(columnName = DbFields.HW_NAME)
    private final String hwName;

    @DatabaseField(columnName = DbFields.SW_VERSION)
    private final String swVersion;

    // OrmLite constructor
    SuuntoLogbookEntry() {
        this(0, 0L, null, null, null, null);
    }
    public SuuntoLogbookEntry(int workoutId, long entryId, String deviceName, String serialNumber,
        String hwName, String swVersion) {
        this.workoutId = workoutId;
        this.entryId = entryId;
        this.deviceName = deviceName;
        this.serialNumber = serialNumber;
        this.hwName = hwName;
        this.swVersion = swVersion;
    }

    @NonNull
    public static SuuntoLogbookEntry fromLogbookEntry(LogbookEntry entry) {
        return new SuuntoLogbookEntry(
            entry.getWorkoutId(),
            entry.getEntryId(),
            entry.getDeviceName(),
            entry.getSerialNumber(),
            entry.getHwName(),
            entry.getSwVersion()
        );
    }

    public LogbookEntry toLogbookEntry() {
        return new LogbookEntry(
            entryId,
            workoutId,
            deviceName,
            serialNumber,
            hwName,
            swVersion
        );
    }

    public int getWorkoutId() {
        return workoutId;
    }

    public long getEntryId() {
        return entryId;
    }

    @Nullable
    public String getDeviceName() {
        return deviceName;
    }

    @Nullable
    public String getSerialNumber() {
        return serialNumber;
    }

    @Nullable
    public String getHwName() {
        return hwName;
    }

    @Nullable
    public String getSwVersion() {
        return swVersion;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SuuntoLogbookEntry that = (SuuntoLogbookEntry) o;

        if (workoutId != that.workoutId) return false;
        if (entryId != that.entryId) return false;
        if (deviceName != null ? !deviceName.equals(that.deviceName) : that.deviceName != null) {
            return false;
        }
        if (serialNumber != null ? !serialNumber.equals(that.serialNumber)
            : that.serialNumber != null) {
            return false;
        }
        if (hwName != null ? !hwName.equals(that.hwName) : that.hwName != null) {
            return false;
        }
        return swVersion != null ? swVersion.equals(that.swVersion) : that.swVersion == null;
    }

    @Override
    public int hashCode() {
        int result = workoutId;
        result = 31 * result + (int) (entryId ^ (entryId >>> 32));
        result = 31 * result + (deviceName != null ? deviceName.hashCode() : 0);
        result = 31 * result + (serialNumber != null ? serialNumber.hashCode() : 0);
        result = 31 * result + (hwName != null ? hwName.hashCode() : 0);
        result = 31 * result + (swVersion != null ? swVersion.hashCode() : 0);
        return result;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.workoutId);
        dest.writeLong(this.entryId);
        dest.writeString(this.deviceName);
        dest.writeString(this.serialNumber);
        dest.writeString(this.hwName);
        dest.writeString(this.swVersion);
    }

    public static final Parcelable.Creator<SuuntoLogbookEntry> CREATOR =
        new Parcelable.Creator<SuuntoLogbookEntry>() {
            @Override
            public SuuntoLogbookEntry createFromParcel(Parcel in) {
                int workoutId = in.readInt();
                long entryId = in.readLong();
                String deviceName = in.readString();
                String serialNumber = in.readString();
                String hwVersion = in.readString();
                String swVersion = in.readString();
                return new SuuntoLogbookEntry(workoutId, entryId, deviceName, serialNumber,
                    hwVersion, swVersion);
            }

            @Override
            public SuuntoLogbookEntry[] newArray(int size) {
                return new SuuntoLogbookEntry[size];
            }
        };

    @NonNull
    public Builder toBuilder() {
        return builder().workoutId(workoutId)
            .entryId(entryId)
            .serialNumber(serialNumber)
            .deviceName(deviceName)
            .hwName(hwName)
            .swVersion(swVersion);
    }

    @NonNull
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private int workoutId;
        private long entryId;
        @Nullable
        private String deviceName;
        @Nullable
        private String serialNumber;
        @Nullable
        private String hwName;
        @Nullable
        private String swVersion;

        @NonNull
        public Builder workoutId(int id) {
            this.workoutId = id;
            return this;
        }

        @NonNull
        public Builder entryId(long entryId) {
            this.entryId = entryId;
            return this;
        }

        @NonNull
        public Builder deviceName(@Nullable String watchName) {
            this.deviceName = watchName;
            return this;
        }

        @NonNull
        public Builder serialNumber(@Nullable String serialNumber) {
            this.serialNumber = serialNumber;
            return this;
        }

        @NonNull
        public Builder hwName(@Nullable String hwName) {
            this.hwName = hwName;
            return this;
        }

        @NonNull
        public Builder swVersion(@Nullable String eswVersion) {
            this.swVersion = eswVersion;
            return this;
        }

        @NonNull
        public SuuntoLogbookEntry build() {
            return new SuuntoLogbookEntry(workoutId, entryId, deviceName, serialNumber, hwName,
                swVersion);
        }
    }
}
