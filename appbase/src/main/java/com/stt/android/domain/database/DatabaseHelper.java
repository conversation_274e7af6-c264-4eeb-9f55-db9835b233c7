package com.stt.android.domain.database;

import android.content.Context;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import com.google.gson.Gson;
import com.j256.ormlite.android.apptools.OrmLiteSqliteOpenHelper;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.squareup.moshi.Moshi;
import com.stt.android.R;
import com.stt.android.data.source.local.RankingDao;
import com.stt.android.data.source.local.billing.PendingPurchaseDao;
import com.stt.android.data.source.local.billing.SubscriptionInfoDao;
import com.stt.android.data.source.local.billing.SubscriptionItemDao;
import com.stt.android.data.source.local.fitnessextension.FitnessExtensionDao;
import com.stt.android.data.source.local.goaldefinition.GoalDefinitionDao;
import com.stt.android.data.source.local.intensityextension.IntensityExtensionDao;
import com.stt.android.data.source.local.routes.RouteDao;
import com.stt.android.data.source.local.summaryextension.SummaryExtensionDao;
import com.stt.android.data.source.local.user.UserDao;
import com.stt.android.data.source.local.workout.WorkoutHeaderDao;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.user.Reaction;
import com.stt.android.domain.user.ReactionSummary;
import com.stt.android.domain.user.VideoInformation;
import com.stt.android.domain.user.workout.SuuntoLogbookEntry;
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary;
import com.stt.android.follow.UserFollowStatus;
import com.stt.android.utils.FileUtils;
import com.stt.android.workoutdetail.comments.WorkoutComment;
import java.sql.SQLException;
import javax.inject.Inject;
import javax.inject.Singleton;
import timber.log.Timber;

/**
 * Database helper class used to manage the creation and upgrading database.
 */
@Singleton
public class DatabaseHelper extends OrmLiteSqliteOpenHelper {
    public static final String DATABASE_NAME = "stt.db";
    /**
     * When increasing database version remember to:
     * <ul>
     * <li>update {@link #onUpgrade(SQLiteDatabase, ConnectionSource, int, int)} </li>
     * <li>create the required {@link DatabaseUpgradeHelper}</li>
     * <li>run {@link DatabaseConfigUtil} to update the ormlite_config file! every time you
     * modify database files</li>
     * </ul>
     */
    private static final int DATABASE_VERSION = 62;

    private static final Class<?>[] TABLE_CLASSES =
        new Class<?>[] {
            ImageInformation.class,
            WorkoutComment.class,
            ReactionSummary.class,
            Reaction.class,
            SlopeSkiSummary.class,
            UserFollowStatus.class,
            VideoInformation.class,
            SuuntoLogbookEntry.class
        };
    private final Gson gson;
    private final FileUtils fileUtils;
    private final RouteDao routeDao;
    private final RankingDao rankingDao;
    private final GoalDefinitionDao goalDefinitionDao;
    private final SummaryExtensionDao summaryExtensionDao;
    private final SharedPreferences sharedPreferences;
    private final UserDao userDao;
    private final WorkoutHeaderDao workoutHeaderDao;

    private final SubscriptionItemDao subscriptionItemDao;

    private final SubscriptionInfoDao subscriptionInfoDao;

    private final PendingPurchaseDao pendingPurchaseDao;
    private final IntensityExtensionDao intensityExtensionDao;

    private final FitnessExtensionDao fitnessExtensionDao;

    private final Moshi moshi;

    @Inject
    public DatabaseHelper(
        Context context,
        Gson gson,
        FileUtils fileUtils,
        RouteDao routeDao,
        RankingDao rankingDao,
        GoalDefinitionDao goalDefinitionDao,
        SummaryExtensionDao summaryExtensionDao,
        UserDao userDao,
        SharedPreferences sharedPreferences,
        WorkoutHeaderDao workoutHeaderDao,
        SubscriptionItemDao subscriptionItemDao,
        SubscriptionInfoDao subscriptionInfoDao,
        PendingPurchaseDao pendingPurchaseDao,
        IntensityExtensionDao intensityExtensionDao,
        FitnessExtensionDao fitnessExtensionDao,
        Moshi moshi
    ) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION, R.raw.ormlite_config);
        this.gson = gson;
        this.fileUtils = fileUtils;
        this.routeDao = routeDao;
        this.rankingDao = rankingDao;
        this.goalDefinitionDao = goalDefinitionDao;
        this.summaryExtensionDao = summaryExtensionDao;
        this.sharedPreferences = sharedPreferences;
        this.userDao = userDao;
        this.workoutHeaderDao = workoutHeaderDao;
        this.subscriptionItemDao = subscriptionItemDao;
        this.subscriptionInfoDao = subscriptionInfoDao;
        this.pendingPurchaseDao = pendingPurchaseDao;
        this.intensityExtensionDao = intensityExtensionDao;
        this.fitnessExtensionDao = fitnessExtensionDao;
        this.moshi = moshi;
    }

    // Used in tests
    public DatabaseHelper(
        Context context,
        Gson gson,
        FileUtils fileUtils,
        RouteDao routeDao,
        RankingDao rankingDao,
        GoalDefinitionDao goalDefinitionDao,
        SummaryExtensionDao summaryExtensionDao,
        UserDao userDao,
        SharedPreferences sharedPreferences,
        WorkoutHeaderDao workoutHeaderDao,
        SubscriptionItemDao subscriptionItemDao,
        SubscriptionInfoDao subscriptionInfoDao,
        PendingPurchaseDao pendingPurchaseDao,
        IntensityExtensionDao intensityExtensionDao,
        FitnessExtensionDao fitnessExtensionDao,
        Moshi moshi,
        String databaseName,
        int databaseVersion
    ) {
        super(context, databaseName, null, databaseVersion, R.raw.ormlite_config);
        this.gson = gson;
        this.fileUtils = fileUtils;
        this.routeDao = routeDao;
        this.rankingDao = rankingDao;
        this.goalDefinitionDao = goalDefinitionDao;
        this.summaryExtensionDao = summaryExtensionDao;
        this.sharedPreferences = sharedPreferences;
        this.userDao = userDao;
        this.workoutHeaderDao = workoutHeaderDao;
        this.subscriptionItemDao = subscriptionItemDao;
        this.subscriptionInfoDao = subscriptionInfoDao;
        this.pendingPurchaseDao = pendingPurchaseDao;
        this.intensityExtensionDao = intensityExtensionDao;
        this.fitnessExtensionDao = fitnessExtensionDao;
        this.moshi = moshi;
    }

    static void addColumnIfNotExist(SQLiteDatabase db, String table, String column) {
        try (Cursor cursor = db.rawQuery("PRAGMA table_info(" + table + ")", null)) {
            int name = cursor.getColumnIndex("name");
            while (cursor.moveToNext()) {
                if (column.equals(cursor.getString(name))) {
                    return;
                }
            }
            db.execSQL("ALTER TABLE " + table + " ADD COLUMN " + column + ";");
        }
    }

    @Override
    public void onCreate(SQLiteDatabase db, ConnectionSource connectionSource) {
        try {
            db.beginTransaction();
            createAllTablesIfNotExist(connectionSource);
            db.setTransactionSuccessful();
        } catch (SQLException e) {
            // TODO: Handle this fatal error properly.
            Timber.e(e, "Can't create database");
            throw new RuntimeException(e);
        } finally {
            if (db.inTransaction()) {
                db.endTransaction();
            }
        }
    }

    @Override
    public void onConfigure(SQLiteDatabase db) {
        super.onConfigure(db);
        setWriteAheadLoggingEnabled(true);
    }

    private void createAllTablesIfNotExist(ConnectionSource connectionSource) throws SQLException {
        for (Class<?> tableClass : TABLE_CLASSES) {
            TableUtils.createTableIfNotExists(connectionSource, tableClass);
        }
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, ConnectionSource connectionSource, int oldVersion,
        int newVersion) {
        Timber.d("DatabaseHelper.onUpgrade() old %d, new %d", oldVersion, newVersion);
        try {
            db.beginTransaction();
            switch (oldVersion) {
                case 1:
                    new DatabaseUpgrade1To2Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 2:
                    new DatabaseUpgrade2To3Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 3:
                case 4:
                    new DatabaseUpgrade3To5Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 5:
                    new DatabaseUpgrade5To6Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 6:
                    new DatabaseUpgrade6To7Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 7:
                    new DatabaseUpgrade7To8Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 8:
                case 9:
                    new DatabaseUpgrade8To10Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 10:
                case 11:
                    new DatabaseUpgrade10To12Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 12:
                    new DatabaseUpgrade12To13Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 13:
                    new DatabaseUpgrade13To14Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 14:
                    new DatabaseUpgrade14To15Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 15:
                    new DatabaseUpgrade15To16Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 16:
                    new DatabaseUpgrade16To17Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 17:
                    new DatabaseUpgrade17To18Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 18:
                    new DatabaseUpgrade18To19Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 19:
                    // fall through
                case 20:
                    new DatabaseUpgrade20To21Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 21:
                    new DatabaseUpgrade21To22Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 22:
                    new DatabaseUpgrade22To23Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 23:
                    new DatabaseUpgrade23To24Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 24:
                    new DatabaseUpgrade24To25Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 25:
                    new DatabaseUpgrade25To26Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 26:
                    new DatabaseUpgrade26To27Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 27:
                    new DatabaseUpgrade27To28Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 28:
                    new DatabaseUpgrade28To29Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 29:
                    new DatabaseUpgrade29To30Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 30:
                    new DatabaseUpgrade30To31Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 31:
                    new DatabaseUpgrade31To32Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 32:
                    new DatabaseUpgrade32To33Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 33:
                    new DatabaseUpgrade33To34Helper(db, connectionSource, this, fileUtils, gson).upgrade();
                    // fall through
                case 34:
                    new DatabaseUpgrade34To35Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 35:
                    new DatabaseUpgrade35To36Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 36:
                    new DatabaseUpgrade36To37Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 37:
                    new DatabaseUpgrade37To38Helper(db, connectionSource, this, routeDao).upgrade();
                    // fall through
                case 38:
                    new DatabaseUpgrade38To39Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 39:
                    new DatabaseUpgrade39To40Helper(db, connectionSource, this).upgrade();
                case 40:
                    new DatabaseUpgrade40To41Helper(db, connectionSource, this, rankingDao).upgrade();
                case 41:
                    new DatabaseUpgrade41To42Helper(db, goalDefinitionDao).migrateToRoom();
                case 42:
                    new DatabaseUpgrade42To43Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 43:
                    new DatabaseUpgrade43To44Helper(db, connectionSource, this).upgrade();
                case 44:
                    new DatabaseUpgrade44To45Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 45:
                    new DatabaseUpgrade45To46Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 46:
                    new DatabaseUpgrade46To47Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 47:
                    new DatabaseUpgrade47To48Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 48:
                    new DatabaseUpgrade48To49Helper(db, connectionSource, this).upgrade();
                    // fall through
                case 49:
                    new DatabaseUpgrade49To50Helper(
                        db,
                        connectionSource,
                        this,
                        summaryExtensionDao,
                        sharedPreferences
                    ).upgrade();
                    // fall through
                case 50:
                    new DatabaseUpgrade50To51Helper(
                        db,
                        connectionSource,
                        this
                    ).upgrade();
                    // fall through
                case 51:
                    new DatabaseUpgrade51To52Helper(
                        db,
                        connectionSource,
                        this
                    ).upgrade();
                    // fall through
                case 52:
                    new DatabaseUpgrade52To53Helper(
                        db,
                        connectionSource,
                        this,
                        summaryExtensionDao
                    ).upgrade();
                    // fall through
                case 53:
                    new DatabaseUpgrade53To54Helper(
                        db,
                        connectionSource,
                        this
                    ).upgrade();
                case 54:
                    new DatabaseUpgrade54To55Helper(
                        db,
                        connectionSource,
                        this
                    ).upgrade();
                case 55:
                    new DatabaseUpgrade55To56Helper(db, userDao).migrateToRoom();
                case 56:
                    new DatabaseUpgrade56To57Helper(
                        db,
                        workoutHeaderDao,
                        userDao,
                        sharedPreferences,
                        moshi
                    ).migrateToRoom();
                case 57:
                    new DatabaseUpgrade57To58Helper(
                        db,
                        userDao,
                        subscriptionItemDao,
                        subscriptionInfoDao,
                        pendingPurchaseDao
                    ).migrateToRoom();
                case 58:
                    new DatabaseUpgrade58To59Helper(
                        db,
                        userDao,
                        intensityExtensionDao
                    ).migrateToRoom();
                case 59:
                    new DatabaseUpgrade59To60Helper(
                        db,
                        userDao,
                        fitnessExtensionDao
                    ).migrateToRoom();
                case 60:
                    new DatabaseUpgrade60To61Helper(db, connectionSource, this).upgrade();
                case 61:
                    new DatabaseUpgrade61To62Helper(db, connectionSource, this)
                        .upgrade();
                    /*
                     * Add new cases without breaks so the upgrade calls continue
                     * from one to the next.
                     */
                default:
                    break;
            }
            db.setTransactionSuccessful();
        } catch (SQLException e) {
            Timber.e(e, "Unable to upgrade DB from %d to %d", oldVersion, newVersion);
            throw new RuntimeException(e);
        } finally {
            if (db.inTransaction()) {
                db.endTransaction();
            }
        }
    }
}
