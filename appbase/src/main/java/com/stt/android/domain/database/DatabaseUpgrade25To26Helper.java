package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.stt.android.domain.user.LegacyWorkoutHeader;
import com.stt.android.domain.user.workout.SuuntoLogbookEntry;
import java.sql.SQLException;

class DatabaseUpgrade25To26Helper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade25To26Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        DatabaseHelper.addColumnIfNotExist(db, LegacyWorkoutHeader.TABLE_NAME,
            LegacyWorkoutHeader.DbFields.SEEN);

        TableUtils.createTableIfNotExists(connectionSource, SuuntoLogbookEntry.class);
    }
}
