package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.domain.user.LegacyWorkoutHeader
import java.sql.SQLException

/**
 * This migration updates values of WorkoutHeader.DbFields.EXTENSIONS_FETCHED column to false
 * in order to fetch WeatherExtension for existing workouts.
 */
internal class DatabaseUpgrade47To48Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    helper: DatabaseHelper
) : DatabaseUpgradeHelper(db, connectionSource, helper) {

    @Throws(SQLException::class)
    override fun upgrade() {
        val workoutHeaderTable = LegacyWorkoutHeader.TABLE_NAME
        val workoutHeaderIdCol = LegacyWorkoutHeader.DbFields.ID
        val extensionsFetchedCol = LegacyWorkoutHeader.DbFields.EXTENSIONS_FETCHED
        val manuallyCreatedCol = LegacyWorkoutHeader.DbFields.MANUALLY_CREATED
        val deletedCol = LegacyWorkoutHeader.DbFields.DELETED

        val summaryExtensionTable = DatabaseUpgrade49To50Helper.SUMMARY_EXTENSION_TABLE_NAME
        val summaryWorkoutId = DatabaseUpgrade49To50Helper.DbFields.WORKOUT_ID

        // Set 'extensionsFetched' flag to false for all workouts recorded with app (e.g. not
        // manually created and not synced from watch). This makes sure that the weather extension
        // gets fetched for those workouts, if it already exists.
        db.execSQL(
            """
            UPDATE $workoutHeaderTable
            SET $extensionsFetchedCol = 0
            WHERE
                $manuallyCreatedCol = 0 AND
                $deletedCol = 0 AND
                $workoutHeaderIdCol NOT IN (SELECT $summaryWorkoutId FROM $summaryExtensionTable)
            """.trimIndent()
        )
    }
}
