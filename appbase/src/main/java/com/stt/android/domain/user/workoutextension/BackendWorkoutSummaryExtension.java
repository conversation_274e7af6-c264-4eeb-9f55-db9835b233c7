package com.stt.android.domain.user.workoutextension;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.gson.annotations.SerializedName;
import com.stt.android.data.workout.WorkoutRemoteExtensionMapperKt;
import com.stt.android.domain.workouts.extensions.SummaryExtension;
import com.stt.android.domain.workouts.extensions.WorkoutExtension;
import com.stt.android.remote.extensions.RemoteWorkoutExtension;
import java.util.List;

public class BackendWorkoutSummaryExtension extends BackendWorkoutExtension {
    public static final String TYPE = "SummaryExtension";
    static final String PTE = "pte";
    static final String FEELING = "feeling";
    static final String AVG_TEMPERATURE = "avgTemperature";
    static final String PEAK_EPOC = "peakEpoc";
    static final String AVG_POWER = "avgPower";
    static final String AVG_CADENCE = "avgCadence";
    static final String AVG_SPEED = "avgSpeed";
    static final String ASCENT_TIME = "ascentTime";
    static final String DESCENT_TIME = "descentTime";
    static final String PERFORMANCE_LEVEL = "performanceLevel";
    static final String RECOVERY_TIME = "recoveryTime";
    static final String ASCENT = "ascent";
    static final String DESCENT = "descent";
    static final String GEAR = "gear";
    static final String DEVICE_HARDWARE_VERSION = "hardwareVersion";
    static final String DEVICE_SOFTWARE_VERSION = "softwareVersion";
    static final String PRODUCT_TYPE = "productType";
    static final String DISPLAY_NAME = "displayName";
    static final String DEVICE_NAME = "name";
    static final String DEVICE_SERIAL_NUMBER = "serialNumber";
    static final String DEVICE_MANUFACTURER = "manufacturer";
    static final String EXERCISE_ID = "exerciseId";
    static final String MAX_CADENCE = "maxCadence";
    static final String REPETITIONCOUNT = "repetitionCount";
    static final String AVGSTRIDELENGTH = "avgStrideLength";
    static final String FAT_CONSUMPTION = "fatConsumption";
    static final String CARBOHYDRATE_CONSUMPTION = "carbohydrateConsumption";
    static final String AVG_GROUND_CONTACT_TIME = "avgGroundContactTime";
    static final String AVG_VERTICAL_OSCILLATION = "avgVerticalOscillation";
    static final String AVG_LEFT_GROUND_CONTACT_BALANCE = "avgLeftGroundContactBalance";
    static final String AVG_RIGHT_GROUND_CONTACT_BALANCE = "avgRightGroundContactBalance";
    static final String LACTIC_TH_HR = "lacticThHr";
    static final String LACTIC_TH_PACE = "lacticThPace";
    static final String AVG_ASCENT_SPEED = "avgAscentSpeed";
    static final String MAX_ASCENT_SPEED = "maxAscentSpeed";
    static final String AVG_DESCENT_SPEED = "avgDescentSpeed";
    static final String MAX_DESCENT_SPEED = "maxDescentSpeed";
    static final String AVG_DISTANCE_PER_STROKE = "avgDistancePerStroke";
    @SerializedName(PTE)
    private float pte;

    @SerializedName(FEELING)
    private int feeling;

    @SerializedName(AVG_TEMPERATURE)
    private float avgTemperature;

    @SerializedName(PEAK_EPOC)
    private float peakEpoc;

    @SerializedName(AVG_POWER)
    private float avgPower;

    @SerializedName(AVG_CADENCE)
    private float avgCadence;

    @SerializedName(AVG_SPEED)
    @Nullable
    public Float avgSpeed;

    @SerializedName(ASCENT_TIME)
    private float ascentTime;

    @SerializedName(DESCENT_TIME)
    private float descentTime;

    @SerializedName(PERFORMANCE_LEVEL)
    private float performanceLevel;

    @SerializedName(RECOVERY_TIME)
    @Nullable
    private Long recoveryTime;

    @SerializedName(ASCENT)
    @Nullable
    private Double ascent;

    @SerializedName(DESCENT)
    @Nullable
    private Double descent;

    @SerializedName(GEAR)
    @Nullable
    private BackendGear gear;

    @SerializedName(EXERCISE_ID)
    @Nullable
    private String exerciseId;
    
    @SerializedName(REPETITIONCOUNT)
    @Nullable
    private Integer repetitionCount;

    @SerializedName(MAX_CADENCE)
    @Nullable
    private float maxCadence;

    @SerializedName(AVGSTRIDELENGTH)
    @Nullable
    private Float avgStrideLength;

    @SerializedName(FAT_CONSUMPTION)
    @Nullable
    private Integer fatConsumption;

    @SerializedName(CARBOHYDRATE_CONSUMPTION)
    @Nullable
    private Integer carbohydrateConsumption;

    @SerializedName(AVG_GROUND_CONTACT_TIME)
    @Nullable
    private Float avgGroundContactTime;

    @SerializedName(AVG_VERTICAL_OSCILLATION)
    @Nullable
    private Float avgVerticalOscillation;

    @SerializedName(AVG_LEFT_GROUND_CONTACT_BALANCE)
    @Nullable
    private Float avgLeftGroundContactBalance;

    @SerializedName(AVG_RIGHT_GROUND_CONTACT_BALANCE)
    @Nullable
    private Float avgRightGroundContactBalance;

    @SerializedName("apps")
    @Nullable
    private List<RemoteWorkoutExtension.RemoteZapp> zapps;

    @SerializedName(LACTIC_TH_HR)
    @Nullable
    private Float lacticThHr;

    @SerializedName(LACTIC_TH_PACE)
    @Nullable
    private Float lacticThPace;

    @SerializedName(AVG_ASCENT_SPEED)
    private Float avgAscentSpeed;

    @SerializedName(MAX_ASCENT_SPEED)
    private Float maxAscentSpeed;

    @SerializedName(AVG_DESCENT_SPEED)
    private Float avgDescentSpeed;

    @SerializedName(MAX_DESCENT_SPEED)
    private Float maxDescentSpeed;

    @SerializedName(AVG_DISTANCE_PER_STROKE)
    private Float avgDistancePerStroke;

    /**
     * No args constructor to make Gson safe. See:https://stackoverflow
     * .com/questions/18645050/is-default-no-args-constructor-mandatory-for-gson
     */
    public BackendWorkoutSummaryExtension() {
        super(TYPE);
    }

    @NonNull
    @Override
    public WorkoutExtension toWorkoutExtension(int workoutId) {
        return new SummaryExtension(workoutId, pte, feeling,
            avgTemperature, peakEpoc, avgPower, avgCadence, avgSpeed, ascentTime, descentTime,
            performanceLevel, recoveryTime, ascent, descent,
            gear != null ? gear.deviceHardwareVersion : null,
            gear != null ? gear.deviceSoftwareVersion : null,
            gear != null ? gear.productType : null,
            gear != null ? gear.displayName : null,
            gear != null ? gear.deviceName : null,
            gear != null ? gear.deviceSerialNumber : null,
            gear != null ? gear.deviceManufacturer : null,
            exerciseId, WorkoutRemoteExtensionMapperKt.toDomainType(zapps), repetitionCount, maxCadence,
            avgStrideLength, fatConsumption, carbohydrateConsumption, avgGroundContactTime, avgVerticalOscillation,
            avgLeftGroundContactBalance, avgRightGroundContactBalance, lacticThHr, lacticThPace,
            avgAscentSpeed, maxAscentSpeed, avgDescentSpeed, maxDescentSpeed, avgDistancePerStroke);
    }

    public float getPte() {
        return pte;
    }

    public int getFeeling() {
        return feeling;
    }

    public float getAvgTemperature() {
        return avgTemperature;
    }

    public float getPeakEpoc() {
        return peakEpoc;
    }

    public float getAvgPower() {
        return avgPower;
    }

    public float getAvgCadence() {
        return avgCadence;
    }

    @Nullable
    public Float getAvgSpeed() {
        return avgSpeed;
    }

    public float getAscentTime() {
        return ascentTime;
    }

    public float getDescentTime() {
        return descentTime;
    }

    public float getPerformanceLevel() {
        return performanceLevel;
    }

    public Long getRecoveryTime() {
        return recoveryTime;
    }

    public Double getAscent() {
        return ascent;
    }

    public Double getDescent() {
        return descent;
    }

    @Nullable
    public BackendGear getGear() {
        return gear;
    }

    public String getExerciseId() {
        return exerciseId;
    }

    @Nullable
    public Float getMaxCadence() {
        return maxCadence;
    }

    @Nullable
    public Integer getRepetitionCount() {
        return repetitionCount;
    }

    @Nullable
    public Float getAvgStrideLength() {
        return avgStrideLength;
    }

    @Nullable
    public Integer getFatConsumption() {
        return fatConsumption;
    }

    @Nullable
    public Integer getCarbohydrateConsumption() {
        return carbohydrateConsumption;
    }

    @Nullable
    public Float getAvgGroundContactTime() {
        return avgGroundContactTime;
    }

    @Nullable
    public Float getAvgVerticalOscillation() {
        return avgVerticalOscillation;
    }

    @Nullable
    public Float getAvgLeftGroundContactBalance() {
        return avgLeftGroundContactBalance;
    }

    @Nullable
    public Float getAvgRightGroundContactBalance() {
        return avgRightGroundContactBalance;
    }

    @Nullable
    public Float getLacticThHr() {
        return lacticThHr;
    }

    @Nullable
    public Float getLacticThPace() {
        return lacticThPace;
    }

    public Float getAvgAscentSpeed() {
        return avgAscentSpeed;
    }

    public Float getMaxAscentSpeed() {
        return maxAscentSpeed;
    }

    public Float getAvgDescentSpeed() {
        return avgDescentSpeed;
    }

    public Float getMaxDescentSpeed() {
        return maxDescentSpeed;
    }

    public Float getAvgDistancePerStroke() {
        return avgDistancePerStroke;
    }

    public static class BackendGear {
        @SerializedName(DEVICE_HARDWARE_VERSION)
        @Nullable
        private String deviceHardwareVersion;

        @SerializedName(DEVICE_SOFTWARE_VERSION)
        @Nullable
        private String deviceSoftwareVersion;

        @SerializedName(PRODUCT_TYPE)
        @Nullable
        private String productType;

        @SerializedName(DISPLAY_NAME)
        @Nullable
        private String displayName;

        @SerializedName(DEVICE_NAME)
        private String deviceName;

        @SerializedName(DEVICE_SERIAL_NUMBER)
        @Nullable
        private String deviceSerialNumber;

        @SerializedName(DEVICE_MANUFACTURER)
        private String deviceManufacturer;

        public BackendGear(@Nullable String deviceHardwareVersion, @Nullable String deviceSoftwareVersion,
            String deviceName, @Nullable String deviceSerialNumber, String deviceManufacturer) {
            this.deviceHardwareVersion = deviceHardwareVersion;
            this.deviceSoftwareVersion = deviceSoftwareVersion;
            this.deviceName = deviceName;
            this.deviceSerialNumber = deviceSerialNumber;
            this.deviceManufacturer = deviceManufacturer;
        }
    }
}
