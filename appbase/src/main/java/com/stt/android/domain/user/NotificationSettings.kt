package com.stt.android.domain.user

data class NotificationSettings internal constructor(
    @get:JvmName("notificationSoundEnabled")
    val notificationSoundEnabled: <PERSON><PERSON><PERSON>,
    @get:JvmName("workoutCommentPushEnabled")
    val workoutCommentPushEnabled: <PERSON><PERSON><PERSON>,
    @get:JvmName("workoutSharePushEnabled")
    val workoutSharePushEnabled: <PERSON><PERSON><PERSON>,
    @get:JvmName("workoutReactionPushEnabled")
    val workoutReactionPushEnabled: <PERSON><PERSON><PERSON>,
    @get:JvmName("facebookFriendJoinPushEnabled")
    val facebookFriendJoinPushEnabled: <PERSON><PERSON><PERSON>,
    @get:JvmName("newFollowerPushEnabled")
    val newFollowerPushEnabled: <PERSON><PERSON>an,
    @get:JvmName("newActivitySyncedLocalEnabled")
    val newActivitySyncedLocalEnabled: <PERSON><PERSON><PERSON>,
    @get:JvmName("workoutCommentEmailEnabled")
    val workoutCommentEmailEnabled: <PERSON><PERSON><PERSON>,
    @get:JvmName("newFollowerEmailEnabled")
    val newFollowerEmailEnabled: <PERSON><PERSON><PERSON>,
    @get:JvmName("workoutShareEmailEnabled")
    val workoutShareEmailEnabled: Boolean,
    @get:JvmName("digestEmailEnabled")
    val digestEmailEnabled: Boolean,
    @get:JvmName("autoApproveFollowersEnabled")
    val autoApproveFollowersEnabled: Boolean,
    @get:JvmName("privateAccount")
    val privateAccount: Boolean
) {

    fun toBuilder(): Builder = Builder(
        notificationSoundEnabled = notificationSoundEnabled,
        workoutCommentPushEnabled = workoutCommentPushEnabled,
        workoutSharePushEnabled = workoutSharePushEnabled,
        workoutReactionPushEnabled = workoutReactionPushEnabled,
        facebookFriendJoinPushEnabled = facebookFriendJoinPushEnabled,
        newFollowerPushEnabled = newFollowerPushEnabled,
        newActivitySyncedLocalEnabled = newActivitySyncedLocalEnabled,
        workoutCommentEmailEnabled = workoutCommentEmailEnabled,
        newFollowerEmailEnabled = newFollowerEmailEnabled,
        workoutShareEmailEnabled = workoutShareEmailEnabled,
        digestEmailEnabled = digestEmailEnabled,
        autoApproveFollowersEnabled = autoApproveFollowersEnabled,
        privateAccount = privateAccount
    )

    @Suppress("LongParameterList")
    class Builder internal constructor(
        private var notificationSoundEnabled: Boolean = false,
        private var workoutCommentPushEnabled: Boolean = false,
        private var workoutSharePushEnabled: Boolean = false,
        private var workoutReactionPushEnabled: Boolean = false,
        private var facebookFriendJoinPushEnabled: Boolean = false,
        private var newFollowerPushEnabled: Boolean = false,
        private var newActivitySyncedLocalEnabled: Boolean = false,
        private var workoutCommentEmailEnabled: Boolean = false,
        private var newFollowerEmailEnabled: Boolean = false,
        private var workoutShareEmailEnabled: Boolean = false,
        private var digestEmailEnabled: Boolean = false,
        private var autoApproveFollowersEnabled: Boolean = false,
        private var privateAccount: Boolean = false
    ) {
        fun notificationSoundEnabled(notificationSoundEnabled: Boolean): Builder =
            apply { this.notificationSoundEnabled = notificationSoundEnabled }

        fun workoutCommentPushEnabled(workoutCommentPushEnabled: Boolean): Builder =
            apply { this.workoutCommentPushEnabled = workoutCommentPushEnabled }

        fun workoutSharePushEnabled(workoutSharePushEnabled: Boolean): Builder =
            apply { this.workoutSharePushEnabled = workoutSharePushEnabled }

        fun workoutReactionPushEnabled(workoutReactionPushEnabled: Boolean): Builder =
            apply { this.workoutReactionPushEnabled = workoutReactionPushEnabled }

        fun facebookFriendJoinPushEnabled(facebookFriendJoinPushEnabled: Boolean): Builder =
            apply { this.facebookFriendJoinPushEnabled = facebookFriendJoinPushEnabled }

        fun newFollowerPushEnabled(newFollowerPushEnabled: Boolean): Builder =
            apply { this.newFollowerPushEnabled = newFollowerPushEnabled }

        fun newActivitySyncedLocalEnabled(newActivitySyncedLocalEnabled: Boolean): Builder =
            apply { this.newActivitySyncedLocalEnabled = newActivitySyncedLocalEnabled }

        fun workoutCommentEmailEnabled(workoutCommentEmailEnabled: Boolean): Builder =
            apply { this.workoutCommentEmailEnabled = workoutCommentEmailEnabled }

        fun newFollowerEmailEnabled(newFollowerEmailEnabled: Boolean): Builder =
            apply { this.newFollowerEmailEnabled = newFollowerEmailEnabled }

        fun workoutShareEmailEnabled(workoutShareEmailEnabled: Boolean): Builder =
            apply { this.workoutShareEmailEnabled = workoutShareEmailEnabled }

        fun digestEmailEnabled(digestEmailEnabled: Boolean): Builder =
            apply { this.digestEmailEnabled = digestEmailEnabled }

        fun autoApproveFollowersEnabled(askToApproveEnabled: Boolean): Builder =
            apply { this.autoApproveFollowersEnabled = askToApproveEnabled }

        fun setPrivateAccount(privateAccount: Boolean): Builder =
            apply { this.privateAccount = privateAccount }

        fun build(): NotificationSettings = NotificationSettings(
            notificationSoundEnabled = notificationSoundEnabled,
            workoutCommentPushEnabled = workoutCommentPushEnabled,
            workoutSharePushEnabled = workoutSharePushEnabled,
            workoutReactionPushEnabled = workoutReactionPushEnabled,
            facebookFriendJoinPushEnabled = facebookFriendJoinPushEnabled,
            newFollowerPushEnabled = newFollowerPushEnabled,
            newActivitySyncedLocalEnabled = newActivitySyncedLocalEnabled,
            workoutCommentEmailEnabled = workoutCommentEmailEnabled,
            newFollowerEmailEnabled = newFollowerEmailEnabled,
            workoutShareEmailEnabled = workoutShareEmailEnabled,
            digestEmailEnabled = digestEmailEnabled,
            autoApproveFollowersEnabled = autoApproveFollowersEnabled,
            privateAccount = privateAccount
        )
    }

    companion object {
        @JvmStatic
        fun builder(): Builder = Builder()
    }
}
