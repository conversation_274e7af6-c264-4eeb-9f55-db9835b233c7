package com.stt.android.domain.user.customproperty

import com.stt.android.data.source.local.DaoFactory
import com.stt.android.data.source.local.usercustomproperty.UserCustomPropertyDao
import com.stt.android.domain.user.CustomPropertyDataSource
import dagger.Binds
import dagger.Module
import dagger.Provides

@Module
abstract class UserCustomPropertyModule {

    @Binds
    abstract fun bindCustomPropertyDataSource(customPropertyDataSourceImpl: CustomPropertyDataSourceImpl): CustomPropertyDataSource

    companion object {
        @Provides
        fun provideUserCustomPropertyDao(daoFactory: DaoFactory): UserCustomPropertyDao {
            return daoFactory.userCustomPropertyDao
        }
    }
}
