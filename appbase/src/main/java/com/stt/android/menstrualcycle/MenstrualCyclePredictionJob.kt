package com.stt.android.menstrualcycle

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.soy.algorithms.menstrualcycle.algorithms.MenstrualCyclePredictionCalc
import com.soy.algorithms.menstrualcycle.algorithms.MenstrualCycleRegularityCalc
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.controllers.UserSettingsController
import com.stt.android.data.usersettings.MenstrualCycleSettings
import com.stt.android.menstrualcycle.alarm.MenstrualCycleAlarmUtils
import com.stt.android.menstrualcycle.domain.MenstrualCycleLocalDataSource
import com.stt.android.menstrualcycle.domain.MenstrualCycleType
import com.stt.android.menstrualcycle.domain.toCalc
import com.stt.android.menstrualcycle.domain.toDomain
import com.stt.android.menstrualcycle.domain.toSimple
import com.stt.android.menstrualcycle.domain.toSimpleDate
import com.stt.android.utils.BrandFlavourConstants
import java.time.LocalDate
import javax.inject.Inject

class MenstrualCyclePredictionJob(
    context: Context,
    params: WorkerParameters,
    private val userSettingsController: UserSettingsController,
    private val menstrualCycleLocalDataSource: MenstrualCycleLocalDataSource,
) : CoroutineWorker(context, params) {
    override suspend fun doWork(): Result {
        if (!BrandFlavourConstants.PROVIDE_MC_FEATURE) return Result.success()

        menstrualCycleLocalDataSource.delete(MenstrualCycleType.PREDICTED)
        cancelReminderAlarms()

        val menstrualCycleSettings =
            userSettingsController.settings.menstrualCycleSetting ?: return Result.success()

        val menstrualCycles = menstrualCycleLocalDataSource.fetchBeforeAt(
            LocalDate.now(),
            MenstrualCycleType.HISTORICAL
        ).filter { it.includedDates.isNotEmpty() }
            .map { it.toSimple() }
        if (menstrualCycles.isEmpty()) return Result.success()

        val regularity = MenstrualCycleRegularityCalc.fetchRegularity(
            menstrualCycles = menstrualCycles,
            cycleLengthInSettings = menstrualCycleSettings.cycleLength,
            regularity = menstrualCycleSettings.cycleRegularity.toCalc()
        )
        regularity.toDomain().takeIf {
            it != menstrualCycleSettings.cycleRegularity
        }?.let { storeMenstrualCycleSettings(menstrualCycleSettings.copy(cycleRegularity = it)) }

        // If the regularity is IRREGULAR, ACL and AD is null, will use settings to make prediction.
        val (averageCycleLength, averagePeriodLength) = MenstrualCycleRegularityCalc.fetchAverageLength(
            menstrualCycles = menstrualCycles,
            cycleLengthInSettings = menstrualCycleSettings.cycleLength,
            regularity = regularity
        )
        MenstrualCyclePredictionCalc.predictMenstrualCycles(
            todayDate = LocalDate.now().toSimpleDate(),
            lastStartDate = menstrualCycles.last().startDate,
            cycleLength = averageCycleLength ?: menstrualCycleSettings.cycleLength,
            periodLength = averagePeriodLength ?: menstrualCycleSettings.periodDuration
        ).map { it.toDomain(MenstrualCycleType.PREDICTED) }
            .takeIf { it.isNotEmpty() }
            ?.let { predictedMenstrualCycles ->
                menstrualCycleLocalDataSource.insert(predictedMenstrualCycles)

                predictedMenstrualCycles.firstOrNull {
                    val today = LocalDate.now()
                    // If it is less than 7 days from the start date, also remind.
                    (it.startDate <= today && today <= it.startDate.plusDays(7)) || today <= it.startDate
                }?.let {
                    setReminderAlarms(it.startDate)
                }
            }

        return Result.success()
    }

    private fun setReminderAlarms(menstrualCycleStartDate: LocalDate) {
        MenstrualCycleAlarmUtils.setPredictAlarm(
            applicationContext,
            menstrualCycleStartDate,
        )
        MenstrualCycleAlarmUtils.setLogAlarm(
            applicationContext,
            menstrualCycleStartDate,
        )
    }

    private fun cancelReminderAlarms() {
        MenstrualCycleAlarmUtils.cancelPredictAlarm(applicationContext)
        MenstrualCycleAlarmUtils.cancelLogAlarm(applicationContext)
    }

    private fun storeMenstrualCycleSettings(menstrualCycleSettings: MenstrualCycleSettings) {
        userSettingsController.storeSettings(
            userSettingsController.settings.setMenstrualCycleSettings(menstrualCycleSettings)
        )
    }

    class Factory @Inject constructor(
        private val userSettingsController: UserSettingsController,
        private val menstrualCycleLocalDataSource: MenstrualCycleLocalDataSource,
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return MenstrualCyclePredictionJob(
                context = context,
                params = params,
                userSettingsController = userSettingsController,
                menstrualCycleLocalDataSource = menstrualCycleLocalDataSource,
            )
        }
    }

    companion object {
        private const val TAG = "MenstrualCyclePredictionJob"

        fun schedule(workManager: WorkManager) {
            workManager.enqueueUniqueWork(
                TAG,
                ExistingWorkPolicy.REPLACE,
                OneTimeWorkRequest.Builder(MenstrualCyclePredictionJob::class.java).build()
            )
        }
    }
}
