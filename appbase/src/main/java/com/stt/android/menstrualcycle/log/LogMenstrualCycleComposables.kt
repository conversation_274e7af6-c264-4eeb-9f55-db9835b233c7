package com.stt.android.menstrualcycle.log

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePickerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyXLarge
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.bottomSheetShape
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberViewInteropNestedScrollConnection
import com.stt.android.compose.widgets.DraggableBottomSheetHandle
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.WheelPickerWithoutLoop
import com.stt.android.menstrualcycle.domain.LengthConstants.PERIOD_DURATION_DEFAULT_INDEX
import com.stt.android.menstrualcycle.domain.LengthConstants.PERIOD_DURATION_SELECTABLE
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.toEpochMilli
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.util.Locale
import kotlin.math.min
import com.stt.android.core.R as CR

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LogMenstrualCycleContent(
    datePickerState: DatePickerState,
    periodDuration: Int,
    onPeriodDurationChange: (Int) -> Unit,
    onDoneClick: () -> Unit,
    onCancelClick: () -> Unit,
    onWheelScrollInProgressChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    var showDatePicker by rememberSaveable {
        mutableStateOf(true)
    }

    val wheelListState = rememberLazyListState()

    LaunchedEffect(wheelListState.isScrollInProgress) {
        onWheelScrollInProgressChanged(wheelListState.isScrollInProgress)
    }

    Column(
        modifier = modifier
            .nestedScroll(rememberViewInteropNestedScrollConnection())
            .clip(MaterialTheme.shapes.bottomSheetShape)
            .background(MaterialTheme.colors.surface)
    ) {
        DraggableBottomSheetHandle()

        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .fillMaxSize()
        ) {
            Text(
                text = stringResource(id = R.string.toolbar_log_period),
                style = MaterialTheme.typography.bodyXLargeBold,
                modifier = Modifier
                    .padding(vertical = MaterialTheme.spacing.large)
                    .align(Alignment.CenterHorizontally)
            )

            Row(
                modifier = Modifier
                    .clickable { showDatePicker = true }
                    .padding(horizontal = MaterialTheme.spacing.large)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.menstrual_cycle_period_started_on),
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium)
                )

                Text(
                    text = datePickerState.selectedDateMillis?.let {
                        ZonedDateTime.ofInstant(Instant.ofEpochMilli(it), ZoneOffset.UTC).format(
                            DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT)
                        )
                    } ?: "",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colors.primary
                )
            }

            if (showDatePicker) {
                M3AppTheme {
                    DatePicker(
                        state = datePickerState,
                        title = null,
                        showModeToggle = false,
                        headline = null,
                        colors = DatePickerDefaults.colors().copy(
                            containerColor = MaterialTheme.colors.surface,
                        )
                    )
                }
            }
            Divider()

            Row(
                modifier = Modifier
                    .clickable(onClick = { showDatePicker = false })
                    .padding(horizontal = MaterialTheme.spacing.large)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.menstrual_cycle_duration),
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium)
                )

                Text(
                    text = pluralStringResource(
                        id = CR.plurals.value_days,
                        periodDuration,
                        periodDuration
                    ),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colors.primary
                )
            }

            if (!showDatePicker) {
                ConstraintLayout(
                    modifier = Modifier
                        .height(216.dp)
                        .padding(MaterialTheme.spacing.medium)
                        .fillMaxWidth()
                ) {
                    val (marker, wheel, unit) = createRefs()

                    Spacer(
                        Modifier
                            .background(
                                colorResource(R.color.wheel_picker_marker),
                                RoundedCornerShape(6.dp)
                            )
                            .height(34.dp)
                            .fillMaxWidth()
                            .constrainAs(marker) {
                                top.linkTo(wheel.top)
                                bottom.linkTo(wheel.bottom)
                            }
                    )

                    WheelPickerWithoutLoop(
                        data = PERIOD_DURATION_SELECTABLE.map { it.toString() },
                        selectIndex = getDurationSelectedIndex(periodDuration),
                        visibleCount = min(PERIOD_DURATION_SELECTABLE.size, 7),
                        listState = wheelListState,
                        modifier = Modifier
                            .constrainAs(wheel) {
                                start.linkTo(parent.start)
                                end.linkTo(parent.end)
                                top.linkTo(parent.top)
                                bottom.linkTo(parent.bottom)
                            },
                        onSelect = { index, _ ->
                            onPeriodDurationChange(PERIOD_DURATION_SELECTABLE[index])
                        }
                    ) {
                        Text(
                            text = it,
                            style = MaterialTheme.typography.bodyXLarge,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }

                    Text(
                        text = pluralStringResource(CR.plurals.unit_days, periodDuration),
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier
                            .padding(start = 80.dp)
                            .constrainAs(unit) {
                                start.linkTo(parent.start)
                                end.linkTo(parent.end)
                                top.linkTo(parent.top)
                                bottom.linkTo(parent.bottom)
                            }
                    )
                }
            }
            Divider()

            PrimaryButton(
                text = stringResource(id = com.stt.android.R.string.done).uppercase(Locale.getDefault()),
                onClick = onDoneClick,
                enabled = datePickerState.selectedDateMillis?.let {
                    it < LocalDate.now().atEndOfDay().toEpochMilli()
                } ?: false,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        vertical = MaterialTheme.spacing.medium,
                        horizontal = MaterialTheme.spacing.large
                    )
            )

            TextButton(
                onClick = onCancelClick,
                modifier = Modifier.align(Alignment.CenterHorizontally)
            ) {
                Text(
                    text = stringResource(R.string.cancel).uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.bodyBold,
                    color = MaterialTheme.colors.primary
                )
            }
        }
    }
}

private fun getDurationSelectedIndex(duration: Int): Int {
    val index = PERIOD_DURATION_SELECTABLE.indexOf(duration)
    return if (index == -1) PERIOD_DURATION_DEFAULT_INDEX else index
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview
@Composable
private fun LogMenstrualCycleContentPreview() {
    AppTheme {
        LogMenstrualCycleContent(
            datePickerState = rememberDatePickerState(),
            periodDuration = 4,
            onPeriodDurationChange = {},
            onDoneClick = {},
            onCancelClick = {},
            onWheelScrollInProgressChanged = {},
        )
    }
}
