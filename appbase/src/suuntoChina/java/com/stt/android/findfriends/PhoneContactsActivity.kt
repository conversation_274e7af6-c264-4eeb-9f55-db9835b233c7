package com.stt.android.findfriends

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import com.stt.android.R
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.findfriends.v2.PhoneContactsScreen
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class PhoneContactsActivity : AppCompatActivity() {

    @Inject
    lateinit var datahubAnalyticsTracker: DatahubAnalyticsTracker

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithM3Theme {
            PhoneContactsScreen(
                onBackClick = { onBackPressedDispatcher.onBackPressed() },
                onInviteFriendsClick = { phone ->
                    inviteFriends(this, datahubAnalyticsTracker, phone)
                },
                getErrorMessageResId = { resId ->
                    getErrorMessageResId(this, resId)
                }
            )
        }
    }
}

@SuppressLint("DiscouragedApi")
private fun getErrorMessageResId(context: Context, code: Int?): Int {
    return context.resources.getIdentifier("error_$code", "string", context.packageName)
}

private fun inviteFriends(
    context: Context,
    datahubAnalyticsTracker: DatahubAnalyticsTracker,
    phone: String
) {
    datahubAnalyticsTracker.trackEvent(
        AnalyticsEvent.SHARE_APP_LINK_SCREEN,
        "Source",
        AnalyticsPropertyValue.FollowSourceProperty.FIND_FB_FRIENDS
    )
    val text = String.format(
        context.getString(R.string.invite_msg_download_link),
        context.getString(R.string.share_app_dialog_msg_alternative),
        context.getString(R.string.share_app_url_st_download_page)
    )
    val intent = Intent(Intent.ACTION_SENDTO).apply {
        data = "smsto:$phone".toUri()
        putExtra("sms_body", text)
    }
    context.startActivity(intent)
}
