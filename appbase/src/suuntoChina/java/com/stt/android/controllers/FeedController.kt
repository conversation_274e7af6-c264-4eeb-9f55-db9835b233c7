package com.stt.android.controllers

import android.content.Context
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.data.marketing.MarketingInboxRemoteDataSource
import com.stt.android.domain.notifications.GetNotificationsCountUseCase
import com.stt.android.domain.notifications.GetUnreadNotificationsCountUseCase
import com.stt.android.utils.HuaweiUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FeedController @Inject constructor(
    @ApplicationContext context: Context,
    getNotificationsCountUseCase: GetNotificationsCountUseCase,
    getUnreadNotificationsCountUseCase: GetUnreadNotificationsCountUseCase,
    marketingInboxRemoteDataSource: MarketingInboxRemoteDataSource,
    dispatchers: CoroutinesDispatchers,
    huaweiUtils: HuaweiUtils,
) : BaseFeedController(
    context,
    getNotificationsCountUseCase,
    getUnreadNotificationsCountUseCase,
    marketingInboxRemoteDataSource,
    dispatchers,
    huaweiUtils,
)
