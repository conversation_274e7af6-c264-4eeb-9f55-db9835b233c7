package com.stt.android.sharing

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.R
import com.stt.android.annualreport.ShareErrorType
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.sharing.AppSharingViewModel.Companion.WEIBO_IMAGE_LIMIT_COUNT
import com.stt.android.sharingplatform.SharingResultState
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import com.stt.android.workouts.sharepreview.customshare.ShareTarget
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * implement sharing content to third platform by app
 */
@AndroidEntryPoint
class AppSharingDialogFragment : SmartBottomSheetDialogFragment() {
    private val viewModel: AppSharingViewModel by viewModels()
    private lateinit var sharingInfo: SharingInfo

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        sharingInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(KEY_SHARE_INFO, SharingInfo::class.java)
        } else {
            @Suppress("DEPRECATION")
            arguments?.getParcelable(KEY_SHARE_INFO)
        } ?: throw IllegalArgumentException("SharingInfo is required")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(
                ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
            )
            setContentWithM3Theme {
                AppSharingScreen(
                    shareTargets = viewModel.shareTargets.collectAsState().value,
                    onClickItem = { shareTarget ->
                        viewModel.share(
                            sharingInfo,
                            shareTarget,
                            ::showShareResultMessage,
                            requireActivity(),
                        )
                        if (shareTarget == ShareTarget.DelegateToOS)
                            dismissAllowingStateLoss()
                    },
                    onDismissRequest = {
                        dismissAllowingStateLoss()
                    }
                )
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                launch {
                    viewModel.shareErrorEvent.collect {
                        showErrorMessage(it)
                    }
                }
            }
        }
        viewModel.loadTargets(sharingInfo.sharingType, sharingInfo.resourceUris.size == 1)
    }

    /**
     * note: activity have to call fragment's onActivityResult
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        data?.let {
            viewModel.setShareResultForWeibo(it, ::showShareResultMessage)
        }
    }

    private fun showErrorMessage(type: ShareErrorType) {
        val message = when (type) {
            ShareErrorType.WEIBO_OVER_IMAGE_COUNT -> getString(
                R.string.weibo_share_image_limit,
                WEIBO_IMAGE_LIMIT_COUNT
            )

            ShareErrorType.NOT_INSTALL_PLATFORM -> getString(R.string.share_fail)

            ShareErrorType.CANCEL -> getString(R.string.share_cancel)
        }
        Toast.makeText(requireContext().applicationContext, message, Toast.LENGTH_SHORT).show()
        dismissAllowingStateLoss()
    }

    private fun showShareResultMessage(state: SharingResultState) {
        val message = when (state) {
            SharingResultState.Success -> getString(R.string.share_suucess)
            SharingResultState.Fail -> getString(R.string.share_fail)
            SharingResultState.Cancel -> getString(R.string.share_cancel)
        }
        Toast.makeText(requireContext().applicationContext, message, Toast.LENGTH_SHORT).show()
        dismissAllowingStateLoss()
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.removeShareCallback()
    }

    companion object {
        private const val KEY_SHARE_INFO = "com.stt.android.sharing.SHARE_INFO"

        fun newSharingDialog(
            sharingInfo: SharingInfo
        ): AppSharingDialogFragment {
            return AppSharingDialogFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(KEY_SHARE_INFO, sharingInfo)
                }
            }
        }
    }
}
