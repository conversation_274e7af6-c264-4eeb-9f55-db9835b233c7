package com.stt.android.di.location;

import android.content.Context;
import android.location.LocationManager;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.stt.android.maps.location.AndroidLocationSource;
import com.stt.android.maps.location.GoogleLocationSource;
import com.stt.android.maps.location.SuuntoLocationSource;
import dagger.Module;
import dagger.Provides;

@Module
public abstract class LocationModule {

    @Provides
    public static SuuntoLocationSource provideSuuntoLocationSource(Context context) {
        if (GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(context) ==
            ConnectionResult.SUCCESS) {
            return new GoogleLocationSource(context);
        } else {
            return new AndroidLocationSource(
                (LocationManager) context.getSystemService(Context.LOCATION_SERVICE));
        }
    }
}
