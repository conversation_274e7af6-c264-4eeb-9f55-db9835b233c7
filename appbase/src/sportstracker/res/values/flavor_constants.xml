<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--This file contains flavor specific constants-->
    <string name="app_deeplink_url_scheme" translatable="false">com.sports-tracker</string>
    <item name="showFriendsToFollowPopup" type="bool">true</item>
    <item name="flavorSupportsSubscriptions" type="bool">true</item>
    <item name="hideAds" type="bool">false</item>
    <item name="suuntoFlavorSpecific" type="bool">false</item>
    <item name="sportsTrackerFlavorSpecific" type="bool">true</item>
    <item name="supportsCustomInbox" type="bool">true</item>
    <item name="supports_invite_friends_in_app" type="bool">false</item>

    <!--
    If set to true, photos are shared using the Facebook SDK. Otherwise plain sharing intent
    is used. Using the SDK requires registering FacebookContentProvider in the manifest. Multiple
    app(flavor)s can not register the content provider using the same Facebook app ID and co-exist
    on the same device.
    -->
    <item name="sharePhotosToFacebookUsingSdk" type="bool">true</item>

    <string name="app_channel_name" translatable="false">SportTracker</string>
    <string name="invite_friends_share_link" translatable="false">https://marketing.suunto.com/h5/sportstracker/invite-friends?username=%1$s</string>
</resources>
