plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.moshi"
}

android {
    namespace 'com.stt.android.menstrualcycle.remote'
    buildFeatures.buildConfig = true
}

dependencies {
    api project(Deps.remoteBase)
    implementation libs.retrofit
    implementation libs.retrofit.scalars
    implementation libs.retrofit.moshi
    implementation libs.okhttp.logging
    implementation libs.okhttp
}
