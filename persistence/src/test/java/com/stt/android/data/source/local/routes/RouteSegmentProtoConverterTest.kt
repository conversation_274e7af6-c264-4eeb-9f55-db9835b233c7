package com.stt.android.data.source.local.routes

import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class RouteSegmentProtoConverterTest {

    private val segment1: LocalRouteSegment = LocalRouteSegment(
        startPoint = LocalPoint(10.0, 20.0, 30.0),
        endPoint = LocalPoint(40.0, 50.0, 60.0),
        position = 0,
        routePoints = listOf(
            LocalPoint(10.0, 20.0, 30.0),
            LocalPoint(20.0, 30.0, 40.0),
            LocalPoint(30.0, 40.0, 50.0),
            LocalPoint(40.0, 50.0, 60.0)
        ),
        ascent = 30.0
    )

    private val segment2: LocalRouteSegment = LocalRouteSegment(
        startPoint = LocalPoint(10.0, 20.0, null),
        endPoint = LocalPoint(4.0, 5.0, null),
        position = 0,
        routePoints = listOf(
            LocalPoint(10.0, 20.0, null),
            LocalPoint(20.0, 30.0, null),
            LocalPoint(30.0, 40.0, null),
            LocalPoint(40.0, 50.0, null)
        ),
        ascent = null
    )

    @Test
    fun `route segment mapping to proto`() {
        assertThat(segment1.toProto().fromProto()).isEqualTo(segment1)
        assertThat(segment2.toProto().fromProto()).isEqualTo(segment2)
    }
}
