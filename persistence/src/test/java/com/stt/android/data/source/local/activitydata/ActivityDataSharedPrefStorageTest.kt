package com.stt.android.data.source.local.activitydata

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import com.stt.android.data.ACTIVITY_DATA_PREFS_NAME
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class ActivityDataSharedPrefStorageTest {
    @Test
    fun saveSleepTrackingMode() = runTest {
        val mock: Application = mock()
        val mockSharedPrefs: SharedPreferences = mock()
        val mockEditor: SharedPreferences.Editor = mock()
        whenever(
            mock.getSharedPreferences(
                ACTIVITY_DATA_PREFS_NAME,
                Context.MODE_PRIVATE
            )
        ).thenReturn(mockSharedPrefs)
        whenever(mockSharedPrefs.edit()).thenReturn(mockEditor)
        whenever(mockEditor.putInt(any(), any())).thenReturn(mockEditor)
        ActivityDataSharedPrefStorage(mock).saveSleepTrackingMode(1)
    }
}
