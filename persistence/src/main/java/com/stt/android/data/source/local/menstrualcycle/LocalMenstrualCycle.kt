package com.stt.android.data.source.local.menstrualcycle

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.stt.android.data.source.local.LocalDateListJsonConverter
import com.stt.android.data.source.local.TABLE_MENSTRUAL_CYCLE
import java.time.LocalDate
import java.util.UUID

@Entity(tableName = TABLE_MENSTRUAL_CYCLE)
@TypeConverters(LocalDateListJsonConverter::class, LocalMenstrualCycleTypeConverter::class)
data class LocalMenstrualCycle(
    @PrimaryKey
    @ColumnInfo(name = LOCAL_ID) val localId: String = generateId(), // The primary key for db
    @ColumnInfo(name = REMOTE_KEY) val remoteKey: String?, // The object id from cloud
    @ColumnInfo(name = INCLUDED_DATES) val includedDates: List<LocalDate>, // The cycle dates may not be continuous, which dates are included
    @ColumnInfo(name = START_DATE) val startDate: Long, // The timestamp of the start date, which should be the smallest in dates
    @ColumnInfo(name = END_DATE) val endDate: Long, // The timestamp of the end date, which should be the largest in dates
    @ColumnInfo(name = MENSTRUAL_CYCLE_TYPE) val menstrualCycleType: LocalMenstrualCycleType, // Data type, which are currently historical and predicted
    @ColumnInfo(name = MODIFIED_TIME) val modifiedTime: Long? // The latest modification time
) {

    companion object {
        const val LOCAL_ID = "localId"
        const val REMOTE_KEY = "remoteKey"
        const val INCLUDED_DATES = "includedDates"
        const val START_DATE = "startDate"
        const val END_DATE = "endDate"
        const val MENSTRUAL_CYCLE_TYPE = "menstrualCycleType"
        const val MODIFIED_TIME = "modifiedTime"

        fun generateId() = UUID.randomUUID().toString()
    }

    // a b c is the fields after obfuscated
    @JsonClass(generateAdapter = true)
    data class OldDateFormat(
        @Json(name = "c") val day: Int,
        @Json(name = "b") val month: Int,
        @Json(name = "a") val year: Int
    )
}
