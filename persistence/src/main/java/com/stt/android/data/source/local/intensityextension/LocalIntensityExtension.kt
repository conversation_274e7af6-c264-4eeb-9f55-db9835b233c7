package com.stt.android.data.source.local.intensityextension

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.TypeConverters
import com.stt.android.data.source.local.TABLE_INTENSITY_EXTENSION
import com.stt.android.data.source.local.workoutextension.LocalWorkoutExtension

@Entity(tableName = TABLE_INTENSITY_EXTENSION)
@TypeConverters(IntensityExtensionTypeConverters::class)
class LocalIntensityExtension(
    workoutId: Int,
    @ColumnInfo(name = "hrZones")
    val hrZones: LocalWorkoutIntensityZone?,
    @ColumnInfo(name = "speedZones")
    val speedZones: LocalWorkoutIntensityZone?,
    @ColumnInfo(name = "powerZones")
    val powerZones: LocalWorkoutIntensityZone?,
) : LocalWorkoutExtension(workoutId)
