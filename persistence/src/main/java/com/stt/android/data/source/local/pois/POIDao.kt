package com.stt.android.data.source.local.pois

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import kotlinx.coroutines.flow.Flow
import java.time.Duration

@Dao
abstract class POIDao {
    @Query("SELECT * FROM pois ORDER BY modified DESC")
    abstract suspend fun fetchAll(): List<LocalPOI>

    @Query("SELECT * FROM pois WHERE deleted = 0 ORDER BY modified DESC")
    abstract suspend fun fetchAllVisible(): List<LocalPOI>

    @Query("SELECT * FROM pois WHERE deleted = 0 ORDER BY creation DESC")
    abstract fun fetchAllVisibleAsFlow(): Flow<List<LocalPOI>>

    /**
     * Note that this method returned oldest modified last, not created
     */
    @Query(
        """
        SELECT * FROM pois
        WHERE deleted = 0 AND watchEnabled = 1 
        ORDER BY modified DESC
        """
    )
    abstract suspend fun fetchAllWatchEnabled(): List<LocalPOI>

    @Query(
        """
        SELECT * FROM pois
        WHERE syncState = 'PENDING_ALL' OR syncState = 'PENDING_WATCH'
        ORDER BY modified
        """
    )
    abstract suspend fun fetchAllPendingWatchSync(): List<LocalPOI>

    @Query(
        """
        SELECT * FROM pois
        WHERE 
            deleted = 0 AND
            watchEnabled = 1 AND
            (syncState != 'PENDING_ALL' AND syncState != 'PENDING_WATCH') AND
            creation NOT IN (:excludeIds)
        ORDER BY modified
        """
    )
    abstract suspend fun fetchAllWatchEnabledNotPendingWatchSync(excludeIds: List<Long>): List<LocalPOI>

    @Query(
        """
        SELECT * FROM pois
        WHERE 
            deleted = 0 AND
            watchEnabled = 0 AND
            (syncState = 'PENDING_ALL' OR syncState = 'PENDING_WATCH') AND
            creation NOT IN (:excludeIds)
        ORDER BY modified
        """
    )
    abstract suspend fun fetchAllPendingWatchRemoveButNotInWatch(excludeIds: List<Long>): List<LocalPOI>

    @Query(
        """
        SELECT * FROM pois
        WHERE 
            deleted = 0 AND
            watchEnabled = 0 AND
            (syncState != 'PENDING_ALL' AND syncState != 'PENDING_WATCH') AND
            creation IN (:ids)
        ORDER BY modified
        """
    )
    abstract suspend fun fetchAllWatchDisabledPOIsFoundIn(ids: List<Long>): List<LocalPOI>

    @Query(
        """
        SELECT * FROM pois
        WHERE syncState = 'PENDING_ALL' OR syncState = 'PENDING_BACKEND'
        ORDER BY modified
        """
    )
    abstract suspend fun fetchAllPendingBackendSync(): List<LocalPOI>

    @Query(
        """
        DELETE FROM pois
        WHERE deleted = 1 AND syncState = 'IDLE'
    """
    )
    abstract fun deleteAllMarkedDeletedAndIdle()

    @Query("SELECT * FROM pois WHERE creation = :poiId")
    abstract suspend fun fetchById(poiId: Long): LocalPOI?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun replace(poi: LocalPOI)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    abstract suspend fun insert(poi: LocalPOI)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    abstract suspend fun insert(poiList: List<LocalPOI>)

    @Update(onConflict = OnConflictStrategy.IGNORE)
    abstract suspend fun update(poi: LocalPOI)

    @Update(onConflict = OnConflictStrategy.IGNORE)
    abstract suspend fun update(poiList: List<LocalPOI>)

    @Delete
    abstract suspend fun delete(poi: LocalPOI)

    @Query("DELETE FROM pois")
    abstract fun deleteAll()

    suspend fun markDeleted(poi: LocalPOI) {
        if (poi.deleted) return
        update(
            poi.copy(
                modified = Duration.ofMillis(System.currentTimeMillis()).seconds,
                watchEnabled = false,
                syncState = if (poi.watchEnabled) {
                    LocalPOISyncState.PENDING_ALL
                } else {
                    LocalPOISyncState.PENDING_BACKEND
                },
                deleted = true
            )
        )
    }

    @Transaction
    open suspend fun markDeleted(pois: List<LocalPOI>) {
        pois.forEach { markDeleted(it) }
    }

    @Query("SELECT COUNT(creation) FROM pois WHERE deleted = 0")
    abstract suspend fun countPOIs(): Int

    @Query("SELECT COUNT(creation) FROM pois WHERE watchEnabled = 1 AND deleted = 0")
    abstract suspend fun countWatchEnabledPOIs(): Int
}
