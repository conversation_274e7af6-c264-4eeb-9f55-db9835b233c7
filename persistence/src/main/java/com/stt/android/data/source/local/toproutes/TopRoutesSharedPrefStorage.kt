package com.stt.android.data.source.local.toproutes

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.data.TOP_ROUTES_PREFS_NAME
import javax.inject.Inject

class TopRoutesSharedPrefStorage
@Inject constructor(
    val application: Application,
    val moshi: <PERSON>shi
) {
    private val sharedPrefs: SharedPreferences =
        application.getSharedPreferences(TOP_ROUTES_PREFS_NAME, Context.MODE_PRIVATE)

    private val jsonAdapter: JsonAdapter<List<Int>> =
        moshi.adapter(Types.newParameterizedType(List::class.java, Integer::class.java))

    fun getSortOrder(): List<Int> = sharedPrefs.getString(KEY_SORT_ORDER, null)
        ?.let {
            jsonAdapter.fromJson(it)
        } ?: emptyList()

    fun saveSortOrder(order: List<Int>) {
        sharedPrefs.edit { putString(KEY_SORT_ORDER, jsonAdapter.toJson(order)) }
    }

    companion object {
        private const val KEY_SORT_ORDER = "KEY_SORT_ORDER"
    }
}
