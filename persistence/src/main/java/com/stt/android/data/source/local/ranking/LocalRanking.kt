package com.stt.android.data.source.local.ranking

import android.provider.BaseColumns
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.stt.android.data.generateId
import com.stt.android.data.source.local.TABLE_RANKINGS

@Entity(tableName = TABLE_RANKINGS)
data class LocalRanking(
    @PrimaryKey
    @ColumnInfo(name = ID)
    val id: String = generateId(),
    @ColumnInfo(name = WORKOUT_KEY) val key: String = "",
    @ColumnInfo(name = RANKING_TYPE) val type: String,
    @ColumnInfo(name = RANKING) val ranking: Int?,
    @ColumnInfo(name = NUMBER_OF_WORKOUTS) val numberOfWorkouts: Int?

) {
    companion object DbFields {
        const val ID = BaseColumns._ID
        const val WORKOUT_KEY = "workoutKey"
        const val RANKING_TYPE = "rankingType"
        const val RANKING = "ranking"
        const val NUMBER_OF_WORKOUTS = "numberOfWorkouts"
    }
}
