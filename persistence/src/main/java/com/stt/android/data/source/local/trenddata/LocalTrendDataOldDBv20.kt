package com.stt.android.data.source.local.trenddata

import android.content.ContentValues
import androidx.room.TypeConverters
import com.stt.android.data.source.local.ZonedDateTimeConverter
import java.time.ZonedDateTime

@Deprecated(
    message = "Use LocalTrendData instead, we don't aggregate sleep on database anymore",
    replaceWith = ReplaceWith("LocalTrendData", "com.stt.android.data.source.local.LocalTrendData")
)
@TypeConverters(ZonedDateTimeConverter::class)
data class LocalTrendDataOldDBv20(
    val serial: String,
    val timestampSeconds: Long,
    val energy: Float,
    val steps: Int,
    val syncedStatus: Int,
    val timeISO8601: ZonedDateTime
) {

    fun asContentValues(recycled: ContentValues? = null): ContentValues {
        val contentValues = recycled?.apply { clear() } ?: ContentValues()
        return contentValues.apply {
            put(COLUMN_SERIAL, serial)
            put(COLUMN_TIMESTAMP_SECONDS, timestampSeconds)
            put(COLUMN_ENERGY, energy)
            put(COLUMN_STEPS, steps)
            put(COLUMN_SYNCED_STATUS, syncedStatus)
            put(COLUMN_TIMESTAMP_ISO, ZonedDateTimeConverter().fromZonedDateTime(timeISO8601))
        }
    }

    companion object {
        const val COLUMN_SERIAL = "serial"
        const val COLUMN_TIMESTAMP_SECONDS = "timestamp_seconds"
        const val COLUMN_ENERGY = "energy"
        const val COLUMN_STEPS = "steps"
        const val COLUMN_SYNCED_STATUS = "synced_status"
        const val COLUMN_TIMESTAMP_ISO = "timestamp_iso"
    }
}
