package com.stt.android.data.source.local.firstpairing

import android.app.Application
import android.content.Context
import com.stt.android.data.SUUNTO_PREFS_NAME
import com.stt.android.data.fetchFromSharedPrefs
import com.stt.android.data.saveToSharedPrefs
import io.reactivex.Completable
import io.reactivex.Single
import timber.log.Timber
import javax.inject.Inject

class FirstPairingSharedPrefStorage
@Inject constructor(
    val application: Application
) {

    fun fetchIsFirstTimePairingAttempt(): Single<Boolean> {
        Timber.d("Fetching first time attempt done")
        return getSuuntoSharedPreferences().fetchFromSharedPrefs(KEY_SUUNTO_FIRST_TIME_PAIRING_EXPERIENCE, true)
    }

    fun saveIsFirstPairingAttemptAsDone(): Completable {
        Timber.d("Marking first time attempt done")
        return getSuuntoSharedPreferences().saveToSharedPrefs(KEY_SUUNTO_FIRST_TIME_PAIRING_EXPERIENCE, false)
    }

    fun fetchHas3FitnessOnboardingEverShown(): Single<Boolean> {
        return getSuuntoSharedPreferences().fetchFromSharedPrefs(KEY_SUUNTO_3_ONBOARDING_EVER_SHOWN, false)
    }

    fun fetchHasSuunto9OnboardingEverShown(): Single<Boolean> {
        return getSuuntoSharedPreferences().fetchFromSharedPrefs(KEY_SUUNTO_9_ONBOARDING_EVER_SHOWN, false)
    }

    fun fetchHasD5OnboardingEverShown(): Single<Boolean> {
        return getSuuntoSharedPreferences().fetchFromSharedPrefs(KEY_SUUNTO_D5_ONBOARDING_EVER_SHOWN, false)
    }

    fun fetchHasOnboardingEverShown(deviceType: String): Single<Boolean> {
        return getSuuntoSharedPreferences()
            .fetchFromSharedPrefs(KEY_SUUNTO_ONBOARDING_EVER_SHOWN_PREFIX + deviceType, false)
    }

    fun save3FitnessOnboardingShownAtLeastOnce(): Completable {
        return getSuuntoSharedPreferences().saveToSharedPrefs(KEY_SUUNTO_3_ONBOARDING_EVER_SHOWN, true)
    }

    fun saveSuunto9OnboardingShownAtLeastOnce(): Completable {
        return getSuuntoSharedPreferences().saveToSharedPrefs(KEY_SUUNTO_9_ONBOARDING_EVER_SHOWN, true)
    }

    fun saveD5OnboardingShownAtLeastOnce(): Completable {
        return getSuuntoSharedPreferences().saveToSharedPrefs(KEY_SUUNTO_D5_ONBOARDING_EVER_SHOWN, true)
    }

    fun saveOnboardingShownAtLeastOnce(deviceType: String): Completable {
        return getSuuntoSharedPreferences().saveToSharedPrefs(KEY_SUUNTO_ONBOARDING_EVER_SHOWN_PREFIX + deviceType, true)
    }

    fun getSuuntoSharedPreferences() =
        application.getSharedPreferences(SUUNTO_PREFS_NAME, Context.MODE_PRIVATE)

    companion object {
        /**
         * True if user has never paired a watch before, false otherwise
         */
        private const val KEY_SUUNTO_FIRST_TIME_PAIRING_EXPERIENCE = "key_suunto_user_paired_watch_before"

        /**
         * True if user has ever been shown Suunto 3 Fitness onboarding
         */
        private const val KEY_SUUNTO_3_ONBOARDING_EVER_SHOWN = "key_suunto_user_first_time_suunto3_onboarding_ever_shown"

        /**
         * True if user has ever been shown Suunto 9 onboarding
         */
        private const val KEY_SUUNTO_9_ONBOARDING_EVER_SHOWN = "key_suunto_user_first_time_suunto9_onboarding_ever_shown"

        /**
         * True if user has ever been shown Suunto D5 onboarding
         */
        private const val KEY_SUUNTO_D5_ONBOARDING_EVER_SHOWN = "key_suunto_user_first_time_suuntoD5_onboarding_ever_shown"

        private const val KEY_SUUNTO_ONBOARDING_EVER_SHOWN_PREFIX = "key_suunto_onboarding_ever_shown_"
    }
}
