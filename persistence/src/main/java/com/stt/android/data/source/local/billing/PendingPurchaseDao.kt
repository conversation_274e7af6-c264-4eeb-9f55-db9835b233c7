package com.stt.android.data.source.local.billing

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.stt.android.data.source.local.TABLE_PENDING_PURCHASE

@Dao
abstract class PendingPurchaseDao {
    @Query("SELECT * FROM $TABLE_PENDING_PURCHASE")
    abstract suspend fun getAll(): List<LocalPendingPurchase>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun upsert(localPendingPurchase: LocalPendingPurchase)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun upsert(localPendingPurchase: List<LocalPendingPurchase>)

    @Delete
    abstract suspend fun deletePendingPurchase(pendingPurchase: LocalPendingPurchase)

    @Query("DELETE FROM $TABLE_PENDING_PURCHASE")
    abstract suspend fun deleteAll()
}
