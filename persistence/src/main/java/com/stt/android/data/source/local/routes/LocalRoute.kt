package com.stt.android.data.source.local.routes

import android.content.ContentValues
import android.database.Cursor
import android.provider.BaseColumns
import androidx.room.ColumnInfo
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.stt.android.data.source.local.IntListJsonConverter
import com.stt.android.data.source.local.TABLE_ROUTES
import com.stt.android.data.source.local.routes.LocalRouteProducer.Companion.PRODUCER_ICON_URL
import com.stt.android.data.source.local.routes.LocalRouteProducer.Companion.PRODUCER_ID
import com.stt.android.data.source.local.routes.LocalRouteProducer.Companion.PRODUCER_NAME
import com.stt.android.db.getBlob
import com.stt.android.db.getDouble
import com.stt.android.db.getDoubleOrNull
import com.stt.android.db.getInt
import com.stt.android.db.getLong
import com.stt.android.db.getString
import com.stt.android.db.getStringOrNull
import java.util.UUID

@Entity(tableName = TABLE_ROUTES)
@TypeConverters(
    IntListJsonConverter::class,
    RouteSegmentProtoConverter::class,
    PointJsonConverter::class
)
data class LocalRoute(
    @PrimaryKey
    @ColumnInfo(name = ID)
    val id: String = generateId(),
    @ColumnInfo(name = WATCH_ROUTE_ID) val watchRouteId: Int = 0,
    @ColumnInfo(name = KEY) val key: String = "",
    @ColumnInfo(name = OWNER_USER_NAME) val ownerUserName: String,
    @ColumnInfo(name = NAME) val name: String,
    @ColumnInfo(name = VISIBILITY) val visibility: String,
    @ColumnInfo(name = ACTIVITY_IDS) val activityIds: List<Int>,
    @ColumnInfo(name = AVERAGE_SPEED) val averageSpeed: Double,
    @ColumnInfo(name = TOTAL_DISTANCE) val totalDistance: Double,
    @ColumnInfo(name = ASCENT) val ascent: Double,
    @ColumnInfo(name = DESCENT) val descent: Double,
    @ColumnInfo(name = START_POINT) val startPoint: LocalPoint,
    @ColumnInfo(name = CENTER_POINT) val centerPoint: LocalPoint,
    @ColumnInfo(name = STOP_POINT) val stopPoint: LocalPoint,
    @ColumnInfo(name = LOCALLY_CHANGED) val locallyChanged: Boolean,
    @ColumnInfo(name = MODIFIED_DATE) val modifiedDate: Long,
    @ColumnInfo(name = DELETED) val deleted: Boolean = false,
    @ColumnInfo(name = CREATED_DATE) val createdDate: Long = System.currentTimeMillis(),
    @ColumnInfo(name = SEGMENTS_MODIFIED_DATE) val segmentsModifiedDate: Long = modifiedDate,
    @ColumnInfo(name = WATCH_SYNC_STATE) val watchSyncState: String,
    @ColumnInfo(name = WATCH_SYNC_RESPONSE_CODE) val watchSyncResponseCode: Int = 0,
    @ColumnInfo(name = WATCH_ENABLED) val watchEnabled: Boolean = false,
    @ColumnInfo(name = SEGMENTS) val segments: List<LocalRouteSegment>,
    @ColumnInfo(name = IS_IN_PROGRESS) val isInProgress: Boolean = false,
    @ColumnInfo(name = TURN_WAYPOINTS_ENABLED) val turnWaypointsEnabled: Boolean = false,
    @Embedded val producer: LocalRouteProducer? = null,
    @ColumnInfo(name = EXTERNAL_URL) val externalUrl: String? = null,
) {

    /**
     * True if this route was mapped from a server response,
     * which therefore does not have local route ID
     */
    val fromServer: Boolean
        get() = this.key.isNotEmpty() && this.id.isEmpty()

    constructor(cv: ContentValues) : this(
        id = cv.getAsString(ID),
        watchRouteId = cv.getAsInteger(WATCH_ROUTE_ID),
        key = cv.getAsString(KEY),
        ownerUserName = cv.getAsString(OWNER_USER_NAME),
        name = cv.getAsString(NAME),
        visibility = cv.getAsString(VISIBILITY),
        activityIds = cv.getAsString(ACTIVITY_IDS)
            .let { intListJsonConverter.toIntList(it) },
        averageSpeed = cv.getAsDouble(AVERAGE_SPEED),
        totalDistance = cv.getAsDouble(TOTAL_DISTANCE),
        ascent = cv.getAsDouble(ASCENT) ?: 0.0,
        descent = cv.getAsDouble(DESCENT) ?: 0.0,
        startPoint = cv.getAsString(START_POINT)
            .let { pointJsonConverter.toNonNullPoint(it) },
        centerPoint = cv.getAsString(CENTER_POINT)
            .let { pointJsonConverter.toNonNullPoint(it) },
        stopPoint = cv.getAsString(STOP_POINT)
            .let { pointJsonConverter.toNonNullPoint(it) },
        locallyChanged = cv.getAsInteger(LOCALLY_CHANGED) != 0,
        modifiedDate = cv.getAsLong(MODIFIED_DATE),
        deleted = cv.getAsInteger(DELETED) != 0,
        createdDate = cv.getAsLong(CREATED_DATE),
        segmentsModifiedDate = cv.getAsLong(SEGMENTS_MODIFIED_DATE),
        watchSyncState = cv.getAsString(WATCH_SYNC_STATE),
        watchSyncResponseCode = cv.getAsInteger(WATCH_SYNC_RESPONSE_CODE),
        watchEnabled = cv.getAsInteger(WATCH_ENABLED) != 0,
        segments = cv.getAsByteArray(SEGMENTS)
            .let { routeSegmentProtoConverter.toRouteSegment(it) },
        isInProgress = cv.getAsInteger(IS_IN_PROGRESS) != 0,
        turnWaypointsEnabled = cv.getAsInteger(TURN_WAYPOINTS_ENABLED) != 0,
        producer = LocalRouteProducer.createOrNull(
            id = cv.getAsString(PRODUCER_ID),
            name = cv.getAsString(PRODUCER_NAME),
            iconUrl = cv.getAsString(PRODUCER_ICON_URL)
        ),
        externalUrl = cv.getAsString(EXTERNAL_URL)
    )

    /**
     * Use with caution, expects [Cursor] parameter to come from table with DB version >= 14
     */
    constructor(c: Cursor) : this(
        id = c.getString(ID),
        watchRouteId = c.getInt(WATCH_ROUTE_ID),
        key = c.getString(KEY),
        ownerUserName = c.getString(OWNER_USER_NAME),
        name = c.getString(NAME),
        visibility = c.getString(VISIBILITY),
        activityIds = c.getString(ACTIVITY_IDS)
            .let { intListJsonConverter.toIntList(it) },
        averageSpeed = c.getDouble(AVERAGE_SPEED),
        totalDistance = c.getDouble(TOTAL_DISTANCE),
        ascent = c.getDoubleOrNull(ASCENT) ?: 0.0,
        descent = c.getDoubleOrNull(DESCENT) ?: 0.0,
        startPoint = c.getString(START_POINT)
            .let { pointJsonConverter.toNonNullPoint(it) },
        centerPoint = c.getString(CENTER_POINT)
            .let { pointJsonConverter.toNonNullPoint(it) },
        stopPoint = c.getString(STOP_POINT)
            .let { pointJsonConverter.toNonNullPoint(it) },
        locallyChanged = c.getInt(LOCALLY_CHANGED) != 0,
        modifiedDate = c.getLong(MODIFIED_DATE),
        deleted = c.getInt(DELETED) != 0,
        createdDate = c.getLong(CREATED_DATE),
        segmentsModifiedDate = c.getLong(SEGMENTS_MODIFIED_DATE),
        watchSyncState = c.getString(WATCH_SYNC_STATE),
        watchSyncResponseCode = c.getInt(WATCH_SYNC_RESPONSE_CODE),
        watchEnabled = c.getInt(WATCH_ENABLED) != 0,
        segments = c.getBlob(SEGMENTS).let { routeSegmentProtoConverter.toRouteSegment(it) },
        isInProgress = c.getInt(IS_IN_PROGRESS) != 0,
        turnWaypointsEnabled = c.getInt(TURN_WAYPOINTS_ENABLED) != 0,
        producer = LocalRouteProducer.createOrNull(
            id = c.getStringOrNull(PRODUCER_ID),
            name = c.getStringOrNull(PRODUCER_NAME),
            iconUrl = c.getStringOrNull(PRODUCER_ICON_URL)
        ),
        externalUrl = c.getStringOrNull(EXTERNAL_URL)
    )

    @Suppress("DEPRECATION")
    constructor(old: LocalRouteUpToVersion13) : this(
        id = old.id,
        watchRouteId = old.watchRouteId,
        key = old.key,
        ownerUserName = old.ownerUserName,
        name = old.name,
        visibility = old.visibility,
        activityIds = old.activityIds,
        averageSpeed = old.averageSpeed,
        totalDistance = old.totalDistance,
        ascent = 0.0,
        descent = 0.0,
        startPoint = old.startPoint,
        centerPoint = old.centerPoint,
        stopPoint = old.stopPoint,
        locallyChanged = old.locallyChanged,
        modifiedDate = old.modifiedDate,
        deleted = old.deleted,
        createdDate = old.createdDate,
        segmentsModifiedDate = old.modifiedDate,
        watchSyncState = old.watchSyncState,
        watchSyncResponseCode = old.watchSyncResponseCode,
        watchEnabled = old.watchEnabled,
        segments = old.segments
    )

    fun asContentValues(): ContentValues {
        return ContentValues().apply {
            put(ID, id)
            put(WATCH_ROUTE_ID, watchRouteId)
            put(KEY, key)
            put(OWNER_USER_NAME, ownerUserName)
            put(NAME, name)
            put(VISIBILITY, visibility)
            put(ACTIVITY_IDS, intListJsonConverter.fromIntList(activityIds))
            put(AVERAGE_SPEED, averageSpeed)
            put(TOTAL_DISTANCE, totalDistance)
            put(ASCENT, ascent)
            put(DESCENT, descent)
            put(START_POINT, pointJsonConverter.fromPoint(startPoint))
            put(CENTER_POINT, pointJsonConverter.fromPoint(centerPoint))
            put(STOP_POINT, pointJsonConverter.fromPoint(stopPoint))
            put(LOCALLY_CHANGED, if (locallyChanged) 1 else 0)
            put(MODIFIED_DATE, modifiedDate)
            put(DELETED, if (deleted) 1 else 0)
            put(CREATED_DATE, createdDate)
            put(SEGMENTS_MODIFIED_DATE, segmentsModifiedDate)
            put(WATCH_SYNC_STATE, watchSyncState)
            put(WATCH_SYNC_RESPONSE_CODE, watchSyncResponseCode)
            put(WATCH_ENABLED, if (watchEnabled) 1 else 0)
            put(SEGMENTS, routeSegmentProtoConverter.fromRouteSegment(segments))
            put(IS_IN_PROGRESS, if (isInProgress) 1 else 0)
            put(TURN_WAYPOINTS_ENABLED, if (turnWaypointsEnabled) 1 else 0)
            put(PRODUCER_ID, producer?.id)
            put(PRODUCER_NAME, producer?.name)
            put(PRODUCER_ICON_URL, producer?.iconUrl)
            put(EXTERNAL_URL, externalUrl)
        }
    }

    companion object DbFields {
        const val ID = BaseColumns._ID
        const val KEY = "key"
        const val OWNER_USER_NAME = "ownerUserName"
        const val NAME = "name"
        const val VISIBILITY = "visibility"
        const val ACTIVITY_IDS = "activityIds"
        const val AVERAGE_SPEED = "avgSpeed"
        const val TOTAL_DISTANCE = "totalDistance"
        const val ASCENT = "ascent"
        const val DESCENT = "descent"
        const val START_POINT = "startPoint"
        const val CENTER_POINT = "centerPoint"
        const val STOP_POINT = "stopPoint"
        const val LOCALLY_CHANGED = "locallyChanged"
        const val DELETED = "deleted"
        const val CREATED_DATE = "created"
        const val MODIFIED_DATE = "modifiedDate"
        const val SEGMENTS_MODIFIED_DATE = "segmentsModifiedDate"
        const val WATCH_SYNC_STATE = "watchSyncState"
        const val WATCH_SYNC_RESPONSE_CODE = "watchSyncResponseCode"
        const val SEGMENTS = "segments"
        const val WATCH_ROUTE_ID = "watchRouteId"
        const val WATCH_ENABLED = "watchEnabled"
        const val IS_IN_PROGRESS = "isInProgress"
        const val TURN_WAYPOINTS_ENABLED = "turnWaypointsEnabled"
        const val EXTERNAL_URL = "externalUrl"

        val intListJsonConverter = IntListJsonConverter()
        val routeSegmentProtoConverter = RouteSegmentProtoConverter()
        val pointJsonConverter = PointJsonConverter()

        fun generateId() = UUID.randomUUID().toString()
    }
}
