package com.stt.android.data.source.local.workout

import androidx.room.ColumnInfo

data class LocalUserWorkoutSummary(
    /**
     * @return the totalWorkouts
     */
    @ColumnInfo(name = "totalWorkouts") val totalWorkouts: Int,
    /**
     * @return the totalDistance in meters
     */
    @ColumnInfo(name = "totalDistance") val totalDistance: Double,
    /**
     * @return the totalDuration in seconds
     */
    @ColumnInfo(name = "totalDuration") val totalDuration: Double,
    /**
     * @return the totalEnergy
     */
    @ColumnInfo(name = "totalEnergyKCal") val totalEnergyKCal: Double
)
