package com.stt.android.data.source.local.user

import androidx.room.TypeConverter
import com.squareup.moshi.JsonClass
import com.stt.android.moshi.buildBasicMoshi

@JsonClass(generateAdapter = true)
data class LocalUserSession(
    val sessionKey: String?,
    // Session key for the watch with limited access to APIs, e.g. to download offline maps
    val watchKey: String? = null,
    val facebookConnected: Boolean,
    val emailVerified: Boolean?,
)

class LocalUserSessionConverter {
    private val moshi = buildBasicMoshi()
    private val userSessionAdapter = moshi.adapter(LocalUserSession::class.java)

    @TypeConverter
    fun fromUserSession(userSession: LocalUserSession?): String? =
        userSession?.let { userSessionAdapter.toJson(it) }

    @TypeConverter
    fun toUserSession(userSessionJson: String?): LocalUserSession? =
        userSessionJson?.let { userSessionAdapter.fromJson(it) }
}
