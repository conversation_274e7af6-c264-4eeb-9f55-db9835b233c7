package com.stt.android.data.source.local.billing

import androidx.test.ext.junit.runners.AndroidJUnit4
import com.google.common.truth.Truth.assertThat
import com.stt.android.data.source.local.RoomAppDatabaseTest
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
internal class SubscriptionItemDaoTest : RoomAppDatabaseTest() {

    private lateinit var subscriptionItemDao: SubscriptionItemDao

    @Before
    fun setUp() {
        subscriptionItemDao = db.subscriptionItemDao()
    }

    @Test
    fun insertAndGetSubscriptionItem() = runTest {
        val original = LocalSubscriptionItem(
            id = 99,
            type = LocalSubscriptionInfo.SubscriptionType.ON_HOLD,
            length = LocalSubscriptionInfo.SubscriptionLength.MONTHLY,
            daysLeft = 11,
            autoRenew = true
        )

        subscriptionItemDao.upsert(original)

        val fetchedSubscriptionItems = subscriptionItemDao.getAll()

        assertThat(fetchedSubscriptionItems).hasSize(1)
        assertThat(fetchedSubscriptionItems.first()).isEqualTo(original)
    }
}
