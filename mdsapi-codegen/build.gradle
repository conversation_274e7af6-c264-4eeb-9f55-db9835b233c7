plugins {
    id 'java-library'
    id 'org.jetbrains.kotlin.jvm'
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

compileKotlin {
    kotlinOptions {
        jvmTarget = "17"
    }
}

dependencies {
    implementation libs.ksp.api
    implementation libs.kotlin.poet
    implementation libs.kotlin.poet.ksp

    // We need the annotations
    implementation project(':mdsapi-annotations')
}
