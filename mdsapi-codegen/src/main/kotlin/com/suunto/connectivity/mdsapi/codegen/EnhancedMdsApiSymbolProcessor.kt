package com.suunto.connectivity.mdsapi.codegen

import com.google.devtools.ksp.processing.*
import com.google.devtools.ksp.symbol.*
import com.google.devtools.ksp.validate
import com.suunto.connectivity.mdsapi.annotations.GenerateMdsConsumerProducer
import com.suunto.connectivity.mdsapi.annotations.MdsQuery
import com.suunto.connectivity.mdsapi.annotations.MdsResponse

/**
 * Enhanced KSP Symbol Processor for generating MDS API Consumer and Producer classes
 * Supports both interface-based (@GenerateMdsConsumerProducer) and class-based (@MdsQuery/@MdsResponse) generation
 */
class EnhancedMdsApiSymbolProcessor(
    private val codeGenerator: CodeGenerator,
    private val logger: KSPLogger,
    private val options: Map<String, String>
) : SymbolProcessor {
    
    private val messageTypeManager = MdsApiMessageTypeManager(codeGenerator, logger)
    private val queryResponsePairs = mutableMapOf<String, Pair<KSClassDeclaration?, KSClassDeclaration?>>()
    
    override fun process(resolver: Resolver): List<KSAnnotated> {
        logger.warn("EnhancedMdsApiSymbolProcessor: Starting processing...")

        // Process interface-based generation (@GenerateMdsConsumerProducer)
        processInterfaceBasedGeneration(resolver)
        
        // Process class-based generation (@MdsQuery/@MdsResponse)
        processClassBasedGeneration(resolver)
        
        // Generate message types and registration
        messageTypeManager.generateMessageTypesFile()
        messageTypeManager.generateMessageTypeRegistration()
        messageTypeManager.generateMessageTypesHelper()

        // Generate enhanced message type validation
        val messageTypesUpdater = MdsApiMessageTypesUpdater(codeGenerator, logger)
        messageTypesUpdater.generateEnhancedMessageTypeValidation(messageTypeManager.getAllMessageTypes())

        logger.warn("EnhancedMdsApiSymbolProcessor: Processing completed")
        return emptyList()
    }
    
    private fun processInterfaceBasedGeneration(resolver: Resolver): List<KSAnnotated> {
        val symbols = resolver.getSymbolsWithAnnotation(
            GenerateMdsConsumerProducer::class.qualifiedName!!
        )

        logger.warn("Found ${symbols.count()} interfaces with @GenerateMdsConsumerProducer")

        val ret = symbols.filter { !it.validate() }.toList()

        symbols
            .filter { it is KSClassDeclaration && it.validate() }
            .forEach { symbol ->
                val classDeclaration = symbol as KSClassDeclaration
                logger.warn("Processing interface ${classDeclaration.qualifiedName?.asString()}")
                try {
                    processApiInterface(classDeclaration)
                } catch (e: Exception) {
                    logger.error("Error processing ${classDeclaration.qualifiedName?.asString()}: ${e.message}", symbol)
                }
            }

        return ret
    }
    
    private fun processClassBasedGeneration(resolver: Resolver) {
        // Process @MdsQuery annotated classes
        val querySymbols = resolver.getSymbolsWithAnnotation(MdsQuery::class.qualifiedName!!)
        logger.warn("Found ${querySymbols.count()} classes with @MdsQuery")
        
        querySymbols
            .filter { it is KSClassDeclaration && it.validate() }
            .forEach { symbol ->
                val classDeclaration = symbol as KSClassDeclaration
                processQueryClass(classDeclaration)
            }
        
        // Process @MdsResponse annotated classes
        val responseSymbols = resolver.getSymbolsWithAnnotation(MdsResponse::class.qualifiedName!!)
        logger.warn("Found ${responseSymbols.count()} classes with @MdsResponse")
        
        responseSymbols
            .filter { it is KSClassDeclaration && it.validate() }
            .forEach { symbol ->
                val classDeclaration = symbol as KSClassDeclaration
                processResponseClass(classDeclaration)
            }
        
        // Generate Consumer/Producer for matched Query/Response pairs
        generateConsumerProducerForPairs()
    }
    
    private fun processApiInterface(classDeclaration: KSClassDeclaration) {
        logger.info("Processing MDS API interface: ${classDeclaration.qualifiedName?.asString()}")
        
        // Validate that it's an interface
        if (classDeclaration.classKind != ClassKind.INTERFACE) {
            logger.error("@GenerateMdsConsumerProducer can only be applied to interfaces", classDeclaration)
            return
        }
        
        // Parse the interface methods
        val apiMethods = parseApiMethods(classDeclaration)
        
        if (apiMethods.isEmpty()) {
            logger.warn("No API methods found in ${classDeclaration.qualifiedName?.asString()}", classDeclaration)
            return
        }
        
        // Register message types for each method
        apiMethods.forEach { method ->
            messageTypeManager.registerMessageType(method.name)
        }
        
        // Generate Consumer
        generateConsumer(classDeclaration, apiMethods)
        
        // Generate Producer
        generateProducer(classDeclaration, apiMethods)
    }
    
    private fun processQueryClass(classDeclaration: KSClassDeclaration) {
        logger.info("Processing MDS Query class: ${classDeclaration.qualifiedName?.asString()}")
        
        val className = classDeclaration.simpleName.asString()
        val baseName = extractBaseName(className, "Query")
        
        // Register message type
        val annotation = classDeclaration.annotations.find { 
            it.shortName.asString() == "MdsQuery" 
        }
        val customMessageType = annotation?.arguments?.find { 
            it.name?.asString() == "messageType" 
        }?.value as? Int
        
        if (customMessageType != null && customMessageType != -1) {
            messageTypeManager.registerMessageType(baseName, customMessageType)
        } else {
            messageTypeManager.registerMessageType(baseName)
        }
        
        // Store for pairing with response
        val currentPair = queryResponsePairs[baseName] ?: (null to null)
        queryResponsePairs[baseName] = currentPair.copy(first = classDeclaration)
    }
    
    private fun processResponseClass(classDeclaration: KSClassDeclaration) {
        logger.info("Processing MDS Response class: ${classDeclaration.qualifiedName?.asString()}")
        
        val className = classDeclaration.simpleName.asString()
        val baseName = extractBaseName(className, "Response")
        
        // Store for pairing with query
        val currentPair = queryResponsePairs[baseName] ?: (null to null)
        queryResponsePairs[baseName] = currentPair.copy(second = classDeclaration)
    }
    
    private fun generateConsumerProducerForPairs() {
        queryResponsePairs.forEach { (baseName, pair) ->
            val (queryClass, responseClass) = pair
            
            if (queryClass != null && responseClass != null) {
                logger.info("Generating Consumer/Producer for $baseName")
                generateConsumerForQueryResponse(baseName, queryClass, responseClass)
                generateProducerForQueryResponse(baseName, queryClass, responseClass)
            } else {
                logger.warn("Incomplete pair for $baseName: query=${queryClass?.simpleName?.asString()}, response=${responseClass?.simpleName?.asString()}")
            }
        }
    }
    
    private fun extractBaseName(className: String, suffix: String): String {
        return if (className.endsWith("${suffix}V2")) {
            className.removeSuffix("${suffix}V2")
        } else if (className.endsWith(suffix)) {
            className.removeSuffix(suffix)
        } else {
            className
        }
    }
    
    private fun parseApiMethods(classDeclaration: KSClassDeclaration): List<MdsApiMethodInfo> {
        return classDeclaration.getAllFunctions()
            .filter { it.isAbstract }
            .mapNotNull { function ->
                try {
                    MdsApiMethodParser.parseMethod(function, logger)
                } catch (e: Exception) {
                    logger.error("Error parsing method ${function.simpleName.asString()}: ${e.message}", function)
                    null
                }
            }
            .toList()
    }
    
    private fun generateConsumer(
        classDeclaration: KSClassDeclaration,
        apiMethods: List<MdsApiMethodInfo>
    ) {
        val generator = MdsApiConsumerGenerator(codeGenerator, logger)
        generator.generate(classDeclaration, apiMethods)
    }
    
    private fun generateProducer(
        classDeclaration: KSClassDeclaration,
        apiMethods: List<MdsApiMethodInfo>
    ) {
        val generator = MdsApiProducerGenerator(codeGenerator, logger)
        generator.generate(classDeclaration, apiMethods)
    }
    
    private fun generateConsumerForQueryResponse(
        baseName: String,
        queryClass: KSClassDeclaration,
        responseClass: KSClassDeclaration
    ) {
        val generator = MdsApiQueryResponseConsumerGenerator(codeGenerator, logger)
        generator.generate(baseName, queryClass, responseClass)
    }
    
    private fun generateProducerForQueryResponse(
        baseName: String,
        queryClass: KSClassDeclaration,
        responseClass: KSClassDeclaration
    ) {
        val generator = MdsApiQueryResponseProducerGenerator(codeGenerator, logger)
        generator.generate(baseName, queryClass, responseClass)
    }
}
