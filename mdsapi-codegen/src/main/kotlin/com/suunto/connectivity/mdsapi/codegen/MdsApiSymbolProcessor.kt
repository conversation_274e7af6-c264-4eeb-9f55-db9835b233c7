package com.suunto.connectivity.mdsapi.codegen

import com.google.devtools.ksp.processing.*
import com.google.devtools.ksp.symbol.*
import com.google.devtools.ksp.validate
import com.suunto.connectivity.mdsapi.annotations.GenerateMdsConsumerProducer

/**
 * KSP Symbol Processor for generating MDS API Consumer and Producer classes
 */
class MdsApiSymbolProcessor(
    private val codeGenerator: CodeGenerator,
    private val logger: KSPLogger,
    private val options: Map<String, String>
) : SymbolProcessor {
    
    override fun process(resolver: Resolver): List<KSAnnotated> {
        logger.warn("MdsApiSymbolProcessor: Starting processing...")

        val symbols = resolver.getSymbolsWithAnnotation(
            GenerateMdsConsumerProducer::class.qualifiedName!!
        )

        logger.warn("MdsApiSymbolProcessor: Found ${symbols.count()} symbols with @GenerateMdsConsumerProducer")

        val ret = symbols.filter { !it.validate() }.toList()

        symbols
            .filter { it is KSClassDeclaration && it.validate() }
            .forEach { symbol ->
                val classDeclaration = symbol as KSClassDeclaration
                logger.warn("MdsApiSymbolProcessor: Processing interface ${classDeclaration.qualifiedName?.asString()}")
                try {
                    processApiInterface(classDeclaration)
                } catch (e: Exception) {
                    logger.error("Error processing ${classDeclaration.qualifiedName?.asString()}: ${e.message}", symbol)
                }
            }

        logger.warn("MdsApiSymbolProcessor: Processing completed")
        return ret
    }
    
    private fun processApiInterface(classDeclaration: KSClassDeclaration) {
        logger.info("Processing MDS API interface: ${classDeclaration.qualifiedName?.asString()}")
        
        // Validate that it's an interface
        if (classDeclaration.classKind != ClassKind.INTERFACE) {
            logger.error("@GenerateMdsConsumerProducer can only be applied to interfaces", classDeclaration)
            return
        }
        
        // Parse the interface methods
        val apiMethods = parseApiMethods(classDeclaration)
        
        if (apiMethods.isEmpty()) {
            logger.warn("No API methods found in ${classDeclaration.qualifiedName?.asString()}", classDeclaration)
            return
        }
        
        // Generate Consumer
        generateConsumer(classDeclaration, apiMethods)
        
        // Generate Producer
        generateProducer(classDeclaration, apiMethods)
    }
    
    private fun parseApiMethods(classDeclaration: KSClassDeclaration): List<MdsApiMethodInfo> {
        return classDeclaration.getAllFunctions()
            .filter { it.isAbstract }
            .mapNotNull { function ->
                try {
                    MdsApiMethodParser.parseMethod(function, logger)
                } catch (e: Exception) {
                    logger.error("Error parsing method ${function.simpleName.asString()}: ${e.message}", function)
                    null
                }
            }
            .toList()
    }
    
    private fun generateConsumer(
        classDeclaration: KSClassDeclaration,
        apiMethods: List<MdsApiMethodInfo>
    ) {
        val generator = MdsApiConsumerGenerator(codeGenerator, logger)
        generator.generate(classDeclaration, apiMethods)
    }
    
    private fun generateProducer(
        classDeclaration: KSClassDeclaration,
        apiMethods: List<MdsApiMethodInfo>
    ) {
        val generator = MdsApiProducerGenerator(codeGenerator, logger)
        generator.generate(classDeclaration, apiMethods)
    }
}
