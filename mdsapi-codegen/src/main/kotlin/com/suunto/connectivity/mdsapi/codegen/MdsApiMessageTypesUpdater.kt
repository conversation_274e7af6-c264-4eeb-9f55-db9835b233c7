package com.suunto.connectivity.mdsapi.codegen

import com.google.devtools.ksp.processing.CodeGenerator
import com.google.devtools.ksp.processing.Dependencies
import com.google.devtools.ksp.processing.KSPLogger
import com.squareup.kotlinpoet.*
import com.squareup.kotlinpoet.ksp.writeTo

/**
 * Updates the MessageTypes annotation and validation logic for SuuntoRepositoryService
 */
class MdsApiMessageTypesUpdater(
    private val codeGenerator: CodeGenerator,
    private val logger: KSPLogger
) {
    
    /**
     * Generate enhanced message type validation
     */
    fun generateEnhancedMessageTypeValidation(messageTypes: Map<String, Int>) {
        if (messageTypes.isEmpty()) {
            logger.warn("No message types to generate validation for")
            return
        }
        
        // Generate the enhanced MessageTypes annotation
        generateEnhancedMessageTypesAnnotation(messageTypes)
        
        // Generate validation helper
        generateMessageTypeValidator(messageTypes)
        
        // Generate service extension
        generateServiceExtension(messageTypes)
    }
    
    private fun generateEnhancedMessageTypesAnnotation(messageTypes: Map<String, Int>) {
        val allMessageTypes = getAllExistingMessageTypes() + messageTypes.values
        
        val annotationClass = TypeSpec.annotationBuilder("EnhancedMessageTypes")
            .addKdoc("Enhanced MessageTypes annotation that includes both existing and generated message types")
            .addProperty(
                PropertySpec.builder("value", IntArray::class)
                    .build()
            )
            .build()
        
        val file = FileSpec.builder("com.suunto.connectivity.mdsapi.generated", "EnhancedMessageTypes")
            .addType(annotationClass)
            .addKdoc("Auto-generated enhanced message types annotation")
            .build()
        
        file.writeTo(codeGenerator, Dependencies.ALL_FILES)
    }
    
    private fun generateMessageTypeValidator(messageTypes: Map<String, Int>) {
        val allMessageTypes = getAllExistingMessageTypes() + messageTypes.values
        
        val validatorClass = TypeSpec.objectBuilder("MdsApiMessageTypeValidator")
            .addKdoc("Validator for MDS API message types")
            .addProperty(
                PropertySpec.builder("EXISTING_MESSAGE_TYPES", SET.parameterizedBy(Int::class.asClassName()))
                    .initializer("setOf(${getAllExistingMessageTypes().joinToString(", ")})")
                    .addKdoc("Existing message types from SuuntoRepositoryService")
                    .build()
            )
            .addProperty(
                PropertySpec.builder("GENERATED_MESSAGE_TYPES", SET.parameterizedBy(Int::class.asClassName()))
                    .initializer("setOf(${messageTypes.values.joinToString(", ")})")
                    .addKdoc("Generated message types from MDS API")
                    .build()
            )
            .addProperty(
                PropertySpec.builder("ALL_VALID_MESSAGE_TYPES", SET.parameterizedBy(Int::class.asClassName()))
                    .initializer("EXISTING_MESSAGE_TYPES + GENERATED_MESSAGE_TYPES")
                    .addKdoc("All valid message types")
                    .build()
            )
            .addFunction(
                FunSpec.builder("isValidMessageType")
                    .addParameter("messageType", Int::class)
                    .returns(Boolean::class)
                    .addStatement("return ALL_VALID_MESSAGE_TYPES.contains(messageType)")
                    .addKdoc("Check if the given message type is valid")
                    .build()
            )
            .addFunction(
                FunSpec.builder("isGeneratedMessageType")
                    .addParameter("messageType", Int::class)
                    .returns(Boolean::class)
                    .addStatement("return GENERATED_MESSAGE_TYPES.contains(messageType)")
                    .addKdoc("Check if the given message type is generated by MDS API")
                    .build()
            )
            .addFunction(
                FunSpec.builder("validateMessageType")
                    .addParameter("messageType", Int::class)
                    .addParameter("methodName", String::class.asClassName().copy(nullable = true), KModifier.VARARG)
                    .returns(Boolean::class)
                    .beginControlFlow("if (!isValidMessageType(messageType))")
                    .addStatement("throw IllegalArgumentException(\"Invalid message type: \$messageType\")")
                    .endControlFlow()
                    .addStatement("return true")
                    .addKdoc("Validate message type and throw exception if invalid")
                    .build()
            )
            .build()
        
        val file = FileSpec.builder("com.suunto.connectivity.mdsapi.generated", "MdsApiMessageTypeValidator")
            .addType(validatorClass)
            .build()
        
        file.writeTo(codeGenerator, Dependencies.ALL_FILES)
    }
    
    private fun generateServiceExtension(messageTypes: Map<String, Int>) {
        val extensionClass = TypeSpec.objectBuilder("SuuntoRepositoryServiceExtension")
            .addKdoc("Extension for SuuntoRepositoryService to handle generated message types")
            .addFunction(
                FunSpec.builder("handleGeneratedMessageType")
                    .addParameter("messageType", Int::class)
                    .addParameter("requestBundle", ClassName("android.os", "Bundle"))
                    .returns(
                        ClassName("rx", "Observable")
                            .parameterizedBy(ClassName("com.suunto.connectivity.repository.commands", "Response"))
                            .copy(nullable = true)
                    )
                    .beginControlFlow("return when (messageType)")
                    .apply {
                        messageTypes.forEach { (methodName, messageType) ->
                            addStatement("$messageType -> handle${methodName.replaceFirstChar { it.titlecase() }}Message(requestBundle)")
                        }
                    }
                    .addStatement("else -> null")
                    .endControlFlow()
                    .addKdoc("Handle generated message types")
                    .build()
            )
            .apply {
                messageTypes.forEach { (methodName, messageType) ->
                    addFunction(generateHandlerMethod(methodName, messageType))
                }
            }
            .build()
        
        val file = FileSpec.builder("com.suunto.connectivity.mdsapi.generated", "SuuntoRepositoryServiceExtension")
            .addType(extensionClass)
            .addImport("rx", "Observable")
            .addImport("android.os", "Bundle")
            .addImport("com.suunto.connectivity.repository.commands", "Response")
            .addImport("com.suunto.connectivity.repository.commands", "ErrorResponse")
            .build()
        
        file.writeTo(codeGenerator, Dependencies.ALL_FILES)
    }
    
    private fun generateHandlerMethod(methodName: String, messageType: Int): FunSpec {
        return FunSpec.builder("handle${methodName.replaceFirstChar { it.titlecase() }}Message")
            .addModifiers(KModifier.PRIVATE)
            .addParameter("requestBundle", ClassName("android.os", "Bundle"))
            .returns(
                ClassName("rx", "Observable")
                    .parameterizedBy(ClassName("com.suunto.connectivity.repository.commands", "Response"))
            )
            .beginControlFlow("return try")
            .addComment("Extract query from bundle")
            .addStatement("val query = requestBundle.getParcelable<${methodName.replaceFirstChar { it.titlecase() }}QueryV2>(\"data\")")
            .beginControlFlow("if (query != null)")
            .addComment("Find appropriate producer to handle this query")
            .addStatement("val producer = findProducerForQuery(query)")
            .beginControlFlow("if (producer != null)")
            .addStatement("producer.provideResponseObservable(query, getWatchBt(query.macAddress))")
            .nextControlFlow("else")
            .addStatement("Observable.just(ErrorResponse(\"No producer found for ${methodName}\"))")
            .endControlFlow()
            .nextControlFlow("else")
            .addStatement("Observable.just(ErrorResponse(\"Invalid query data for ${methodName}\"))")
            .endControlFlow()
            .nextControlFlow("catch (e: Exception)")
            .addStatement("Observable.just(ErrorResponse(\"Error handling ${methodName}: \${e.message}\"))")
            .endControlFlow()
            .addKdoc("Handle $methodName message (messageType: $messageType)")
            .build()
    }
    
    private fun getAllExistingMessageTypes(): List<Int> {
        // This would ideally be extracted from the actual SuuntoRepositoryService
        // For now, we'll include the known message types
        return listOf(
            0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
            21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
            41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60,
            61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80,
            81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100,
            101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120,
            121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140,
            141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153
        )
    }
}
