package com.suunto.connectivity.mdsapi.codegen

import com.google.devtools.ksp.processing.CodeGenerator
import com.google.devtools.ksp.processing.Dependencies
import com.google.devtools.ksp.processing.KSPLogger
import com.google.devtools.ksp.symbol.KSClassDeclaration
import com.squareup.kotlinpoet.*
import com.squareup.kotlinpoet.ParameterizedTypeName.Companion.parameterizedBy
import com.squareup.kotlinpoet.ksp.toTypeName

import com.squareup.kotlinpoet.ksp.toClassName
import com.squareup.kotlinpoet.ksp.writeTo
import java.util.Locale

/**
 * Generator for MDS API Producer classes (server side)
 */
class MdsApiProducerGenerator(
    private val codeGenerator: CodeGenerator,
    private val logger: KSPLogger
) {
    
    fun generate(
        classDeclaration: KSClassDeclaration,
        apiMethods: List<MdsApiMethodInfo>
    ) {
        val packageName = classDeclaration.packageName.asString()
        val interfaceName = classDeclaration.simpleName.asString()
        val producerClassName = "${interfaceName}Producer"
        
        logger.info("Generating Producer: $packageName.$producerClassName")
        
        val producerClass = TypeSpec.classBuilder(producerClassName)
            .addSuperinterface(
                ClassName("com.suunto.connectivity", "SuuntoResponseProducer")
                    .parameterizedBy(ClassName("com.suunto.connectivity.repository.commands", "Response"))
            )
            .primaryConstructor(
                FunSpec.constructorBuilder()
                    .addParameter(
                        "suuntoRepositoryService",
                        ClassName("com.suunto.connectivity.repository", "SuuntoRepositoryService")
                    )
                    .addParameter(
                        "mdsApiFactory",
                        ClassName("com.suunto.connectivity.mdsapi", "MdsApiFactory")
                    )
                    .build()
            )
            .addProperty(
                PropertySpec.builder(
                    "suuntoRepositoryService",
                    ClassName("com.suunto.connectivity.repository", "SuuntoRepositoryService")
                )
                    .initializer("suuntoRepositoryService")
                    .addModifiers(KModifier.PRIVATE)
                    .build()
            )
            .addProperty(
                PropertySpec.builder(
                    "actualApi",
                    classDeclaration.toClassName()
                )
                    .delegate(
                        """
                        lazy {
                            mdsApiFactory.create(${interfaceName}::class.java)
                        }
                        """.trimIndent()
                    )
                    .addModifiers(KModifier.PRIVATE)
                    .build()
            )
            .addFunction(generateIsRelatedFunction(apiMethods))
            .addFunction(generateProvideResponseObservableFunction(apiMethods, packageName, classDeclaration))
            .apply {
                apiMethods.forEach { method ->
                    addFunction(generateHandlerMethod(method, packageName))
                }
            }
            .build()
        
        val fileBuilder = FileSpec.builder(packageName, producerClassName)
            .addType(producerClass)
            .addType(generateAutoRegistrationObject(producerClassName))
            .addImport("android.os", "Bundle")
            .addImport("rx", "Observable")
            .addImport("rx", "Single")
            .addImport("kotlinx.coroutines.rx2", "rxSingle")
            .addImport("com.stt.android.utils", "toV1")
            .addImport("com.suunto.connectivity.repository.commands", "Response")
            .addImport("com.suunto.connectivity.repository.commands", "ErrorResponse")
            .addImport("com.suunto.connectivity.repository", "SuuntoRepositoryService")
            .addImport("com.suunto.connectivity.watch", "WatchBt")
            .addImport("com.suunto.connectivity.mdsapi.integration", "MdsApiAutoRegistration")

        // Add imports for existing Query and Response classes from RunSportModesEntities
        apiMethods.forEach { method ->
            val (queryClassName, responseClassName) = getActualClassNames(method.name)
            fileBuilder.addImport("com.suunto.connectivity.mdsapi.apis", queryClassName)
            fileBuilder.addImport("com.suunto.connectivity.mdsapi.apis", responseClassName)
        }

        val file = fileBuilder.build()
        
        file.writeTo(codeGenerator, Dependencies(false, classDeclaration.containingFile!!))
    }

    private fun getActualClassNames(methodName: String): Pair<String, String> {
        // 使用生成的Query和Response类名
        val queryName = "${methodName.replaceFirstChar { if (it.isLowerCase()) it.titlecase(java.util.Locale.US) else it.toString() }}QueryV2"
        val responseName = "${methodName.replaceFirstChar { if (it.isLowerCase()) it.titlecase(java.util.Locale.US) else it.toString() }}ResponseV2"
        return queryName to responseName
    }

    private fun generateIsRelatedFunction(apiMethods: List<MdsApiMethodInfo>): FunSpec {
        val messageTypes = apiMethods.map { method ->
            val (queryClassName, _) = getActualClassNames(method.name)
            "${queryClassName}(\"\", ${getDefaultParametersForQuery(method, queryClassName)}).messageType"
        }.joinToString(",\n            ")

        return FunSpec.builder("isRelated")
            .addModifiers(KModifier.OVERRIDE)
            .addParameter("messageType", Int::class)
            .returns(Boolean::class)
            .addCode(
                """
                val types = listOf(
                    $messageTypes
                )
                return messageType in types
                """.trimIndent()
            )
            .build()
    }

    private fun getDefaultParametersForQuery(method: MdsApiMethodInfo, queryClassName: String): String {
        // 为生成的Query类提供默认参数（除了macAddress）
        val otherParams = method.parameters.filter { it.annotation != ParameterAnnotationType.SERIAL }
        return otherParams.joinToString(", ") { param ->
            when {
                param.type.toTypeName().toString().contains("Boolean") -> "false"
                param.type.toTypeName().toString().contains("Int") -> "0"
                param.type.toTypeName().toString().contains("String") -> "\"\""
                param.type.toTypeName().toString().contains("List") -> "emptyList()"
                param.isNullable -> "null"
                else -> "null"
            }
        }
    }
    
    private fun generateProvideResponseObservableFunction(apiMethods: List<MdsApiMethodInfo>, packageName: String, classDeclaration: KSClassDeclaration): FunSpec {
        val methodBuilder = FunSpec.builder("provideResponseObservable")
            .addModifiers(KModifier.OVERRIDE)
            .addParameter("messageType", Int::class)
            .addParameter("bundle", ClassName("android.os", "Bundle"))
            .returns(
                ClassName("rx", "Observable")
                    .parameterizedBy(ClassName("com.suunto.connectivity.repository.commands", "Response"))
            )
        
        methodBuilder.beginControlFlow("return when (messageType)")

        apiMethods.forEach { method ->
            val (queryClass, _) = getActualClassNames(method.name)
            val handlerMethod = "handle${method.name.replaceFirstChar { if (it.isLowerCase()) it.titlecase(java.util.Locale.US) else it.toString() }}"
            // 使用现有Query类的messageType
            val messageTypeExpression = "${queryClass}(\"\", ${getDefaultParametersForQuery(method, queryClass)}).messageType"

            methodBuilder.addCode(
                """
                $messageTypeExpression -> {
                    val query = bundle.getParcelable<com.suunto.connectivity.mdsapi.apis.${queryClass}>(SuuntoRepositoryService.ArgumentKeys.ARG_DATA)
                        ?: return Observable.just(ErrorResponse("Query data missing"))
                    $handlerMethod(query)
                }

                """.trimIndent()
            )
        }
        
        methodBuilder.addStatement("else -> Observable.just(ErrorResponse(\"Unsupported message type: \$messageType\"))")
        methodBuilder.endControlFlow()
        
        return methodBuilder.build()
    }
    
    private fun generateHandlerMethod(method: MdsApiMethodInfo, packageName: String): FunSpec {
        val handlerName = "handle${method.name.replaceFirstChar { if (it.isLowerCase()) it.titlecase(java.util.Locale.US) else it.toString() }}"
        val (queryClass, responseClass) = getActualClassNames(method.name)

        return FunSpec.builder(handlerName)
            .addModifiers(KModifier.PRIVATE)
            .addParameter("query", ClassName(packageName, queryClass))
            .returns(
                ClassName("rx", "Observable")
                    .parameterizedBy(ClassName("com.suunto.connectivity.repository.commands", "Response"))
            )
            .addCode(
                """
                val watchBt = suuntoRepositoryService.activeDevices.getWatchBt(query.macAddress)
                    ?: return Observable.just(ErrorResponse("Watch not found with MAC address: ${'$'}{query.macAddress}"))

                return rxSingle {
                    try {
                        val result = actualApi.${method.name}(${generateParameterList(method)})
                        // 生成的Response类实现了MdsResponse接口，直接返回
                        result
                    } catch (e: Exception) {
                        // 返回错误Response
                        $responseClass(
                            success = false,
                            data = null,
                            error = e.message ?: "Unknown error",
                            statusCode = null
                        )
                    }
                }.toV1().toObservable() as Observable<Response>
                """.trimIndent()
            )
            .build()
    }
    
    private fun generateParameterList(method: MdsApiMethodInfo): String {
        return method.parameters.joinToString(", ") { param ->
            when (param.annotation) {
                ParameterAnnotationType.SERIAL -> "watchBt.serial"
                else -> "query.${param.name}"
            }
        }
    }

    private fun generateAutoRegistrationObject(producerClassName: String): TypeSpec {
        return TypeSpec.objectBuilder("${producerClassName}AutoRegistration")
            .addKdoc("Auto-registration object for $producerClassName")
            .addInitializerBlock(
                CodeBlock.builder()
                    .addStatement("MdsApiAutoRegistration.registerProducerClass($producerClassName::class.java)")
                    .build()
            )
            .build()
    }
}
