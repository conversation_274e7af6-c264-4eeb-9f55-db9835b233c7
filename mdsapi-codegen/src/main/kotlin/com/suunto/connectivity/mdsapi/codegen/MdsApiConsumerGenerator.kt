package com.suunto.connectivity.mdsapi.codegen

import com.google.devtools.ksp.processing.CodeGenerator
import com.google.devtools.ksp.processing.Dependencies
import com.google.devtools.ksp.processing.KSPLogger
import com.google.devtools.ksp.symbol.KSClassDeclaration
import com.squareup.kotlinpoet.*
import com.squareup.kotlinpoet.ParameterizedTypeName.Companion.parameterizedBy
import com.squareup.kotlinpoet.ksp.toTypeName
import com.squareup.kotlinpoet.ksp.toClassName
import com.squareup.kotlinpoet.ksp.writeTo
import java.util.Locale

/**
 * Generator for MDS API Consumer classes (client side)
 */
class MdsApiConsumerGenerator(
    private val codeGenerator: CodeGenerator,
    private val logger: KSPLogger
) {
    
    fun generate(
        classDeclaration: KSClassDeclaration,
        apiMethods: List<MdsApiMethodInfo>
    ) {
        val packageName = classDeclaration.packageName.asString()
        val interfaceName = classDeclaration.simpleName.asString()
        val consumerClassName = "${interfaceName}Consumer"
        
        logger.info("Generating Consumer: $packageName.$consumerClassName")
        
        val consumerClass = TypeSpec.classBuilder(consumerClassName)
            .addSuperinterface(classDeclaration.toClassName())
            .addSuperinterface(ClassName("com.suunto.connectivity", "SuuntoQueryConsumer"))
            .primaryConstructor(
                FunSpec.constructorBuilder()
                    .addParameter(
                        "suuntoRepositoryClient",
                        ClassName("com.suunto.connectivity.repository", "SuuntoRepositoryClient")
                    )
                    .build()
            )
            .addProperty(
                PropertySpec.builder(
                    "suuntoRepositoryClient",
                    ClassName("com.suunto.connectivity.repository", "SuuntoRepositoryClient")
                )
                    .initializer("suuntoRepositoryClient")
                    .addModifiers(KModifier.PRIVATE)
                    .build()
            )
            .addProperty(generateRelatedClassProperty(apiMethods))
            .addFunction(generateIsResponseRelatedFunction())
            .addFunction(generateGetResponseMessageFunction())
            .apply {
                apiMethods.forEach { method ->
                    addFunction(generateConsumerMethod(method))
                }
            }
            .build()
        
        val fileBuilder = FileSpec.builder(packageName, consumerClassName)
            .addType(consumerClass)
            .addType(generateAutoRegistrationObject(consumerClassName))
            .addImport("com.stt.android.coroutines", "await")
            .addImport("com.suunto.connectivity.repository.commands", "Response")
            .addImport("com.suunto.connectivity.repository.commands", "ErrorResponse")
            .addImport("com.suunto.connectivity.repository", "ResponseMessage")
            .addImport("com.suunto.connectivity.repository", "SuuntoRepositoryException")
            .addImport("com.suunto.connectivity.mdsapi", "MdsResponse")
            .addImport("com.suunto.connectivity.mdsapi.integration", "MdsApiAutoRegistration")

        // 明确导入每个Query和Response类
        apiMethods.forEach { method ->
            val (queryClassName, responseClassName) = getActualClassNames(method.name)
            fileBuilder.addImport("com.suunto.connectivity.mdsapi.apis", queryClassName)
            fileBuilder.addImport("com.suunto.connectivity.mdsapi.apis", responseClassName)
        }

        val file = fileBuilder.build()
        
        file.writeTo(codeGenerator, Dependencies(false, classDeclaration.containingFile!!))
    }

    private fun getActualClassNames(methodName: String): Pair<String, String> {
        // 使用生成的Query和Response类名
        val queryName = "${methodName.replaceFirstChar { if (it.isLowerCase()) it.titlecase(java.util.Locale.US) else it.toString() }}QueryV2"
        val responseName = "${methodName.replaceFirstChar { if (it.isLowerCase()) it.titlecase(java.util.Locale.US) else it.toString() }}ResponseV2"
        return queryName to responseName
    }

    private fun generateRelatedClassProperty(apiMethods: List<MdsApiMethodInfo>): PropertySpec {
        val responseTypes = apiMethods.map { method ->
            val (_, responseClassName) = getActualClassNames(method.name)
            "$responseClassName::class.java"
        }.joinToString(",\n        ")
        
        return PropertySpec.builder(
            "relatedClass",
            LIST.parameterizedBy(
                Class::class.asClassName().parameterizedBy(STAR)
            )
        )
            .addModifiers(KModifier.PRIVATE)
            .initializer(
                """
                listOf(
                    $responseTypes
                )
                """.trimIndent()
            )
            .build()
    }
    
    private fun generateIsResponseRelatedFunction(): FunSpec {
        return FunSpec.builder("isResponseRelated")
            .addModifiers(KModifier.OVERRIDE)
            .addParameter("response", ClassName("com.suunto.connectivity.repository.commands", "Response"))
            .returns(Boolean::class)
            .addStatement("return response::class.java in relatedClass")
            .build()
    }
    
    private fun generateGetResponseMessageFunction(): FunSpec {
        return FunSpec.builder("getResponseMessage")
            .addModifiers(KModifier.OVERRIDE)
            .addParameter("messageId", Int::class)
            .addParameter("response", ClassName("com.suunto.connectivity.repository.commands", "Response"))
            .returns(
                ClassName("com.suunto.connectivity.repository", "ResponseMessage")
                    .parameterizedBy(STAR)
                    .copy(nullable = true)
            )
            .addStatement("return ResponseMessage(messageId, response)")
            .build()
    }
    
    private fun generateConsumerMethod(method: MdsApiMethodInfo): FunSpec {
        val methodBuilder = FunSpec.builder(method.name)
            .addModifiers(KModifier.OVERRIDE, KModifier.SUSPEND)
        
        // Add parameters
        method.parameters.forEach { param ->
            methodBuilder.addParameter(
                param.name,
                param.type.toTypeName().copy(nullable = param.isNullable)
            )
        }
        
        // Add return type - should match the original interface method
        val returnType = method.returnType.toTypeName()
        methodBuilder.returns(returnType)
        
        // Generate method body
        val (queryClassName, responseClassName) = getActualClassNames(method.name)
        
        // Build parameter list for Query constructor (macAddress first, then method parameters)
        val serialParam = method.parameters.find { it.annotation == ParameterAnnotationType.SERIAL }
        val macAddress = serialParam?.name ?: "serial"
        val otherParams = method.parameters.filter { it.annotation != ParameterAnnotationType.SERIAL }
        val parameterList = if (otherParams.isNotEmpty()) {
            "$macAddress, " + otherParams.joinToString(", ") { it.name }
        } else {
            macAddress
        }
        
        methodBuilder.addCode(
            """
            val query = $queryClassName($parameterList)
            return suuntoRepositoryClient.waitForServiceReady()
                .andThen(suuntoRepositoryClient.sendQuery(query).first().toSingle().map { response ->
                    when (response) {
                        is $responseClassName -> {
                            // 生成的Response类已经实现了MdsResponse接口，直接返回
                            response
                        }
                        is ErrorResponse -> $responseClassName(
                            success = false,
                            data = null,
                            error = response.message ?: "Unknown error",
                            statusCode = null
                        )
                        else -> $responseClassName(
                            success = false,
                            data = null,
                            error = "Invalid response [${'$'}response]",
                            statusCode = null
                        )
                    }
                })
                .await()
            """.trimIndent()
        )
        
        return methodBuilder.build()
    }
    
    private fun getActualReturnType(returnType: TypeName): String {
        return when (returnType) {
            is ParameterizedTypeName -> {
                if (returnType.rawType.simpleName == "MdsResponse") {
                    returnType.typeArguments.firstOrNull()?.toString() ?: "Unit"
                } else {
                    returnType.toString()
                }
            }
            else -> returnType.toString()
        }
    }

    private fun generateAutoRegistrationObject(consumerClassName: String): TypeSpec {
        return TypeSpec.objectBuilder("${consumerClassName}AutoRegistration")
            .addKdoc("Auto-registration object for $consumerClassName")
            .addInitializerBlock(
                CodeBlock.builder()
                    .addStatement("MdsApiAutoRegistration.registerConsumerClass($consumerClassName::class.java)")
                    .build()
            )
            .build()
    }
}
