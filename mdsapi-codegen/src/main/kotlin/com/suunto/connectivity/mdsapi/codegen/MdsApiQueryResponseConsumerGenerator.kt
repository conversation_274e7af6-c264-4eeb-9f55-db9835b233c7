package com.suunto.connectivity.mdsapi.codegen

import com.google.devtools.ksp.processing.CodeGenerator
import com.google.devtools.ksp.processing.Dependencies
import com.google.devtools.ksp.processing.KSPLogger
import com.google.devtools.ksp.symbol.KSClassDeclaration
import com.squareup.kotlinpoet.*
import com.squareup.kotlinpoet.ParameterizedTypeName.Companion.parameterizedBy
import com.squareup.kotlinpoet.ksp.toClassName
import com.squareup.kotlinpoet.ksp.writeTo

/**
 * Generator for MDS API Consumer classes based on Query/Response pairs
 */
class MdsApiQueryResponseConsumerGenerator(
    private val codeGenerator: CodeGenerator,
    private val logger: KSPLogger
) {
    
    fun generate(
        baseName: String,
        queryClass: KSClassDeclaration,
        responseClass: KSClassDeclaration
    ) {
        val packageName = queryClass.packageName.asString()
        val consumerClassName = "${baseName}Consumer"
        
        logger.info("Generating Consumer: $packageName.$consumerClassName")
        
        val consumerClass = TypeSpec.classBuilder(consumerClassName)
            .addSuperinterface(ClassName("com.suunto.connectivity", "SuuntoQueryConsumer"))
            .primaryConstructor(
                FunSpec.constructorBuilder()
                    .addParameter(
                        "suuntoRepositoryClient",
                        ClassName("com.suunto.connectivity.repository", "SuuntoRepositoryClient")
                    )
                    .build()
            )
            .addProperty(
                PropertySpec.builder(
                    "suuntoRepositoryClient",
                    ClassName("com.suunto.connectivity.repository", "SuuntoRepositoryClient")
                )
                    .initializer("suuntoRepositoryClient")
                    .addModifiers(KModifier.PRIVATE)
                    .build()
            )
            .addFunction(generateIsRelatedFunction(queryClass))
            .addFunction(generateConsumeFunction(baseName, queryClass, responseClass))
            .build()
        
        val file = FileSpec.builder(packageName, consumerClassName)
            .addType(consumerClass)
            .addType(generateAutoRegistrationObject(consumerClassName))
            .addImport("com.stt.android.coroutines", "await")
            .addImport("com.suunto.connectivity.repository.commands", "Response")
            .addImport("com.suunto.connectivity.repository.commands", "ErrorResponse")
            .addImport("com.suunto.connectivity.repository", "ResponseMessage")
            .addImport("com.suunto.connectivity.repository", "SuuntoRepositoryException")
            .addImport("com.suunto.connectivity.mdsapi.integration", "MdsApiAutoRegistration")
            .build()
        
        file.writeTo(codeGenerator, Dependencies(false, queryClass.containingFile!!))
    }
    
    private fun generateIsRelatedFunction(queryClass: KSClassDeclaration): FunSpec {
        return FunSpec.builder("isRelated")
            .addModifiers(KModifier.OVERRIDE)
            .addParameter("query", ClassName("com.suunto.connectivity.repository.commands", "Query"))
            .returns(Boolean::class)
            .addStatement("return query is %T", queryClass.toClassName())
            .build()
    }
    
    private fun generateConsumeFunction(
        baseName: String,
        queryClass: KSClassDeclaration,
        responseClass: KSClassDeclaration
    ): FunSpec {
        return FunSpec.builder("consume")
            .addModifiers(KModifier.OVERRIDE, KModifier.SUSPEND)
            .addParameter("query", ClassName("com.suunto.connectivity.repository.commands", "Query"))
            .returns(ClassName("com.suunto.connectivity.repository.commands", "Response"))
            .beginControlFlow("return try")
            .addStatement("val typedQuery = query as %T", queryClass.toClassName())
            .addStatement("")
            .addComment("Send query to repository service")
            .addStatement("val responseMessage = suuntoRepositoryClient.sendQuery(typedQuery).await()")
            .addStatement("")
            .beginControlFlow("when (responseMessage)")
            .beginControlFlow("is ResponseMessage.Success ->")
            .addStatement("val response = responseMessage.response as? %T", responseClass.toClassName())
            .beginControlFlow("if (response != null)")
            .addStatement("response")
            .nextControlFlow("else")
            .addStatement("ErrorResponse(\"Invalid response type for ${baseName}\")")
            .endControlFlow()
            .endControlFlow()
            .beginControlFlow("is ResponseMessage.Error ->")
            .addStatement("ErrorResponse(responseMessage.message)")
            .endControlFlow()
            .endControlFlow()
            .nextControlFlow("catch (e: SuuntoRepositoryException)")
            .addStatement("ErrorResponse(e.message ?: \"Unknown error in ${baseName}Consumer\")")
            .nextControlFlow("catch (e: Exception)")
            .addStatement("ErrorResponse(\"Unexpected error in ${baseName}Consumer: \${e.message}\")")
            .endControlFlow()
            .build()
    }
    
    private fun generateAutoRegistrationObject(consumerClassName: String): TypeSpec {
        return TypeSpec.objectBuilder("${consumerClassName}AutoRegistration")
            .addKdoc("Auto-registration object for $consumerClassName")
            .addInitializerBlock(
                CodeBlock.builder()
                    .addStatement("MdsApiAutoRegistration.registerConsumerClass($consumerClassName::class.java)")
                    .build()
            )
            .build()
    }
}
