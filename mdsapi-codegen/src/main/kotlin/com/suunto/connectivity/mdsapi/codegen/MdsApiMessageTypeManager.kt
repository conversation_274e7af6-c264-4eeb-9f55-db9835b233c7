package com.suunto.connectivity.mdsapi.codegen

import com.google.devtools.ksp.processing.CodeGenerator
import com.google.devtools.ksp.processing.Dependencies
import com.google.devtools.ksp.processing.KSPLogger
import com.squareup.kotlinpoet.*
import com.squareup.kotlinpoet.ksp.writeTo

/**
 * Manager for generating and tracking MDS API message types
 */
class MdsApiMessageTypeManager(
    private val codeGenerator: CodeGenerator,
    private val logger: KSPLogger
) {
    
    private val messageTypeRegistry = mutableMapOf<String, Int>()
    private val baseMessageType = 10000
    
    /**
     * Register a message type for the given method
     */
    fun registerMessageType(methodName: String, customMessageType: Int? = null): Int {
        val messageType = customMessageType ?: generateUniqueMessageType(methodName)
        messageTypeRegistry[methodName] = messageType
        logger.info("Registered message type $messageType for method $methodName")
        return messageType
    }
    
    /**
     * Get the message type for a method
     */
    fun getMessageType(methodName: String): Int? {
        return messageTypeRegistry[methodName]
    }
    
    /**
     * Generate a unique message type based on method name
     */
    private fun generateUniqueMessageType(methodName: String): Int {
        val hash = methodName.hashCode().and(0x7FFF) // Ensure positive value
        var messageType = baseMessageType + hash
        
        // Ensure uniqueness
        while (messageTypeRegistry.values.contains(messageType)) {
            messageType++
        }
        
        return messageType
    }
    
    /**
     * Generate the MessageTypes constants file
     */
    fun generateMessageTypesFile() {
        if (messageTypeRegistry.isEmpty()) {
            logger.warn("No message types to generate")
            return
        }
        
        val messageTypesClass = TypeSpec.objectBuilder("MdsApiMessageTypes")
            .addKdoc("Auto-generated message types for MDS API")
            .apply {
                messageTypeRegistry.forEach { (methodName, messageType) ->
                    val constantName = "MSG_${methodName.uppercase()}_V2"
                    addProperty(
                        PropertySpec.builder(constantName, Int::class)
                            .addModifiers(KModifier.CONST)
                            .initializer("$messageType")
                            .addKdoc("Message type for $methodName API call")
                            .build()
                    )
                }
            }
            .build()
        
        val file = FileSpec.builder("com.suunto.connectivity.mdsapi.generated", "MdsApiMessageTypes")
            .addType(messageTypesClass)
            .addKdoc("Auto-generated file containing MDS API message type constants")
            .build()
        
        file.writeTo(codeGenerator, Dependencies.ALL_FILES)
        logger.info("Generated MdsApiMessageTypes with ${messageTypeRegistry.size} message types")
    }
    
    /**
     * Generate the MessageTypes helper class
     */
    fun generateMessageTypesHelper() {
        if (messageTypeRegistry.isEmpty()) {
            return
        }
        
        val messageTypesList = messageTypeRegistry.values.sorted()
        
        val helperClass = TypeSpec.objectBuilder("MdsApiMessageTypesHelper")
            .addKdoc("Helper class for MDS API message types validation and lookup")
            .addProperty(
                PropertySpec.builder("ALL_MESSAGE_TYPES", SET.parameterizedBy(Int::class.asClassName()))
                    .initializer("setOf(${messageTypesList.joinToString(", ")})")
                    .build()
            )
            .addProperty(
                PropertySpec.builder("METHOD_TO_MESSAGE_TYPE", MAP.parameterizedBy(String::class.asClassName(), Int::class.asClassName()))
                    .initializer(buildString {
                        append("mapOf(")
                        messageTypeRegistry.entries.joinToString(", ") { (method, type) ->
                            "\"$method\" to $type"
                        }.let { append(it) }
                        append(")")
                    })
                    .build()
            )
            .addFunction(
                FunSpec.builder("isValidMessageType")
                    .addParameter("messageType", Int::class)
                    .returns(Boolean::class)
                    .addStatement("return ALL_MESSAGE_TYPES.contains(messageType)")
                    .addKdoc("Check if the given message type is valid for MDS API")
                    .build()
            )
            .addFunction(
                FunSpec.builder("getAllMessageTypes")
                    .returns(SET.parameterizedBy(Int::class.asClassName()))
                    .addStatement("return ALL_MESSAGE_TYPES")
                    .addKdoc("Get all valid message types for MDS API")
                    .build()
            )
            .addFunction(
                FunSpec.builder("getMessageTypeFor")
                    .addParameter("methodName", String::class)
                    .returns(Int::class.asClassName().copy(nullable = true))
                    .addStatement("return METHOD_TO_MESSAGE_TYPE[methodName]")
                    .addKdoc("Get message type for the given method name")
                    .build()
            )
            .addFunction(
                FunSpec.builder("getMethodNameFor")
                    .addParameter("messageType", Int::class)
                    .returns(String::class.asClassName().copy(nullable = true))
                    .addStatement("return METHOD_TO_MESSAGE_TYPE.entries.find { it.value == messageType }?.key")
                    .addKdoc("Get method name for the given message type")
                    .build()
            )
            .build()
        
        val file = FileSpec.builder("com.suunto.connectivity.mdsapi.generated", "MdsApiMessageTypesHelper")
            .addType(helperClass)
            .build()
        
        file.writeTo(codeGenerator, Dependencies.ALL_FILES)
    }
    
    /**
     * Generate auto-registration code for message types
     */
    fun generateMessageTypeRegistration() {
        if (messageTypeRegistry.isEmpty()) {
            return
        }
        
        val registrationClass = TypeSpec.objectBuilder("MdsApiMessageTypeRegistration")
            .addKdoc("Auto-registration for MDS API message types")
            .addInitializerBlock(
                CodeBlock.builder()
                    .apply {
                        messageTypeRegistry.forEach { (methodName, messageType) ->
                            addStatement(
                                "MdsApiMessageRegistry.registerMessageType(%S, %L)",
                                methodName,
                                messageType
                            )
                        }
                    }
                    .build()
            )
            .build()
        
        val file = FileSpec.builder("com.suunto.connectivity.mdsapi.generated", "MdsApiMessageTypeRegistration")
            .addType(registrationClass)
            .addImport("com.suunto.connectivity.mdsapi.crossprocess", "MdsApiMessageRegistry")
            .build()
        
        file.writeTo(codeGenerator, Dependencies.ALL_FILES)
        logger.info("Generated message type registration for ${messageTypeRegistry.size} methods")
    }
    
    /**
     * Get all registered message types
     */
    fun getAllMessageTypes(): Map<String, Int> {
        return messageTypeRegistry.toMap()
    }
    
    /**
     * Clear all registered message types (for testing)
     */
    fun clear() {
        messageTypeRegistry.clear()
    }
}
