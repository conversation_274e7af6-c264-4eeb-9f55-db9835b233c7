package com.suunto.connectivity.mdsapi.codegen

import com.google.devtools.ksp.processing.KSPLogger
import com.google.devtools.ksp.symbol.*
import com.suunto.connectivity.mdsapi.annotations.*

/**
 * Data class representing parsed MDS API method information
 */
data class MdsApiMethodInfo(
    val name: String,
    val httpMethod: HttpMethod,
    val urlTemplate: String,
    val parameters: List<MdsApiParameterInfo>,
    val returnType: KSType,
    val isSuspend: Boolean,
    val isFlow: Boolean,
    val timeout: Long?
)

/**
 * Data class representing parsed parameter information
 */
data class MdsApiParameterInfo(
    val name: String,
    val type: KSType,
    val annotation: ParameterAnnotationType,
    val index: Int,
    val isNullable: Boolean
)

/**
 * Enum representing parameter annotation types
 */
enum class ParameterAnnotationType(val annotationName: String) {
    SERIAL("Serial"),
    PATH("Path"),
    QUERY("Query"),
    BODY("Body")
}

/**
 * Parser for MDS API method information
 */
object MdsApiMethodParser {
    
    fun parseMethod(function: KSFunctionDeclaration, logger: KSPLogger): MdsApiMethodInfo {
        val httpMethod = parseHttpMethod(function)
        val urlTemplate = parseUrlTemplate(function, httpMethod)
        val parameters = parseParameters(function, logger)
        val returnType = parseReturnType(function)
        val isSuspend = function.modifiers.contains(Modifier.SUSPEND)
        val isFlow = isFlowReturnType(returnType)
        val timeout = parseTimeout(function)
        
        return MdsApiMethodInfo(
            name = function.simpleName.asString(),
            httpMethod = httpMethod,
            urlTemplate = urlTemplate,
            parameters = parameters,
            returnType = returnType,
            isSuspend = isSuspend,
            isFlow = isFlow,
            timeout = timeout
        )
    }
    
    private fun parseHttpMethod(function: KSFunctionDeclaration): HttpMethod {
        return when {
            function.hasAnnotation<GET>() -> HttpMethod.GET
            function.hasAnnotation<POST>() -> HttpMethod.POST
            function.hasAnnotation<PUT>() -> HttpMethod.PUT
            function.hasAnnotation<DELETE>() -> HttpMethod.DELETE
            function.hasAnnotation<SUBSCRIBE>() -> HttpMethod.SUBSCRIBE
            else -> throw IllegalArgumentException("Method ${function.simpleName.asString()} must have HTTP method annotation")
        }
    }
    
    private fun parseUrlTemplate(function: KSFunctionDeclaration, httpMethod: HttpMethod): String {
        return when (httpMethod) {
            HttpMethod.GET -> function.getAnnotation<GET>()?.value ?: ""
            HttpMethod.POST -> function.getAnnotation<POST>()?.value ?: ""
            HttpMethod.PUT -> function.getAnnotation<PUT>()?.value ?: ""
            HttpMethod.DELETE -> function.getAnnotation<DELETE>()?.value ?: ""
            HttpMethod.SUBSCRIBE -> function.getAnnotation<SUBSCRIBE>()?.value ?: ""
        }
    }
    
    private fun parseParameters(function: KSFunctionDeclaration, logger: KSPLogger): List<MdsApiParameterInfo> {
        return function.parameters.mapIndexedNotNull { index, parameter ->
            val parameterName = parameter.name?.asString() ?: return@mapIndexedNotNull null
            val parameterType = parameter.type.resolve()
            val isNullable = parameterType.isMarkedNullable
            
            val annotationType = when {
                parameter.hasAnnotation<Serial>() -> ParameterAnnotationType.SERIAL
                parameter.hasAnnotation<Path>() -> ParameterAnnotationType.PATH
                parameter.hasAnnotation<Query>() -> ParameterAnnotationType.QUERY
                parameter.hasAnnotation<Body>() -> ParameterAnnotationType.BODY
                else -> {
                    logger.warn("Parameter $parameterName has no MDS annotation, assuming @Body", parameter)
                    ParameterAnnotationType.BODY
                }
            }
            
            MdsApiParameterInfo(
                name = parameterName,
                type = parameterType,
                annotation = annotationType,
                index = index,
                isNullable = isNullable
            )
        }
    }
    
    private fun parseReturnType(function: KSFunctionDeclaration): KSType {
        return function.returnType?.resolve() 
            ?: throw IllegalArgumentException("Function ${function.simpleName.asString()} must have a return type")
    }
    
    private fun isFlowReturnType(returnType: KSType): Boolean {
        val declaration = returnType.declaration
        return declaration.qualifiedName?.asString()?.startsWith("kotlinx.coroutines.flow.Flow") == true
    }
    
    private fun parseTimeout(function: KSFunctionDeclaration): Long? {
        return function.getAnnotation<Timeout>()?.value
    }
    
    // Extension functions for annotation checking
    private inline fun <reified T : Annotation> KSAnnotated.hasAnnotation(): Boolean {
        return annotations.any { 
            it.shortName.asString() == T::class.simpleName
        }
    }
    
    private inline fun <reified T : Annotation> KSAnnotated.getAnnotation(): T? {
        val annotation = annotations.firstOrNull { 
            it.shortName.asString() == T::class.simpleName 
        } ?: return null
        
        return try {
            when (T::class) {
                GET::class -> {
                    val value = annotation.arguments.firstOrNull()?.value as? String ?: ""
                    GET(value) as T
                }
                POST::class -> {
                    val value = annotation.arguments.firstOrNull()?.value as? String ?: ""
                    POST(value) as T
                }
                PUT::class -> {
                    val value = annotation.arguments.firstOrNull()?.value as? String ?: ""
                    PUT(value) as T
                }
                DELETE::class -> {
                    val value = annotation.arguments.firstOrNull()?.value as? String ?: ""
                    DELETE(value) as T
                }
                SUBSCRIBE::class -> {
                    val value = annotation.arguments.firstOrNull()?.value as? String ?: ""
                    SUBSCRIBE(value) as T
                }
                Path::class -> {
                    val value = annotation.arguments.firstOrNull()?.value as? String ?: ""
                    Path(value) as T
                }
                Query::class -> {
                    val value = annotation.arguments.firstOrNull()?.value as? String ?: ""
                    Query(value) as T
                }
                Body::class -> Body() as T
                Serial::class -> Serial() as T
                Timeout::class -> {
                    val value = annotation.arguments.firstOrNull()?.value as? Long ?: 30000L
                    Timeout(value) as T
                }
                else -> null
            }
        } catch (e: Exception) {
            null
        }
    }
}
