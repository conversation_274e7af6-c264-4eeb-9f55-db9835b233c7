package com.suunto.connectivity.mdsapi.codegen

import com.google.devtools.ksp.processing.CodeGenerator
import com.google.devtools.ksp.processing.Dependencies
import com.google.devtools.ksp.processing.KSPLogger
import com.google.devtools.ksp.symbol.KSClassDeclaration
import com.squareup.kotlinpoet.*
import com.squareup.kotlinpoet.ParameterizedTypeName.Companion.parameterizedBy
import com.squareup.kotlinpoet.ksp.toClassName
import com.squareup.kotlinpoet.ksp.writeTo

/**
 * Generator for MDS API Producer classes based on Query/Response pairs
 */
class MdsApiQueryResponseProducerGenerator(
    private val codeGenerator: CodeGenerator,
    private val logger: KSPLogger
) {
    
    fun generate(
        baseName: String,
        queryClass: KSClassDeclaration,
        responseClass: KSClassDeclaration
    ) {
        val packageName = queryClass.packageName.asString()
        val producerClassName = "${baseName}Producer"
        
        logger.info("Generating Producer: $packageName.$producerClassName")
        
        val producerClass = TypeSpec.classBuilder(producerClassName)
            .addSuperinterface(
                ClassName("com.suunto.connectivity", "SuuntoResponseProducer")
                    .parameterizedBy(ClassName("com.suunto.connectivity.repository.commands", "Response"))
            )
            .primaryConstructor(
                FunSpec.constructorBuilder()
                    .addParameter(
                        "suuntoRepositoryService",
                        ClassName("com.suunto.connectivity.repository", "SuuntoRepositoryService")
                    )
                    .addParameter(
                        "mdsApiFactory",
                        ClassName("com.suunto.connectivity.mdsapi", "MdsApiFactory")
                    )
                    .build()
            )
            .addProperty(
                PropertySpec.builder(
                    "suuntoRepositoryService",
                    ClassName("com.suunto.connectivity.repository", "SuuntoRepositoryService")
                )
                    .initializer("suuntoRepositoryService")
                    .addModifiers(KModifier.PRIVATE)
                    .build()
            )
            .addProperty(
                PropertySpec.builder(
                    "mdsApiFactory",
                    ClassName("com.suunto.connectivity.mdsapi", "MdsApiFactory")
                )
                    .initializer("mdsApiFactory")
                    .addModifiers(KModifier.PRIVATE)
                    .build()
            )
            .addFunction(generateIsRelatedFunction(queryClass))
            .addFunction(generateProvideResponseObservableFunction(baseName, queryClass, responseClass))
            .build()
        
        val file = FileSpec.builder(packageName, producerClassName)
            .addType(producerClass)
            .addType(generateAutoRegistrationObject(producerClassName))
            .addImport("android.os", "Bundle")
            .addImport("rx", "Observable")
            .addImport("rx", "Single")
            .addImport("kotlinx.coroutines.rx2", "rxSingle")
            .addImport("com.stt.android.utils", "toV1")
            .addImport("com.suunto.connectivity.repository.commands", "Response")
            .addImport("com.suunto.connectivity.repository.commands", "ErrorResponse")
            .addImport("com.suunto.connectivity.repository", "SuuntoRepositoryService")
            .addImport("com.suunto.connectivity.watch", "WatchBt")
            .addImport("com.suunto.connectivity.mdsapi.integration", "MdsApiAutoRegistration")
            .build()
        
        file.writeTo(codeGenerator, Dependencies(false, queryClass.containingFile!!))
    }
    
    private fun generateIsRelatedFunction(queryClass: KSClassDeclaration): FunSpec {
        return FunSpec.builder("isRelated")
            .addModifiers(KModifier.OVERRIDE)
            .addParameter("query", ClassName("com.suunto.connectivity.repository.commands", "Query"))
            .returns(Boolean::class)
            .addStatement("return query is %T", queryClass.toClassName())
            .build()
    }
    
    private fun generateProvideResponseObservableFunction(
        baseName: String,
        queryClass: KSClassDeclaration,
        responseClass: KSClassDeclaration
    ): FunSpec {
        return FunSpec.builder("provideResponseObservable")
            .addModifiers(KModifier.OVERRIDE)
            .addParameter("query", ClassName("com.suunto.connectivity.repository.commands", "Query"))
            .addParameter("watchBt", ClassName("com.suunto.connectivity.watch", "WatchBt"))
            .returns(
                ClassName("rx", "Observable")
                    .parameterizedBy(ClassName("com.suunto.connectivity.repository.commands", "Response"))
            )
            .beginControlFlow("return try")
            .addStatement("val typedQuery = query as %T", queryClass.toClassName())
            .addStatement("")
            .addComment("Create API instance")
            .addStatement("val api = mdsApiFactory.create(%T::class.java)", generateApiInterface(baseName))
            .addStatement("")
            .addComment("Execute API call and convert to response")
            .addStatement("rxSingle {")
            .addStatement("    try {")
            .addStatement("        val result = api.${generateApiMethodCall(baseName)}(${generateParameterList(queryClass)})")
            .addStatement("        %T(", responseClass.toClassName())
            .addStatement("            success = true,")
            .addStatement("            data = result,")
            .addStatement("            error = null,")
            .addStatement("            statusCode = 200")
            .addStatement("        )")
            .addStatement("    } catch (e: Exception) {")
            .addStatement("        %T(", responseClass.toClassName())
            .addStatement("            success = false,")
            .addStatement("            data = null,")
            .addStatement("            error = e.message ?: \"Unknown error\",")
            .addStatement("            statusCode = null")
            .addStatement("        )")
            .addStatement("    }")
            .addStatement("}.toObservable()")
            .nextControlFlow("catch (e: Exception)")
            .addStatement("Observable.just(ErrorResponse(\"Error in ${baseName}Producer: \${e.message}\"))")
            .endControlFlow()
            .build()
    }
    
    private fun generateApiInterface(baseName: String): ClassName {
        // Assume the API interface follows naming convention
        return ClassName("com.suunto.connectivity.mdsapi.apis", "${baseName}MdsApiV2")
    }
    
    private fun generateApiMethodCall(baseName: String): String {
        // Convert baseName to camelCase method name
        return baseName.replaceFirstChar { it.lowercase() }
    }
    
    private fun generateParameterList(queryClass: KSClassDeclaration): String {
        // Extract parameters from query class properties
        val properties = queryClass.getAllProperties().toList()
        val parameters = mutableListOf<String>()
        
        properties.forEach { property ->
            val propertyName = property.simpleName.asString()
            when (propertyName) {
                "macAddress" -> parameters.add("watchBt.serial")
                "messageType" -> {} // Skip messageType
                else -> parameters.add("typedQuery.$propertyName")
            }
        }
        
        return parameters.joinToString(", ")
    }
    
    private fun generateAutoRegistrationObject(producerClassName: String): TypeSpec {
        return TypeSpec.objectBuilder("${producerClassName}AutoRegistration")
            .addKdoc("Auto-registration object for $producerClassName")
            .addInitializerBlock(
                CodeBlock.builder()
                    .addStatement("MdsApiAutoRegistration.registerProducerClass($producerClassName::class.java)")
                    .build()
            )
            .build()
    }
}
