package com.stt.android.maps.amap

import android.graphics.Bitmap
import android.graphics.Point
import android.location.Location
import com.amap.api.maps.AMap
import com.amap.api.maps.AMap.CancelableCallback
import com.amap.api.maps.AMap.OnMapScreenShotListener
import com.amap.api.maps.AMapOptions
import com.amap.api.maps.CameraUpdate
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.CoordinateConverter
import com.amap.api.maps.LocationSource
import com.amap.api.maps.model.BitmapDescriptor
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.CameraPosition
import com.amap.api.maps.model.CircleOptions
import com.amap.api.maps.model.MarkerOptions
import com.amap.api.maps.model.PolylineOptions
import com.amap.api.maps.model.TileOverlayOptions
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.maps.MAP_TYPE_DARK
import com.stt.android.maps.MAP_TYPE_SATELLITE
import com.stt.android.maps.MarkerZPriority
import com.stt.android.maps.SuuntoActivityDotDescriptor
import com.stt.android.maps.SuuntoActivityNumberDotDescriptor
import com.stt.android.maps.SuuntoBitmapDescriptor
import com.stt.android.maps.SuuntoBitmapResourceDescriptor
import com.stt.android.maps.SuuntoCameraOptions
import com.stt.android.maps.SuuntoCameraPosition
import com.stt.android.maps.SuuntoCameraUpdate
import com.stt.android.maps.SuuntoCameraUpdateNewLatLngBounds
import com.stt.android.maps.SuuntoCameraUpdateNewPosition
import com.stt.android.maps.SuuntoCircleOptions
import com.stt.android.maps.SuuntoFreeCameraUpdate
import com.stt.android.maps.SuuntoLabelDescriptor
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMap.Companion.MAP_3D_MODE_TILT
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMaps
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.SuuntoMyLocationDescriptor
import com.stt.android.maps.SuuntoPersonalHeatMapDotDescriptor
import com.stt.android.maps.SuuntoPolylineOptions
import com.stt.android.maps.SuuntoTileOverlayOptions
import com.stt.android.maps.SuuntoTopRoutesMarkerDotDescriptor
import com.stt.android.maps.amap.delegate.AMapDelegate
import com.stt.android.maps.location.SuuntoLocationListener
import com.stt.android.maps.location.SuuntoLocationRequest
import com.stt.android.maps.location.SuuntoLocationSource
import timber.log.Timber

fun String.toAMapType(): Int = when (this) {
    MAP_TYPE_SATELLITE -> AMap.MAP_TYPE_SATELLITE
    MAP_TYPE_DARK -> AMap.MAP_TYPE_NIGHT
    else -> AMap.MAP_TYPE_NORMAL
}

fun SuuntoMapOptions.toAMap(provider: AMapsProvider): AMapOptions {
    val options = AMapOptions()

    // If 3D-mode is requested, set the camera tilt to MAP_3D_MODE_TILT unless the tilt is
    // explicitly set to a non-zero value.
    if (map3dMode == true && (cameraPosition?.tilt ?: 0f) == 0f) {
        (cameraPosition ?: SuuntoCameraOptions()).copy(tilt = MAP_3D_MODE_TILT)
    } else {
        cameraPosition
    }?.let {
        options.camera(it.toAMap())
    }
    mapType?.let { options.mapType(provider.options.customMapTypes[it]?.aMapType ?: it.toAMapType()) }
    uiZoomControls?.let { options.zoomControlsEnabled(it) }
    uiCompass?.let { options.compassEnabled(it) }
    uiZoomGestures?.let { options.zoomGesturesEnabled(it) }
    uiScrollGestures?.let { options.scrollGesturesEnabled(it) }
    uiRotateGestures?.let { options.rotateGesturesEnabled(it) }
    uiTiltGestures?.let { options.tiltGesturesEnabled(it) }
    zOrderOnTop?.let { options.zOrderOnTop(it) }

    return options
}

fun SuuntoCameraOptions.toAMap(currentCameraPosition: SuuntoCameraPosition? = null): CameraPosition {
    val builder = if (currentCameraPosition != null) {
        CameraPosition.Builder(currentCameraPosition.toAMap())
    } else {
        CameraPosition.Builder()
    }

    return with(builder) {
        target?.let { target(it.toAMap()) }
        zoom?.let { zoom(it) }
        bearing?.let { bearing(it) }
        tilt?.let { tilt(it) }
        build()
    }
}

fun SuuntoCameraPosition.toAMap(): CameraPosition {
    return CameraPosition.Builder()
        .target(this.target.toAMap())
        .zoom(this.zoom)
        .bearing(this.bearing)
        .tilt(this.tilt)
        .build()
}

fun SuuntoCircleOptions.toAMap(): CircleOptions {
    return CircleOptions()
        .center(this.center?.toAMap())
        .radius(this.radius)
        .strokeColor(this.strokeColor)
        .strokeWidth(this.strokeWidth)
        .fillColor(this.fillColor)
        .zIndex(this.zIndex)
}

fun LatLng.toAMap(): com.amap.api.maps.model.LatLng {
    val converter = CoordinateConverter(SuuntoMaps.getContext())
    converter.from(CoordinateConverter.CoordType.GPS)
    return converter.coord(com.amap.api.maps.model.LatLng(this.latitude, this.longitude)).convert()
}

fun LatLngBounds.toAMap(): com.amap.api.maps.model.LatLngBounds {
    return com.amap.api.maps.model.LatLngBounds(
        this.southwest.toAMap(),
        this.northeast.toAMap()
    )
}

fun SuuntoCameraUpdate.toAMap(amapDelegate: AMapDelegate): CameraUpdate {
    return when (this) {
        is SuuntoCameraUpdateNewPosition -> {
            val updatedCameraOptions = updateCameraOptionsByPadding(amapDelegate, cameraOptions)
            val currentCameraPosition = amapDelegate.getCameraPosition()
            CameraUpdateFactory.newCameraPosition(updatedCameraOptions.toAMap(currentCameraPosition))
        }

        is SuuntoCameraUpdateNewLatLngBounds ->
            CameraUpdateFactory.newLatLngBoundsRect(
                bounds.toAMap(),
                amapDelegate.mapPadding[0].toInt() + padding,
                amapDelegate.mapPadding[1].toInt() + padding,
                amapDelegate.mapPadding[2].toInt() + padding,
                amapDelegate.mapPadding[3].toInt() + padding,
            )

        is SuuntoFreeCameraUpdate ->
            CameraUpdateFactory.changeBearing(cameraBearing.toFloat())
    }
}

/**
 * When the visible range of the map view changes, update [SuuntoCameraOptions.target] by paddings
 */
private fun updateCameraOptionsByPadding(
    amapDelegate: AMapDelegate,
    cameraOptions: SuuntoCameraOptions,
): SuuntoCameraOptions {
    val projection = amapDelegate.map.projection
    val markerPoint = projection.toScreenLocation(cameraOptions.target?.toAMap())
    if (markerPoint.x == 0 && markerPoint.y == 0) {
        return cameraOptions
    }
    val zoomLevel = amapDelegate.map.cameraPosition.zoom
    if (cameraOptions.zoom != null && zoomLevel != cameraOptions.zoom) {
        return cameraOptions
    }
    val offsetX = (amapDelegate.mapPadding[2] - amapDelegate.mapPadding[0]).toInt() / 2
    val offsetY = (amapDelegate.mapPadding[3] - amapDelegate.mapPadding[1]).toInt() / 2
    val updatedPoint = Point(markerPoint.x + offsetX, markerPoint.y + offsetY)
    val updatedTarget = projection.fromScreenLocation(updatedPoint)
    return cameraOptions.copy(
        target = updatedTarget.toGoogle(),
    )
}

fun SuuntoMap.CancelableCallback.toAMap(): CancelableCallback {
    val that = this

    return object : CancelableCallback {
        override fun onFinish() {
            that.onFinish()
        }

        override fun onCancel() {
            that.onCancel()
        }
    }
}

fun SuuntoMap.SnapshotReadyCallback.toAMap(): OnMapScreenShotListener {
    val that = this

    return object : OnMapScreenShotListener {
        override fun onMapScreenShot(bitmap: Bitmap?) {
            Timber.d("onMapScreenShot00 ---> bitmap:$bitmap")
        }

        override fun onMapScreenShot(bitmap: Bitmap?, status: Int) {
            Timber.d("onMapScreenShot ---> bitmap:$bitmap, status:$status")
            if (status != 0) {
                Timber.d("Map rendering completed, screenshot without grid")
            } else {
                Timber.d("Map rendering not completed, screenshot with grid")
            }
            that.onSnapshotReady(bitmap)
        }
    }
}

fun SuuntoBitmapDescriptor.toAMap(iconScale: Float = 1.0f): BitmapDescriptor =
    when (this) {
        is SuuntoBitmapResourceDescriptor -> {
            if (isVectorDrawable) {
                BitmapDescriptorFactory.fromBitmap(
                    asBitmap(iconScale)
                        ?: throw IllegalArgumentException("Bitmap is null, can't get descriptor")
                )
            } else {
                BitmapDescriptorFactory.fromBitmap(getNoVectorBitmap(resourceId, iconScale))
            }
        }

        is SuuntoActivityDotDescriptor ->
            BitmapDescriptorFactory.fromBitmap(asBitmap())

        is SuuntoPersonalHeatMapDotDescriptor ->
            BitmapDescriptorFactory.fromBitmap(asBitmap(2.0f))

        is SuuntoLabelDescriptor ->
            BitmapDescriptorFactory.fromBitmap(asBitmap())

        is SuuntoActivityNumberDotDescriptor ->
            BitmapDescriptorFactory.fromBitmap(asBitmap(iconScale))

        is SuuntoMyLocationDescriptor ->
            BitmapDescriptorFactory.fromBitmap(asBitmap())

        is SuuntoTopRoutesMarkerDotDescriptor ->
            BitmapDescriptorFactory.fromBitmap(asBitmap(iconScale))

        else -> BitmapDescriptorFactory.defaultMarker()
    }

fun SuuntoMarkerOptions.toAMap(): MarkerOptions {
    val markerOptions = MarkerOptions()

    latLng?.let { markerOptions.position(it.toAMap()) }
    iconDescriptor?.let { markerOptions.icon(it.toAMap(this.iconScale)) }
    markerOptions.anchor(anchor.first, anchor.second)
    markerOptions.alpha(alpha)
    markerOptions.draggable(draggable)
    markerOptions.isFlat = flat
    markerOptions.rotateAngle(rotation.toAMapRotation())
    markerOptions.zIndex(zPriority.zIndex)

    return markerOptions
}

fun SuuntoPolylineOptions.toAMap() = PolylineOptions()
    .addAll(points.map { it.toAMap() })
    .color(color)
    .width(width)
    .zIndex(getPolylineZIndex(zIndex))
    .apply {
        isDottedLine = dashedLine
    }

private fun getPolylineZIndex(zIndex: Float): Float {
    val selectedStartingPointIndex =
        MarkerZPriority.SELECTED_STARTING_POINT.zIndex
    return if (zIndex < selectedStartingPointIndex) {
        zIndex
    } else {
        zIndex - (zIndex - selectedStartingPointIndex)
    }
}

fun SuuntoTileOverlayOptions.toAMap(): TileOverlayOptions {
    val options = TileOverlayOptions()
    visible?.let { options.visible(it) }
    options.zIndex(zIndex)

    return options
}

fun SuuntoLocationSource.toAMap(): LocationSource {
    val suuntoLocationSource = this

    return object : LocationSource, SuuntoLocationListener {
        private var amapListener: LocationSource.OnLocationChangedListener? = null

        override fun activate(listener: LocationSource.OnLocationChangedListener) {
            amapListener = listener
            suuntoLocationSource.requestLocationUpdates(
                SuuntoLocationRequest.DEFAULT_LOCATION_REQUEST,
                this
            )
        }

        override fun deactivate() {
            suuntoLocationSource.removeLocationUpdates(this)
            amapListener = null
        }

        override fun onLocationChanged(location: Location, source: SuuntoLocationSource) {
            val latLng = LatLng(location.latitude, location.longitude)
            val gLatLng = latLng.toAMap()
            location.latitude = gLatLng.latitude
            location.longitude = gLatLng.longitude
            amapListener?.onLocationChanged(location)
        }

        override fun onLocationAvailability(
            locationAvailable: Boolean,
            source: SuuntoLocationSource
        ) {
            // No action
        }
    }
}

internal fun Float.toAMapRotation(): Float {
    return if (this == 0f) this else 360 - this
}
