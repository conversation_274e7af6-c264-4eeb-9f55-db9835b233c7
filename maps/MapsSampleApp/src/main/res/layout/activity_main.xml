<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context="com.stt.android.mapssample.MainActivity">

    <fragment xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/map"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:uiCompass="false"
        app:uiTiltGestures="false"
        class="com.stt.android.mapssample.DemoMapFragment" />

    <View
        android:id="@+id/topPadding"
        android:layout_width="match_parent"
        android:layout_height="@dimen/map_padding_top"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/colorPrimary"
        android:alpha="0.5" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="@+id/topPadding"
        app:layout_constraintBottom_toBottomOf="@id/topPadding"
        android:text="@string/padding"
        android:textColor="@android:color/white"
        />

    <View
        android:id="@+id/bottomPadding"
        android:layout_width="match_parent"
        android:layout_height="@dimen/map_padding_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/colorPrimary"
        android:alpha="0.5" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="@+id/bottomPadding"
        app:layout_constraintBottom_toBottomOf="@id/bottomPadding"
        android:text="@string/padding"
        android:textColor="@android:color/white"
        />

    <Button
        android:id="@+id/moveCamera"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Move camera"
        app:layout_constraintTop_toTopOf="@+id/bottomPadding"
        app:layout_constraintBottom_toBottomOf="@+id/bottomPadding"
        app:layout_constraintStart_toStartOf="@+id/bottomPadding"
        app:layout_constraintEnd_toEndOf="@+id/bottomPadding"
        />

</androidx.constraintlayout.widget.ConstraintLayout>
