package com.stt.android.maps

// Built-in map types
const val MAP_TYPE_NORMAL: String = "NORMAL"
const val MAP_TYPE_SATELLITE: String = "SATELLITE"
const val MAP_TYPE_TERRAIN: String = "TERRAIN"
const val MAP_TYPE_HYBRID: String = "HYBRID"
const val MAP_TYPE_DARK: String = "DARK"
const val MAP_TYPE_LIGHT: String = "LIGHT"
const val MAP_TYPE_SKI: String = "SKI"
const val MAP_TYPE_AVALANCHE: String = "AVALANCHE"
const val MAP_TYPE_FINLAND_MAANMITTAUSLAITOS_TERRAIN: String = "MAANMITTAUSLAITOS_FINLAND_TERRAIN"

// Known dynamic map types
// TODO Remove later after we fully rely on maptypes-v2.json.
const val MAP_TYPE_MAPBOX_OPEN_STREET_MAP: String = "OPEN_STREET_MAP"
const val MAP_TYPE_MAPBOX_OPEN_CYCLE_MAP: String = "OPEN_CYCLE_MAP"
const val MAP_TYPE_MAPBOX_OUTDOORS_MAP: String = "OUTDOORS_MAP"
const val MAP_TYPE_MAPBOX_LANDSCAPE_MAP: String = "LANDSCAPE_MAP"
const val MAP_TYPE_MAPBOX_NORWAY_KARTVERKET: String = "NORWAY_KARTVERKET"
const val MAP_TYPE_MAPBOX_NORWAY_SEA_AND_LAKE: String = "NORWAY_SEA_AND_LAKE"
const val MAP_TYPE_MAPBOX_SPAIN_INSTITUTO_GEOGRAFICO: String = "SPAIN_INSTITUTO_GEOGRAFICO"
const val MAP_TYPE_FINLAND_BELECTRO_RASTER_TERRAIN_CONTOUR: String = "FINLAND_BELECTRO_HIGH_RESOLUTION"
const val MAP_TYPE_SUUNTO_HILLSHADING: String = "SUUNTO_HILLSHADING"

const val MAP_TYPE_DEFAULT: String = MAP_TYPE_TERRAIN
