package com.stt.android.maps.delegate

import com.google.android.gms.maps.GoogleMap
import com.stt.android.maps.SuuntoMap

/**
 * Map delegate internal interface. These methods are used in the internal implementation and
 * are not visible from [SuuntoMap].
 *
 * Note that similar listener setters are exposed through [SuuntoMapListeners] interface, but they
 * have different implementation adding support for multiple simultaneous listeners.
 */
interface MapDelegateInternal {

    fun setOnCameraIdleListener(listener: GoogleMap.OnCameraIdleListener?)

    fun setOnCameraMoveListener(listener: GoogleMap.OnCameraMoveListener?)

    fun setOnCameraMoveStartedListener(listener: GoogleMap.OnCameraMoveStartedListener?)

    fun setOnMap3dModeChangedWithTiltListener(listener: SuuntoMap.OnMap3dModeChangedListener?)

    fun setOnMapClickListener(listener: SuuntoMap.OnMapClickListener?)

    fun setOnMapLongClickListener(listener: SuuntoMap.OnMapLongClickListener?)

    fun setOnMapMoveListener(listener: SuuntoMap.OnMapMoveListener?)

    fun setOnMarkerClickListener(listener: SuuntoMap.OnMarkerClickListener?)

    fun setOnMarkerDragListener(listener: SuuntoMap.OnMarkerDragListener?)

    fun setOnScaleListener(listener: SuuntoMap.OnScaleListener?)
}
