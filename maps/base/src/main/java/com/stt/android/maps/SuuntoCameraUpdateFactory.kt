@file:JvmName("SuuntoCameraUpdateFactory")

package com.stt.android.maps

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds

fun newCameraPosition(cameraOptions: SuuntoCameraOptions) =
    SuuntoCameraUpdateNewPosition(cameraOptions)

fun newLatLng(latLng: LatLng) =
    SuuntoCameraUpdateNewPosition(SuuntoCameraOptions(target = latLng))

fun newLatLngBounds(bounds: LatLngBounds, padding: Int) =
    SuuntoCameraUpdateNewLatLngBounds(bounds = bounds, padding = padding)

fun newLatLngZoom(latLng: LatLng, zoom: Float) =
    SuuntoCameraUpdateNewPosition(SuuntoCameraOptions(target = latLng, zoom = zoom))
