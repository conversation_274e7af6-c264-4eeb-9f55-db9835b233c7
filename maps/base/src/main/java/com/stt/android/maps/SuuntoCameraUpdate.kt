package com.stt.android.maps

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds

sealed class SuuntoCameraUpdate

data class SuuntoCameraUpdateNewPosition(
    val cameraOptions: SuuntoCameraOptions
) : SuuntoCameraUpdate()

data class SuuntoCameraUpdateNewLatLngBounds(
    val bounds: LatLngBounds,
    val padding: Int
) : SuuntoCameraUpdate()

data class SuuntoFreeCameraUpdate(
    val markerPosition: LatLng,
    val cameraPosition: LatLng,
    val cameraAltitude: Double,
    val cameraPitch: Double,
    val cameraBearing: Double
) : SuuntoCameraUpdate()
