package com.stt.android.maps

import com.google.android.gms.maps.model.LatLngBounds

/**
 * Tile source for [SuuntoTileOverlay]
 *
 * @property tileEndpoints One or more tile endpoint URL. {z}, {x} and {y} are replaced with the
 * corresponding integers. If multiple endpoints are specified, they will be used evenly while
 * requesting tiles. All endpoints MUST return the same content.
 * Example: http://heatmaprestapi-production.eu-west-1.elasticbeanstalk.com/heatmap/v1/Running/{z}/{x}/{y}.png
 * @property tileSize Tile size in pixels
 * @property scheme Influences the y direction of the tile coordinates
 */
class SuuntoTileSource(
    val tileEndpoints: List<String>,
    val tileSize: Int = DEFAULT_TILE_SIZE,
    val scheme: Scheme = DEFAULT_SCHEME
) {
    var minZoom = DEFAULT_MIN_ZOOM
    var maxZoom = DEFAULT_MAX_ZOOM
    var bounds: LatLngBounds? = null

    constructor(
        tileEndpoint: String,
        tileSize: Int = DEFAULT_TILE_SIZE,
        scheme: Scheme = DEFAULT_SCHEME
    ) : this(listOf(tileEndpoint), tileSize, scheme)

    enum class Scheme {
        XYZ,
        TMS
    }

    companion object {
        const val DEFAULT_TILE_SIZE = 512
        const val DEFAULT_MIN_ZOOM = 0
        const val DEFAULT_MAX_ZOOM = 22
        val DEFAULT_SCHEME = Scheme.XYZ
    }
}
