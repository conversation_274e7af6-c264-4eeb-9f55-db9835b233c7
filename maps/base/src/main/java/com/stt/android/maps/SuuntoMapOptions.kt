package com.stt.android.maps

import android.content.Context
import android.content.res.TypedArray
import android.os.Parcelable
import android.util.AttributeSet
import android.util.TypedValue
import androidx.core.content.ContextCompat
import com.google.android.gms.maps.model.LatLng
import kotlinx.parcelize.Parcelize

@Parcelize
data class SuuntoMapOptions(
    var mapType: String? = null,
    var mapsProvider: String? = null,
    var minZoomPreference: Float? = null,
    var maxZoomPreference: Float? = null,
    var cameraPosition: SuuntoCameraOptions? = null,
    var uiZoomControls: Boolean? = null,
    var uiCompass: Boolean? = null,
    var uiMapToolbar: Boolean? = null,
    var uiAttribution: Boolean? = null,
    var uiLogo: Boolean? = null,
    var uiZoomGestures: Boolean? = null,
    var uiScrollGestures: Boolean? = null,
    var uiRotateGestures: Boolean? = null,
    var uiTiltGestures: Boolean? = null,
    var zOrderOnTop: Boolean? = null,
    var useViewLifecycle: Boolean? = null,
    var liteMode: Boolean? = null,
    var textureMode: Boolean? = null,
    var map3dMode: Boolean? = null,
    var enable3dLocation: Boolean? = null,
    var scaleBarAlwaysVisible: Boolean? = null,
    var showMyLocationMarker: Boolean? = null,
) : Parcelable {
    fun mapType(mapType: String) = apply { this.mapType = mapType }
    fun mapProvider(mapProvider: String) = apply { this.mapsProvider = mapProvider }
    fun minZoomPreference(zoom: Float) = apply { this.minZoomPreference = zoom }
    fun maxZoomPreference(zoom: Float) = apply { this.maxZoomPreference = zoom }
    fun camera(cameraPosition: SuuntoCameraOptions) = apply { this.cameraPosition = cameraPosition }
    fun zoomControlsEnabled(enabled: Boolean) = apply { this.uiZoomControls = enabled }
    fun compassEnabled(enabled: Boolean) = apply { this.uiCompass = enabled }
    fun mapToolbarEnabled(enabled: Boolean) = apply { this.uiMapToolbar = enabled }
    fun attributionEnabled(enabled: Boolean) = apply { this.uiAttribution = enabled }
    fun logoEnabled(enabled: Boolean) = apply { this.uiLogo = enabled }
    fun zoomGesturesEnabled(enabled: Boolean) = apply { this.uiZoomGestures = enabled }
    fun scrollGesturesEnabled(enabled: Boolean) = apply { this.uiScrollGestures = enabled }
    fun rotateGesturesEnabled(enabled: Boolean) = apply { this.uiRotateGestures = enabled }
    fun tiltGesturesEnabled(enabled: Boolean) = apply { this.uiTiltGestures = enabled }
    fun zOrderOnTop(zOrderOnTop: Boolean) = apply { this.zOrderOnTop = zOrderOnTop }
    fun useViewLifecycleInFragment(useViewLifecycle: Boolean) =
        apply { this.useViewLifecycle = useViewLifecycle }
    fun liteMode(enabled: Boolean) = apply { this.liteMode = enabled }
    fun textureMode(enabled: Boolean) = apply { this.textureMode = enabled }
    fun map3dMode(enabled: Boolean) = apply { this.map3dMode = enabled }
    fun enable3dLocation(enabled: Boolean) = apply { this.enable3dLocation = enabled }
    fun scaleBarAlwaysVisible(visible: Boolean) = apply { this.scaleBarAlwaysVisible = visible }
    fun showMyLocationMarker(visible: Boolean) = apply { this.showMyLocationMarker = visible }

    fun merge(other: SuuntoMapOptions?): SuuntoMapOptions {
        if (other == null) return this

        return copy(
            mapType = mapType ?: other.mapType,
            mapsProvider = mapsProvider ?: other.mapsProvider,
            minZoomPreference = minZoomPreference ?: other.minZoomPreference,
            maxZoomPreference = maxZoomPreference ?: other.maxZoomPreference,
            cameraPosition = cameraPosition ?: other.cameraPosition,
            uiZoomControls = uiZoomControls ?: other.uiZoomControls,
            uiCompass = uiCompass ?: other.uiCompass,
            uiMapToolbar = uiMapToolbar ?: other.uiMapToolbar,
            uiAttribution = uiAttribution ?: other.uiAttribution,
            uiLogo = uiLogo ?: other.uiLogo,
            uiZoomGestures = uiZoomGestures ?: other.uiZoomGestures,
            uiScrollGestures = uiScrollGestures ?: other.uiScrollGestures,
            uiRotateGestures = uiRotateGestures ?: other.uiRotateGestures,
            uiTiltGestures = uiTiltGestures ?: other.uiTiltGestures,
            zOrderOnTop = zOrderOnTop ?: other.zOrderOnTop,
            useViewLifecycle = useViewLifecycle ?: other.useViewLifecycle,
            liteMode = liteMode ?: other.liteMode,
            textureMode = textureMode ?: other.textureMode,
            map3dMode = map3dMode ?: other.map3dMode,
            enable3dLocation = enable3dLocation ?: other.enable3dLocation,
            scaleBarAlwaysVisible = scaleBarAlwaysVisible ?: other.scaleBarAlwaysVisible,
            showMyLocationMarker = showMyLocationMarker ?: other.showMyLocationMarker,
        )
    }

    companion object {
        val DEFAULTS = SuuntoMapOptions(
            mapType = MAP_TYPE_NORMAL,
            minZoomPreference = 0f,
            maxZoomPreference = 20f,
            cameraPosition = SuuntoCameraOptions.fromLatLngZoom(LatLng(0.0, 0.0), 2f),
            map3dMode = false,
            enable3dLocation = false,
            showMyLocationMarker = true,
        )

        @JvmStatic
        fun createFromAttributes(context: Context, attrs: AttributeSet): SuuntoMapOptions {
            val options = SuuntoMapOptions()
            val typedArray = context.resources.obtainAttributes(attrs, R.styleable.SuuntoMapAttrs)

            with(typedArray) {
                getString(R.styleable.SuuntoMapAttrs_appMapType)?.let {
                    options.mapType(it)
                }
                getString(R.styleable.SuuntoMapAttrs_mapsProvider)?.let {
                    options.mapProvider(it)
                }
                getFloat(R.styleable.SuuntoMapAttrs_minZoomPreference)?.let {
                    options.minZoomPreference(it)
                }
                getFloat(R.styleable.SuuntoMapAttrs_maxZoomPreference)?.let {
                    options.maxZoomPreference(it)
                }
                getCameraPosition()?.let {
                    options.camera(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_uiZoomControls)?.let {
                    options.zoomControlsEnabled(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_uiCompass)?.let {
                    options.compassEnabled(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_uiMapToolbar)?.let {
                    options.mapToolbarEnabled(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_uiAttribution)?.let {
                    options.attributionEnabled(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_uiLogo)?.let {
                    options.logoEnabled(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_uiZoomGestures)?.let {
                    options.zoomGesturesEnabled(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_uiScrollGestures)?.let {
                    options.scrollGesturesEnabled(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_uiRotateGestures)?.let {
                    options.rotateGesturesEnabled(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_uiTiltGestures)?.let {
                    options.tiltGesturesEnabled(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_zOrderOnTop)?.let {
                    options.zOrderOnTop(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_useViewLifecycle)?.let {
                    options.useViewLifecycleInFragment(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_liteMode)?.let {
                    options.liteMode(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_textureMode)?.let {
                    options.textureMode(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_map3dMode)?.let {
                    options.map3dMode(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_enable3dLocation)?.let {
                    options.enable3dLocation(it)
                }
                getBoolean(R.styleable.SuuntoMapAttrs_showMyLocationMarker)?.let {
                    options.showMyLocationMarker(it)
                }
            }

            typedArray.recycle()

            return options
        }

        private fun TypedArray.getBoolean(index: Int): Boolean? {
            return if (hasValue(index)) getBoolean(index, false) else null
        }

        private fun TypedArray.getFloat(index: Int): Float? {
            return if (hasValue(index)) getFloat(index, 0f) else null
        }

        private fun TypedArray.getInt(index: Int): Int? {
            return if (hasValue(index)) getInt(index, 0) else null
        }

        private fun TypedArray.getColor(context: Context, index: Int): Int? {
            if (hasValue(index)) {
                val value = TypedValue()
                if (getValue(index, value)) {
                    if (value.type == TypedValue.TYPE_REFERENCE && value.resourceId != 0) {
                        // Reference to color
                        return ContextCompat.getColor(context, value.resourceId)
                    } else if (value.type in TypedValue.TYPE_FIRST_COLOR_INT..TypedValue.TYPE_LAST_COLOR_INT) {
                        // Color int value directly
                        return value.data
                    }
                }
            }
            return null
        }

        private fun TypedArray.getCameraPosition(): SuuntoCameraOptions? {
            val lat = getFloat(R.styleable.SuuntoMapAttrs_cameraTargetLat)
            val lng = getFloat(R.styleable.SuuntoMapAttrs_cameraTargetLng)
            val zoom = getFloat(R.styleable.SuuntoMapAttrs_cameraZoom)
            val bearing = getFloat(R.styleable.SuuntoMapAttrs_cameraBearing)
            val tilt = getFloat(R.styleable.SuuntoMapAttrs_cameraTilt)

            val target = if (lat != null && lng != null) {
                LatLng(lat.toDouble(), lng.toDouble())
            } else {
                null
            }

            return if (target == null && zoom == null && bearing == null && tilt == null) {
                null
            } else {
                SuuntoCameraOptions(target, zoom, bearing, tilt)
            }
        }
    }
}
