<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <declare-styleable name="SuuntoMapAttrs">

        <attr name="appMapType" format="string" />
        <attr name="mapsProvider" format="string" />

        <attr name="minZoomPreference" format="float" />
        <attr name="maxZoomPreference" format="float" />

        <attr name="cameraTargetLat" format="float" />
        <attr name="cameraTargetLng" format="float" />
        <attr name="cameraZoom" format="float" />
        <attr name="cameraBearing" format="float" />
        <attr name="cameraTilt" format="float" />

        <attr name="uiZoomControls" format="boolean" />
        <attr name="uiCompass" format="boolean" />
        <attr name="uiMapToolbar" format="boolean" />
        <attr name="uiAttribution" format="boolean" />
        <attr name="uiLogo" format="boolean" />

        <attr name="uiZoomGestures" format="boolean" />
        <attr name="uiScrollGestures" format="boolean" />
        <attr name="uiRotateGestures" format="boolean" />
        <attr name="uiTiltGestures" format="boolean" />

        <attr name="zOrderOnTop" format="boolean" />
        <attr name="useViewLifecycle" format="boolean" />
        <attr name="liteMode" format="boolean" />
        <attr name="textureMode" format="boolean" />

        <attr name="map3dMode" format="boolean" />
        <attr name="enable3dLocation" format="boolean" />

        <attr name="showMyLocationMarker" format="boolean" />

    </declare-styleable>
    
</resources>
