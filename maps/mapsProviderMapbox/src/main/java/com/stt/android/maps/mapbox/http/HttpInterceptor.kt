package com.stt.android.maps.mapbox.http

import com.mapbox.common.HttpRequest
import com.mapbox.common.HttpResponse

/**
 * Interface for intercepting Mapbox HTTP requests.
 *
 * Interceptors can modify both the request and the response. An interceptor can pass the request
 * to the next interceptor in the chain or return a response directly.
 *
 * The interceptor interface is similar to OkHttp, but the functions are suspending.
 */
fun interface HttpInterceptor {

    /**
     * Called by the HTTP interceptor [chain] to let the interceptor modify the request, available
     * in [Chain.request]. Use [Chain.proceed] to pass the request to the next interceptor in the
     * chain and to get the response. The interceptor may modify the response received from
     * downstream before returning it.
     */
    suspend fun intercept(chain: Chain): HttpResponse

    /**
     * Interceptor chain that handles processing all the interceptors in order.
     */
    interface Chain {

        /**
         * The HTTP request received from the previous interceptor in the chain.
         */
        val request: HttpRequest

        /**
         * Passes a modified [request] to the next interceptor in the chain and receives the
         * response.
         */
        suspend fun proceed(request: HttpRequest): HttpResponse

        /**
         * Passes the unmodified request to the next interceptor in the chain and receives the
         * response.
         */
        suspend fun proceed() = proceed(request)
    }
}
