package com.stt.android.maps.mapbox.delegate

import android.graphics.Point
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.android.gms.maps.model.VisibleRegion
import com.mapbox.maps.MapboxMap
import com.mapbox.maps.ScreenCoordinate
import com.stt.android.maps.delegate.ProjectionDelegate
import com.stt.android.maps.mapbox.toGoogle
import com.stt.android.maps.mapbox.toMapbox
import kotlin.math.roundToInt

class MapboxProjectionDelegate(private val map: MapboxMap) : ProjectionDelegate {

    override fun fromScreenLocation(point: Point): LatLng {
        return map
            .coordinateForPixel(ScreenCoordinate(point.x.toDouble(), point.y.toDouble()))
            .toGoogle()
    }

    override fun getVisibleRegion(): VisibleRegion {
        val size = map.getSize()

        val screenBottomLeft = ScreenCoordinate(0.0, size.height.toDouble())
        val screenBottomRight = ScreenCoordinate(size.width.toDouble(), size.height.toDouble())
        val screenTopLeft = ScreenCoordinate(0.0, 0.0)
        val screenTopRight = ScreenCoordinate(size.width.toDouble(), 0.0)

        val coordinates = map.coordinatesForPixels(
            listOf(
                screenBottomLeft,
                screenBottomRight,
                screenTopLeft,
                screenTopRight
            )
        ).map {
            it.toGoogle()
        }

        val nearLeft = coordinates[0]
        val nearRight = coordinates[1]
        val farLeft = coordinates[2]
        val farRight = coordinates[3]

        val bounds = LatLngBounds.builder().apply {
            coordinates.forEach { include(it) }
        }.build()

        return VisibleRegion(nearLeft, nearRight, farLeft, farRight, bounds)
    }

    override fun toScreenLocation(location: LatLng): Point {
        val screenCoordinate = map.pixelForCoordinate(location.toMapbox())
        return Point(screenCoordinate.x.roundToInt(), screenCoordinate.y.roundToInt())
    }
}
