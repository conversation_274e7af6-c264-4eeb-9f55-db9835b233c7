package com.stt.android.divetrack.render

import android.util.Size
import com.stt.android.divetrack.DiveTrack
import com.stt.android.divetrack.render.DiveTrackRenderer.Companion.simplifyAndSmooth
import org.rajawali3d.math.vector.Vector3
import kotlin.math.atan
import kotlin.math.cos
import kotlin.math.max
import kotlin.math.sin
import kotlin.math.sqrt
import kotlin.math.tan

internal data class SceneConfig(
    val sceneMaxX: Double,
    val sceneMaxY: Double,
    val sceneMaxZ: Double,
    val cameraPosition: Vector3,
    val diveRoute: List<Vector3>,
) {
    val planeWidth = sceneMaxX * PLANE_SCALE_FACTOR
    val planeHeight = sceneMaxZ * PLANE_SCALE_FACTOR

    // Defines how far the user can zoom out from the look at point
    val maxDistance: Double get() = cameraPosition.length() * 2

    // Make sure all objects are always visible even at max distance
    val farPlane: Double
        get() = maxDistance + max(
            sceneMaxX,
            sceneMaxY
        ).coerceAtLeast(sceneMaxZ) * PLANE_SCALE_FACTOR

    companion object {
        // Make the plane bigger to allow some space around the track
        private const val PLANE_SCALE_FACTOR = 1.5F

        // We need to limit the scene size otherwise things like start & end icons can not be
        // rendered correctly
        private const val MAX_SCENE_SIZE = 5.0

        fun from(
            diveTrack: DiveTrack,
            viewSize: Size
        ): SceneConfig {
            val xRange = diveTrack.xRange.range
            val yRange = diveTrack.yRange.range
            val zRange = diveTrack.zRange.range

            val maxX: Double
            val maxY: Double
            val maxZ: Double
            if (xRange > zRange) {
                maxX = MAX_SCENE_SIZE
                maxY = yRange * maxX / xRange
                maxZ = zRange * maxX / xRange
            } else {
                maxZ = MAX_SCENE_SIZE
                maxY = yRange * maxZ / zRange
                maxX = xRange * maxZ / zRange
            }

            val diveRoute = diveTrack.simplifyAndSmooth(maxX = maxX, maxY = maxY, maxZ = maxZ)
            val (sphereCenter, sphereRadius) = calculateBoundingSphere(diveRoute)

            val cameraPosition = calculateCameraPosition(
                sphereCenter = sphereCenter,
                sphereRadius = sphereRadius,
                size = viewSize,
            )

            return SceneConfig(
                sceneMaxX = maxX,
                sceneMaxY = maxY,
                sceneMaxZ = maxZ,
                cameraPosition = cameraPosition,
                diveRoute = diveRoute,
            )
        }

        private fun calculateCameraPosition(
            sphereCenter: Vector3,
            sphereRadius: Float,
            size: Size,
        ): Vector3 {
            val limitingFOVRadians = fovLimitRadians(size)

            // Calculate camera distance to ensure the entire sphere is visible
            val cameraDistance = sphereRadius / tan(limitingFOVRadians / 2f)

            val isoAngleRadians = Math.toRadians(35.0).toFloat()
            val rotationRadians = Math.toRadians(45.0).toFloat()

            val cameraHeight = cameraDistance * sin(isoAngleRadians)
            val cameraDistanceAdjusted = cameraDistance * cos(isoAngleRadians)

            return Vector3(
                sphereCenter.x + cameraDistanceAdjusted * cos(rotationRadians),
                sphereCenter.y + cameraHeight,
                sphereCenter.z + cameraDistanceAdjusted * sin(rotationRadians)
            )
        }

        fun fovLimitRadians(size: Size): Float {
            val aspect = size.width / size.height.toFloat()
            val verticalFOVDegrees = 42.0
            val verticalFOVRadians = Math.toRadians(verticalFOVDegrees).toFloat()

            val horizontalFOVRadians = 2f * atan(tan(verticalFOVRadians / 2f) * aspect)

            // Determine the limiting field of view based on the aspect ratio
            val limitingFOVRadians = if (aspect >= 1f) verticalFOVRadians else horizontalFOVRadians
            return limitingFOVRadians
        }

        private fun calculateBoundingSphere(points: List<Vector3>): Pair<Vector3, Float> {
            if (points.isEmpty()) {
                return Pair(Vector3(0.0, 0.0, 0.0), 0f)
            }

            val bounds = calculatePathBounds(points)
            val center = bounds.first
            val size = bounds.second

            var radius = sqrt((size.x * size.x + size.y * size.y + size.z * size.z))
                .toFloat() / 2

            points.forEach { point ->
                val distance = distance(point, center)
                if (distance > radius) {
                    radius = (radius + distance) / 2
                }
            }

            return Pair(center, radius)
        }

        private fun calculatePathBounds(points: List<Vector3>): Pair<Vector3, Vector3> {
            if (points.isEmpty()) {
                return Pair(Vector3(0.0, 0.0, 0.0), Vector3(0.0, 0.0, 0.0))
            }

            var minX = points[0].x
            var maxX = points[0].x
            var minY = points[0].y
            var maxY = points[0].y
            var minZ = points[0].z
            var maxZ = points[0].z

            points.forEach { point ->
                minX = minOf(minX, point.x)
                maxX = maxOf(maxX, point.x)
                minY = minOf(minY, point.y)
                maxY = maxOf(maxY, point.y)
                minZ = minOf(minZ, point.z)
                maxZ = maxOf(maxZ, point.z)
            }

            val width = maxX - minX
            val height = maxY - minY
            val depth = maxZ - minZ
            val centerX = minX + width / 2
            val centerY = minY + height / 2
            val centerZ = minZ + depth / 2

            return Pair(Vector3(centerX, centerY, centerZ), Vector3(width, height, depth))
        }

        private fun distance(point1: Vector3, point2: Vector3): Float {
            return sqrt(
                ((point1.x - point2.x) * (point1.x - point2.x) +
                    (point1.y - point2.y) * (point1.y - point2.y) +
                    (point1.z - point2.z) * (point1.z - point2.z))
            ).toFloat()
        }
    }
}

internal val ClosedRange<Double>.range: Double get() = endInclusive - start

internal val ClosedRange<Double>.center: Double get() = (start + endInclusive) / 2.0
