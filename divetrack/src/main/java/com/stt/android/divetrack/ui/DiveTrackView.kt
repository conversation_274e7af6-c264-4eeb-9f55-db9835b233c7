package com.stt.android.divetrack.ui

import android.content.Context
import android.view.TextureView
import android.view.ViewGroup
import android.widget.FrameLayout
import com.stt.android.divetrack.DiveTrack
import com.stt.android.divetrack.DiveTrackSettings
import com.stt.android.divetrack.render.DiveTrackRenderer
import org.rajawali3d.renderer.ISurfaceRenderer
import org.rajawali3d.view.ISurface
import org.rajawali3d.view.TextureView as RajawaliTextureView

fun FrameLayout.addDiveTrackView(
    diveTrack: DiveTrack,
    settings: DiveTrackSettings,
    onCameraMoved: (() -> Unit)?,
): TextureView = DiveTrackView.newInstance(
    context = context,
    diveTrack = diveTrack,
    settings = settings,
    onCameraMoved = onCameraMoved,
).apply {
    layoutParams = ViewGroup.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT,
    )
    <EMAIL>(this)
}

internal class DiveTrackView private constructor(context: Context) : RajawaliTextureView(context) {
    lateinit var diveTrackRenderer: DiveTrackRenderer
        private set

    init {
        setAntiAliasingMode(ISurface.ANTI_ALIASING_CONFIG.MULTISAMPLING)
    }

    override fun setSurfaceRenderer(renderer: ISurfaceRenderer) {
        diveTrackRenderer = renderer as DiveTrackRenderer
        super.setSurfaceRenderer(renderer)
    }

    companion object {
        fun newInstance(
            context: Context,
            diveTrack: DiveTrack,
            settings: DiveTrackSettings,
            onCameraMoved: (() -> Unit)?,
        ): DiveTrackView = DiveTrackView(context).apply {
            val renderer = DiveTrackRenderer(
                surfaceView = this,
                diveTrack = diveTrack,
                settings = settings,
                onCameraMoved = onCameraMoved,
            )
            setSurfaceRenderer(renderer)
        }
    }
}
