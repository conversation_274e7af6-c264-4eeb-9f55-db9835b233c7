package com.stt.android.divetrack.render

import org.rajawali3d.Object3D
import org.rajawali3d.materials.Material
import org.rajawali3d.math.vector.Vector3

internal class WallMesh(topPoints: List<Vector3>) : Object3D() {
    init {
        isTransparent = true
        isDoubleSided = true
        material = Material().apply {
            color = Environment.COLOR_WALL_MESH.toInt()
            enableLighting(false)
        }
        createWallNode(topPoints)
    }

    private fun createWallNode(routePoints: List<Vector3>) {
        val vertices = arrayListOf<Vector3>()
        val indices = arrayListOf<Int>()

        // Iterate over the route points to calculate vertices
        routePoints.forEachIndexed { index, point ->
            val adjustedPoint = Vector3(point.x, 0.0, point.z)
            vertices.add(point) // Original route point
            vertices.add(adjustedPoint) // Adjusted point either at water level or below

            // Skip the first point since it has no previous point to form triangles with
            if (index > 0) {
                val baseIndex = index * 2
                // Create two triangles for each segment
                indices.addAll(
                    listOf(
                        baseIndex - 2,
                        baseIndex,
                        baseIndex - 1,
                        baseIndex - 1,
                        baseIndex,
                        baseIndex + 1
                    )
                )
            }
        }

        setData(vertices.toFloatArray(), null, null, null, indices.toIntArray(), true)
    }

    private companion object {
        fun List<Vector3>.toFloatArray(): FloatArray {
            val floatArray = FloatArray(this.size * 3)

            this.forEachIndexed { index, vector3 ->
                floatArray[index * 3] = vector3.x.toFloat()
                floatArray[index * 3 + 1] = vector3.y.toFloat()
                floatArray[index * 3 + 2] = vector3.z.toFloat()
            }

            return floatArray
        }
    }
}
