package com.stt.android.domain.session

data class EmailOrUsernameStatus(
    val emailStatus: EmailStatus? = null,
    val usernameStatus: UsernameStatus? = null
)

data class EmailStatus(
    val existsInMovescount: Boolean,
    val activeBrand: String?,
    val existsInAsko: Boolean // Suunto, SportsTracker or Atomic account
)

data class FacebookSignInParam(
    val token: String,
    val termscode: String,
)

enum class PhoneNumberStatus {
    MISSING,
    UNVERIFIED,
    VERIFIED
}

enum class LoginMethod(
    val analyticsName: String
) {
    EMAIL("Email"),
    PHONE("PhoneNumber"),
    AUTOMATIC("Automatic"),
    FACEBOOK("Facebook"),
    APPLE("Apple"),
    GOOGLE("Google"),
}

enum class SessionInitType {
    LOGIN,
    SIGNUP
}

enum class MobileApp(val value: String, val displayName: String) {
    SPORTS_TRACKER("sportstracker", "Sports Tracker"),
    SUUNTO_APP("suuntoapp", "Suunto app"),
    ATOMIC_APP("atomicapp", "Atomic app"),
    SUUNTO_LINK("suuntolink", "Suuntolink")
}

enum class UsernameStatus {
    USER_EXISTS,
    USER_DOES_NOT_EXIST,
}
