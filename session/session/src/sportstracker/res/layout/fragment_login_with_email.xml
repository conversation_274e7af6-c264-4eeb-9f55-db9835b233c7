<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="onLoginClicked"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.stt.android.session.signin.SignInOnboardingViewModel" />

        <variable
            name="onActionDone"
            type="com.stt.android.utils.OnActionDone" />

        <import type="android.view.View" />

        <import type="android.view.inputmethod.EditorInfo" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:theme="@style/WhiteTheme">

        <include
            android:id="@+id/login_email_appbar"
            layout="@layout/view_signup_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/login_email_appbar"
            app:layout_constraintWidth_max="@dimen/content_max_width">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:theme="@style/WhiteTheme">

                <TextView
                    android:id="@+id/login_email_title_text"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_large"
                    android:layout_marginTop="@dimen/size_spacing_xlarge"
                    android:layout_marginEnd="@dimen/size_spacing_large"
                    android:gravity="center"
                    android:text="@{context.getString(viewModel.existingAccountInfo.titleString)}"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="@string/log_in_with_your_st_account" />

                <ImageView
                    android:id="@+id/login_email_existing_account_logo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginTop="@dimen/size_spacing_medium"
                    android:contentDescription="@null"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/login_email_title_text"
                    app:srcCompat="@{viewModel.existingAccountInfo.logo}"
                    app:visible="@{viewModel.existingAccountInfo.visible}"
                    tools:src="@drawable/movescount_app_icon"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/login_email_existing_description_text"
                    style="@style/Body.Medium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:text="@{viewModel.existingAccountInfo.descriptionString != null ? context.getString(viewModel.existingAccountInfo.descriptionString) : @string/empty}"
                    app:layout_constraintBottom_toBottomOf="@id/login_email_existing_account_logo"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/login_email_existing_account_logo"
                    app:layout_constraintTop_toTopOf="@id/login_email_existing_account_logo"
                    app:visible="@{viewModel.existingAccountInfo.visible}"
                    tools:text="@string/log_in_st_account_description"
                    tools:visibility="visible" />

                <include
                    android:id="@+id/login_email_email_input"
                    layout="@layout/view_email_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/size_spacing_medium"
                    app:layout_constraintTop_toBottomOf="@id/login_email_existing_description_text"
                    app:readOnly="@{true}"
                    app:viewEmailInputHint="@{viewModel.continueWithEmailHint}"
                    app:viewModel="@{viewModel}" />

                <include
                    android:id="@+id/login_email_password_input"
                    layout="@layout/view_password_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:imeOptions="@{EditorInfo.IME_ACTION_DONE}"
                    app:inputError="@{viewModel.loginPasswordInputError}"
                    app:layout_constraintTop_toBottomOf="@id/login_email_email_input"
                    app:onActionDone="@{onActionDone}"
                    app:viewModel="@{viewModel}" />

                <Button
                    android:id="@+id/forgot_password_button"
                    style="@style/ButtonFlat.Medium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:onClick="@{() -> viewModel.showForgotPasswordPage(context)}"
                    android:text="@string/forgot_your_password"
                    android:textAllCaps="false"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/login_email_password_input" />

                <Button
                    android:id="@+id/login_email_login_email_button"
                    style="@style/Button.RoundedSecondaryAccent"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/height_button"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginTop="@dimen/size_spacing_medium"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:enabled="@{viewModel.canLogin}"
                    android:onClick="@{onLoginClicked}"
                    android:text="@string/login"
                    android:transitionName="@string/transition_name_main_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/forgot_password_button" />

                <ProgressBar
                    android:id="@+id/login_email_login_progress_bar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/size_spacing_medium"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/login_email_login_email_button"
                    app:visible="@{viewModel.loginInProgress}" />

                <Space
                    android:id="@+id/stretch_area"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/size_spacing_large"
                    app:layout_constraintBottom_toTopOf="@id/login_email_contact_support_button"
                    app:layout_constraintTop_toBottomOf="@id/login_email_login_progress_bar"
                    app:layout_goneMarginTop="@dimen/size_spacing_large" />

                <include
                    android:id="@+id/login_email_contact_support_button"
                    layout="@layout/view_contact_support"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_xlarge"
                    android:layout_marginEnd="@dimen/size_spacing_xlarge"
                    android:layout_marginBottom="@dimen/size_spacing_large"
                    app:layout_constraintBottom_toTopOf="@id/login_email_terms_and_conditions"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:viewModel="@{viewModel}" />

                <include
                    android:id="@+id/login_email_terms_and_conditions"
                    layout="@layout/view_terms_and_conditions"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/size_spacing_large"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
