<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="onToggleButtonChecked"
            type="com.google.android.material.button.MaterialButtonToggleGroup.OnButtonCheckedListener" />

        <variable
            name="onSignInWithAppleClicked"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.stt.android.session.signin.SignInOnboardingViewModel" />

        <variable
            name="onActionDone"
            type="com.stt.android.utils.OnActionDone" />

        <import type="android.view.View" />

        <import type="com.stt.android.domain.session.LoginMethod" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:theme="@style/WhiteTheme">

        <include
            android:id="@+id/continue_with_email_or_phone_appbar"
            layout="@layout/view_signup_toolbar"
            app:layout_constraintTop_toTopOf="parent" />

        <ScrollView
            android:id="@+id/phone_ask_for_email_scrollview"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/continue_with_email_or_phone_appbar"
            app:layout_constraintWidth_max="@dimen/content_max_width">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:theme="@style/WhiteTheme">

                <TextView
                    android:id="@+id/continue_with_email_or_phone_welcome_text"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_large"
                    android:layout_marginTop="@dimen/size_spacing_xlarge"
                    android:layout_marginEnd="@dimen/size_spacing_large"
                    android:gravity="center"
                    android:text="@string/signup_or_login_welcome_to_app_title"
                    app:layout_constraintTop_toTopOf="parent" />

                <include
                    android:id="@+id/continue_with_email_or_phone_toggle"
                    layout="@layout/view_phone_number_email_toggle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginTop="@dimen/size_spacing_large"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:visibility="@{viewModel.config.supportsEmailAndPhoneLogin ? View.VISIBLE : View.GONE}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/continue_with_email_or_phone_welcome_text"
                    app:onToggleButtonChecked="@{onToggleButtonChecked}"
                    app:viewModel="@{viewModel}" />

                <include
                    android:id="@+id/view_phone_number"
                    layout="@layout/view_phone_number"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginTop="@dimen/size_spacing_xlarge"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:visibility="@{viewModel.loginMethod == LoginMethod.PHONE ? View.VISIBLE : View.INVISIBLE}"
                    app:inputError="@{viewModel.phoneNumberInputError}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/continue_with_email_or_phone_toggle"
                    app:nationalNumber="@{viewModel.nationalNumber}"
                    app:onActionDone="@{onActionDone}"
                    app:onPhoneRegionClicked="@{()->viewModel.onPhoneRegionClicked()}"
                    app:phoneRegion="@{viewModel.phoneRegion}"
                    app:phoneRegionEnabled="@{viewModel.phoneRegionSelectionEnabled}"
                    app:readOnly="@{false}" />

                <include
                    android:id="@+id/continue_with_email_or_phone_email_input"
                    layout="@layout/view_email_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="@{viewModel.loginMethod == LoginMethod.PHONE ? View.INVISIBLE : View.VISIBLE}"
                    app:inputError="@{viewModel.emailInputError}"
                    app:layout_constraintTop_toTopOf="@id/view_phone_number"
                    app:onActionDone="@{onActionDone}"
                    app:readOnly="@{false}"
                    app:viewEmailInputHint="@{viewModel.continueWithEmailHint}"
                    app:viewModel="@{viewModel}" />

                <CheckBox
                    android:id="@+id/continue_with_pre_approve_terms_and_conditions_checkbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_xlarge"
                    android:checked="@{viewModel.preApprovedTermsAndConditions}"
                    android:gravity="center"
                    android:minHeight="@dimen/height_button"
                    android:onCheckedChanged="@{(view, checked) -> viewModel.preApprovedTermsAndConditionsChanged(checked)}"
                    android:padding="@dimen/size_spacing_small"
                    android:paddingTop="@dimen/size_spacing_medium"
                    android:visibility="@{viewModel.config.forcePreApproveOfTermsAndConditions ? View.VISIBLE : View.GONE}"
                    app:layout_constraintBottom_toBottomOf="@+id/continue_with_pre_approve_terms_and_conditions_text"
                    app:layout_constraintEnd_toStartOf="@+id/continue_with_pre_approve_terms_and_conditions_text"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/continue_with_pre_approve_terms_and_conditions_text"
                    tools:checked="true" />

                <TextView
                    android:id="@+id/continue_with_pre_approve_terms_and_conditions_text"
                    style="@style/Body.Medium.URL"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/size_spacing_small"
                    android:layout_marginEnd="@dimen/size_spacing_xlarge"
                    android:gravity="center_vertical"
                    android:minHeight="@dimen/height_button"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:visibility="@{viewModel.config.forcePreApproveOfTermsAndConditions ? View.VISIBLE : View.GONE}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/continue_with_pre_approve_terms_and_conditions_checkbox"
                    app:layout_constraintTop_toBottomOf="@+id/view_phone_number"
                    app:linkHtmlToCustomTab="@{@string/tos_and_privacy_links_accept}"
                    tools:text="Terms of Service and Privacy Policy" />

                <Button
                    android:id="@+id/continue_with_email_continue_button"
                    style="@style/Button.RoundedSecondaryAccent"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/height_button"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginTop="@dimen/size_spacing_large"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:enabled="@{viewModel.canContinue}"
                    android:onClick="@{() -> viewModel.continueWithEmailOrPhoneNumber()}"
                    android:text="@string/continue_str"
                    android:transitionName="@string/transition_name_main_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/continue_with_pre_approve_terms_and_conditions_checkbox" />

                <Space
                    android:id="@+id/continue_with_email_stretch_area"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/size_spacing_large"
                    app:layout_constraintBottom_toTopOf="@+id/continue_with_email_apple_button"
                    app:layout_constraintTop_toBottomOf="@id/continue_with_email_continue_button"
                    app:layout_goneMarginTop="@dimen/size_spacing_large" />

                <TextView
                    android:id="@+id/continue_with_email_apple_button"
                    style="@style/Body.Medium"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/size_spacing_medium"
                    android:gravity="center"
                    android:minHeight="@dimen/smallButton_height"
                    android:textColorLink="?android:textColorPrimary"
                    app:layout_constraintBottom_toTopOf="@id/continue_with_email_or_phone_terms_and_conditions"
                    app:linkClickListener="@{onSignInWithAppleClicked}"
                    app:linkText="@{@string/sign_in_with_apple}"
                    app:visible="@{viewModel.config.supportsAppleLogin}"
                    tools:text="@string/sign_in_with_apple" />

                <include
                    android:id="@+id/continue_with_email_or_phone_terms_and_conditions"
                    layout="@layout/view_terms_and_conditions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/size_spacing_large"
                    android:visibility="@{!viewModel.config.forcePreApproveOfTermsAndConditions ? View.VISIBLE : View.GONE}"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <ProgressBar
                    android:id="@+id/continue_with_email_or_phone_progress_bar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/size_spacing_medium"
                    android:visibility="@{viewModel.fetchEmailOrPhoneNumberStatusInProgress ? View.VISIBLE : View.GONE}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/continue_with_email_continue_button" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
