package com.stt.android.session.di

import android.app.Application
import android.content.Context
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.auth.api.phone.SmsRetrieverClient
import com.google.i18n.phonenumbers.NumberParseException
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber
import com.stt.android.controllers.UserSettingsController
import com.stt.android.session.PhoneNumberVerificationForExistingUserHook
import com.stt.android.session.configuration.SignInConfiguration
import com.stt.android.session.phonenumberverification.PhoneNumberCodeVerificationAnalyticsTracker
import com.stt.android.session.phonenumberverification.PhoneNumberCodeVerificationAnalyticsTrackerImpl
import com.stt.android.session.phonenumberverification.SmsBroadcastReceiver
import com.stt.android.session.phonenumberverification.existinguser.PhoneNumberVerificationForExistingUserHookImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.components.SingletonComponent
import timber.log.Timber

@Module
@InstallIn(SingletonComponent::class)
abstract class PhoneNumberVerificationAppModule {
    @Binds
    abstract fun bindPhoneNumberVerificationForExistingUserHook(
        phoneNumberVerificationForExistingUserHookImpl: PhoneNumberVerificationForExistingUserHookImpl
    ): PhoneNumberVerificationForExistingUserHook
}

@Module
@InstallIn(ViewModelComponent::class)
abstract class PhoneNumberVerificationViewModelModule {

    @Binds
    abstract fun bindTracker(
        tracker: PhoneNumberCodeVerificationAnalyticsTrackerImpl
    ): PhoneNumberCodeVerificationAnalyticsTracker

    companion object {

        @Provides
        fun providePhoneNumber(
            userSettingsController: UserSettingsController,
            application: Application,
            signInConfiguration: SignInConfiguration
        ): PhoneNumber {
            return try {
                PhoneNumberUtil.getInstance()
                    .parse(userSettingsController.settings.phoneNumber, null)
            } catch (e: NumberParseException) {
                Timber.w(e, "Error in parsing PhoneNumber %s", e.message)
                val phoneNumber = PhoneNumber()
                val countryCode: Int = signInConfiguration.defaultPhoneNumberCountryCode
                    ?: PhoneNumberUtil.getInstance()
                        .getCountryCodeForRegion(
                            application.resources.configuration.locale.country
                        )
                phoneNumber.countryCode = countryCode
                phoneNumber
            }
        }
    }
}

@Module
@InstallIn(FragmentComponent::class)
abstract class PhoneNumberVerificationFragmentModule {

    companion object {
        @Provides
        fun provideSmsReSmsRetrieverClient(context: Context): SmsRetrieverClient =
            SmsRetriever.getClient(context)

        @Provides
        fun provideSmsBroadcastReceiver(): SmsBroadcastReceiver = SmsBroadcastReceiver()
    }
}
