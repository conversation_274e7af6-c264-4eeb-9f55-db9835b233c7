<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/signup_appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="@dimen/elevation_toolbar"
        android:transitionName="@string/transition_name_app_bar"
        app:layout_constraintTop_toTopOf="parent"
        tools:theme="@style/WhiteTheme">

        <com.stt.android.ui.components.CenteredToolbar
            android:id="@+id/signup_toolbar"
            style="@style/Toolbar.Native"
            app:logo="@drawable/app_logo_small"
            app:theme="@style/Toolbar.Native" />
    </com.google.android.material.appbar.AppBarLayout>
</layout>
