package com.stt.android.session.datasource

import androidx.core.util.PatternsCompat
import com.stt.android.data.session.phonenumberverification.PhoneNumberVerificationRepository
import com.stt.android.domain.session.phonenumberverification.PhoneNumberVerificationParameter
import com.stt.android.newemail.UserInfoFromChina
import com.stt.android.home.settings.PhoneNumberUtil.PHONE_REGION_CHINA
import com.stt.android.resetpassword.ResetPasswordDataSource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

class UserCenterChinaRepository @Inject constructor(
    private val userCenterChinaDataSource: UserCenterChinaDataSource,
    private val phoneNumberVerificationRepository: PhoneNumberVerificationRepository,
    private val resetPasswordDataSource: ResetPasswordDataSource
) {
    suspend fun sendVerificationCode(phoneNumber: String): Boolean = withContext(Dispatchers.IO) {
        withContext(Dispatchers.IO) {
            val fullPhoneNumber = "$PHONE_REGION_CHINA$phoneNumber"
            phoneNumberVerificationRepository.requestSMS(fullPhoneNumber)
        }
    }

    suspend fun checkVerificationCode(phoneNumber: String, pin: String): String =
        withContext(Dispatchers.IO) {
            val fullPhoneNumber = "$PHONE_REGION_CHINA$phoneNumber"
            phoneNumberVerificationRepository.verifyPhoneNumber(PhoneNumberVerificationParameter(fullPhoneNumber, pin))
        }

    suspend fun signInOrSignUp(
        phoneNumberVerificationToken: String,
        phoneNumber: String
    ): UserInfoFromChina = withContext(
        Dispatchers.IO
    ) {
        val fullPhoneNumber = "$PHONE_REGION_CHINA$phoneNumber"
        userCenterChinaDataSource.signInOrSignUp(phoneNumberVerificationToken, fullPhoneNumber)
    }

    suspend fun loginChina(id: String, password: String): UserInfoFromChina =
        withContext(Dispatchers.IO) {
            userCenterChinaDataSource.loginChina(
                "${
                    if (isEmailAddressValid(id)) {
                        ""
                    } else {
                        PHONE_REGION_CHINA
                    }
                }$id",
                password
            )
        }

    suspend fun sendVerificationCodeForResetPassword(phoneNumber: String) =
        withContext(Dispatchers.IO) {
            val fullPhoneNumber = "$PHONE_REGION_CHINA$phoneNumber"
            resetPasswordDataSource.sendVerificationCodeForResetPassword(fullPhoneNumber)
        }

    suspend fun checkVerificationCodeForResetPassword(
        phoneNumber: String,
        pin: String
    ): String = withContext(Dispatchers.IO) {
        val fullPhoneNumber = "$PHONE_REGION_CHINA$phoneNumber"
        resetPasswordDataSource.checkVerificationCodeForResetPassword(fullPhoneNumber, pin)
    }

    suspend fun sendPasswordResetEmail(brand: String, username: String) = withContext(Dispatchers.IO) {
        userCenterChinaDataSource.sendPasswordResetEmail(brand, username)
    }
    private fun isEmailAddressValid(email: String) = PatternsCompat.EMAIL_ADDRESS.matcher(email).matches()
}
