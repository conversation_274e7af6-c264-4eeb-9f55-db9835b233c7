package com.stt.android.remote.session

import com.stt.android.exceptions.remote.ClientError
import com.stt.android.exceptions.remote.STTError
import com.stt.android.remote.otp.GenerateOTPUseCase
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.mockito.Mock
import org.mockito.junit.MockitoJUnit
import org.mockito.junit.MockitoRule
import org.mockito.kotlin.any
import org.mockito.kotlin.given

class AuthRemoteApiTest {
    @Rule
    @JvmField
    val mockitoRule: MockitoRule = MockitoJUnit.rule()

    @Mock
    private lateinit var authRestApi: AuthRestApi

    @Mock
    private lateinit var generateOTPUseCase: GenerateOTPUseCase

    private lateinit var authRemoteApi: AuthRemoteApi

    @Before
    fun setup() {
        authRemoteApi = AuthRemoteApi(authRestApi, generateOTPUseCase)
    }

    @Test(expected = STTError.InvalidPinCode::class)
    fun `verifyPin should throw InvalidPinCode exception when 403 Forbidden is returned`() = runTest {
        val pin = "pin"
        val phoneNumber = "123"
        val totpToken = "topt"
        given(generateOTPUseCase.generateTOTP(any())).willReturn(totpToken)
        given(authRestApi.verifyPin(totpToken, phoneNumber, pin)).willThrow(ClientError.Forbidden("pin code is invalid"))

        authRemoteApi.verifyPin(phoneNumber, pin)
    }
}
