package com.stt.android.remote.session

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import java.time.LocalDate

internal const val PATH_LOGIN_2 = "login2"
internal const val PATH_LOGIN = "login"
internal const val PATH_LOGIN_FACEBOOK = "login/facebook"
internal const val PARAM_LOGIN_USERNAME_OR_EMAIL = "l"
internal const val PARAM_LOGIN_PASSWORD = "p"
internal const val PARAM_SESSION_TOTP = "totp"
internal const val PARAM_LOGIN_ACCESS_TOKEN = "accessToken"
internal const val HEADER_PHONE_NUMBER_VERIFICATION = "X-Phone-Number-Verification"

@JsonClass(generateAdapter = true)
data class RemoteUserSession(
    @Json(name = "sessionkey")
    val sessionKey: String?,
    @Json(name = "watchUserKey")
    val watchKey: String?,
    @Json(name = "facebookConnected")
    val facebookConnected: Boolean,
    @Json(name = "emailVerified")
    val emailVerified: Boolean,
)

@JsonClass(generateAdapter = true)
data class RemoteNewUser(
    @Json(name = "birthDate")
    val birthday: Long?,
    @Json(name = "email")
    val email: String?,
    @Json(name = "realName")
    val realName: String?,
    @Json(name = "username")
    val username: String?
)

@JsonClass(generateAdapter = true)
data class RemoteEmailStatus(
    @Json(name = "existsInMovescount")
    val existsInMovescount: Boolean,
    @Json(name = "activeBrand")
    val activeBrand: String?,
    @Json(name = "existsInSportsTracker")
    val existsInSportsTracker: Boolean
)

@JsonClass(generateAdapter = false)
enum class RemoteUsernameStatus {
    USER_EXISTS,
    USER_DOES_NOT_EXIST,
}

data class RemoteNewUserCredentials(
    val fullName: String?,
    val password: String?,
    val email: String?,
    val sex: RemoteSex?,
    val birthday: LocalDate?,
    val facebookAccessToken: String?,
    val phoneNumber: String?
)

@JsonClass(generateAdapter = false)
enum class RemotePhoneNumberStatus {
    MISSING,
    UNVERIFIED,
    VERIFIED
}

/**
 * Note: we are very modern with this approach
 */
@JsonClass(generateAdapter = false)
enum class RemoteSex {
    MALE,
    FEMALE
}

data class SignatureParams(
    val signingKeyPart1: String,
    val signingKeyPart2: String,
    val signingKeyPart3: String,
    val packageName: String
)

data class SignatureData(
    val timestamp: Long,
    val salt: String,
    val signature: String
)
