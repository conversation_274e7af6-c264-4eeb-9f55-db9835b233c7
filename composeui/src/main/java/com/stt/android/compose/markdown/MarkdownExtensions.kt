package com.stt.android.compose.markdown

import org.commonmark.node.Document
import org.commonmark.node.Image
import org.commonmark.node.Node
import org.commonmark.node.Paragraph
import org.commonmark.node.Text

val Node.children: Iterator<Node>
    get() = iterator {
        var child: Node? = firstChild
        while (child != null) {
            yield(child)
            child = child.next
        }
    }

val Node.isTopLevel: <PERSON><PERSON><PERSON>
    get() = parent == null || parent is Document

fun Document.appendPlainText(text: String) = apply {
    appendChild(
        Paragraph().apply {
            appendChild(Text(text))
        }
    )
}

/**
 * Calculate an extremely rough estimate on how many lines of text this Markdown text might take
 * when rendered on a phone display. Definitely not to be used for layout purposes, but only to
 * decide of the whole Markdown document should be shown at once or should it be shown inside of
 * a collapsible container.
 */
fun Node.estimatedNumberOfTextRows(): Int {
    var count = 0
    if (this is Text) {
        count += literal.estimatedNumberOfRows()
    } else if (this is Image) {
        count += 5 // Assume images are large and take as much as a couple of lines
    }

    return count + children.asSequence().sumOf { it.estimatedNumberOfTextRows() }
}

private fun String.estimatedNumberOfRows(): Int = split('\n')
    .sumOf { line ->
        // Assume lines longer than 55 characters will wrap onto multiple lines
        (line.length.plus(54) / 55).coerceAtLeast(1)
    }
