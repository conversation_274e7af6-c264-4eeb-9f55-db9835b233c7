package com.stt.android.compose.widgets

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.bottomSheetShape
import com.stt.android.compose.theme.isMaterial3
import com.stt.android.compose.theme.spacing
import com.stt.android.core.R
import androidx.compose.material.MaterialTheme as M2Theme
import androidx.compose.material.Surface as M2Surface

@Composable
fun DraggableBottomSheetHandle(
    modifier: Modifier = Modifier,
    topPadding: Dp = if (isMaterial3()) {
        MaterialTheme.spacing.smaller
    } else {
        M2Theme.spacing.smaller
    },
    bottomPadding: Dp = if (isMaterial3()) {
        MaterialTheme.spacing.smaller
    } else {
        M2Theme.spacing.smaller
    },
    tint: Color = Color.Unspecified,
) {
    // This is never really scrollable, but use verticalScroll modifier
    // so that ViewInteropNestedScrollConnection forwards scroll events
    // to the bottom sheet dialog fragment allowing it to be closed by dragging.
    BottomSheetHandle(
        modifier = modifier.verticalScroll(rememberScrollState()),
        topPadding = topPadding,
        bottomPadding = bottomPadding,
        tint = tint,
    )
}

@Composable
fun BottomSheetHandle(
    modifier: Modifier = Modifier,
    topPadding: Dp = if (isMaterial3()) {
        MaterialTheme.spacing.smaller
    } else {
        M2Theme.spacing.smaller
    },
    bottomPadding: Dp = if (isMaterial3()) {
        MaterialTheme.spacing.smaller
    } else {
        M2Theme.spacing.smaller
    },
    tint: Color = Color.Unspecified,
) {
    Image(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                top = topPadding,
                bottom = bottomPadding
            ),
        painter = painterResource(id = R.drawable.ic_info_handle),
        contentDescription = null,
        colorFilter = tint.takeIf { it != Color.Unspecified }?.let { ColorFilter.tint(it) },
    )
}

@Preview(widthDp = 320, heightDp = 400)
@Composable
private fun M2BottomSheetHandlePreview() {
    AppTheme {
        M2Surface(shape = M2Theme.shapes.bottomSheetShape) {
            Column {
                BottomSheetHandle()
            }
        }
    }
}

@Preview(widthDp = 320, heightDp = 400)
@Composable
private fun BottomSheetHandlePreview() {
    M3AppTheme {
        Surface(shape = MaterialTheme.shapes.bottomSheetShape) {
            Column {
                BottomSheetHandle()
            }
        }
    }
}
