package com.stt.android.compose.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.ui.R
import kotlin.math.max

data class GenericComparisonSegmentInfo(
    val title: String,
    val subTitle: String,
    val summary: String?,
    val color: Color,
)

@Composable
fun GenericComparisonSelection(
    primaryInfo: GenericComparisonSegmentInfo,
    secondaryInfo: GenericComparisonSegmentInfo,
    onPrimaryClick: () -> Unit,
    onSecondaryClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .border(
                1.dp,
                color = MaterialTheme.colorScheme.dividerColor,
                shape = RoundedCornerShape(16.dp),
            )
            .fillMaxWidth(),
    ) {
        var minHeightPx by remember(primaryInfo, secondaryInfo) { mutableIntStateOf(0) }
        val minHeight = with(LocalDensity.current) { minHeightPx.toDp() }
        GenericComparisonSegment(
            modifier = Modifier
                .onSizeChanged { minHeightPx = max(minHeightPx, it.height) }
                .defaultMinSize(minHeight = minHeight)
                .weight(1f),
            info = primaryInfo,
            onClick = onPrimaryClick,
        )
        VerticalDivider(
            modifier = Modifier.height(minHeight),
            color = MaterialTheme.colorScheme.dividerColor,
        )
        GenericComparisonSegment(
            modifier = Modifier
                .onSizeChanged { minHeightPx = max(minHeightPx, it.height) }
                .defaultMinSize(minHeight = minHeight)
                .weight(1f),
            info = secondaryInfo,
            onClick = onSecondaryClick,
            alignStart = false,
        )
    }
}

@Composable
private fun GenericComparisonSegment(
    info: GenericComparisonSegmentInfo,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    alignStart: Boolean = true,
) {
    val layoutDirection = if (alignStart) LayoutDirection.Ltr else LayoutDirection.Rtl
    CompositionLocalProvider(LocalLayoutDirection provides layoutDirection) {
        Row(
            modifier = modifier
                .clip(RoundedCornerShape(topStart = 16.dp, bottomStart = 16.dp))
                .clickable(onClick = onClick)
                .padding(all = MaterialTheme.spacing.medium),
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.CenterVertically)
                    .padding(end = MaterialTheme.spacing.small)
                    .clip(CircleShape)
                    .background(info.color)
                    .size(8.dp),
            )
            CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
                Column(
                    modifier = Modifier.weight(1f),
                    horizontalAlignment = if (alignStart) Alignment.Start else Alignment.End,
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
                ) {
                    Text(
                        text = info.subTitle,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = if (alignStart) TextAlign.Start else TextAlign.End,
                    )
                    Text(
                        text = info.title,
                        style = MaterialTheme.typography.bodyLargeBold,
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = if (alignStart) TextAlign.Start else TextAlign.End,
                    )
                    info.summary?.let {
                        Text(
                            text = it,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.secondary,
                            textAlign = if (alignStart) TextAlign.Start else TextAlign.End,
                        )
                    }
                }
            }
            Icon(
                painter = painterResource(R.drawable.ic_arrow_down_18),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
            )
        }
    }
}
