package com.stt.android.compose.layout

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.ui.R

/**
 * A layout composable that places its children in a vertical sequence. As a difference to
 * [Column], limits the maximum width of the children to [R.dimen.content_max_width], placed
 * children in horizontally on the center of the parent.
 * Typically used as the root composable of the content under [androidx.compose.material.Scaffold].
 *
 * Example usage:
 * ```
 * Scaffold(
 *     topBar = ..
 * ) { internalPadding ->
 *     ContentCenteringColumn(
 *         modifier = Modifier
 *             .padding(internalPadding)
 *             .fillMaxSize()
 *     ) {
 *         Text('Hello')
 *         Text('World')
 *     }
 * }
 *
 * @see Column
 */
@Composable
inline fun ContentCenteringColumn(
    modifier: Modifier = Modifier,
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    horizontalAlignment: Alignment.Horizontal = Alignment.Start,
    content: @Composable ColumnScope.() -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .narrowContent(),
        verticalArrangement = verticalArrangement,
        horizontalAlignment = horizontalAlignment,
        content = content,
    )
}
