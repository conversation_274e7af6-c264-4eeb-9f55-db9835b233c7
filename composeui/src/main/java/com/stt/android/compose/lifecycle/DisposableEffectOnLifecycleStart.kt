package com.stt.android.compose.lifecycle

import android.app.Activity
import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner

@Composable
fun DisposableEffectOnLifecycleStart(
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    context: Context = LocalContext.current,
    ignoreChangingConfigurations: Boolean = false,
    effect: () -> Unit
) {
    var isChangingConfigurations by rememberSaveable { mutableStateOf(false) }

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_DESTROY -> {
                    if (context is Activity && context.isChangingConfigurations) {
                        isChangingConfigurations = true
                    }
                }
                Lifecycle.Event.ON_START -> {
                    if (!isChangingConfigurations || !ignoreChangingConfigurations) {
                        effect()
                    }
                    isChangingConfigurations = false
                }
                else -> {
                    /* no-op */
                }
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}
