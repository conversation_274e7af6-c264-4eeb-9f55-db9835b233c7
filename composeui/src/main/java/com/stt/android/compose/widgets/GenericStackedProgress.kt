package com.stt.android.compose.widgets

import android.annotation.SuppressLint
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.addOutline
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.spacing
import kotlin.math.roundToInt

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun GenericStackedProgress(
    value: Float,
    items: List<StackedProgressItem>,
    modifier: Modifier = Modifier,
    showTick: Boolean = true,
    trimTick: Boolean = false,
    showMarkers: Boolean = true,
) {
    val tickCenterRatio by remember(value, items) {
        mutableFloatStateOf((value - items.first().min) / (items.last().max - items.first().min))
    }
    val tickColor by remember(value, items) {
        mutableStateOf(items.firstOrNull { value in it.min..it.max }?.color ?: Color.Black)
    }
    val itemRatioMap by remember(items) {
        mutableStateOf(buildMap {
            val minValue = items.first().min
            val length = items.last().max - items.first().min
            items.forEachIndexed { index, item ->
                val startRatio = (item.min - minValue) / length
                val widthRatio = (item.max - item.min) / length
                put(index, Pair(startRatio, widthRatio))
            }
        })
    }

    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
    ) {
        val barHeight = 8.dp
        val tickWidth = 3.dp
        val tickHeight = if (showTick || !trimTick) 18.dp else barHeight
        val tickGap = 2.dp
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(tickHeight),
        ) {
            val itemY = (tickHeight - barHeight).toPx() / 2f
            val itemHeight = barHeight.toPx()
            val saveCount = drawContext.canvas.nativeCanvas.saveLayer(null, null)
            translate(top = itemY) {
                clipPath(path = Path().apply {
                    addOutline(
                        CircleShape.createOutline(
                            size.copy(height = itemHeight),
                            layoutDirection,
                            this@Canvas,
                        )
                    )
                }) {
                    items.forEachIndexed { index, item ->
                        val (startRatio, widthRatio) = itemRatioMap[index] ?: return@forEachIndexed
                        drawRoundRect(
                            item.color,
                            topLeft = Offset(x = startRatio * size.width, y = 0f),
                            size = Size(widthRatio * size.width, itemHeight),
                        )
                    }
                }
            }
            val tickCenter = tickCenterRatio * size.width
            if (showTick) {
                drawRect(
                    Color.Transparent,
                    topLeft = Offset(tickCenter - (tickWidth / 2f + tickGap).toPx(), y = 0f),
                    size = Size((tickWidth + tickGap * 2f).toPx(), tickHeight.toPx()),
                    blendMode = BlendMode.Clear,
                )
            }
            drawContext.canvas.nativeCanvas.restoreToCount(saveCount)
            if (showTick) {
                drawRoundRect(
                    tickColor,
                    topLeft = Offset(tickCenter - tickWidth.toPx() / 2f, y = 0f),
                    size = Size(tickWidth.toPx(), tickHeight.toPx()),
                    cornerRadius = CornerRadius(
                        x = tickWidth.toPx() / 2f,
                        y = tickWidth.toPx() / 2f,
                    ),
                )
            }
        }
        if (showMarkers) {
            BoxWithConstraints(modifier = Modifier.fillMaxWidth()) {
                val width = constraints.maxWidth.toFloat()
                items.withIndex().drop(1).forEach { (index, item) ->
                    val (startRatio, _) = itemRatioMap[index] ?: return@forEach
                    var textWidth by remember { mutableIntStateOf(0) }
                    Text(
                        modifier = Modifier
                            .offset {
                                IntOffset(
                                    x = (startRatio * width - textWidth / 2f).roundToInt(),
                                    y = 0,
                                )
                            }
                            .onSizeChanged { textWidth = it.width },
                        text = item.min.toInt().toString(),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center,
                    )
                }
            }
        }
    }
}

data class StackedProgressItem(
    val min: Float,
    val max: Float,
    val color: Color
)
