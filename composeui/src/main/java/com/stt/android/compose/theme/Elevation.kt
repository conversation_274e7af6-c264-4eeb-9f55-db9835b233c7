package com.stt.android.compose.theme

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.material3.MaterialTheme as M3MaterialTheme

// For the full list, check https://m3.material.io/styles/elevation/tokens
data class SuuntoElevation(
    val elevatedCard: Dp = 1.dp,
    val zero: Dp = 0.dp,
)

val LocalElevation = staticCompositionLocalOf { SuuntoElevation() }

val MaterialTheme.elevation: SuuntoElevation
    @Composable
    @ReadOnlyComposable
    get() = LocalElevation.current

val M3MaterialTheme.elevation: SuuntoElevation
    @Composable
    @ReadOnlyComposable
    get() = LocalElevation.current
