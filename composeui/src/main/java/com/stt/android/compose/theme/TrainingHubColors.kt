package com.stt.android.compose.theme

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color

// Todo Should be merged into Colors.kt as these are colors from our color palette and not just for
//  training hub.
data class TrainingHubColors(
    val unclassified: Color? = null,
    val easyRecovery: Color = Color(0xFF4DBEE5),
    val aerobic: Color = Color(0xFF3949D6),
    val longAerobicBase: Color = Color(0xFFA0C655),
    val aboveThresholdVo2max: Color = Color(0xFF54247A),
    val hardAnaerobicEffort: Color = Color(0xFFDE0101),
    val aerobicToAnaerobic: Color = Color(0xFFFFC221),
    val hardLongAerobicBase: Color = Color(0xFF01AD3A),
    val anaerobicThreshold: Color = Color(0xFFFF7C02),
    val speedAndAgility: Color = Color(0xFFFFC221),
    val speedAndStrength: Color = Color(0xFFA0C655),
    val flexibility: Color = Color(0xFF4DBEE5),
    val strength: Color = Color(0xFFFF7C02),
    val coachOrange: Color = Color(0xFFFF7C3B)
)

private val LocalTrainingHubColors = staticCompositionLocalOf { TrainingHubColors() }

val MaterialTheme.trainingHubColors: TrainingHubColors
    @Composable
    @ReadOnlyComposable
    get() = LocalTrainingHubColors.current

val androidx.compose.material3.MaterialTheme.trainingHubColors: TrainingHubColors
    @Composable
    @ReadOnlyComposable
    get() = LocalTrainingHubColors.current
