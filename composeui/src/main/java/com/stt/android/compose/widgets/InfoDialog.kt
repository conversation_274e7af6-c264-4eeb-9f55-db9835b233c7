package com.stt.android.compose.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.DialogProperties
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.isMaterial3
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.spacing
import java.util.Locale
import androidx.compose.material.AlertDialog as M2AlertDialog
import androidx.compose.material.MaterialTheme as M2Theme
import androidx.compose.material.Text as M2Text
import androidx.compose.material.TextButton as M2TextButton

@Composable
fun InfoDialog(
    text: String,
    confirmButtonText: String,
    onDismissRequest: () -> Unit,
    onConfirm: () -> Unit,
    modifier: Modifier = Modifier,
    properties: DialogProperties = DialogProperties(),
) {
    InfoDialog(
        text = AnnotatedString(text),
        confirmButtonText = confirmButtonText,
        onDismissRequest = onDismissRequest,
        onConfirm = onConfirm,
        modifier = modifier,
        properties = properties,
    )
}

@Composable
fun InfoDialog(
    title: String,
    text: String,
    confirmButtonText: String,
    onDismissRequest: () -> Unit,
    onConfirm: () -> Unit,
    modifier: Modifier = Modifier,
    properties: DialogProperties = DialogProperties(),
) {
    InfoDialog(
        text = buildAnnotatedString {
            withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                append(title)
            }
            append("\n\n$text")
        },
        confirmButtonText = confirmButtonText,
        onDismissRequest = onDismissRequest,
        onConfirm = onConfirm,
        modifier = modifier,
        properties = properties,
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InfoDialog(
    text: AnnotatedString,
    confirmButtonText: String,
    onDismissRequest: () -> Unit,
    onConfirm: () -> Unit,
    modifier: Modifier = Modifier,
    properties: DialogProperties = DialogProperties(),
) {
    if (isMaterial3()) {
        BasicAlertDialog(
            onDismissRequest = onDismissRequest,
            modifier = modifier
                .clip(RoundedCornerShape(MaterialTheme.spacing.small))
                .background(MaterialTheme.colorScheme.surface),
            properties = properties,
        ) {
            Column {
                Text(
                    text = text,
                    modifier = Modifier.padding(MaterialTheme.spacing.large),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                )

                TextButton(
                    onClick = onConfirm,
                    modifier = Modifier.align(Alignment.End)
                        .padding(horizontal = MaterialTheme.spacing.xsmall),
                ) {
                    Text(
                        text = confirmButtonText.uppercase(Locale.getDefault()),
                        style = MaterialTheme.typography.bodyBold,
                    )
                }
            }
        }
    } else {
        M2AlertDialog(
            modifier = modifier,
            text = {
                M2Text(
                    text = text,
                    style = M2Theme.typography.bodyLarge,
                    color = M2Theme.colors.onSurface,
                )
            },
            onDismissRequest = onDismissRequest,
            confirmButton = {
                M2TextButton(onClick = onConfirm) {
                    M2Text(
                        text = confirmButtonText.uppercase(Locale.getDefault()),
                        style = M2Theme.typography.bodyBold,
                    )
                }
            },
            properties = properties,
        )
    }
}

@Composable
@Preview
private fun M2NonDestructiveInfoDialogPreview() {
    AppTheme {
        InfoDialog(
            text = "Very important info to acknowledge. (M2)",
            confirmButtonText = "OK",
            onDismissRequest = {},
            onConfirm = {},
        )
    }
}

@Composable
@Preview
private fun NonDestructiveInfoDialogPreview() {
    M3AppTheme {
        InfoDialog(
            text = "Very important info to acknowledge. (M3)",
            confirmButtonText = "OK",
            onDismissRequest = {},
            onConfirm = {},
        )
    }
}

@Composable
@Preview
private fun M2InfoDialogWithAnnotatedTextPreview() {
    AppTheme {
        InfoDialog(
            text = buildAnnotatedString {
                withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                    append("Important info (M2)")
                }
                append("\n\nVery important info to acknowledge.")
            },
            confirmButtonText = "OK",
            onDismissRequest = {},
            onConfirm = {},
        )
    }
}

@Composable
@Preview
private fun InfoDialogWithAnnotatedTextPreview() {
    M3AppTheme {
        InfoDialog(
            text = buildAnnotatedString {
                withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                    append("Important info (M3)")
                }
                append("\n\nVery important info to acknowledge.")
            },
            confirmButtonText = "OK",
            onDismissRequest = {},
            onConfirm = {},
        )
    }
}

@Composable
@Preview
private fun M2InfoDialogWithTitleTextPreview() {
    AppTheme {
        InfoDialog(
            title = "Important info (M2)",
            text = "Very important info to acknowledge.",
            confirmButtonText = "OK",
            onDismissRequest = {},
            onConfirm = {},
        )
    }
}

@Composable
@Preview
private fun InfoDialogWithTitleTextPreview() {
    M3AppTheme {
        InfoDialog(
            title = "Important info (M3)",
            text = "Very important info to acknowledge.",
            confirmButtonText = "OK",
            onDismissRequest = {},
            onConfirm = {},
        )
    }
}
