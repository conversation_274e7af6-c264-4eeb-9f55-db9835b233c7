package com.stt.android.compose.util

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.produceState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

/**
 * Utility for converting a StateFlow into State for Compose UIs.
 *
 * Exactly the same as StateFlow.collectAsState but with an additional [predicate] for filtering out
 * out some of the emitted items.
 */
@Suppress("StateFlowValueCalledInComposition")
@Composable
fun <T> StateFlow<T>.conditionallyCollectAsState(
    context: CoroutineContext = EmptyCoroutineContext,
    predicate: (T) -> Boolean,
): State<T> = conditionallyCollectAsState(
    initial = value,
    context = context,
    predicate = predicate
)

/**
 * Utility for converting a Flow into State for Compose UIs.
 *
 * Exactly the same as Flow.collectAsState but with an additional [predicate] for filtering out
 * out some of the emitted items.
 */
// This @SuppressLint annotation should be removed once the fix is released.
// https://issuetracker.google.com/issues/376491756
@SuppressLint("ProduceStateDoesNotAssignValue")
@Composable
fun <T : R, R> Flow<T>.conditionallyCollectAsState(
    initial: R,
    context: CoroutineContext = EmptyCoroutineContext,
    predicate: (T) -> Boolean,
): State<R> = produceState(initial, this, context) {
    if (context == EmptyCoroutineContext) {
        collect {
            if (predicate(it)) {
                value = it
            }
        }
    } else {
        withContext(context) {
            collect {
                if (predicate(it)) {
                    value = it
                }
            }
        }
    }
}
