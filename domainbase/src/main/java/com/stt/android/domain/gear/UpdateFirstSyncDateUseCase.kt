package com.stt.android.domain.gear

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.CoroutineUseCase
import timber.log.Timber
import javax.inject.Inject

class UpdateFirstSyncDateUseCase
@Inject constructor(
    private val gearRepository: GearRepository
) : CoroutineUseCase<Unit, String?> {
    @Suppress("PARAMETER_NAME_CHANGED_ON_OVERRIDE")
    override suspend fun run(serial: String?) {
        runSuspendCatching { gearRepository.updateFirstSyncDate(serial) }
            .onFailure { Timber.w(it, "Fetching gear failed.") }
    }
}
