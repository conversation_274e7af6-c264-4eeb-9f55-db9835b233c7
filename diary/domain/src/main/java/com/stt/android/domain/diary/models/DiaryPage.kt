package com.stt.android.domain.diary.models

enum class DiaryPage {
    TRAINING,
    SCUBA_DIVING,
    FREE_DIVING,
    PROGRESS,
    RECOVERY,
    DAILY_ACTIVITY,
    SLEEP,
    OVERVIEW,
    SUMMARY,
    STATISTICS,
}

val DiaryPage.primaryGraphDataTypePrefsKey: String get() = "${this}_PRIMARY_GRAPH_DATA_TYPE"
val DiaryPage.secondaryGraphDataTypePrefsKey: String get() = "${this}_SECONDARY_GRAPH_DATA_TYPE"

val DiaryPage.primaryGraphDataTypes: List<GraphDataType>
    get() = when (this) {
        DiaryPage.TRAINING -> GraphDataTypes.TRAINING_PRIMARY_GRAPH_DATA_TYPES
        DiaryPage.SCUBA_DIVING -> GraphDataTypes.SCUBA_DIVING_PRIMARY_GRAPH_DATA_TYPES
        DiaryPage.FREE_DIVING -> GraphDataTypes.FREE_DIVING_PRIMARY_GRAPH_DATA_TYPES
        DiaryPage.PROGRESS -> emptyList()
        DiaryPage.RECOVERY -> GraphDataTypes.RECOVERY_PRIMARY_GRAPH_DATA_TYPES
        DiaryPage.DAILY_ACTIVITY -> GraphDataTypes.DAILY_ACTIVITY_PRIMARY_GRAPH_DATA_TYPES
        DiaryPage.SLEEP -> GraphDataTypes.SLEEP_PRIMARY_GRAPH_DATA_TYPES
        DiaryPage.OVERVIEW -> emptyList()
        DiaryPage.SUMMARY -> GraphDataTypes.SUMMARY_PRIMARY_GRAPH_DATA_TYPES
        DiaryPage.STATISTICS -> emptyList()
    }

val DiaryPage.secondaryGraphDataTypes: List<GraphDataType>
    get() = when (this) {
        DiaryPage.TRAINING -> GraphDataTypes.TRAINING_SECONDARY_GRAPH_DATA_TYPES
        DiaryPage.SCUBA_DIVING -> GraphDataTypes.SCUBA_DIVING_SECONDARY_GRAPH_DATA_TYPES
        DiaryPage.FREE_DIVING -> GraphDataTypes.FREE_DIVING_SECONDARY_GRAPH_DATA_TYPES
        DiaryPage.PROGRESS -> emptyList()
        DiaryPage.RECOVERY -> GraphDataTypes.RECOVERY_SECONDARY_GRAPH_DATA_TYPES
        DiaryPage.DAILY_ACTIVITY -> GraphDataTypes.DAILY_ACTIVITY_SECONDARY_GRAPH_DATA_TYPES
        DiaryPage.SLEEP -> GraphDataTypes.SLEEP_SECONDARY_GRAPH_DATA_TYPES
        DiaryPage.OVERVIEW -> emptyList()
        DiaryPage.SUMMARY -> GraphDataTypes.SUMMARY_SECONDARY_GRAPH_DATA_TYPES
        DiaryPage.STATISTICS -> emptyList()
    }
