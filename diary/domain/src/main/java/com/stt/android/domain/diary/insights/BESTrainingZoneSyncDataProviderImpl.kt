package com.stt.android.domain.diary.insights

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.releasedMilli
import com.stt.android.domain.diary.GetTrainingProgressDataUseCase
import com.stt.android.domain.diary.models.TrainingProgressData
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.restheartrate.FetchRestHeartRateUseCase
import com.stt.android.domain.sleep.FetchSleepHrvUseCase
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.user.CurrentUserDataSource
import com.stt.android.domain.workouts.BasicWorkoutHeader
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.domain.workouts.extensions.SummaryExtensionDataSource
import com.stt.android.domain.workouts.extensions.intensity.FitnessExtensionDataSource
import com.stt.android.domain.workouts.extensions.intensity.GetIntensityExtensionUseCase
import com.stt.android.domain.workouts.feeling.FetchDailyFeelingUseCase
import com.stt.android.infomodel.getMcIdForStId
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.iterator
import com.stt.android.utils.takeIfNotEmpty
import com.stt.android.utils.toEpochMilli
import com.suunto.connectivity.trainingzone.BESTrainingZoneSyncData
import com.suunto.connectivity.trainingzone.BESTrainingZoneSyncDataProvider
import com.suunto.connectivity.trainingzone.HrvRange
import com.suunto.connectivity.trainingzone.Sleep
import com.suunto.connectivity.trainingzone.SportSummary
import kotlinx.coroutines.flow.firstOrNull
import timber.log.Timber
import java.time.DayOfWeek
import java.time.Duration
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.math.roundToLong

class BESTrainingZoneSyncDataProviderImpl @Inject constructor(
    private val currentUserDataSource: CurrentUserDataSource,
    private val getTrainingProgressDataUseCase: GetTrainingProgressDataUseCase,
    private val workoutDataSource: WorkoutDataSource,
    private val fitnessExtensionDataSource: FitnessExtensionDataSource,
    private val intensityExtensionUseCase: GetIntensityExtensionUseCase,
    private val fetchSleepHrvUseCase: FetchSleepHrvUseCase,
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val summaryExtensionDataSource: SummaryExtensionDataSource,
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val fetchRecoveryStateScoreUseCase: FetchRecoveryStateScoreUseCase,
    private val fetchRestHeartRateUseCase: FetchRestHeartRateUseCase,
    private val fetchDailyFeelingUseCase: FetchDailyFeelingUseCase,
) : BESTrainingZoneSyncDataProvider {

    override suspend fun getAnalysis(firstDayOfWeek: DayOfWeek): BESTrainingZoneSyncData {
        val currentUser = currentUserDataSource.getCurrentUserOrNull() ?: run {
            throw IllegalStateException("No current user is found")
        }
        val username = currentUser.username
        val today = LocalDate.now()

        val last42Day = today.minusDays(41)..today
        val last2Day = today.minusDays(1)..today
        val last14Day = today.minusDays(13)..today
        val last56Day = today.minusDays(55)..today

        val trainingProgresses56d = loadTrainingProgress(username = username, range = last56Day)
        val trainingProgresses42d = trainingProgresses56d.takeLast(42)

        val last56DayTSS = trainingProgresses56d.mapFieldValue(last56Day) { it.exactTss }

        val last42DayTSB = trainingProgresses42d.mapFieldValue(last42Day) { it.form }

        val last42DayCTL =
            trainingProgresses42d.mapFieldValue(last42Day) { it.fitness }

        val last2DayATL =
            trainingProgresses56d.takeLast(2).mapFieldValue(last2Day) { it.fatigue }

        val last56DayWorkout = workoutDataSource.fetchUserWorkoutsBasic(
            username = username,
            minStartTime = last56Day.start.atStartOfDay().toEpochMilli(),
            maxStartTime = last56Day.endInclusive.atEndOfDay().toEpochMilli()
        )

        val last42DayWorkout = last56DayWorkout.filter {
            it.startTime > last42Day.start.atStartOfDay().toEpochMilli()
        }

        val last14DayWorkout = last56DayWorkout.filter {
            it.startTime > last14Day.start.atStartOfDay().toEpochMilli()
        }

        val sleepHrv = runSuspendCatching {
            fetchSleepHrvUseCase.fetchAvgHrv(date = today)
        }.getOrNull()

        val recoveryStateGraphData = fetchRecoveryStateScoreUseCase(
            fromDate = today,
            toDate = today,
            includeContributors = false,
        ).firstOrNull()?.getOrNull(0)

        val restHRByDate = fetchRestHeartRateUseCase.fetchRestHeartRateForDateRange(
            fromDate = today.minusDays(6L),
            toDate = today,
        ).firstOrNull()?.groupBy { it.localDate }?.mapValues { it.value.first() } ?: emptyMap()
        val last7DayRestHR = (6 downTo 0).map {
            val date = today.minusDays(it.toLong())
            restHRByDate[date]?.restHeartRate?.inBpm?.roundToInt() ?: 0
        }

        val feelingByDate = fetchDailyFeelingUseCase.fetchDailyFeelingForDateRange(
            fromDate = today.minusDays(41L),
            toDate = today,
        ).firstOrNull()?.groupBy { it.localDate }?.mapValues { it.value.first() } ?: emptyMap()
        val last42DayFeeling =  (41 downTo 0).map {
            val date = today.minusDays(it.toLong())
            val feelings = feelingByDate[date]?.values ?: return@map 0f
            (feelings.size * 10000 + feelings.sum()).toFloat()
        }

        return BESTrainingZoneSyncData(
            tssDaily56d = last56DayTSS,
            tsbDaily42d = last42DayTSB,
            ctlDaily42d = last42DayCTL,
            atlDaily2d = last2DayATL,
            sportSummary14d = getSportSummaries(last14Day, last14DayWorkout),
            sportTime56d = getSportTimes(last56Day, last56DayWorkout),
            hrIntensityZone = getHrIntensityZones(last14Day, last14DayWorkout),
            vo2Max42d = getVo2Maxes(username, last42Day, last42DayWorkout),
            latestFitnessAge = fitnessExtensionDataSource.findLatestFitnessAge(username),
            latestLacticThHr = summaryExtensionDataSource.findLatestLacticThHr(username),
            latestLacticThPace = summaryExtensionDataSource.findLatestLacticThPace(username),
            recoveryPercent = recoveryStateGraphData?.recoveryStateData?.recoveryScore,
            hrvRange = sleepHrv?.normalRange?.let {
                HrvRange(start = it.start.roundToInt(), end = it.endInclusive.roundToInt())
            },
            lastNightHrv = sleepHrv?.previousAvgHrv?.roundToInt(),
            sleep60d = getSleeps(),
            oldestWorkoutStartTime = workoutHeaderDataSource.findOldestWorkout(
                username,
                releasedMilli // Filter out workout that start time before 2004, they may have been added manually.
            )?.startTime?.let { Duration.of(it, ChronoUnit.MILLIS).toSeconds() },
            restHr7d = last7DayRestHR,
            feeling42d = last42DayFeeling,
        ).also {
            Timber.i("sync recovery to watch: $it")
        }
    }

    private suspend fun getSleeps(): List<Sleep> {
        val today = LocalDate.now()
        val sinceMillis = today.minusDays(60).atTime(12, 0).toEpochMilli()
        val dailySleep = fetchSleepUseCase.fetchSleeps(
            from = today.minusDays(60),
            to = today,
        ).firstOrNull()
            ?.filter { it.timestamp >= sinceMillis }
            ?.groupBy { it.timestamp }
            ?.mapKeys { it.key.toLocalDate() }
        if (dailySleep.isNullOrEmpty()) return emptyList()
        // Sleep data for the last 60 days.
        return buildList {
            (today.minusDays(59)..today).iterator().withIndex().forEach { date ->
                dailySleep[date.value]?.map { sleep ->
                    Sleep(
                        day = date.index,
                        longSleep = sleep.longSleep?.sleepDuration?.inWholeSeconds?.toInt() ?: 0,
                        shortSleep = sleep.naps.sumOf { it.duration.inWholeSeconds }.toInt(),
                        hrv = sleep.longSleep?.avgHrv ?: 0f,
                        hr = sleep.longSleep?.avgHr?.inBpm?.roundToInt() ?: 0,
                    )
                }?.let(::addAll)
            }
        }
    }

    private suspend fun loadTrainingProgress(
        username: String,
        range: ClosedRange<LocalDate>
    ): List<TrainingProgressData> {
        return getTrainingProgressDataUseCase(
            GetTrainingProgressDataUseCase.Params(
                username = username,
                firstDay = range.start,
                lastDay = range.endInclusive,
                addZeroValuesBeforeFirstRecordedTssDate = true,
                preCalculatedTSSSummaries = null
            )
        )
    }

    private fun List<TrainingProgressData>.mapFieldValue(
        range: ClosedRange<LocalDate>,
        transfer: (TrainingProgressData) -> Float
    ): List<Float> {
        val valueList = mutableListOf<Float>()
        range.iterator().forEach { day ->
            valueList.add(find { it.day == day }?.let(transfer) ?: 0f)
        }
        return valueList
    }

    private fun getSportSummaries(
        range: ClosedRange<LocalDate>,
        workouts: List<BasicWorkoutHeader>
    ): List<SportSummary> {
        val sportSummaries = mutableListOf<SportSummary>()
        range.iterator().withIndex().forEach { day ->
            val sportSummariesByDay =
                workouts.filter { it.startTime.toLocalDate() == day.value }.map { workout ->
                    SportSummary(
                        day = day.index,
                        sportType = getMcIdForStId(workout.activityTypeId),
                        tssValue = workout.tss?.trainingStressScore ?: 0f,
                        sportTime = workout.totalTime.roundToLong(),
                        sportDistance = workout.totalDistance.roundToLong(),
                        sportCalorie = workout.energyConsumption.roundToInt(),
                    )
                }
            sportSummaries.addAll(sportSummariesByDay)
        }
        return sportSummaries
    }

    private fun getSportTimes(
        range: ClosedRange<LocalDate>,
        workouts: List<BasicWorkoutHeader>
    ): List<Long> {
        val sportTimes = mutableListOf<Long>()
        range.iterator().forEach { day ->
            sportTimes.add(
                workouts.filter { it.startTime.toLocalDate() == day }
                    .sumOf { it.totalTime }
                    .roundToLong()
            )
        }
        return sportTimes
    }

    private suspend fun getVo2Maxes(
        username: String,
        range: ClosedRange<LocalDate>,
        workouts: List<BasicWorkoutHeader>
    ): List<Float> {
        val vo2Maxes = mutableListOf<Float>()
        range.iterator().forEach { day ->
            vo2Maxes.add(
                workouts.filter { it.startTime.toLocalDate() == day }
                    .mapNotNull { fitnessExtensionDataSource.findById(it.id) }
                    .takeIfNotEmpty()?.maxOf { it.vo2Max } ?: 0f
            )
        }
        // Effective range [30, 85]
        // The ESW requires that the value of the first day be within the valid range, if not, continue to look before that day until it is found.
        val vo2MaxRange = 30f..85f
        if (!vo2MaxRange.contains(vo2Maxes[0])) {
            vo2Maxes[0] = fitnessExtensionDataSource.findLatestVo2MaxInRangeByTime(
                username = username,
                vo2MaxRange = vo2MaxRange,
                untilMillis = range.start.atStartOfDay().toEpochMilli()
            )?.vo2Max ?: 0f
        }
        return vo2Maxes
    }

    private suspend fun getHrIntensityZones(
        range: ClosedRange<LocalDate>,
        workouts: List<BasicWorkoutHeader>
    ): List<List<Int>> {
        val hrIntensityZones = mutableListOf<List<Int>>()
        range.iterator().forEach { day ->
            val hrIntensityZonesSum =
                workouts.filter { it.startTime.toLocalDate() == day }.sumIntensityZones()
            hrIntensityZones.add(hrIntensityZonesSum.toList())
        }
        return hrIntensityZones
    }

    private fun IntensityZones.toList() = buildList {
        add(zone1Duration.roundToInt())
        add(zone2Duration.roundToInt())
        add(zone3Duration.roundToInt())
        add(zone4Duration.roundToInt())
        add(zone5Duration.roundToInt())
    }

    private suspend fun List<BasicWorkoutHeader>.sumIntensityZones(): IntensityZones =
        this.map { workout ->
            intensityExtensionUseCase.loadLocalExtension(workout.id)?.intensityZones?.hr?.let {
                IntensityZones(
                    zone1Duration = it.zone1Duration,
                    zone2Duration = it.zone2Duration,
                    zone3Duration = it.zone3Duration,
                    zone4Duration = it.zone4Duration,
                    zone5Duration = it.zone5Duration
                )
            } ?: IntensityZones.EMPTY
        }.fold(IntensityZones.EMPTY) { acc, intensityZones ->
            acc.copy(
                zone1Duration = acc.zone1Duration + intensityZones.zone1Duration,
                zone2Duration = acc.zone2Duration + intensityZones.zone2Duration,
                zone3Duration = acc.zone3Duration + intensityZones.zone3Duration,
                zone4Duration = acc.zone4Duration + intensityZones.zone4Duration,
                zone5Duration = acc.zone5Duration + intensityZones.zone5Duration,
            )
        }

    internal data class IntensityZones(
        val zone1Duration: Float,
        val zone2Duration: Float,
        val zone3Duration: Float,
        val zone4Duration: Float,
        val zone5Duration: Float,
    ) {
        companion object {
            val EMPTY = IntensityZones(0f, 0f, 0f, 0f, 0f)
        }
    }
}
