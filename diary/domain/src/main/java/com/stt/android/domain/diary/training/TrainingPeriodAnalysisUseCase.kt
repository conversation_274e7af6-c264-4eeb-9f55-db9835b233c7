package com.stt.android.domain.diary.training

import androidx.annotation.FloatRange
import com.soy.algorithms.impact.WorkoutImpact
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.workouts.BasicWorkoutHeader
import com.stt.android.domain.workouts.GetWorkoutHeadersForRangeUseCase
import com.stt.android.domain.workouts.extensions.intensity.IntensityDistributionUseCase
import com.stt.android.domain.workouts.extensions.intensity.TrainingIntensityModelUseCase
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.toBasic
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.distributionBy
import com.stt.android.utils.takeIfNotEmpty
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.time.LocalDate
import javax.inject.Inject

class TrainingPeriodAnalysisUseCase @Inject constructor(
    private val intensityDistributionUseCase: IntensityDistributionUseCase,
    private val trainingIntensityModelUseCase: TrainingIntensityModelUseCase,
    private val getWorkoutHeadersForRangeUseCase: GetWorkoutHeadersForRangeUseCase,
) {

    /**
     * @param username username of the user owning the workouts
     * @param startDate first day to be included in the period
     * @param endDateInclusive last day to be included in the period
     */
    suspend fun getAnalysisForPeriod(
        username: String,
        startDate: LocalDate,
        endDateInclusive: LocalDate,
        @FloatRange(from = 1.0) avgFactor: Float,
        activityTypes: List<CoreActivityType> = CoreActivityType.entries,
    ): TrainingPeriodAnalysis = withContext(IO) {
        require(startDate.isBefore(endDateInclusive) || startDate.isEqual(endDateInclusive)) {
            "startDate: $startDate is after endDateInclusive: $endDateInclusive"
        }

        val activityTypeIds = activityTypes.map { it.id }
        val workoutHeaders = getWorkoutHeadersForRangeUseCase(
            params = GetWorkoutHeadersForRangeUseCase.Params(
                username = username,
                activityTypeIds = activityTypeIds,
                sinceMs = startDate.atStartOfDay().toEpochMilli(),
                untilMs = endDateInclusive.atEndOfDay().toEpochMilli(),
            ),
        )
        val basicWorkoutHeaders = workoutHeaders.map { it.toBasic() }

        val intensityDistributionAsync = async {
            intensityDistributionUseCase.calculate(basicWorkoutHeaders, avgFactor)
        }

        val trainingIntensityModelAsync = async {
            trainingIntensityModelUseCase.calculate(basicWorkoutHeaders)
        }

        val workoutImpactsDistribution = workoutHeaders.flatMap { workoutHeader ->
            workoutHeader.suuntoTags
                .mapNotNull(SuuntoTag::workoutImpact)
                .takeIfNotEmpty()
                ?: listOf(WorkoutImpact.UNCLASSIFIED)
        }
            .distributionBy { it }
            .mapValues { (_, count) -> count / avgFactor }

        val trainingIntensityModelWithDistribution = trainingIntensityModelAsync.await()

        TrainingPeriodAnalysis(
            startDate = startDate,
            endDateInclusive = endDateInclusive,
            intensityDistribution = intensityDistributionAsync.await(),
            trainingIntensityModel = trainingIntensityModelWithDistribution.trainingIntensityModel,
            trainingIntensityDistribution = trainingIntensityModelWithDistribution.distribution
                .mapValues { (_, count) ->
                    count.toFloat() / avgFactor
                },
            trainingTotals = getTrainingTotals(basicWorkoutHeaders, avgFactor),
            workoutImpactsDistribution = workoutImpactsDistribution,
        )
    }

    private fun getTrainingTotals(
        workouts: List<BasicWorkoutHeader>,
        avgFactor: Float
    ): List<TrainingTotals> {
        if (avgFactor <= 0f) return emptyList()

        return workouts.groupBy { CoreActivityType.valueOf(it.activityTypeId) }
            .map { (activityType, workouts) ->
                val workoutsGroupedByDay = workouts.groupBy {
                    it.startTime.toLocalDate()
                }
                TrainingTotals(
                    activityType = activityType,
                    activitiesCount = workouts.size / avgFactor,
                    totalDuration = workouts.sumOf { it.totalTime } / avgFactor,
                    totalDistance = if (activityType.hasDistance) workouts.sumOf { it.totalDistance } / avgFactor else 0.0,
                    totalAscent = if (activityType.hasAscent) workouts.sumOf { it.totalAscent } / avgFactor else 0.0,
                    totalTSS = workouts.sumOf {
                        it.tss?.trainingStressScore?.toDouble() ?: 0.0
                    } / avgFactor,
                    tssByDay = workoutsGroupedByDay
                        .mapValues { (_, workoutsInDay) ->
                            workoutsInDay.sumOf {
                                it.tss?.trainingStressScore?.toDouble() ?: 0.0
                            } / avgFactor
                        },
                    durationByDay = workoutsGroupedByDay
                        .mapValues { (_, workoutsInDay) ->
                            workoutsInDay.sumOf { it.totalTime } / avgFactor
                        },
                    activitiesCountByDay = workoutsGroupedByDay
                        .mapValues { (_, workoutsInDay) ->
                            workoutsInDay.size.toDouble()
                        },
                    ascentByDay = workoutsGroupedByDay
                        .mapValues { (_, workoutsInDay) ->
                            if (activityType.hasAscent) workoutsInDay.sumOf { it.totalAscent } / avgFactor else 0.0
                        },
                    distanceByDay = workoutsGroupedByDay
                        .mapValues { (_, workoutsInDay) ->
                            if (activityType.hasDistance) workoutsInDay.sumOf { it.totalDistance } / avgFactor else  0.0
                        },
                )
            }
    }

    class Params(
        val username: String,
        val firstDay: LocalDate,
        val lastDay: LocalDate,
    )
}
