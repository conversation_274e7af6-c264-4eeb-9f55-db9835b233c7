package com.stt.android.domain.diary.insights

import com.soy.algorithms.coach.CoachPhrase
import com.soy.algorithms.coach.SuuntoCoach
import com.soy.algorithms.coach.SuuntoCoachInsightType
import com.soy.algorithms.impact.WorkoutImpact
import com.soy.algorithms.intensity.WorkoutIntensity
import com.stt.android.core.domain.workouts.CoreActivityGroup
import com.stt.android.domain.sleep.SleepHrv
import com.stt.android.domain.workouts.extensions.intensity.TrainingIntensityModel
import kotlinx.coroutines.Dispatchers.Default
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.DayOfWeek
import java.time.LocalDate
import javax.inject.Inject

class TrainingHubSuuntoCoachUseCase @Inject constructor() {

    suspend fun getCoachFeedback(
        currentPeriodAnalysis: TrainingHubPeriodAnalysis,
        comparisonPeriodAnalysis: TrainingHubPeriodAnalysis,
        firstDayOfWeek: DayOfWeek,
        isCurrentWeek: Boolean,
        sleepHrv: SleepHrv?
    ): Map<SuuntoCoachInsightType, CoachPhrase> = withContext(Default) {
        val input = generateCoachInput(
            currentPeriodAnalysis = currentPeriodAnalysis,
            comparisonPeriodAnalysis = comparisonPeriodAnalysis,
            firstDayOfWeek = firstDayOfWeek,
            isCurrentWeek = isCurrentWeek,
            sleepHrv = sleepHrv
        )
        Timber.d("Requesting Suunto coach with input = $input")
        val output = SuuntoCoach().generateFeedback(input)
        Timber.d("Suunto coach responded with output = $output")
        output
    }

    private fun generateCoachInput(
        currentPeriodAnalysis: TrainingHubPeriodAnalysis,
        comparisonPeriodAnalysis: TrainingHubPeriodAnalysis,
        firstDayOfWeek: DayOfWeek,
        isCurrentWeek: Boolean,
        sleepHrv: SleepHrv?
    ): SuuntoCoach.Input = SuuntoCoach.Input(
        lastConsideredDayIndexInWeek = if (isCurrentWeek) {
            (LocalDate.now().dayOfWeek.value - firstDayOfWeek.value).let {
                if (it < 0) it + 8 else it + 1
            }
        } else {
            7 // If it is not the current week, we get the data as it is the last day of the selected week
        },
        lastConsideredDayTSB = currentPeriodAnalysis.recoveryAnalysis.form?.toDouble(),
        lastConsideredDayTSS = currentPeriodAnalysis.totalTSS,
        hrvNormalRangeMin = sleepHrv?.normalRange?.start,
        hrvNormalRangeMax = sleepHrv?.normalRange?.endInclusive,
        avg7DayHrv = sleepHrv?.avg7DayHrv,
        latestAvgHrv = sleepHrv?.avgHrv,
        currentPeriod = currentPeriodAnalysis.toCoachWeeklyValues(),
        comparisonPeriod = comparisonPeriodAnalysis.toCoachWeeklyValues(),
    )

    private fun TrainingHubPeriodAnalysis.toCoachWeeklyValues() = SuuntoCoach.Input.WeeklyAvgValues(
        totalTSS = totalTSS,
        ctl = recoveryAnalysis.fitness?.toDouble(),
        workoutIntensityDistribution = trainingIntensityDistribution.mapKeys { (intensity, _) ->
            when (intensity) {
                WorkoutIntensity.ZONE_1 -> SuuntoCoach.WorkoutIntensity.ZONE_1
                WorkoutIntensity.ZONE_2 -> SuuntoCoach.WorkoutIntensity.ZONE_2
                WorkoutIntensity.ZONE_3 -> SuuntoCoach.WorkoutIntensity.ZONE_3
                WorkoutIntensity.ZONE_4 -> SuuntoCoach.WorkoutIntensity.ZONE_4
                WorkoutIntensity.ZONE_5 -> SuuntoCoach.WorkoutIntensity.ZONE_5
            }
        },
        totalDuration = totalDuration,
        totalRunDistance = groupedTotals[CoreActivityGroup.RUN]?.totalDistance ?: 0.0,
        totalRideDistance = groupedTotals[CoreActivityGroup.RIDE]?.totalDistance ?: 0.0,
        totalSwimDistance = groupedTotals[CoreActivityGroup.SWIM]?.totalDistance ?: 0.0,
        impactStrengthCount = workoutImpactsDistribution.getOrDefault(WorkoutImpact.STRENGTH, 0f),
        impactAnaerobicCount =
        workoutImpactsDistribution.getOrDefault(WorkoutImpact.AEROBIC_TO_ANAEROBIC, 0f) +
            workoutImpactsDistribution.getOrDefault(WorkoutImpact.ANAEROBIC_THRESHOLD, 0f) +
            workoutImpactsDistribution.getOrDefault(WorkoutImpact.ABOVE_THRESHOLD_VO2MAX, 0f) +
            workoutImpactsDistribution.getOrDefault(WorkoutImpact.HARD_ANAEROBIC_EFFORT, 0f),
        impactAerobicCount =
        workoutImpactsDistribution.getOrDefault(WorkoutImpact.EASY_RECOVERY, 0f) +
            workoutImpactsDistribution.getOrDefault(WorkoutImpact.AEROBIC, 0f) +
            workoutImpactsDistribution.getOrDefault(WorkoutImpact.LONG_AEROBIC_BASE, 0f) +
            workoutImpactsDistribution.getOrDefault(WorkoutImpact.HARD_LONG_AEROBIC_BASE, 0f),
        trainingIntensityModel = when (trainingIntensityModel) {
            TrainingIntensityModel.BASE_ENDURANCE -> SuuntoCoach.TrainingIntensityModel.BASE_ENDURANCE
            TrainingIntensityModel.HIGH_INTENSITY -> SuuntoCoach.TrainingIntensityModel.HIGH_INTENSITY
            TrainingIntensityModel.SWEETSPOT -> SuuntoCoach.TrainingIntensityModel.SWEETSPOT
            TrainingIntensityModel.POLARIZED -> SuuntoCoach.TrainingIntensityModel.POLARIZED
            TrainingIntensityModel.PYRAMID -> SuuntoCoach.TrainingIntensityModel.PYRAMID
            TrainingIntensityModel.UNIDENTIFIED -> SuuntoCoach.TrainingIntensityModel.UNIDENTIFIED
        },
        sleepDurationAvg = recoveryAnalysis.avgSleepDuration?.toDouble(),
        feelingAvg = recoveryAnalysis.avgFeeling,
        workoutsCount = workoutsCount
    )
}
