package com.stt.android.diary.dailyhealth.usecase

import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.highlightTitleRes
import com.stt.android.chart.impl.screen.ChartHighlightViewData
import com.stt.android.chart.impl.usecases.FormatChartHighlightDateTimeUseCase
import com.stt.android.diary.dailyhealth.ChartType
import com.stt.android.diary.dailyhealth.HealthItem
import com.stt.android.diary.dailyhealth.data.CaloriesChartDataLoader
import com.stt.android.diary.dailyhealth.data.HeartRateChartDataLoader
import com.stt.android.diary.dailyhealth.data.StepsChartDataLoader
import javax.inject.Inject

class CreateChartHighlightUseCase @Inject constructor(
    private val caloriesChartDataLoader: CaloriesChartDataLoader,
    private val heartRateChartDataLoader: HeartRateChartDataLoader,
    private val stepsChartDataLoader: StepsChartDataLoader,
    private val formatDateTimeUseCase: FormatChartHighlightDateTimeUseCase,
) {
    operator fun invoke(
        healthItem: HealthItem,
        chartGranularity: ChartGranularity,
        entryX: Long?
    ): ChartHighlightViewData {
        if (entryX == null) {
            return ChartHighlightViewData.None
        }

        return createHighlightData(healthItem, chartGranularity, entryX)
    }

    private fun createHighlightData(
        healthItem: HealthItem,
        chartGranularity: ChartGranularity,
        entryX: Long
    ): ChartHighlightViewData {
        return try {
            val dateTime = formatDateTimeUseCase.formatDateTime(
                chartGranularity = chartGranularity,
                x = entryX,
            )

            val (valueType, formattedValue) = when (healthItem.chartType) {
                ChartType.HEART_RATE -> {
                    if (chartGranularity == ChartGranularity.DAILY) {
                        val entry = healthItem.chartData?.series
                            ?.asSequence()
                            ?.mapNotNull { series ->
                                series.entries.firstOrNull { it.x == entryX }
                            }
                            ?.firstOrNull()
                            ?: return ChartHighlightViewData.None
                        
                        val value = heartRateChartDataLoader.formatHighlightData(entry.y.toFloat())
                        ChartContent.HEART_RATE.highlightTitleRes(chartGranularity) to value
                    } else {
                        val candlestickEntry = healthItem.chartData?.series
                            ?.firstOrNull()
                            ?.candlestickEntries
                            ?.firstOrNull { it.x == entryX }
                            ?: return ChartHighlightViewData.None
                        
                        val rangeValue = heartRateChartDataLoader.formatHighlightDataRange(
                            candlestickEntry.low.toFloat(),
                            candlestickEntry.high.toFloat()
                        )
                        ChartContent.HEART_RATE.highlightTitleRes(chartGranularity) to rangeValue
                    }
                }
                ChartType.STEPS -> {
                    val entry = healthItem.chartData?.series
                        ?.firstOrNull()
                        ?.entries
                        ?.firstOrNull { it.x == entryX }
                        ?: return ChartHighlightViewData.None
                    
                    val value = stepsChartDataLoader.formatHighlightData(entry.y.toInt())
                    ChartContent.STEPS.highlightTitleRes(chartGranularity) to value
                }
                ChartType.CALORIES -> {
                    val entry = healthItem.chartData?.series
                        ?.firstOrNull()
                        ?.entries
                        ?.firstOrNull { it.x == entryX }
                        ?: return ChartHighlightViewData.None
                    
                    val value = caloriesChartDataLoader.formatHighlightData(entry.y.toInt())
                    ChartContent.CALORIES.highlightTitleRes(chartGranularity) to value
                }
            }

            ChartHighlightViewData.Highlight(
                valueType = valueType,
                primaryValue = ChartHighlightViewData.Value(
                    value = formattedValue,
                    dateTime = dateTime,
                ),
                comparisonValue = null,
            )
        } catch (e: Exception) {
            ChartHighlightViewData.None
        }
    }
} 
