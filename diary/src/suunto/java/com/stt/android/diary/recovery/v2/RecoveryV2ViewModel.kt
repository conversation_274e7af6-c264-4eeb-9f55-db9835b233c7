package com.stt.android.diary.recovery.v2

import android.content.Context
import android.os.SystemClock
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.soy.algorithms.coach.CoachPhrase
import com.soy.algorithms.coach.SuuntoCoachInsightType
import com.soy.algorithms.coach.SuuntoCoachInsightTypeTheme
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.toAnalyticsTimeDim
import com.stt.android.chart.impl.screen.ChartHighlightViewData
import com.stt.android.chart.impl.usecases.CalculateDateRangeUseCase
import com.stt.android.chart.impl.usecases.ChartHighlightDateFormatterUseCase
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.diary.recovery.model.RecoverySleepComparisonHighlightViewData
import com.stt.android.diary.recovery.usecases.CreateRecoveryChartHighlightUseCase
import com.stt.android.diary.recovery.usecases.CreateRecoverySleepComparisonHighlightUseCase
import com.stt.android.diary.recovery.usecases.FetchDailyRecoveryDataUseCase
import com.stt.android.diary.recovery.usecases.FetchRecoveryChartDataUseCase
import com.stt.android.diary.recovery.usecases.LoadSleepComparisonChartDataUseCase
import com.stt.android.diary.trainingv2.TrainingDatePeriodFormatter
import com.stt.android.diary.trainingv2.TrainingDateRange
import com.stt.android.domain.diary.insights.TrainingHubPeriodAnalysisUseCase
import com.stt.android.domain.diary.insights.TrainingHubSuuntoCoachUseCase
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.diary.training.TrainingPeriodsCalculationUseCase
import com.stt.android.domain.sleep.FetchSleepHrvUseCase
import com.stt.android.home.diary.InfoBottomSheet
import com.stt.android.home.diary.R
import com.stt.android.ui.utils.DateFormatter
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.diary.recovery.model.toAnalyticsMainGraphTypeValue
import com.stt.android.diary.recovery.model.toAnalyticsSubGraphTypeValue
import com.stt.android.home.diary.analyticsClickName
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.collections.immutable.toImmutableMap
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import timber.log.Timber
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject

@HiltViewModel
class RecoveryV2ViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    @ApplicationContext private val context: Context,
    private val calculateDateRangeUseCase: CalculateDateRangeUseCase,
    private val fetchRecoveryChartDataUseCase: FetchRecoveryChartDataUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val loadSleepComparisonChartDataUseCase: LoadSleepComparisonChartDataUseCase,
    private val loadDailyRecoveryDataUseCase: FetchDailyRecoveryDataUseCase,
    private val trainingHubPeriodAnalysisUseCase: TrainingHubPeriodAnalysisUseCase,
    private val trainingHubSuuntoCoachUseCase: TrainingHubSuuntoCoachUseCase,
    private val fetchSleepHrvUseCase: FetchSleepHrvUseCase,
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val createRecoveryChartHighlightUseCase: CreateRecoveryChartHighlightUseCase,
    private val createRecoverySleepComparisonHighlightUseCase: CreateRecoverySleepComparisonHighlightUseCase,
    private val dateFormatter: DateFormatter,
    private val chartHighlightDateFormatterUseCase: ChartHighlightDateFormatterUseCase,
    private val trainingPeriodsCalculationUseCase: TrainingPeriodsCalculationUseCase,
    private val trainingDatePeriodFormatter: TrainingDatePeriodFormatter,
    private val recoveryAnalytics: RecoveryV2Analytics,
) : ViewModel() {
    private val _currentDate = MutableStateFlow(LocalDate.now())
    private val _currentTimeGranularity: StateFlow<ChartGranularity> = savedStateHandle
        .getStateFlow(KEY_CHART_GRANULARITY, ChartGranularity.DAILY)
    private val _pageNumber = MutableStateFlow(0)
    private val hasNext = MutableStateFlow(false)
    private val hasPrevious = MutableStateFlow(true)
    private val _leftSleepSelectionType = MutableStateFlow(SleepChartSelectionType.TOTAL_TIME)
    private val _rightSleepSelectionType =
        MutableStateFlow(SleepChartSelectionType.WAKE_UP_RESOURCES)
    private val _onShowRecoveryStateInfoSheet = MutableStateFlow<((InfoBottomSheet) -> Unit)?>(null)
    private val _onContributorClick = MutableStateFlow<((ContributorType) -> Unit)?>(null)
    private val _viewState = MutableStateFlow<RecoveryV2State>(RecoveryV2State.Loading)
    private val _isCoachExpanded = MutableStateFlow(false)

    private var _pageExposureSource: String? = null

    private var _trackButtonEventType: String? = null
    private var showHighlightJob: Job? = null
    private var viewDurationAccumulatedMs: Long = 0L
    private var viewDurationStartedAtMs: Long? = null
    private val fullyVisibleModuleNames: MutableSet<String> = linkedSetOf()
    
    private var lastPrimaryGraphTypeChange: SleepChartSelectionType? = null
    private var lastSecondaryGraphTypeChange: SleepChartSelectionType? = null
    private var primaryGraphTypeChanged = false
    private var secondaryGraphTypeChanged = false
    private var sleepComparisonGraphTypeChangeJob: Job? = null
    
    private var _sleepChartChangedPageName: String = AnalyticsPropertyValue.SleepChartChangedPageNameProperty.RECOVERY_DETAILS_SCREEN_SLEEP_MODEL
    
    private var _isRecoveryDetail: Boolean = false
    
    private val appLifecycleObserver = object : DefaultLifecycleObserver {
        override fun onStart(owner: LifecycleOwner) {
            startViewDuration()
        }

        override fun onStop(owner: LifecycleOwner) {
            stopAndReportViewDuration()
        }
    }

    private fun createTimeLabels(viewingDate: LocalDate): TimeLabels {
        val today = LocalDate.now()
        val yesterday = today.minusDays(1)

        val currentLabel = when (viewingDate) {
            today -> context.getString(R.string.date_picker_today)
            yesterday -> context.getString(R.string.date_picker_yesterday)
            else -> chartHighlightDateFormatterUseCase.getYearAbbreviatedMonthDate(viewingDate)
        }
        val previousLabel = when (viewingDate.minusDays(1)) {
            today -> context.getString(R.string.date_picker_today)
            yesterday -> context.getString(R.string.date_picker_yesterday)
            else -> chartHighlightDateFormatterUseCase.getYearAbbreviatedMonthDate(
                viewingDate.minusDays(
                    1
                )
            )
        }

        return TimeLabels(
            currentLabel = currentLabel,
            previousLabel = previousLabel
        )
    }

    private fun chartGranularityToGraphTimeRange(granularity: ChartGranularity): GraphTimeRange =
        when (granularity) {
            ChartGranularity.WEEKLY -> GraphTimeRange.CURRENT_WEEK
            ChartGranularity.MONTHLY -> GraphTimeRange.CURRENT_MONTH
            ChartGranularity.SIX_MONTHS -> GraphTimeRange.SIX_MONTHS
            ChartGranularity.SEVEN_DAYS -> GraphTimeRange.SEVEN_DAYS
            ChartGranularity.THIRTY_DAYS -> GraphTimeRange.THIRTY_DAYS
            ChartGranularity.YEARLY -> GraphTimeRange.CURRENT_YEAR
            else -> throw IllegalArgumentException("Unsupported chart granularity: $granularity")
        }

    private fun getPeriodsByPageNumber(
        pageNumber: Int,
        timeRange: GraphTimeRange
    ): ClosedRange<LocalDate> {
        val periods = trainingPeriodsCalculationUseCase(
            pageNumber = pageNumber,
            firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek,
            timeRange = timeRange,
        )
        return periods.firstPeriod
    }

    init {
        ProcessLifecycleOwner.get().lifecycle.addObserver(appLifecycleObserver)
        if (ProcessLifecycleOwner.get().lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)) {
            startViewDuration()
        }

        combine(
            _currentDate,
            _currentTimeGranularity,
            _pageNumber,
            ::updateViewData
        ).flowOn(coroutinesDispatchers.io)
            .launchIn(viewModelScope)
    }

    private suspend fun updateViewData(
        date: LocalDate,
        currentTimeGranularity: ChartGranularity,
        pageNumber: Int
    ) {
        _viewState.update { current ->
            if (current is RecoveryV2State.Loaded) {
                current.copy(isLoading = true)
            } else {
                current
            }
        }

        val (timeRange, actualDate) = if (currentTimeGranularity == ChartGranularity.DAILY) {
            val range = calculateDateRangeUseCase(
                chartGranularity = currentTimeGranularity,
                chartPageIndex = 0,
                chartPageCount = 1,
                today = date
            )
            val hasNextValue = !date.isEqual(LocalDate.now())
            hasNext.value = hasNextValue
            hasPrevious.value = true

            range to date
        } else {
            val graphTimeRange = chartGranularityToGraphTimeRange(currentTimeGranularity)
            val range = getPeriodsByPageNumber(pageNumber, graphTimeRange)

            val hasNextValue = !range.contains(LocalDate.now())
            hasNext.value = hasNextValue
            hasPrevious.value = true

            range to range.endInclusive
        }

        val datePickerData = if (currentTimeGranularity == ChartGranularity.DAILY) {
            val displayTitle = when {
                actualDate.isEqual(LocalDate.now()) -> context.getString(R.string.date_picker_today)
                actualDate.isEqual(
                    LocalDate.now().minusDays(1)
                ) -> context.getString(R.string.date_picker_yesterday)

                else -> dateFormatter.formatRelativeDate(actualDate)
            }
            DatePickerData(
                currentDate = actualDate,
                displayTitle = displayTitle,
                canNavigateForward = hasNext.value,
                canNavigateBack = hasPrevious.value,
                trainingDateRange = TrainingDateRange.CustomRange(displayTitle)
            )
        } else {
            val displayTitle = trainingDatePeriodFormatter.format(
                timeRange,
                chartGranularityToGraphTimeRange(currentTimeGranularity)
            )
            DatePickerData(
                currentDate = actualDate,
                displayTitle = displayTitle,
                canNavigateForward = hasNext.value,
                canNavigateBack = hasPrevious.value,
                trainingDateRange = TrainingDateRange.CustomRange(displayTitle)
            )
        }
        val enhancedDatePickerData =
            enhanceDatePickerData(datePickerData, actualDate, currentTimeGranularity)
        val (mainTimeGranularities, extraTimeGranularities) = getDefaultTimeGranularities()
        val timeLabels = createTimeLabels(actualDate)

        if (currentTimeGranularity == ChartGranularity.DAILY) {
            val dailyData = loadDailyRecoveryDataUseCase(
                fromDate = timeRange.start,
                toDate = timeRange.endInclusive
            ).first()

            _viewState.value = RecoveryV2State.Loaded(
                mainTimeGranularities = mainTimeGranularities,
                extraTimeGranularities = extraTimeGranularities,
                currentTimeGranularity = currentTimeGranularity,
                datePickerData = enhancedDatePickerData,
                timeLabels = timeLabels,
                isLoading = false,
                recoveryDailyStateData = dailyData,
                recoveryChartStateData = RecoveryChartStateData.None,
                showPrimaryComparisonGraphSelection = false,
                showSecondaryComparisonGraphSelection = false,
                recoveryCoachPhrasesIds = persistentListOf(),
                isCoachExpanded = false
            )
        } else {
            _viewState.value = RecoveryV2State.Loaded(
                mainTimeGranularities = mainTimeGranularities,
                extraTimeGranularities = extraTimeGranularities,
                currentTimeGranularity = currentTimeGranularity,
                datePickerData = enhancedDatePickerData,
                timeLabels = timeLabels,
                isLoading = true,
                recoveryDailyStateData = RecoveryDailyStateData.None,
                recoveryChartStateData = createDefaultRecoveryChartStateLoaded(),
                showPrimaryComparisonGraphSelection = false,
                showSecondaryComparisonGraphSelection = false,
                recoveryCoachPhrasesIds = persistentListOf(),
                isCoachExpanded = false
            )

            val chartStateData = fetchRecoveryChartDataUseCase(
                chartGranularity = currentTimeGranularity,
                from = timeRange.start,
                to = timeRange.endInclusive,
                leftSleepSelectionType = _leftSleepSelectionType.value,
                rightSleepSelectionType = _rightSleepSelectionType.value,
                displayTitle = datePickerData.displayTitle
            ).first()

            _viewState.update { current ->
                if (current is RecoveryV2State.Loaded) {
                    current.copy(
                        recoveryChartStateData = chartStateData,
                        isLoading = false,
                        showPrimaryComparisonGraphSelection = false,
                        showSecondaryComparisonGraphSelection = false,
                        recoveryCoachPhrasesIds = persistentListOf(),
                        isCoachExpanded = false
                    )
                } else {
                    current
                }
            }
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private val _recoveryCoachPhrasesIds: StateFlow<ImmutableList<Int>> = _currentDate
        .mapLatest { date ->
            try {
                val username = currentUserController.username
                if (username.isBlank()) {
                    return@mapLatest persistentListOf()
                }

                val canNavigateForward = !date.isEqual(LocalDate.now())
                if (canNavigateForward) {
                    return@mapLatest persistentListOf()
                }

                val currentWeekRange = calculateDateRangeUseCase(
                    chartGranularity = ChartGranularity.WEEKLY,
                    chartPageIndex = 0,
                    chartPageCount = 1,
                    today = date
                )

                val previousWeekRange = calculateDateRangeUseCase(
                    chartGranularity = ChartGranularity.WEEKLY,
                    chartPageIndex = 1,
                    chartPageCount = 1,
                    today = date
                )

                val currentPeriodAnalysis = trainingHubPeriodAnalysisUseCase.getAnalysisForPeriod(
                    username = username,
                    startDate = currentWeekRange.start,
                    endDateInclusive = currentWeekRange.endInclusive,
                    preCalculatedTSSSummaries = null
                )

                val previousPeriodAnalysis = trainingHubPeriodAnalysisUseCase.getAnalysisForPeriod(
                    username = username,
                    startDate = previousWeekRange.start,
                    endDateInclusive = previousWeekRange.endInclusive,
                    preCalculatedTSSSummaries = null
                )

                val sleepHrv = getSleepHrv(date)

                val coachFeedback = trainingHubSuuntoCoachUseCase.getCoachFeedback(
                    currentPeriodAnalysis = currentPeriodAnalysis,
                    comparisonPeriodAnalysis = previousPeriodAnalysis,
                    firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek,
                    isCurrentWeek = date >= LocalDate.now().minusDays(6),
                    sleepHrv = sleepHrv
                )

                getRecoveryCoachPhrasesIds(coachFeedback.toImmutableMap())
            } catch (e: Exception) {
                Timber.w(e, "Error loading recovery coach phrases")
                persistentListOf()
            }
        }.flowOn(coroutinesDispatchers.io)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = persistentListOf()
        )

    @OptIn(ExperimentalCoroutinesApi::class)
    private val _recoveryTipsResourceId: StateFlow<Int?> = _currentDate
        .mapLatest { date ->
            try {
                val canNavigateForward = !date.isEqual(LocalDate.now())
                if (canNavigateForward) {
                    return@mapLatest null
                }

                val recoveryIndex = getRecoveryIndex(date)
                getRecoveryTipsResourceId(recoveryIndex)
            } catch (e: Exception) {
                Timber.w(e, "Error loading recovery tips resource ID")
                null
            }
        }.flowOn(coroutinesDispatchers.io)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = null
        )

    val viewState: StateFlow<RecoveryV2State> = combine(
        _viewState,
        _recoveryCoachPhrasesIds,
        _recoveryTipsResourceId,
        _isCoachExpanded
    ) { currentState, recoveryPhrasesIds, recoveryTipsResourceId, isCoachExpanded ->
        when (currentState) {
            is RecoveryV2State.Loading -> currentState
            is RecoveryV2State.Loaded -> currentState.copy(
                recoveryCoachPhrasesIds = recoveryPhrasesIds,
                recoveryTipsResourceId = recoveryTipsResourceId,
                isCoachExpanded = isCoachExpanded
            )
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = RecoveryV2State.Loading
    )

    private suspend fun updateSleepChartData(
        leftSleepSelectionType: SleepChartSelectionType,
        rightSleepSelectionType: SleepChartSelectionType,
        currentTimeGranularity: ChartGranularity
    ) {
        if (currentTimeGranularity == ChartGranularity.DAILY) return

        val graphTimeRange = chartGranularityToGraphTimeRange(currentTimeGranularity)
        val timeRange = getPeriodsByPageNumber(_pageNumber.value, graphTimeRange)
        val actualDate = timeRange.endInclusive

        val displayTitle = trainingDatePeriodFormatter.format(timeRange, graphTimeRange)
        val datePickerData = DatePickerData(
            currentDate = actualDate,
            displayTitle = displayTitle,
            canNavigateForward = hasNext.value,
            canNavigateBack = hasPrevious.value,
            trainingDateRange = TrainingDateRange.CustomRange(displayTitle)
        )

        val updatedSleepComparisonChartData = loadSleepComparisonChartDataUseCase(
            chartGranularity = currentTimeGranularity,
            leftSelectionType = leftSleepSelectionType,
            rightSelectionType = rightSleepSelectionType,
            from = timeRange.start,
            to = timeRange.endInclusive,
            timeRange = datePickerData.displayTitle
        ).first()

        _viewState.update { current ->
            if (current !is RecoveryV2State.Loaded) return@update current

            val currentChartState = current.recoveryChartStateData as? RecoveryChartStateData.Loaded
                ?: return@update current

            val updatedChartStateData = currentChartState.copy(
                recoveryChartContributors = currentChartState.recoveryChartContributors?.copy(
                    sleepComparisonChartData = updatedSleepComparisonChartData
                )
            )

            current.copy(recoveryChartStateData = updatedChartStateData)
        }
    }

    fun onEvent(event: RecoveryV2Event) {
        when (event) {
            is RecoveryV2Event.NavigatePrevious -> onPreviousClick()
            is RecoveryV2Event.NavigateNext -> onNextClick()
            is RecoveryV2Event.BackToCurrent -> onBackToCurrentClick()

            is RecoveryV2Event.UpdateTimeGranularity -> {
                stopAndReportViewDuration()
                fullyVisibleModuleNames.clear()
                startViewDuration()
                
                savedStateHandle[KEY_CHART_GRANULARITY] = event.timeGranularity
                _currentDate.value = LocalDate.now()
                _pageNumber.value = 0
            }

            is RecoveryV2Event.ShowRecoveryStateInfoSheet -> {
                _onShowRecoveryStateInfoSheet.value?.invoke(event.infoBottomSheet)
                trackButtonClick(event.infoBottomSheet.analyticsClickName)
            }

            is RecoveryV2Event.ContributorClicked -> {
                _onContributorClick.value?.invoke(event.contributorType)
            }

            is RecoveryV2Event.ToggleCoachExpanded -> {
                _isCoachExpanded.update { !it }
            }

            is RecoveryV2Event.ShowSleepComparisonPrimaryGraphSelection -> {
                _viewState.update {
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showPrimaryComparisonGraphSelection = true)
                    } else it
                }
            }

            is RecoveryV2Event.HideSleepComparisonPrimaryGraphSelection -> {
                _viewState.update {
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showPrimaryComparisonGraphSelection = false)
                    } else it
                }
            }

            is RecoveryV2Event.ShowSleepComparisonSecondaryGraphSelection -> {
                _viewState.update {
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showSecondaryComparisonGraphSelection = true)
                    } else it
                }
            }

            is RecoveryV2Event.HideSleepComparisonSecondaryGraphSelection -> {
                _viewState.update {
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showSecondaryComparisonGraphSelection = false)
                    } else it
                }
            }

            is RecoveryV2Event.UpdateSleepLeftSelectionType -> {
                _leftSleepSelectionType.value = event.type
                _viewState.update {
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showPrimaryComparisonGraphSelection = false)
                    } else it
                }
                
                handleSleepComparisonGraphTypeChange(
                    graphType = event.type,
                    isPrimary = true
                )
                
                viewModelScope.launch {
                    updateSleepChartData(
                        leftSleepSelectionType = event.type,
                        rightSleepSelectionType = _rightSleepSelectionType.value,
                        currentTimeGranularity = savedStateHandle.chartGranularity
                    )
                }
            }

            is RecoveryV2Event.UpdateSleepRightSelectionType -> {
                _rightSleepSelectionType.value = event.type
                _viewState.update {
                    if (it is RecoveryV2State.Loaded) {
                        it.copy(showSecondaryComparisonGraphSelection = false)
                    } else it
                }
                
                handleSleepComparisonGraphTypeChange(
                    graphType = event.type,
                    isPrimary = false
                )
                
                viewModelScope.launch {
                    updateSleepChartData(
                        leftSleepSelectionType = _leftSleepSelectionType.value,
                        rightSleepSelectionType = event.type,
                        currentTimeGranularity = savedStateHandle.chartGranularity
                    )
                }
            }

            is RecoveryV2Event.ShowHighlight -> showHighlight(event)
            is RecoveryV2Event.HideHighlight -> hideHighlight()
            
            is RecoveryV2Event.ModuleFullyVisible -> {
                fullyVisibleModuleNames.add(event.moduleName)
            }
            
            is RecoveryV2Event.TrackButtonClick -> {
                trackButtonClick(event.buttonName)
            }
        }
    }

    fun setShowRecoveryStateInfoSheetCallback(callback: (InfoBottomSheet) -> Unit) {
        _onShowRecoveryStateInfoSheet.value = callback
    }

    fun setContributorClickCallback(callback: (ContributorType) -> Unit) {
        _onContributorClick.value = callback
    }

    fun setIsRecoveryDetail(isRecoveryDetail: Boolean) {
        _isRecoveryDetail = isRecoveryDetail
        updateRecoveryDetailDependentValues()
    }
    
    private fun updateRecoveryDetailDependentValues() {
        _pageExposureSource = if (_isRecoveryDetail) {
            AnalyticsPropertyValue.WidgetDetailPageExposureSourceProperty.DASHBOARD
        } else {
            null
        }
        
        _sleepChartChangedPageName = if (_isRecoveryDetail) {
            AnalyticsPropertyValue.SleepChartChangedPageNameProperty.RECOVERY_DETAILS_SCREEN_SLEEP_MODEL
        } else {
            AnalyticsPropertyValue.SleepChartChangedPageNameProperty.TRAINING_ZONE_RECOVERY_SLEEP_MODEL
        }
        
        _trackButtonEventType = if (_isRecoveryDetail) {
            AnalyticsEvent.WIDGET_DETAIL_PAGE_BUTTON_CLICK
        } else {
            AnalyticsEvent.TRAINING_ZONE_BUTTON_CLICK
        }
        
        trackRecoveryPageExposure()
    }

    private fun onPreviousClick() {
        when (savedStateHandle.chartGranularity) {
            ChartGranularity.DAILY -> {
                _currentDate.update { it.minusDays(1) }
            }

            else -> {
                _pageNumber.update { it - 1 }
            }
        }
        trackButtonClick(AnalyticsPropertyValue.TrainingZoneButtonClickProperty.LAST)
    }

    private fun onNextClick() {
        when (savedStateHandle.chartGranularity) {
            ChartGranularity.DAILY -> {
                _currentDate.update { currentDate ->
                    val newDate = currentDate.plusDays(1)
                    if (newDate.isAfter(LocalDate.now())) LocalDate.now() else newDate
                }
            }

            else -> {
                _pageNumber.update { (it + 1).coerceAtMost(0) }
            }
        }
        trackButtonClick(AnalyticsPropertyValue.TrainingZoneButtonClickProperty.NEXT)
    }

    private fun onBackToCurrentClick() {
        when (savedStateHandle.chartGranularity) {
            ChartGranularity.DAILY -> {
                _currentDate.update { LocalDate.now() }
            }

            else -> {
                _pageNumber.update { 0 }
            }
        }
    }

    private fun getRecoveryCoachPhrasesIds(
        coachFeedback: ImmutableMap<SuuntoCoachInsightType, CoachPhrase>?
    ): ImmutableList<Int> {
        val recoveryTypes = listOf(
            SuuntoCoachInsightType.OVERALL_RECOVERY,
            SuuntoCoachInsightType.SLEEP,
            SuuntoCoachInsightType.FEELING,
            SuuntoCoachInsightType.TSB,
            SuuntoCoachInsightType.HRV,
        )

        return coachFeedback
            ?.filterKeys { it.theme == SuuntoCoachInsightTypeTheme.RECOVERY }
            ?.filterKeys { recoveryTypes.contains(it) }
            ?.map { it.value.id }
            ?.toImmutableList()
            ?: persistentListOf()
    }

    private suspend fun getRecoveryIndex(date: LocalDate): Int? {
        return try {
            val dailyData = loadDailyRecoveryDataUseCase(
                fromDate = date,
                toDate = date
            ).first()

            when (dailyData) {
                is RecoveryDailyStateData.Loaded -> {
                    dailyData.recoveryStateData.recoveryScore.takeIf { it > 0 }
                }

                else -> null
            }
        } catch (e: Exception) {
            Timber.w(e, "Error getting recovery index for date: $date")
            null
        }
    }

    private fun getRecoveryTipsResourceId(recoveryIndex: Int?): Int? {
        return when (recoveryIndex) {
            in 0..19 -> R.string.recovery_tips_poor
            in 20..39 -> R.string.recovery_tips_fair
            in 40..59 -> R.string.recovery_tips_optimal
            in 60..79 -> R.string.recovery_tips_good
            in 80..100 -> R.string.recovery_tips_excellent
            else -> null
        }
    }

    private fun enhanceDatePickerData(
        originalData: DatePickerData,
        date: LocalDate,
        timeGranularity: ChartGranularity
    ): DatePickerData {
        val isDaily = timeGranularity == ChartGranularity.DAILY
        val isToday = date.isEqual(LocalDate.now())

        val dateTimeText = if (isDaily && isToday) {
            val currentTime = LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm"))
            context.getString(R.string.recovery_state_date_time, currentTime)
        } else if (isDaily) {
            dateFormatter.formatRelativeDate(date)
        } else {
            context.getString(R.string.recovery_daily_average)
        }

        return originalData.copy(
            dateTimeText = dateTimeText,
            canNavigateForward = hasNext.value,
            canNavigateBack = hasPrevious.value,
            isDaily = isDaily
        )
    }

    private suspend fun getSleepHrv(date: LocalDate) =
        runSuspendCatching {
            fetchSleepHrvUseCase.fetchAvgHrv(date)
        }.getOrElse { e ->
            Timber.w(e, "Fetching HRV failed for date: $date")
            null
        }

    private fun showHighlight(event: RecoveryV2Event.ShowHighlight) {
        showHighlightJob?.cancel()
        showHighlightJob = viewModelScope.launch {
            val currentData = _viewState.value as? RecoveryV2State.Loaded ?: return@launch
            val chartState = currentData.recoveryChartStateData as? RecoveryChartStateData.Loaded
                ?: return@launch

            when (event.contributorType) {
                null -> {
                    val highlightData = createRecoveryChartHighlightUseCase(
                        recoveryChartStateData = chartState,
                        contributorType = null,
                        entryX = event.entryX,
                        chartGranularity = currentData.currentTimeGranularity
                    )

                    val recoveryChartData = chartState.recoveryChartData ?: return@launch
                    val updatedRecoveryChartData =
                        recoveryChartData.copy(chartHighlight = highlightData)
                    val updatedChartState = chartState.copy(
                        recoveryChartData = updatedRecoveryChartData
                    )

                    _viewState.update {
                        if (it is RecoveryV2State.Loaded) {
                            it.copy(recoveryChartStateData = updatedChartState)
                        } else it
                    }
                }

                ContributorType.SLEEP -> {
                    val sleepData = chartState.recoveryChartContributors?.sleepComparisonChartData
                        ?: return@launch
                    val sleepHighlightData = createRecoverySleepComparisonHighlightUseCase(
                        sleepComparisonChartData = sleepData,
                        entryX = event.entryX,
                        chartGranularity = currentData.currentTimeGranularity
                    )
                    val updatedSleepData = sleepData.copy(chartHighlight = sleepHighlightData)
                    val updatedContributors = chartState.recoveryChartContributors.copy(
                        sleepComparisonChartData = updatedSleepData
                    )
                    val updatedChartState = chartState.copy(
                        recoveryChartContributors = updatedContributors
                    )

                    _viewState.update {
                        if (it is RecoveryV2State.Loaded) {
                            it.copy(recoveryChartStateData = updatedChartState)
                        } else it
                    }
                }

                else -> {
                    val highlightData = createRecoveryChartHighlightUseCase(
                        recoveryChartStateData = chartState,
                        contributorType = event.contributorType,
                        entryX = event.entryX,
                        chartGranularity = currentData.currentTimeGranularity
                    )

                    val commonContributors =
                        chartState.recoveryChartContributors?.commonChartContributors
                    if (commonContributors != null) {
                        val updatedContributors = commonContributors.map { contributor ->
                            if (contributor.contributorType == event.contributorType) {
                                contributor.copy(chartHighlight = highlightData)
                            } else {
                                contributor
                            }
                        }

                        val updatedChartContributors = chartState.recoveryChartContributors.copy(
                            commonChartContributors = updatedContributors
                        )

                        val updatedChartState = chartState.copy(
                            recoveryChartContributors = updatedChartContributors
                        )

                        _viewState.update {
                            if (it is RecoveryV2State.Loaded) {
                                it.copy(recoveryChartStateData = updatedChartState)
                            } else it
                        }
                    }
                }
            }
        }
    }

    private fun hideHighlight() {
        showHighlightJob?.cancel()

        val currentData = _viewState.value as? RecoveryV2State.Loaded ?: return
        val chartState =
            currentData.recoveryChartStateData as? RecoveryChartStateData.Loaded ?: return

        val updatedRecoveryChartData = chartState.recoveryChartData?.copy(
            chartHighlight = ChartHighlightViewData.None
        )

        val updatedSleepData = chartState.recoveryChartContributors?.sleepComparisonChartData?.copy(
            chartHighlight = RecoverySleepComparisonHighlightViewData.None
        )

        val updatedCommonContributors =
            chartState.recoveryChartContributors?.commonChartContributors?.map { contributor ->
                contributor.copy(chartHighlight = ChartHighlightViewData.None)
            }

        val updatedChartContributors = chartState.recoveryChartContributors?.copy(
            sleepComparisonChartData = updatedSleepData,
            commonChartContributors = updatedCommonContributors
        )

        val updatedChartState = chartState.copy(
            recoveryChartData = updatedRecoveryChartData,
            recoveryChartContributors = updatedChartContributors
        )

        _viewState.update {
            if (it is RecoveryV2State.Loaded) {
                it.copy(recoveryChartStateData = updatedChartState)
            } else it
        }
    }

    private fun startViewDuration() {
        if (viewDurationStartedAtMs == null) {
            viewDurationStartedAtMs = SystemClock.elapsedRealtime()
        }
    }

    private fun stopAndReportViewDuration() {
        val startedAt = viewDurationStartedAtMs ?: return
        val elapsed = SystemClock.elapsedRealtime() - startedAt
        viewDurationAccumulatedMs += elapsed
        viewDurationStartedAtMs = null

        val totalSeconds = (viewDurationAccumulatedMs / 1000).toInt()
        if (totalSeconds <= 0) return

        viewDurationAccumulatedMs = 0L

        val timeDim = savedStateHandle.chartGranularity.toAnalyticsTimeDim()
        val module = if (fullyVisibleModuleNames.isNotEmpty()) {
            fullyVisibleModuleNames.toList()
        } else {
            listOf(AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.RECOVERY_STATE)
        }
        
        val eventType = if (_isRecoveryDetail) {
            AnalyticsEvent.LEAVE_WIDGETS_DETAIL_PAGE
        } else {
            AnalyticsEvent.TRAINING_ZONE_TAB_LEAVE
        }
        
        recoveryAnalytics.trackLeavePage(
            pageName = AnalyticsPropertyValue.WidgetDetailPageExposureWidgetNameProperty.RECOVERY,
            timeDim = timeDim,
            browsingDuration = totalSeconds.toLong(),
            moduleNames = module,
            eventType = eventType
        )
    }
    
    private fun trackRecoveryPageExposure() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            val timeDim = savedStateHandle.chartGranularity.toAnalyticsTimeDim()

            val eventType = if (_isRecoveryDetail) {
                AnalyticsEvent.WIDGET_DETAIL_PAGE_EXPOSURE
            } else {
                AnalyticsEvent.TRAINING_ZONE_PAGE_EXPOSURE
            }
            
            recoveryAnalytics.trackPageExposure(
                pageName = AnalyticsPropertyValue.WidgetDetailPageExposureWidgetNameProperty.RECOVERY,
                timeDim = timeDim,
                source = _pageExposureSource,
                eventType = eventType
            )
        }
    }

    private fun handleSleepComparisonGraphTypeChange(
        graphType: SleepChartSelectionType,
        isPrimary: Boolean
    ) {
        if (isPrimary) {
            lastPrimaryGraphTypeChange = graphType
            primaryGraphTypeChanged = true
        } else {
            lastSecondaryGraphTypeChange = graphType
            secondaryGraphTypeChanged = true
        }
        
        if (sleepComparisonGraphTypeChangeJob?.isActive == true) {
            return
        }
        
        sleepComparisonGraphTypeChangeJob = viewModelScope.launch {
            delay(5000)
            reportSleepComparisonGraphTypeChange()
        }
    }

    private fun reportSleepComparisonGraphTypeChange() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            val timeDim = savedStateHandle.chartGranularity.toAnalyticsTimeDim()
            
            if (timeDim.isNotEmpty()) {
                val currentPrimaryGraphType = _leftSleepSelectionType.value
                val currentSecondaryGraphType = _rightSleepSelectionType.value
                
                val changedContent = when {
                    primaryGraphTypeChanged && secondaryGraphTypeChanged -> AnalyticsPropertyValue.ChangedContentProperty.ALL
                    primaryGraphTypeChanged -> AnalyticsPropertyValue.ChangedContentProperty.MAIN_GRAPH_TYPE
                    secondaryGraphTypeChanged -> AnalyticsPropertyValue.ChangedContentProperty.SUB_GRAPH_TYPE
                    else -> AnalyticsPropertyValue.ChangedContentProperty.ALL
                }
                
                val mainGraphTypeValue = currentPrimaryGraphType.toAnalyticsMainGraphTypeValue()
                val subGraphTypeValue = currentSecondaryGraphType.toAnalyticsSubGraphTypeValue()

                recoveryAnalytics.trackSleepComparisonGraphTypeChanged(
                    pageName = _sleepChartChangedPageName,
                    timeDim = timeDim,
                    changedContent = changedContent,
                    mainGraphType = mainGraphTypeValue,
                    subGraphType = subGraphTypeValue
                )
                
                primaryGraphTypeChanged = false
                secondaryGraphTypeChanged = false
                lastPrimaryGraphTypeChange = null
                lastSecondaryGraphTypeChange = null
            }
        }
    }

    private fun trackButtonClick(buttonName: String) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            val timeDim = savedStateHandle.chartGranularity.toAnalyticsTimeDim()
            recoveryAnalytics.trackButtonClick(
                eventType = _trackButtonEventType,
                pageName = AnalyticsPropertyValue.WidgetDetailPageExposureWidgetNameProperty.RECOVERY,
                timeDim = timeDim,
                buttonName = buttonName
            )
        }
    }

    override fun onCleared() {
        ProcessLifecycleOwner.get().lifecycle.removeObserver(appLifecycleObserver)
        stopAndReportViewDuration()
        sleepComparisonGraphTypeChangeJob?.cancel()
        super.onCleared()
    }

    private companion object {
        private const val KEY_CHART_GRANULARITY = "recovery_v2_chart_granularity"

        val SavedStateHandle.chartGranularity: ChartGranularity
            get() =
                get<ChartGranularity>(KEY_CHART_GRANULARITY) ?: ChartGranularity.DAILY

        fun getDefaultTimeGranularities(): Pair<ImmutableList<ChartGranularity>, ImmutableList<ChartGranularity>> {
            val mainTimeGranularities = persistentListOf(
                ChartGranularity.DAILY,
                ChartGranularity.WEEKLY,
                ChartGranularity.MONTHLY,
                ChartGranularity.SIX_MONTHS,
            )
            val extraTimeGranularities = persistentListOf(
                ChartGranularity.SEVEN_DAYS,
                ChartGranularity.THIRTY_DAYS,
                ChartGranularity.YEARLY
            )
            return Pair(mainTimeGranularities, extraTimeGranularities)
        }

        fun createDefaultRecoveryChartStateLoaded(): RecoveryChartStateData.Loaded {
            return RecoveryChartStateData.Loaded(
                recoveryChartData = RecoveryChartData(
                    recoveryScore = null,
                    recoveryZone = null,
                    recoveryChart = null,
                    chartHighlight = ChartHighlightViewData.None,
                ),
                recoveryChartContributors = RecoveryChartContributors(
                    sleepComparisonChartData = SleepComparisonChartData(
                        timeRange = null,
                        leftSelectionType = SleepChartSelectionType.SLEEP_DURATION,
                        leftValue = null,
                        rightSelectionType = SleepChartSelectionType.SLEEP_REGULARITY,
                        rightValue = null,
                        value = null,
                        valueType = null,
                        contributorType = ContributorType.SLEEP,
                        sleepChartData = null,
                        chartHighlight = RecoverySleepComparisonHighlightViewData.None,
                    ),
                    commonChartContributors = persistentListOf(
                        CommonChartContributor(
                            contributorType = ContributorType.HRV,
                            value = null,
                            valueType = null,
                            chartData = null,
                            chartHighlight = ChartHighlightViewData.None,
                        ),
                        CommonChartContributor(
                            contributorType = ContributorType.REST_HR,
                            value = null,
                            valueType = null,
                            chartData = null,
                            chartHighlight = ChartHighlightViewData.None,
                        ),
                        CommonChartContributor(
                            contributorType = ContributorType.RESOURCES,
                            value = null,
                            valueType = null,
                            chartData = null,
                            chartHighlight = ChartHighlightViewData.None,
                        )
                    )
                )
            )
        }
    }
}
