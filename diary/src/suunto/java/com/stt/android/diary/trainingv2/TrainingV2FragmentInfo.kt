package com.stt.android.diary.trainingv2

import androidx.fragment.app.Fragment
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.diary.DiaryFragmentInfo
import com.stt.android.home.diary.R
import javax.inject.Inject

class TrainingV2FragmentInfo @Inject constructor() : DiaryFragmentInfo {
    override fun clazz(): Class<out Fragment> = TrainingV2Fragment::class.java
    override fun getTitleResId(): Int = R.string.training_tab_title
    override fun getTabTypeForAnalytics(): String = AnalyticsPropertyValue.SuuntoDiaryType.TRAINING
}
