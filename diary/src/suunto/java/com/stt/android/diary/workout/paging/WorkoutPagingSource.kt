package com.stt.android.diary.workout.paging

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController.WORKOUTS_PAGE_SIZE
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.DaoFactory
import com.stt.android.data.source.local.TABLE_WORKOUT_HEADERS
import com.stt.android.diary.common.RoomTableObserver
import com.stt.android.diary.common.RoomTableObserverDelegate
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.GetPagedWorkoutHeadersUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import timber.log.Timber

class WorkoutPagingSource(
    scope: CoroutineScope,
    daoFactory: DaoFactory,
    private val getPagedWorkoutHeadersUseCase: GetPagedWorkoutHeadersUseCase,
    private val currentUserController: CurrentUserController,
    private val activityTypeFilter: ActivityTypeFilter?,
) : PagingSource<Int, WorkoutHeader>(), RoomTableObserver by RoomTableObserverDelegate() {
    init {
        initRoomTableObserver(setOf(TABLE_WORKOUT_HEADERS), this, daoFactory, scope)
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, WorkoutHeader> =
        withContext(IO) {
            // Initial load (key is null) is configured to load two pages worth of data,
            // and invalidations (key exists) expect to load data "around"
            // the key so load neighbouring pages with it
            val (firstPageToLoad, lastPageToLoad) = if (params is LoadParams.Refresh) {
                if (params.key != null) {
                    val key = params.key!!
                    (key - 1).coerceAtLeast(FIRST_PAGE) to (key + 1)
                } else {
                    FIRST_PAGE to params.loadSize / WORKOUTS_PAGE_SIZE
                }
            } else {
                (params.key ?: FIRST_PAGE) to (params.key ?: FIRST_PAGE)
            }
            val numPagesToLoad = 1 + lastPageToLoad - firstPageToLoad

            runSuspendCatching {
                val headers = (firstPageToLoad..lastPageToLoad).flatMap { pageNumber ->
                    val useCaseParams = if (activityTypeFilter is ActivityTypeFilter.OnlyActivityType) {
                        GetPagedWorkoutHeadersUseCase.Params.OnlyActivityType(
                            currentUserController.username,
                            activityTypeFilter.type.id,
                            pageNumber
                        )
                    } else {
                        val excludedTypes = (activityTypeFilter as? ActivityTypeFilter.ExcludeActivityTypes)
                            ?.types
                            ?.map { it.id }
                            ?.toSet()

                        GetPagedWorkoutHeadersUseCase.Params.ExcludeActivityTypes(
                            currentUserController.username,
                            excludedTypes ?: emptySet(),
                            pageNumber
                        )
                    }

                    getPagedWorkoutHeadersUseCase(useCaseParams)
                }

                LoadResult.Page(
                    itemsBefore = WORKOUTS_PAGE_SIZE * (firstPageToLoad - 1),
                    data = headers,
                    prevKey = if (firstPageToLoad == FIRST_PAGE) null else firstPageToLoad - 1,
                    nextKey = if (headers.size < (WORKOUTS_PAGE_SIZE * numPagesToLoad)) null else lastPageToLoad + 1
                )
            }.getOrElse { e ->
                Timber.w(e, "Error while loading workout headers")
                LoadResult.Error(e)
            }
        }

    override fun getRefreshKey(state: PagingState<Int, WorkoutHeader>): Int? {
        // We need to get the previous key (or next key if previous is null) of the page
        // that was closest to the most recently accessed index.
        // Anchor position is the most recently accessed index
        return state.anchorPosition?.let { anchorPosition ->
            state.closestPageToPosition(anchorPosition)?.prevKey?.plus(1)
                ?: state.closestPageToPosition(anchorPosition)?.nextKey?.minus(1)
        }
    }

    sealed class ActivityTypeFilter {
        class ExcludeActivityTypes(val types: Set<ActivityType>) : ActivityTypeFilter()
        class OnlyActivityType(val type: ActivityType) : ActivityTypeFilter()
    }

    companion object {
        private const val FIRST_PAGE = 1
    }
}
