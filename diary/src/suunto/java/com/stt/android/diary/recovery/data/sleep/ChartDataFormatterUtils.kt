package com.stt.android.diary.recovery.data.sleep

import android.content.Context
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.stt.android.diary.recovery.model.formatByGraphType
import com.stt.android.diary.recovery.model.secondsToHourMinute
import com.stt.android.diary.recovery.v2.SleepChartSelectionType
import kotlin.math.roundToInt
import kotlin.time.Duration
import com.stt.android.core.R as CR

object ChartDataFormatterUtils {

    fun formatByGraphType(
        context: Context,
        graphType: SleepChartSelectionType,
        average: Float,
    ): String {
        return average.formatByGraphType(context, graphType)
    }

    fun Long.formatSleepTime(context: Context) = buildAnnotatedString {
        val (hours, minutes) = secondsToHourMinute()
        if (hours > 0L || minutes == 0L) {
            withStyle(SpanStyle(fontSize = 24.sp)) {
                append(hours.toString())
            }
            withStyle(SpanStyle(fontSize = 12.sp)) {
                append(" ")
                append(context.getString(CR.string.hour))
            }
        }
        if (hours > 0L && minutes > 0L) {
            append(" ")
        }
        if (minutes > 0L) {
            withStyle(SpanStyle(fontSize = 24.sp)) {
                append(minutes.toString())
            }
            withStyle(SpanStyle(fontSize = 12.sp)) {
                append(" ")
                append(context.getString(CR.string.minute))
            }
        }
    }

    fun Float.toPercent() = (this * 100).roundToInt().toFloat()

    fun Long.toLocalDate() = java.time.Instant.ofEpochMilli(this)
        .atZone(java.time.ZoneId.systemDefault()).toLocalDate()

    fun Long.secondsFromStartOfDay(): Long {
        val zonedDateTime = java.time.Instant.ofEpochMilli(this)
            .atZone(java.time.ZoneId.systemDefault())
        return with(zonedDateTime) { hour * 3600L + minute * 60L + second }
    }

    // seconds to hours
    fun Float.toHours() = this / 3600f

    fun Duration.toHours(): Float = inWholeMilliseconds / 3600_000F
} 
