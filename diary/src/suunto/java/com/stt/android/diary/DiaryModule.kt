package com.stt.android.diary

import com.stt.android.analytics.TrendsAnalytics
import com.stt.android.diary.analytics.DefaultTrendsAnalytics
import com.stt.android.diary.common.DiaryGraphSetupDelegate
import com.stt.android.domain.diary.models.DiaryGraphSetup
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class DiaryModule {
    @Binds
    abstract fun bindTrendsAnalytics(analytics: DefaultTrendsAnalytics): TrendsAnalytics

    @Binds
    abstract fun bindDiaryTabData(data: DiaryGraphSetupDelegate): DiaryGraphSetup
}
