package com.stt.android.diary.insights

import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.AnalyticsProperties
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TrainingHubAnalyticsImpl
@Inject constructor(
    private val datahubAnalyticsTracker: DatahubAnalyticsTracker,
) : TrainingHubAnalytics {
    override fun trackTrainingZoneOverviewExpansion(
        sourceView: TrainingHubSourceView,
        isExpanded: Boolean
    ) {
        Timber.v("Sending ${EventName.TRAINING_ZONE_OVERVIEW_FOLDING.analyticsName} to Amplitude")
        AnalyticsProperties().apply {
            put(EventProperty.SOURCE_VIEW.analyticsName, sourceView.analyticsName)
            putYesNo(EventProperty.IS_FOLDED.analyticsName, !isExpanded)
        }.also {
            datahubAnalyticsTracker.trackEvent(
                EventName.TRAINING_ZONE_OVERVIEW_FOLDING.analyticsName,
                it
            )
        }
    }

    override fun trackTrainingZoneOverview(coachIds: List<Int>) {
        Timber.v("Sending ${EventName.TRAINING_ZONE_OVERVIEW.analyticsName} to Amplitude")
        if (coachIds.isEmpty()) {
            Timber.v("Attempts to send empty list of ids to trackTrainingZoneOverview")
            return
        }

        val sortedIds =
            coachIds.sorted().map { it.toString().padStart(3, '0') } // 12 -> 012, 112 -> 112

        AnalyticsProperties().apply {
            put(EventProperty.COACH_TEXTS.analyticsName, sortedIds)
            put(EventProperty.COACH_TEXTS_STRING.analyticsName, sortedIds.joinToString(","))
        }.also {
            datahubAnalyticsTracker.trackEvent(
                EventName.TRAINING_ZONE_OVERVIEW.analyticsName,
                it
            )
        }
    }

    override fun trackTrainingZoneOverviewWeekSwiped() {
        Timber.v("Sending ${EventName.TRAINING_ZONE_OVERVIEW_WEEK_SWIPED.analyticsName} to Amplitude")
        datahubAnalyticsTracker.trackEvent(EventName.TRAINING_ZONE_OVERVIEW_WEEK_SWIPED.analyticsName)
    }

    override fun trackTrainingZoneOverviewInfo(sourceView: TrainingHubSourceView) {
        Timber.v("Sending ${EventName.TRAINING_ZONE_OVERVIEW_INFO.analyticsName} to Amplitude")
        AnalyticsProperties().apply {
            put(EventProperty.SOURCE_VIEW.analyticsName, sourceView.analyticsName)
        }.also {
            datahubAnalyticsTracker.trackEvent(
                EventName.TRAINING_ZONE_OVERVIEW_INFO.analyticsName,
                it
            )
        }
    }
}

enum class TrainingHubSourceView(val analyticsName: String) {
    INTENSITY("Intensity"),
    TRAINING_LOAD("TrainingLoad"),
    VOLUME("Volume"),
    COACH("Coach"),
    IMPACT("Impact"),
    TRAINING_MODEL("TrainingModel"),
    PROGRESS("Progress"),
    HRV("HRV"),
    FORM("Form"),
    SLEEP("Sleep"),
    FEELING("Feeling")
}

private enum class EventName(val analyticsName: String) {
    TRAINING_ZONE_OVERVIEW_FOLDING("TrainingZoneOverviewFolding"),
    TRAINING_ZONE_OVERVIEW("TrainingZoneOverview"),
    TRAINING_ZONE_OVERVIEW_WEEK_SWIPED("TrainingZoneOverviewWeekSwiped"),
    TRAINING_ZONE_OVERVIEW_INFO("TrainingZoneOverviewInfo")
}

private enum class EventProperty(val analyticsName: String) {
    SOURCE_VIEW("SourceView"),
    COACH_TEXTS_STRING("CoachTextsString"),
    COACH_TEXTS("CoachTexts"),
    IS_FOLDED("IsFolded")
}
