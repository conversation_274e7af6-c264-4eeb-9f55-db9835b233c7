package com.stt.android.diary.recovery.model

import com.stt.android.analytics.AnalyticsPropertyValue

object RecoveryItemKeys {
    
    const val RECOVERY_COACH = "recovery_coach"
    
    const val BOTTOM_SPACER = "bottom_spacer"
    
    const val RECOVERY_STATE_HEADER = "recovery_state_header"
    
    const val RECOVERY_CHART = "recovery_chart"
    
    const val RECOVERY_STATE_CONTRIBUTORS_HEADER = "recovery_state_contributors_header"
    
    const val CONTRIBUTOR_SLEEP = "contributor_sleep"
    
    const val CONTRIBUTOR_HRV = "contributor_hrv"
    
    const val CONTRIBUTOR_TRAINING_FATIGUE = "contributor_training_fatigue"
    
    const val CONTRIBUTOR_RESOURCES = "contributor_resources"
    
    const val CHART_CONTRIBUTORS_HEADER = "chart_contributors_header"
    
    const val CHART_SLEEP_COMPARISON = "chart_sleep_comparison"

    const val CHART_CONTRIBUTOR_HRV = "chart_contributor_hrv"
    
    const val CHART_CONTRIBUTOR_TRAINING_FATIGUE = "chart_contributor_training_fatigue"
    
    const val CHART_CONTRIBUTOR_RESOURCES = "chart_contributor_resources"
    
    fun chartContributor(type: String) = "chart_contributor_${type.lowercase()}"
    
    fun getAnalyticsModuleName(itemKey: String): String? {
        return when (itemKey) {
            CONTRIBUTOR_SLEEP -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART1
            CONTRIBUTOR_HRV -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART2
            CONTRIBUTOR_TRAINING_FATIGUE -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART4
            CONTRIBUTOR_RESOURCES -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART5
            RECOVERY_CHART -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART1
            CHART_SLEEP_COMPARISON -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART2
            CHART_CONTRIBUTOR_HRV -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART3
            CHART_CONTRIBUTOR_RESOURCES -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART5
            else -> null
        }
    }
}
