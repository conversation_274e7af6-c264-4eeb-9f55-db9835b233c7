package com.stt.android.diary.recovery.composables

import android.content.Context
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.key
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.stt.android.chart.impl.screen.components.ChartHighlight
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.recovery.model.RecoveryItemKeys
import com.stt.android.diary.recovery.model.RecoverySleepComparisonHighlightData
import com.stt.android.diary.recovery.model.RecoverySleepComparisonHighlightViewData
import com.stt.android.diary.recovery.model.formatDuration
import com.stt.android.diary.recovery.model.formatPercent
import com.stt.android.diary.recovery.model.formatTime
import com.stt.android.diary.recovery.model.getColorRes
import com.stt.android.diary.recovery.model.getIconRes
import com.stt.android.diary.recovery.model.getNameRes
import com.stt.android.diary.recovery.model.getTitleRes
import com.stt.android.diary.recovery.model.valueType
import com.stt.android.diary.recovery.v2.CommonChartContributor
import com.stt.android.diary.recovery.v2.ContributorType
import com.stt.android.diary.recovery.v2.RecoveryChartContributors
import com.stt.android.diary.recovery.v2.RecoveryV2Event
import com.stt.android.diary.recovery.v2.SleepChartSelectionType
import com.stt.android.diary.recovery.v2.SleepComparisonChartData
import com.stt.android.home.diary.InfoBottomSheet
import com.stt.android.home.diary.R
import com.stt.android.utils.sumByFloat
import kotlin.math.roundToLong
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

internal fun LazyListScope.recoveryStateContributorsChartItems(
    chartContributors: RecoveryChartContributors,
    onInfoClick: (InfoBottomSheet) -> Unit,
    onEvent: (RecoveryV2Event) -> Unit = {},
) {
    item(key = RecoveryItemKeys.CHART_CONTRIBUTORS_HEADER) {
        Text(
            text = stringResource(R.string.recovery_state_contributors),
            style = MaterialTheme.typography.bodyXLargeBold,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.medium
                )
        )
    }

    chartContributors.sleepComparisonChartData?.let { sleepData ->
        item(key = RecoveryItemKeys.CHART_SLEEP_COMPARISON) {
            SleepComparisonChart(
                sleepData = sleepData,
                onEvent = onEvent
            )
        }
    }

    chartContributors.commonChartContributors?.forEach { contributor ->
        val itemKey = when (contributor.contributorType) {
            ContributorType.HRV -> RecoveryItemKeys.CHART_CONTRIBUTOR_HRV
            ContributorType.TRAINING_FATIGUE -> RecoveryItemKeys.CHART_CONTRIBUTOR_TRAINING_FATIGUE
            ContributorType.RESOURCES -> RecoveryItemKeys.CHART_CONTRIBUTOR_RESOURCES
            else -> RecoveryItemKeys.chartContributor(contributor.contributorType.name)
        }
        
        item(key = itemKey) {
            CommonContributorChart(
                contributor = contributor,
                onInfoClick = onInfoClick,
                onEvent = onEvent
            )
        }
    }
}

@Composable
private fun SleepComparisonChart(
    sleepData: SleepComparisonChartData,
    onEvent: (RecoveryV2Event) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        ContributorHeader(
            iconRes = BaseR.drawable.sleep_fill_32dp,
            headerRes = R.string.contributors_content_sleep,
            colorRes = BaseR.color.dashboard_widget_sleep,
            onInfoClick = null,
            onClick = {
                onEvent(RecoveryV2Event.ContributorClicked(ContributorType.SLEEP))
            }
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.spacing.medium)
        ) {
            Text(
                text = sleepData.value ?: AnnotatedString("--"),
                style = MaterialTheme.typography.titleLarge.copy(fontWeight = FontWeight.Bold),
            )

            Text(
                text = sleepData.valueType ?: stringResource(ContributorType.SLEEP.valueType()),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.nearBlack
            )
        }

        Box {
            SleepComparisonSelection(
                chartTimeRange = sleepData.timeRange ?: "Loading...",
                primaryGraphType = sleepData.leftSelectionType,
                primaryColor = colorResource(id = BaseR.color.dashboard_widget_sleep),
                primaryValue = sleepData.leftValue ?: "--",
                secondaryGraphType = sleepData.rightSelectionType,
                secondaryColor = colorResource(id = CR.color.near_black),
                secondaryValue = sleepData.rightValue ?: "--",
                onEvent = onEvent,
                modifier = Modifier.padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.small
                )
            )

            RecoverySleepComparisonHighlight(
                viewData = sleepData.chartHighlight,
                primaryGraphType = sleepData.leftSelectionType,
                primaryColor = colorResource(id = BaseR.color.dashboard_widget_sleep),
                secondaryGraphType = sleepData.rightSelectionType,
                secondaryColor = colorResource(id = CR.color.near_black),
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(horizontal = MaterialTheme.spacing.medium, vertical = MaterialTheme.spacing.small)
            )
        }
        key(sleepData.sleepChartData) {
            RecoverySleepComparisonChart(
                chartData = sleepData.sleepChartData,
                onEntrySelected = { entryX ->
                    onEvent(RecoveryV2Event.ShowHighlight(ContributorType.SLEEP, entryX))
                },
                onNoEntrySelected = {
                    onEvent(RecoveryV2Event.HideHighlight)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                        bottom = MaterialTheme.spacing.small
                    )
            )
        }
    }
}

@Composable
private fun CommonContributorChart(
    contributor: CommonChartContributor,
    onInfoClick: (InfoBottomSheet) -> Unit,
    onEvent: (RecoveryV2Event) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        ContributorHeader(
            iconRes = contributor.contributorType.getIconRes(),
            headerRes = contributor.contributorType.getTitleRes(),
            onInfoClick = {
                val infoBottomSheet = when (contributor.contributorType) {
                    ContributorType.HRV -> InfoBottomSheet.RECOVERY_INFO_HRV
                    ContributorType.REST_HR -> InfoBottomSheet.RECOVERY_INFO_RHR
                    ContributorType.MIN_HR -> InfoBottomSheet.RECOVERY_INFO_MIN_HR
                    ContributorType.TRAINING_FATIGUE -> InfoBottomSheet.RECOVERY_INFO_TRAINING_FATIGUE
                    ContributorType.RESOURCES -> InfoBottomSheet.RECOVERY_INFO_RESOURCES
                    ContributorType.SLEEP -> InfoBottomSheet.TRAINING_HUB_RECOVERY_STATE
                }
                onInfoClick(infoBottomSheet)
            },
            colorRes = contributor.contributorType.getColorRes()
        )

        Box {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(77.dp)
                    .padding(MaterialTheme.spacing.medium)
            ) {
                Text(
                    text = contributor.value ?: AnnotatedString("--"),
                    style = MaterialTheme.typography.titleLarge.copy(fontWeight = FontWeight.Bold),
                )

                Text(
                    text = contributor.valueType
                        ?: stringResource(contributor.contributorType.valueType()),
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (contributor.contributorType == ContributorType.RESOURCES) {
                        MaterialTheme.colorScheme.darkGrey
                    } else {
                        MaterialTheme.colorScheme.nearBlack
                    }
                )
            }

            ChartHighlight(
                viewData = contributor.chartHighlight,
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.spacing.medium)
                    .align(Alignment.BottomCenter),
            )
        }

        key(contributor.chartData) {
            CommonChart(
                chartData = contributor.chartData,
                onEntrySelected = { entryX ->
                    onEvent(RecoveryV2Event.ShowHighlight(contributor.contributorType, entryX))
                },
                onNoEntrySelected = {
                    onEvent(RecoveryV2Event.HideHighlight)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                        bottom = MaterialTheme.spacing.medium
                    )
            )
        }
    }
}

@Composable
private fun ContributorHeader(
    @StringRes headerRes: Int?,
    @DrawableRes iconRes: Int?,
    @ColorRes colorRes: Int?,
    onInfoClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        modifier = modifier
            .fillMaxWidth()
            .padding(
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.xsmall,
                top = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.xsmall
            )
            .clickableThrottleFirst(enabled = onClick != null, onClick = onClick ?: {}),
    ) {
        iconRes?.let {
            Icon(
                painter = painterResource(iconRes),
                tint = colorResource(colorRes ?: BaseR.color.transparent),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            )
        }
        headerRes?.let {
            Text(
                text = stringResource(headerRes),
                style = MaterialTheme.typography.bodyMegaBold,
                modifier = Modifier
                    .weight(1f)
            )
        }
        onInfoClick?.let {
            IconButton(onClick = onInfoClick) {
                Icon(
                    painter = painterResource(BaseR.drawable.ic_info_outline),
                    contentDescription = null,
                    modifier = Modifier.size(MaterialTheme.iconSizes.small),
                )
            }
        }
        onClick?.let {
            Icon(
                painter = painterResource(CR.drawable.ic_chevron_right_fill),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            )
        }
    }
}

@Composable
private fun RecoverySleepComparisonHighlight(
    viewData: RecoverySleepComparisonHighlightViewData,
    primaryGraphType: SleepChartSelectionType,
    primaryColor: Color,
    secondaryGraphType: SleepChartSelectionType,
    secondaryColor: Color,
    modifier: Modifier = Modifier,
) {
    if (viewData !is RecoverySleepComparisonHighlightViewData.Loaded) return

    val highlightData = viewData.highlightData.collectAsState(initial = null).value ?: return

    val context = LocalContext.current

    Row(
        modifier = modifier
            .graphicsLayer(
                shadowElevation = with(LocalDensity.current) { 2.dp.toPx() },
                shape = RoundedCornerShape(8.dp),
                clip = true,
            )
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        formatRecoveryHighlightByGraphType(
            context,
            primaryGraphType,
            highlightData.primaryEntries
        )?.let {
            RecoverySleepComparisonHighlightSegment(
                modifier = Modifier.weight(1f),
                dotColor = primaryColor,
                title = stringResource(primaryGraphType.getNameRes()),
                value = it,
                date = highlightData.date,
            )
        } ?: run {
            Spacer(modifier = Modifier.weight(1f))
        }
        formatRecoveryHighlightByGraphType(
            context,
            secondaryGraphType,
            highlightData.secondaryEntries
        )?.let {
            RecoverySleepComparisonHighlightSegment(
                modifier = Modifier.weight(1f),
                dotColor = secondaryColor,
                title = stringResource(secondaryGraphType.getNameRes()),
                value = it,
                date = highlightData.date,
                alignStart = false,
            )
        } ?: run {
            Spacer(modifier = Modifier.weight(1f))
        }
    }
}

@Composable
private fun RecoverySleepComparisonHighlightSegment(
    dotColor: Color,
    title: String,
    value: String,
    date: String,
    modifier: Modifier = Modifier,
    alignStart: Boolean = true,
) {
    val layoutDirection = if (alignStart) LayoutDirection.Ltr else LayoutDirection.Rtl
    CompositionLocalProvider(LocalLayoutDirection provides layoutDirection) {
        Row(
            modifier = modifier,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Box(
                modifier = Modifier
                    .padding(end = MaterialTheme.spacing.small)
                    .clip(CircleShape)
                    .background(dotColor)
                    .size(8.dp),
            )
            CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
                Column(
                    modifier = Modifier.weight(1f),
                    horizontalAlignment = if (alignStart) Alignment.Start else Alignment.End,
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.body,
                        color = MaterialTheme.colorScheme.secondary,
                    )
                    Text(
                        text = value,
                        style = MaterialTheme.typography.bodyLargeBold,
                        color = MaterialTheme.colorScheme.onSurface,
                    )
                    Text(
                        text = date,
                        style = MaterialTheme.typography.body,
                        color = MaterialTheme.colorScheme.secondary,
                    )
                }
            }
        }
    }
}

private fun formatRecoveryHighlightByGraphType(
    context: Context,
    graphType: SleepChartSelectionType,
    entries: List<RecoverySleepComparisonHighlightData.Entry>,
) = when (graphType) {
    SleepChartSelectionType.SLEEP_DURATION,
    SleepChartSelectionType.NAP_DURATION,
    SleepChartSelectionType.TOTAL_TIME,
    SleepChartSelectionType.TRAINING_DURATION -> entries.sumByFloat { it.high * 3600f }
        .roundToLong()
        .formatDuration(context)

    SleepChartSelectionType.MAX_SLEEP_SPO2,
    SleepChartSelectionType.WAKE_UP_RESOURCES -> entries.sumByFloat { it.high }
        .takeIf { it > 0f }?.formatPercent()

    SleepChartSelectionType.MIN_SLEEP_HR,
    SleepChartSelectionType.AVG_SLEEP_HR -> entries.sumByFloat { it.high }
        .takeIf { it > 0f }?.formatBpm(context)

    SleepChartSelectionType.SLEEP_REGULARITY -> {
        val entry = entries.getOrNull(0)
        if (entry != null) {
            "${entry.low.toLong().formatTime()}-${entry.high.toLong().formatTime()}"
        } else null
    }

    SleepChartSelectionType.NONE -> null
}

private fun Float.formatBpm(context: Context): String {
    return "${this.toInt()} ${context.getString(BaseR.string.heart_unit)}"
}
