package com.stt.android.diary.recovery.v2

import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.eventtracking.EventTracker
import javax.inject.Inject

class RecoveryV2Analytics @Inject constructor(
    private val eventTracker: EventTracker,
) {

    fun trackPageExposure(
        pageName: String,
        timeDim: String?,
        source: String?,
        eventType: String
    ) {
        val eventProperties = mutableMapOf<String, String>()
        eventProperties[AnalyticsEventProperty.WIDGET_NAME] = pageName
        eventProperties[AnalyticsEventProperty.TIME_DIM] = timeDim ?: ""
        source?.let { eventProperties[AnalyticsEventProperty.SOURCE] = it }

        eventTracker.trackEvent(
            eventType,
            eventProperties
        )
    }

    fun trackLeavePage(
        pageName: String,
        timeDim: String?,
        browsingDuration: Long,
        moduleNames: List<String>?,
        eventType: String
    ) {
        val eventProperties = mutableMapOf<String, Any>()
        eventProperties[AnalyticsEventProperty.WIDGET_NAME] = pageName
        eventProperties[AnalyticsEventProperty.TIME_DIM] = timeDim ?: ""
        eventProperties[AnalyticsEventProperty.BROWSING_DURATION] = browsingDuration
        eventProperties[AnalyticsEventProperty.MODULE_NAME] = moduleNames ?: listOf(AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.RECOVERY_STATE)

        eventTracker.trackEvent(
            eventType,
            eventProperties
        )
    }

    fun trackSleepComparisonGraphTypeChanged(
        pageName: String,
        timeDim: String?,
        changedContent: String,
        mainGraphType: String?,
        subGraphType: String?
    ) {
        val eventProperties = mutableMapOf<String, String>()
        eventProperties[AnalyticsEventProperty.WIDGET_NAME] = pageName
        eventProperties[AnalyticsEventProperty.TIME_DIM] = timeDim ?: ""
        eventProperties[AnalyticsEventProperty.CHANGED_CONTENT] = changedContent
        
        if (mainGraphType != null && subGraphType != null) {
            eventProperties[AnalyticsEventProperty.MAIN_GRAPH_TYPE] = mainGraphType
            eventProperties[AnalyticsEventProperty.SUB_GRAPH_TYPE] = subGraphType
        }

        eventTracker.trackEvent(
            AnalyticsEvent.SLEEP_CHART_CHANGED,
            eventProperties
        )
    }

    fun trackButtonClick(
        eventType: String?,
        pageName: String,
        timeDim: String?,
        buttonName: String,
    ) {
        if (eventType.isNullOrBlank() || timeDim.isNullOrBlank()) {
            return
        }
        
        val eventProperties = mutableMapOf<String, String>()
        eventProperties[AnalyticsEventProperty.PAGE_NAME] = pageName
        eventProperties[AnalyticsEventProperty.TIME_DIM] = timeDim
        eventProperties[AnalyticsEventProperty.BUTTON_NAME] = buttonName

        eventTracker.trackEvent(
            eventType,
            eventProperties
        )
    }
}
