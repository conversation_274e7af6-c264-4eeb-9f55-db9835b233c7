package com.stt.android.diary.dailyhealth.data

import android.content.Context
import androidx.annotation.ColorInt
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.chart.impl.model.valueTypeRes
import com.stt.android.controllers.UserSettingsController
import com.stt.android.data.toEpochMilli
import com.stt.android.diary.dailyhealth.HealthItem
import com.stt.android.diary.dailyhealth.ValueInfo
import com.stt.android.diary.dailyhealth.usecase.CreateTimeInfoUseCase
import com.stt.android.diary.recovery.data.minutesSinceEpoch
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.averageOfDouble
import com.stt.android.utils.toEpochMilli
import com.suunto.algorithms.data.HeartRate
import com.suunto.algorithms.data.HeartRate.Companion.hz
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject
import kotlin.math.ceil
import kotlin.math.roundToInt
import kotlin.time.Duration.Companion.minutes
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR
import com.stt.android.diary.dailyhealth.ChartType as DailyHealthChartType
import com.stt.android.home.diary.R as DiaryR

class HeartRateChartDataLoader @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val trendDataRepository: TrendDataRepository,
    private val createTimeInfoUseCase: CreateTimeInfoUseCase,
    private val userSettingsController: UserSettingsController,
) {
    private companion object {
        const val NO_DATA_VALUE = -1

        private const val MIN_Y = 40.0
        private const val DEFAULT_MAX_Y = 100.0
        private const val LOW_HEART_RATE_THRESHOLD = 50

        private const val GRID_STEP_MULTIPLE = 10.0
        private const val CHART_DIVISIONS = 3.0
    }

    fun formatHighlightData(y: Float?): String = y?.let { value ->
        if (value <= 0f) {
            appContext.getString(BaseR.string.widget_no_data_title)
        } else {
            "${value.toInt()} ${appContext.getString(CR.string.bpm)}"
        }
    } ?: appContext.getString(BaseR.string.widget_no_data_title)

    fun formatHighlightDataRange(low: Float?, high: Float?): String {
        val unit = appContext.getString(CR.string.bpm)

        return when {
            low == null || high == null -> appContext.getString(BaseR.string.widget_no_data_title)
            low <= 0f || high <= 0f -> appContext.getString(BaseR.string.widget_no_data_title)
            low == high -> "${low.toInt()} $unit"
            else -> "${low.toInt()}-${high.toInt()} $unit"
        }
    }

    suspend fun loadDailyHealthItem(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate
    ): HealthItem {
        val trendDataList = loadTrendData(from, to)

        val chartColor = getChartColor()

        val chartSeriesList = createChartSeries(
            chartGranularity = chartGranularity,
            from = from,
            to = to,
            trendDataList = trendDataList,
            chartColor = chartColor
        )

        val chartData = ChartData(
            chartContent = ChartContent.HEART_RATE,
            chartGranularity = chartGranularity,
            series = chartSeriesList.toImmutableList(),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            currentValues = persistentListOf(),
            colorIndicator = null,
        )

        val valueInfo = ValueInfo(
            label = appContext.getString(ChartContent.HEART_RATE.valueTypeRes(chartGranularity)),
            value = chartSeriesList.firstOrNull()?.value ?: AnnotatedString("--"),
            timeInfo = createTimeInfoUseCase(chartGranularity, from, to)
        )

        return HealthItem(
            chartType = DailyHealthChartType.HEART_RATE,
            iconRes = DiaryR.drawable.ic_heart_fill,
            titleRes = DiaryR.string.training_hub_intensity_type_heart_rate,
            valueInfo = valueInfo,
            chartData = chartData,
            hasInfoButton = true
        )
    }

    private interface HeartRateSeriesStrategy {
        fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
        ): List<ChartData.Series>
    }

    private val seriesStrategies: Map<ChartGranularity, HeartRateSeriesStrategy> = mapOf(
        ChartGranularity.DAILY to DailyHeartRateSeriesStrategy(),
        ChartGranularity.WEEKLY to DayByDayHeartRateSeriesStrategy(),
        ChartGranularity.MONTHLY to DayByDayHeartRateSeriesStrategy(),
        ChartGranularity.YEARLY to MonthlyHeartRateSeriesStrategy(),
        ChartGranularity.SEVEN_DAYS to DayByDayHeartRateSeriesStrategy(),
        ChartGranularity.THIRTY_DAYS to DayByDayHeartRateSeriesStrategy(),
        ChartGranularity.SIX_WEEKS to DayByDayHeartRateSeriesStrategy(),
        ChartGranularity.SIX_MONTHS to WeeklyHeartRateSeriesStrategy(),
        ChartGranularity.EIGHT_YEARS to YearlyHeartRateSeriesStrategy(),
    )

    @ColorInt
    private fun getChartColor(): Int =
        appContext.getColor(BaseR.color.dashboard_widget_minimum_heart_rate)

    private fun createChartSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        chartColor: Int,
    ): List<ChartData.Series> {
        val validHeartRates = trendDataList.mapNotNull { it.hr?.hz }.filter { it.inBpm > 0 }

        if (validHeartRates.isEmpty()) {
            return listOf(createEmptyChartSeries(chartGranularity, from, to, chartColor))
        }

        return seriesStrategies[chartGranularity]
            ?.createSeries(from, to, trendDataList, chartColor)
            ?: throw IllegalArgumentException("Unsupported granularity: $chartGranularity")
    }

    private fun createEmptyChartSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        chartColor: Int,
    ): ChartData.Series {
        val (minX, maxX) = getAxisRangeForGranularity(chartGranularity, from, to)

        return ChartData.Series(
            chartType = ChartType.LINE,
            color = chartColor,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = 40.0,
                maxY = 160.0,
            ),
            entries = persistentListOf(),
            value = formatHeartRateRangeValue(0, 0),
            candlestickEntries = persistentListOf(),
            lineConfig = null,
            backgroundRegion = null,
            groupStackBarStyle = null,
        )
    }

    private fun getAxisRangeForGranularity(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate
    ): Pair<Double, Double> {
        return when (chartGranularity) {
            ChartGranularity.DAILY -> {
                val minX = from.atStartOfDay().minutesSinceEpoch
                val maxX = to.atEndOfDay().minutesSinceEpoch - 10L
                minX.toDouble() to maxX.toDouble()
            }

            ChartGranularity.YEARLY -> {
                from.withDayOfMonth(1).epochMonth.toDouble() to to.withDayOfMonth(1).epochMonth.toDouble()
            }

            ChartGranularity.MONTHLY -> {
                from.toEpochDay().toDouble() to to.toEpochDay().toDouble()
            }

            ChartGranularity.SIX_MONTHS -> {
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                val adjustedFrom = from.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                adjustedFrom.toEpochDay().toDouble() to to.toEpochDay().toDouble()
            }

            ChartGranularity.EIGHT_YEARS -> {
                from.year.toDouble() to to.year.toDouble()
            }

            else -> {
                from.toEpochDay().toDouble() to to.toEpochDay().toDouble()
            }
        }
    }

    private fun adjustYAxisRangeForCandleChart(
        minHeartRate: Int,
        maxHeartRate: Int
    ): Pair<Double, Double> {
        if (maxHeartRate <= LOW_HEART_RATE_THRESHOLD) {
            return Pair(MIN_Y, DEFAULT_MAX_Y)
        }
        val candidateMinY =
            kotlin.math.floor(minHeartRate.toDouble() / GRID_STEP_MULTIPLE) * GRID_STEP_MULTIPLE
        val candidateMaxY = ceil(maxHeartRate.toDouble() / GRID_STEP_MULTIPLE) * GRID_STEP_MULTIPLE

        val minRequiredRange = candidateMaxY - candidateMinY
        val minRequiredStep = minRequiredRange / CHART_DIVISIONS
        val alignedStep = ceil(minRequiredStep / GRID_STEP_MULTIPLE) * GRID_STEP_MULTIPLE

        val finalMinY = candidateMinY.coerceAtLeast(MIN_Y)
        val finalMaxY = finalMinY + CHART_DIVISIONS * alignedStep
        return Pair(finalMinY, finalMaxY)
    }

    private inner class DailyHeartRateSeriesStrategy : HeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
        ): List<ChartData.Series> {
            val allOriginalHeartRates = mutableListOf<HeartRate>()
            val allDataPoints = mutableListOf<ChartData.Entry>()

            val timeWindowMap = groupTrendDataByMinutes(
                from = from,
                to = to,
                trendDataList = trendDataList,
                intervalMillis = 10.minutes.inWholeMilliseconds,
            )

            timeWindowMap.entries.sortedBy { it.key }.forEach { (_, trendsInWindow) ->
                val heartRatesInWindow = trendsInWindow.mapNotNull { trendData ->
                    trendData.hr
                        ?.takeIf { it > 0.0 }
                        ?.hz
                }

                if (heartRatesInWindow.isNotEmpty()) {
                    val avgHrInBpm = heartRatesInWindow.averageOfDouble(HeartRate::inBpm)
                    allOriginalHeartRates.addAll(heartRatesInWindow)

                    val firstTrend =
                        trendsInWindow.minByOrNull { it.timestamp } ?: trendsInWindow.first()
                    val x = firstTrend.timeISO8601.minutesSinceEpoch

                    allDataPoints.add(ChartData.Entry(x = x, y = avgHrInBpm))
                }
            }

            val minX = from.atStartOfDay().minutesSinceEpoch
            val maxX = to.atEndOfDay().minutesSinceEpoch - 10L

            val minBpm = allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0
            val maxBpm = allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForCandleChart(minBpm, maxBpm)

            val dailyLineChartConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = false,
                showAreaFill = true,
                areaAlpha = 0.2f
            )

            val axisRange = ChartData.AxisRange(
                minX = minX.toDouble(),
                maxX = maxX.toDouble(),
                minY = minY,
                maxY = maxY,
            )

            val valueFormatted = formatHeartRateRangeValue(minBpm, maxBpm)

            if (allDataPoints.isEmpty()) {
                return listOf(
                    ChartData.Series(
                        chartType = ChartType.LINE,
                        color = color,
                        axisRange = axisRange,
                        entries = persistentListOf(),
                        value = valueFormatted,
                        candlestickEntries = persistentListOf(),
                        lineConfig = dailyLineChartConfig,
                        backgroundRegion = null,
                        groupStackBarStyle = null,
                    )
                )
            }

            val seriesList = mutableListOf<ChartData.Series>()
            val currentSeriesEntries = mutableListOf<ChartData.Entry>()

            val gapThresholdMinutes = 30L

            for (i in allDataPoints.indices) {
                val currentPoint = allDataPoints[i]
                currentSeriesEntries.add(currentPoint)

                val isLastPoint = i == allDataPoints.size - 1
                val hasGap = if (!isLastPoint) {
                    val nextPoint = allDataPoints[i + 1]
                    (nextPoint.x - currentPoint.x) > gapThresholdMinutes
                } else {
                    false
                }

                if (isLastPoint || hasGap) {
                    seriesList.add(
                        ChartData.Series(
                            chartType = ChartType.LINE,
                            color = color,
                            axisRange = axisRange,
                            entries = currentSeriesEntries.toImmutableList(),
                            value = valueFormatted,
                            candlestickEntries = persistentListOf(),
                            lineConfig = dailyLineChartConfig,
                            backgroundRegion = null,
                            groupStackBarStyle = null,
                        )
                    )

                    currentSeriesEntries.clear()
                }
            }

            return seriesList
        }
    }

    private inner class DayByDayHeartRateSeriesStrategy : HeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
        ): List<ChartData.Series> {
            val entries = mutableListOf<ChartData.Entry>()
            val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            val allOriginalHeartRates = mutableListOf<HeartRate>()

            val trendDataByDay = trendDataList.groupBy { it.timeISO8601.toLocalDate() }

            var currentDate = from
            while (currentDate <= to) {
                val dailyTrends = trendDataByDay[currentDate] ?: emptyList()
                val dailyHeartRates = dailyTrends.mapNotNull { it.hr?.hz }

                val x = currentDate.toEpochDay()

                if (dailyHeartRates.isNotEmpty()) {
                    allOriginalHeartRates.addAll(dailyHeartRates)

                    val avgHrInBpm = dailyHeartRates.averageOfDouble(HeartRate::inBpm)
                    val minHr = dailyHeartRates.minOrNull()?.inBpm ?: avgHrInBpm
                    val maxHr = dailyHeartRates.maxOrNull()?.inBpm ?: avgHrInBpm

                    entries.add(ChartData.Entry(x = x, y = avgHrInBpm))

                    candlestickEntries.add(
                        ChartData.CandlestickEntry(
                            x = x,
                            open = minHr,
                            close = maxHr,
                            low = minHr,
                            high = maxHr,
                        )
                    )
                }

                currentDate = currentDate.plusDays(1)
            }

            val minBpm = allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0
            val maxBpm = allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForCandleChart(minBpm, maxBpm)

            return listOf(
                ChartData.Series(
                    chartType = ChartType.CANDLESTICK,
                    color = color,
                    axisRange = ChartData.AxisRange(
                        minX = from.toEpochDay().toDouble(),
                        maxX = to.toEpochDay().toDouble(),
                        minY = minY,
                        maxY = maxY,
                    ),
                    entries = entries.toImmutableList(),
                    value = formatHeartRateRangeValue(minBpm, maxBpm),
                    candlestickEntries = candlestickEntries.toImmutableList(),
                    lineConfig = null,
                    backgroundRegion = null,
                    groupStackBarStyle = null,
                )
            )
        }
    }

    private inner class WeeklyHeartRateSeriesStrategy : HeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
        ): List<ChartData.Series> {
            val entries = mutableListOf<ChartData.Entry>()
            val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            val allOriginalHeartRates = mutableListOf<HeartRate>()

            val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
            val adjustedFrom = from.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))

            val groupedData = trendDataList.groupBy { trendData ->
                trendData.timeISO8601
                    .toLocalDate()
                    .with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                    .toEpochDay()
            }

            groupedData.forEach { (x, weekTrendDataList) ->
                val allHeartRatesInWeek = weekTrendDataList.mapNotNull { it.hr?.hz }
                allOriginalHeartRates.addAll(allHeartRatesInWeek)

                if (allHeartRatesInWeek.isNotEmpty()) {
                    val weeklyAvgHrInBpm = allHeartRatesInWeek.averageOfDouble(HeartRate::inBpm)
                    val minHrInBpm = allHeartRatesInWeek.minOrNull()?.inBpm ?: weeklyAvgHrInBpm
                    val maxHrInBpm = allHeartRatesInWeek.maxOrNull()?.inBpm ?: weeklyAvgHrInBpm

                    entries.add(ChartData.Entry(x = x, y = weeklyAvgHrInBpm))

                    candlestickEntries.add(
                        ChartData.CandlestickEntry(
                            x = x,
                            open = minHrInBpm,
                            close = maxHrInBpm,
                            low = minHrInBpm,
                            high = maxHrInBpm,
                        )
                    )
                }
            }

            val minBpm = allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0
            val maxBpm = allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForCandleChart(minBpm, maxBpm)

            return listOf(
                ChartData.Series(
                    chartType = ChartType.CANDLESTICK,
                    color = color,
                    axisRange = ChartData.AxisRange(
                        minX = adjustedFrom.toEpochDay().toDouble(),
                        maxX = to.toEpochDay().toDouble(),
                        minY = minY,
                        maxY = maxY,
                    ),
                    entries = entries.toImmutableList(),
                    value = formatHeartRateRangeValue(minBpm, maxBpm),
                    candlestickEntries = candlestickEntries.toImmutableList(),
                    lineConfig = null,
                    backgroundRegion = null,
                    groupStackBarStyle = null,
                )
            )
        }
    }

    private inner class MonthlyHeartRateSeriesStrategy : HeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
        ): List<ChartData.Series> {
            val entries = mutableListOf<ChartData.Entry>()
            val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            val allOriginalHeartRates = mutableListOf<HeartRate>()

            val trendDataByDay = trendDataList.groupBy { it.timeISO8601.toLocalDate() }

            var currentMonth = from.withDayOfMonth(1)
            val endMonth = to.withDayOfMonth(1)

            while (currentMonth <= endMonth) {
                val lastDayOfMonth = currentMonth.plusMonths(1).minusDays(1).coerceAtMost(to)
                val allHeartRatesInMonth = mutableListOf<HeartRate>()

                var currentDay = currentMonth
                while (currentDay <= lastDayOfMonth) {
                    val dailyHeartRates =
                        trendDataByDay[currentDay]?.mapNotNull { it.hr?.hz } ?: emptyList()
                    allHeartRatesInMonth.addAll(dailyHeartRates)
                    allOriginalHeartRates.addAll(dailyHeartRates)
                    currentDay = currentDay.plusDays(1)
                }

                val x = currentMonth.epochMonth.toLong()

                if (allHeartRatesInMonth.isNotEmpty()) {
                    val monthlyAvgHrInBpm = allHeartRatesInMonth.averageOfDouble(HeartRate::inBpm)
                    val minHrInBpm = allHeartRatesInMonth.minOrNull()?.inBpm ?: monthlyAvgHrInBpm
                    val maxHrInBpm = allHeartRatesInMonth.maxOrNull()?.inBpm ?: monthlyAvgHrInBpm

                    entries.add(ChartData.Entry(x = x, y = monthlyAvgHrInBpm))

                    candlestickEntries.add(
                        ChartData.CandlestickEntry(
                            x = x,
                            open = minHrInBpm,
                            close = maxHrInBpm,
                            low = minHrInBpm,
                            high = maxHrInBpm,
                        )
                    )
                }

                currentMonth = currentMonth.plusMonths(1)
            }

            val minBpm = allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0
            val maxBpm = allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForCandleChart(minBpm, maxBpm)

            return listOf(
                ChartData.Series(
                    chartType = ChartType.CANDLESTICK,
                    color = color,
                    axisRange = ChartData.AxisRange(
                        minX = from.withDayOfMonth(1).epochMonth.toDouble(),
                        maxX = to.withDayOfMonth(1).epochMonth.toDouble(),
                        minY = minY,
                        maxY = maxY,
                    ),
                    entries = entries.toImmutableList(),
                    value = formatHeartRateRangeValue(minBpm, maxBpm),
                    candlestickEntries = candlestickEntries.toImmutableList(),
                    lineConfig = null,
                    backgroundRegion = null,
                    groupStackBarStyle = null,
                )
            )
        }
    }

    private inner class YearlyHeartRateSeriesStrategy : HeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
        ): List<ChartData.Series> {
            val entries = mutableListOf<ChartData.Entry>()
            val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            val allOriginalHeartRates = mutableListOf<HeartRate>()

            val trendDataByDay = trendDataList.groupBy { it.timeISO8601.toLocalDate() }

            var currentYear = from.withDayOfYear(1)
            val endYear = to.withDayOfYear(1)

            while (currentYear.year <= endYear.year) {
                val lastDayOfYear =
                    currentYear.withDayOfYear(currentYear.lengthOfYear()).coerceAtMost(to)
                val allHeartRatesInYear = mutableListOf<HeartRate>()

                var currentDay = if (currentYear.year == from.year) from else currentYear
                while (currentDay <= lastDayOfYear) {
                    val dailyHeartRates =
                        trendDataByDay[currentDay]?.mapNotNull { it.hr?.hz } ?: emptyList()
                    allHeartRatesInYear.addAll(dailyHeartRates)
                    allOriginalHeartRates.addAll(dailyHeartRates)
                    currentDay = currentDay.plusDays(1)
                }

                val x = currentYear.year.toLong()

                if (allHeartRatesInYear.isNotEmpty()) {
                    val yearlyAvgHrInBpm = allHeartRatesInYear.averageOfDouble(HeartRate::inBpm)
                    val minHrInBpm = allHeartRatesInYear.minOrNull()?.inBpm ?: yearlyAvgHrInBpm
                    val maxHrInBpm = allHeartRatesInYear.maxOrNull()?.inBpm ?: yearlyAvgHrInBpm

                    entries.add(ChartData.Entry(x = x, y = yearlyAvgHrInBpm))

                    candlestickEntries.add(
                        ChartData.CandlestickEntry(
                            x = x,
                            open = minHrInBpm,
                            close = maxHrInBpm,
                            low = minHrInBpm,
                            high = maxHrInBpm,
                        )
                    )
                }

                currentYear = currentYear.plusYears(1)
            }

            val minBpm = allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0
            val maxBpm = allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForCandleChart(minBpm, maxBpm)

            return listOf(
                ChartData.Series(
                    chartType = ChartType.CANDLESTICK,
                    color = color,
                    axisRange = ChartData.AxisRange(
                        minX = from.year.toDouble(),
                        maxX = to.year.toDouble(),
                        minY = minY,
                        maxY = maxY,
                    ),
                    entries = entries.toImmutableList(),
                    value = formatHeartRateRangeValue(minBpm, maxBpm),
                    candlestickEntries = candlestickEntries.toImmutableList(),
                    lineConfig = null,
                    backgroundRegion = null,
                    groupStackBarStyle = null,
                )
            )
        }
    }

    private fun formatHeartRateRangeValue(min: Int, max: Int): AnnotatedString {
        val unit = appContext.getString(CR.string.bpm)
        return when {
            min == 0 && max == 0 -> buildAnnotatedString {
                append(appContext.getString(BaseR.string.widget_no_data_title))
            }

            min == NO_DATA_VALUE || max == NO_DATA_VALUE -> buildAnnotatedString {
                append(appContext.getString(BaseR.string.widget_no_data_title))
            }

            min == max -> buildAnnotatedString {
                append("$min")
                withStyle(SpanStyle(fontSize = 12.sp)) {
                    append(" ")
                    append(unit)
                }
            }

            else -> buildAnnotatedString {
                append("$min-$max")
                withStyle(SpanStyle(fontSize = 12.sp)) {
                    append(" ")
                    append(unit)
                }
            }
        }
    }

    private fun groupTrendDataByMinutes(
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        intervalMillis: Long,
    ): Map<Long, List<TrendData>> {
        val fromMillis = from.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()
        val toMillis = to.atEndOfDay().toEpochMilli()
        val timeRange = fromMillis until toMillis

        val result = mutableMapOf<Long, MutableList<TrendData>>()
        trendDataList.forEach { trendData ->
            val timestamp = trendData.timeISO8601.toInstant().toEpochMilli()
            if (timestamp in timeRange) {
                val windowKey =
                    (timestamp - fromMillis) / intervalMillis * intervalMillis + fromMillis
                result.getOrPut(windowKey) { mutableListOf() }.add(trendData)
            }
        }
        return result
    }

    private suspend fun loadTrendData(from: LocalDate, to: LocalDate): List<TrendData> {
        val fromMillis = from.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        val toMillis = to.plusDays(1).atStartOfDay(ZoneId.systemDefault()).minusNanos(1).toInstant()
            .toEpochMilli()

        return trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = fromMillis,
            toTimestamp = toMillis,
            aggregated = false
        ).catch { emit(emptyList()) }.first()
    }
} 
