<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="total_energy">Totalt %d kcal</string>

    <!-- Training HUB -->
    <string name="training_hub_comparison_avg">Gj.sn. %s</string>
    <string name="training_hub_volume_title">Volum</string>
    <string name="training_hub_ascent">Oppstigning</string>
    <string name="training_hub_distance">Avstand</string>
    <string name="training_hub_load">Belastning</string>
    <string name="training_hub_no_activities">Ingen aktiviteter</string>
    <plurals name="training_hub_weeks_avg">
        <item quantity="one">%d ukes snitt</item>
        <item quantity="few">%d ukers snitt</item>
        <item quantity="many">%d ukers snitt</item>
        <item quantity="other">%d ukers snitt</item>
    </plurals>
    <plurals name="training_hub_category_subtitle">
        <item quantity="one">%1$d aktivitet</item>
        <item quantity="other">%1$d aktiviteter</item>
    </plurals>
    <string name="training_hub_training_load_title">Treningsbelastning</string>
    <string name="training_hub_total_activities">Aktiviteter</string>
    <string name="training_hub_total_duration">Varighet</string>
    <string name="training_hub_training_stress">Treningsstress</string>
    <string name="training_hub_avg">Snitt</string>
    <string name="training_hub_this_week">Denne uken</string>
    <string name="training_hub_intensity_title">Intensitet</string>
    <string name="training_hub_volume_no_data">Det er ingen data å vise.</string>
    <string name="training_hub_intensity_type_heart_rate">Puls</string>
    <string name="training_hub_intensity_type_pace">Tempo</string>
    <string name="training_hub_intensity_type_running_power">Løpskraft</string>
    <string name="training_hub_intensity_type_cycling_power">Sykling ytelse</string>
    <string name="training_hub_view_more">Vis mer</string>
    <string name="training_hub_view_less">Vis mindre</string>
    <string name="training_hub_training">Trening</string>
    <string name="training_hub_sleep_start">Søvnstart</string>
    <string name="training_hub_recovery">Restitusjon</string>
    <string name="training_hub_form">Form</string>
    <string name="training_hub_form_sub_title">Treningsstressbalanse</string>
    <string name="training_hub_tsb_trend">TSB-trend</string>
    <string name="training_hub_tsb">TSB</string>
    <string name="training_hub_sleep">Søvn</string>
    <string name="training_hub_sleep_trend">Søvntrend</string>
    <string name="training_hub_no_form_data">Fullfør aktiviteter for å få innsikt.</string>
    <string name="training_hub_no_sleep_data">Mål søvn for å få innsikt.</string>
    <string name="training_hub_no_feeling_data">Registrer følelsen din etter aktiviteter for å få innsikt i treningsprogresjon.</string>
    <string name="training_hub_coach_feedback_title">Suunto Coach</string>
    <string name="training_hub_feeling">Følelse</string>
    <string name="training_hub_impact">Innvirkning</string>
    <string name="training_hub_sub_section_training_model">Treningsmodell</string>
    <string name="training_hub_sub_section_impacts">Innvirkninger</string>
    <plurals name="training_hub_impact_unclassified_workouts_count">
        <item quantity="one">Du har %d uklassifisert treningsøkt</item>
        <item quantity="few">Du har %d uklassifiserte treningsøkter</item>
        <item quantity="many">Du har %d uklassifiserte treningsøkter</item>
        <item quantity="other">Du har %d uklassifiserte treningsøkter</item>
    </plurals>
    <string name="training_hub_no_data">Ingen data</string>
    <string name="training_hub_no_data_to_show">Det er ingen data å vise.</string>
    <string name="training_hub_impact_number_of_workouts">Ant. treningsøkter</string>
    <string name="training_hub_progress">Fremgang</string>
    <string name="training_hub_progress_fitness_change">Endring i form</string>
    <string name="training_hub_progress_fitness_change_sub_title">Kronisk treningsbelastning</string>
    <string name="training_hub_progress_ctl_abbreviation">CTL</string>
    <string name="training_hub_progress_ctl_ramp_rate">CTL stigningsforhold</string>
    <string name="training_hub_progress_ctl_fitness_loss">Formnedgang</string>
    <string name="training_hub_progress_ctl_fitness_gain">Formstigning</string>
    <string name="training_hub_progress_ctl_ramp_rate_six_weeks">6 ukers periode</string>
    <string name="training_hub_view_progress">Vis fremgang</string>
    <string name="training_hub_previous_six_weeks">Forr. 6 uker</string>

    <!--  HRV-->
    <string name="sleep_hrv_sub_title">Pulsvariasjon</string>
    <string name="sleep_hrv_today_label">I dag ms</string>
    <string name="sleep_hrv_yesterday_label">I går ms</string>
    <string name="sleep_hrv_7_day_avg_missing">Spor søvnen 3 ganger per uke for en effektiv gjennomsnittlig pulsvariasjon</string>
    <string name="sleep_hrv_coach_requires_normal_range">Finn ditt pulsvariasjonsområde først for å få innsikt</string>
    <string name="recovery_tab_description">Pulsvariasjon indikerer hvor klar kroppen din er til å trene. Optimal pulsvariasjon er innenfor ditt normale område.</string>
    <string name="recovery_tab_sub_header">Gjeldende status</string>

    <string name="training_hub_info_sheet_volume_and_load_title">Belastning/Volum</string>
    <string name="training_hub_info_sheet_volume_and_load_md">## Treningsvolum\n Den totale treningsmengden en utøver gjennomfører over en bestemt periode. Den måles ofte i distanse eller tid.\n ## Treningsbelastning\n Belastningsmengden fra treningsvolumet og treningsintensiteten som en utøvers kropp utsettes for under trening.\n\n ## Training Stress Score (TSS)\n En måling av treningsbelastning som brukes til å vurdere om en utøver trener på et passende nivå for å nå sine mål. TSS-beregning bruker varigheten og intensiteten i hver treningsøkt. Intensiteten er basert på en utøvers anaerobe terskel som i Suunto er definert som sone 4/5-grensen for puls, tempo og effektsoner.\n\n Treningsbelastning har en betydelig innvirkning på en utøvers prestasjoner og evne til å komme seg etter trening. En økning av treningsbelastning kan hjelpe en utøver med å forbedre utholdenhet og generell fitness. Det er likevel viktig å huske at overdreven treningsbelastning kan medføre utbrenthet eller andre uønskede konsekvenser. Det anbefales derfor generelt å øke belastning gradvis over tid istedenfor plutselige eller drastiske økninger.\n\n</string>
    <string name="training_hub_info_sheet_intensity_title">Intensitet</string>
    <string name="training_hub_info_sheet_intensity_md" formatted="false">Trening med forskjellig intensitet belaster kroppen på forskjellige måter og bidrar til fysiologisk tilpasning.\n\n\n\n Ved lett eller middels belastning kommer energien fra det oksidative systemet ved at du forbrenner fett og karbohydrater, og melkesyrenivåene forblir som ved hvile (0,8–1,5 mmol/L).\n\n\n\n Når intensiteten øker, bygger melkesyren seg opp i musklene dine. Kroppen klarer fremdeles å skille den ut, men melkesyrenivået blir høyere enn ved hvile. I treningsterminologien kalles dette aerob terskel (vanligvis rundt 1,5–2,0 mmol/L).\n\n\n\n Hvis intensiteten øker enda mer, vil hjerte og blodårer etterhvert ikke kunne levere nok oksygen til musklene dine, og da dannes melkesyre raskere enn musklene klarer å fjerne den. Dette punktet kalles anaerob terskel (vanligvis rundt 4,0 mmol/L).\n\n\n\n ## Sone 5:\n Maksimalt\n Sone 5 er over anaerob terskel og går til makspuls.\n\n\n\n Trening i denne sonen vil oppleves som ekstremt hard. Melkesyre dannes mye raskere i systemet enn det kan fjernes, og du kan måtte stoppe etter noen få minutter.\n\n\n\n ## Sone 4: Veldig hardt\n 94–99 % av anaerob terskel. Øvre grense for sone 4 er din anaerobe terskel.\n\n\n\n Trening i sone 4 forbereder systemet for konkurranse og lignende samt høy hastighet. Høyintensitetstrening utvikler fitnessnivået raskt og effektivt, men hvis det utføres for ofte eller ved for høy intensitet, kan det føre til overtrening, som kan tvinge deg til å ta en lang pause fra treningsprogrammet ditt.\n\n\n\n ## Sone 3: Hardt\n 89–93 % av anaerob terskel.\n\n\n\n Trening i sone 3 vil forbedre evnen din til å bevege deg raskt og økonomisk. I denne sonen begynner det å dannes melkesyre i systemet, men kroppen er fortsatt i stand til å kvitte seg med den. Du bør trene med denne intensiteten maks et par ganger i uka, da det medfører tung belastning for kroppen.\n\n\n\n ## Sone 2: Middels\n 83–88 % av anaerob terskel. Øvre grense for sone 2 er din aerobe terskel.\n\n\n\n Trening i sone 2 føles lett, men langvarige treningsøkter kan gi svært god treningseffekt. Mesteparten av kardiovaskulær kondisjonstrening skal utføres i denne sonen. Langvarige treningsøkter i denne sonen forbruker mye energi, spesielt fra kroppens fettlagre.\n\n\n\n ## Sone 1: Lett\n &lt;82 % av anaerob terskel. Sone 1 starter ved hvilepuls.\n\n\n\n Trening i sone 1 er relativt lite slitsomt. Det er bra for restitusjonstrening og forbedring av grunnleggende fitness når du begynner å trene eller etter en lang pause.\n\n\n\n ## Angi intensitetssoner\n Bruk SuuntoPlus™-sportsappen **Anaerob terskel** for løping og **FTP Test** for sykling for å finne dine anaerobe terskelnivåer.\n\n\n\n For å stille inn sonene går du til Treningsinnstillinger på Suunto-klokken og oppdaterer intensitetssonene for alle idretter eller spesifikke idretter.\n\n\n\n</string>

    <string name="training_hub_info_sheet_impact_title">Innvirkninger</string>
    <string name="training_hub_info_sheet_cardio_impact_md">Innvirkningen på fysikken din bestemmes av aktivitetstype og treningens varighet og intensitet. En treningsøkt kan ha innvirkning på kondisjon og muskler. Effektive treningsprogrammer blander forskjellige typer trening for å variere hva slags innvirkning treningen har.\n\n # INNVIRKNING PÅ KONDISJON\n VO₂maks\n VO₂maks-trening skal styrke maksimalt oksygenopptak. Denne typen trening går ut på trene med høy intensitet over kort tid med påfølgende restitusjonsperioder. Dette kan være effektiv trening for bedre kondisjon og idrettsprestasjoner, og vanligvis utføres denne typen trening kun få ganger i løpet av en uke.\n\n ##Anaerob\n Anaerob trening består av korte, men høyintensive treningsøkter. Det er viktig å vite at anaerob trening bør balanseres med aerob trening for generelt god form og helse.\n\n ## Anaerob – hardt\n Disse treningsøktene gjennomføres med lengre og hardere innsats og nært opp mot anaerob terskel. Former for kappløp karakteriseres typisk som hard anaerob trening.\n\n ## Aerob/anaerob\n Trening av denne typen kan man holde gående over lengre tid. Den kan fremdeles føles tung da det bygger seg opp melkesyre i løpet av denne fasen.\n\n # AEROB\n ## Aerob\n Aerob trening innebærer repetitiv fysisk aktivitet ved lav til middels intensitet som utføres over lengre tid. Målet med aerob trening er å styrke kroppens evne til å bruke oksygen. Dette gjør hjerte- og karsystemet mer effektivt og styrker generell fysisk utholdenhet. Eksempler på aerob trening er løping, sykling, svømming og turgåing.\n\n ## Lang aerob\n Lange, aerobe økter vil bidra til å styrke utholdenhet og evnen til å opprettholde fysisk aktivitet over lengre tid. Dette kan være nyttig for idretter og aktiviteter som krever varig innsats. Vanligvis er det nødvendig med konstant påfyll av vann og næring under lange aerobe økter.\n\n ## Tung aerob\n Hardere lange, aerobe økter har høyere treningsbelastning og varer vanligvis i flere timer. Dette kan være lange kappløp eller hardere treningsøkter som kan ha variabel intensitet.\n\n ## Restitusjon\n Restitusjonstrening bidrar til restitusjon. Slike treningsøkter har lavere intensitet og varer ikke like lenge som en typisk treningsøkt. De kan også være mer rettet mot teknikker som tøying, skumrulling og massasje for å hjelpe kroppen med restitusjon.\n\n</string>

    <string name="training_hub_info_sheet_muscular_impact_md">Innvirkningen på fysikken din bestemmes av aktivitetstype og treningens varighet og intensitet. En treningsøkt kan ha innvirkning på kondisjon og muskler. Effektive treningsprogrammer blander forskjellige typer trening for å variere hva slags innvirkning treningen har.\n\n # INNVIRKNING PÅ MUSKLER\n ## Styrke\n Denne typen trening kan styrke og definere muskler og bidra til å forbedre fysikk og prestasjoner generelt. Det kan være trening med løse vekter (som manualer og vektstenger), treningsmaskiner, elastiske bånd og kroppsvektøvelser.\n Alt i alt er styrketrening en viktig del av et komplett treningsprogram.\n\n ## Fart og smidighet\n Denne typen trening styrker kroppens evne til å bevege seg effektivt samt koordinasjon og balanse. Disse ferdighetene er viktige på mange områder og er nyttige enten du driver med idrett eller ei.\n\n ## Bevegelse\n Bevegelighetstrening bidrar til å øke bevegeligheten i ledd slik at hverdagslige gjøremål og aktiviteter blir enklere å gjennomføre. Den kan gjøre at man presterer bedre i idretter som krever høy bevegelighet som gymnastikk, dans og kampsport.\n\n</string>

    <string name="training_hub_info_sheet_training_models_title">Treningsmodeller</string>
    <string name="training_hub_info_sheet_training_models_md">Treningsmodellen en utøver følger blander ofte forskjellige intensitetsnivåer. Den identifiserte modellen er basert på å klassifisere hver treningsøkts innvirkning på kondisjonen.\n\n ## Grunnleggende utholdenhet\n Grunnleggende utholdenhetstrening refererer til en treningsperiode med fokus på et høyt volum lite til middels intens trening for å bygge utholdenhet og styrke kroppens evne til å bruke oksygen effektivt. Dette er typisk første treningsfase for utholdenhetsidrettsutøvere som løpere, syklister og triatleter.\n\n ## Polarisert\n Polarisert trening er en type treningsmetode som går ut på å gjennomføre de fleste treningsøktene med lav intensitet og noen økter med høy intensitet. Denne tilnærmingen handler om å styrke utholdenhet og prestasjon ved høy intensitet samtidig som faren for overtrening eller skade reduseres.\n\n ## Sweetspot\n Sweetspot-trening er en type trening som går ut på å trene med moderat intensitet, også kalt «sweet spot intensity». Denne intensiteten defineres typisk som nivået der kroppen jobber hardt nok til å utfordres men ikke så hardt at den blir sliten.\n\n ## Høyintensitet\n Høyintensitetstrening (HIT) er fysisk trening på et høyt intensitetsnivå, som ofte er av kort varighet. Målet med HIT er sterkere muskler, effekt og utholdenhet, og det kan være effektivt med tanke på å øke fysisk fitness og prestasjonsnivå.\n\n ## Pyramide\n Pyramidemodellen er et treningsprogram hvor mesteparten av treningsøktene gjennomføres med lav aerob intensitet, noen med middels intensitet og noen få med høy intensitet. Denne intensitetsmodellen er ganske typisk for idrettsutøvere. Hele intensitetsspekteret er i bruk, men trening ved høy intensitet utgjør alltid en mindre del av volumet for å minimere faren for overtrening og skade.\n\n</string>

    <string name="training_hub_info_sheet_recovery_hrv_title">Pulsvariasjon og måling forklart</string>
    <string name="training_hub_info_sheet_recovery_hrv_md">## Hva er pulsvariasjon?\n Pulsvariasjon (HRV) måler tidsvariasjon i tidsintervallene mellom hjerteslag. Den sier noe om balansen i det autonome nervesystemet og gir innsikt i generell helsetilstand og stressnivå. Pulsvariasjon er nyttig for å forstå autonom funksjon og for å fremme velvære.\n\n ## Hvordan leser jeg dataene?\n For optimal pulsvariasjon bør verdien din holde seg innenfor ditt normale område og litt nærmere «For høy»-grensen. Høyere pulsvariasjon vurderes generelt som bedre, men pulsvariasjon bør alltid sammenlignes med ditt normale område. Forskjellige situasjoner og forhold som en avslappende ferie, fysiske og mentale anstrengelser eller sykdomsutvikling kan føre til endringer i pulsvariasjon.\n\n ## Hvordan måler jeg pulsvariasjonen min?\n Suunto måler pulsvariasjon mens du sover. For å få informasjon om pulsvariasjon, bør du sove med klokka og påse at søvnsporing er aktivert.\n\n Mens du sover, måles pulsvariasjon kontinuerlig for å kalkulere snittverdi for nattens RMSSD. RMSSD er kvadratisk gjennomsnitt av suksessive forskjeller mellom normale hjerteslag og er et vanlig mål på pulsvariasjon.\n\n ## Forklaring til verdiene\n Dagens pulsvariasjon bygger på målinger tatt natten før mens gårsdagens pulsvariasjon stammer fra natten før.\n\n 7-dagers gjennomsnitt bygger på pulsvariasjonsmålinger fra de siste 7 nettene.\n\n For å finne 7-dagers gjennomsnitt må du ha minst 3 målinger i løpet av en 7-dagersperiode.\n\n For å finne normalintervallet ditt, bør du ha tatt totalt 14 målinger over 60 dager.\n\n ##Ditt normale intervall\n Restitusjonsstatus vurderes ved å sammenligne 7-dagersmålet med ditt vanlige intervall. Stolpen vil illustrere dette med Suunto Coach og gi innsikt i restitusjonsstatusen din.\n\n For å vise stolpen, må du ha tatt minst 14 målinger de siste 60 dagene. Verdien i indikatoren viser gjennomsnittet de siste 7 dagene.\n\n</string>

    <string name="training_hub_info_sheet_recovery_form_title">Treningsstressbalanse</string>
    <string name="training_hub_info_sheet_recovery_form_md">Treningsstressbalanse regnes ut ved å trekke en utøvers kroniske treningsbelastning (CTL) fra akutt treningsbelastning (ATL) for å avgjøre om en utøver overtrener eller undertrener.\n\n En positiv TSB-verdi viser at en utøver har høyere formnivå enn utmattelsesnivå og kan være godt uthvilt og i stand til å prestere på høyt nivå. Det optimale intervallet for topprestasjoner er +15 til +25.\n\n En negativ TSB-verdi viser at utøveren opplever et høyere utmattelsesnivå enn formnivå. Det optimale intervallet for trening er -10 til -30. For å komme under -30 kreves det omfattende restitusjon.\n\n EN TSB-verdi på null viser at utøveren har balanserte form- og utmattelsesnivåer.\n\n Bruk TSB sammen med andre mål, som subjektiv følelse av form, for å styre treningsbelastning og optimalisere prestasjon.\n\n</string>

    <string name="training_hub_info_sheet_recovery_sleep_title">Søvn</string>
    <string name="training_hub_info_sheet_recovery_sleep_md">Tilstrekkelig søvn bidrar til å styrke fysiske og mentale prestasjoner. Det er bra for konsentrasjonen, hukommelsen og humøret, og immunforsvaret styrkes også. For lite søvn vil ha motsatt virkning og kan skade dømmekraft og koordinasjon slik at faren for ulykker og skader øker. Overtrening kan føre til endringer i søvnmønster som innsovningsvansker eller andre søvnproblemer.\n\n</string>

    <string name="training_hub_info_sheet_recovery_feeling_title">Følelse</string>
    <string name="training_hub_info_sheet_recovery_feeling_md">Ved å registrere hvordan du føler deg etter hver treningsøkt kan hjelpe deg med å unngå overtrening.\n\n Overtrening medfører en tilstand karakterisert av sterk fysisk og/eller mental utmattelse som skyldes overdreven treningsaktivitet uten tilstrekkelig hvile og restitusjon. Et av de mest åpenbare tegnene på overtrening er fallende prestasjonsnivå med nedgang i styrke, utholdenhet og fart.\n\n Det er viktig for idrettsutøvere å være obs på disse tegnene og unngå overtrening ved å sette av tid til tilstrekkelig hvile i treningsprogrammet.\n\n</string>

    <string name="training_hub_info_sheet_progress_title">Fremgang</string>
    <string name="training_hub_info_sheet_progress_md" formatted="false">##Kronisk treningsbelastning (CTL)\n Kronisk treningsbelastning eller CTL er et 42-dagers gjennomsnitt av daglig TSS. Tallet varierer fra utøver til utøver og er ikke et fast tall.\n\n CTL øker når en utøver konsekvent trener med en TSS som er minst 25 % over gjeldende CTL. Likeledes vil CTL gå ned og formen avta når en utøver reduserer treningsbelastningen. En jevn progresjon med tanke på CTL er viktig for å forebygge skade, og i hvileuker bør CTL falle.\n\n Stigningsforholdet i kronisk treningsbelastning viser endringsraten i form over tid. Et optimalt stigningsforhold kan opprettholdes i noen få uker.\n\n ##Formtester\n Formtester brukes for å vurdere helhetlig fysisk form og for å identifisere spesifikke styrker og svakheter. Suunto tilbyr en rekke SuuntoPlus™-sportsapper for tester som FTP-testen, anaerob terskel og Cooper-testen.\n\n</string>

    <string name="training_hub_info_sheet_recovery_state_title">Hva er restitusjonsstatus?</string>"
    <string name="training_hub_info_sheet_recovery_state_md" formatted="false">Restitusjonsstatus viser hvor godt kroppen din er restituert, og hvor klar den er for aktivitet. Det hjelper deg med å avgjøre når du skal trene hardt, trene lett eller hvile.\n\n ## Restitusjonsstatus er basert på trening, søvn og daglig aktivitet:\n *Trening: Trening påvirker restitusjonsstatusen gjennom belastning, intensitet og tretthet.\n *Søvn: Hjertefrekvensvariasjon (HRV), kvalitet og varighet under søvn er vesentlige.\n *Daglig aktivitet: Høy aktivitet og stress kan redusere restitusjon og beredskap.\n\n ## Maksimer innsikt\n Bruk Suunto-enheten til trening, søvn og daglig sporing for å få best mulig restitusjonsstatus.\n\n</string>
    <string name="training_hub_info_sheet_recovery_state_read_more">Tips for god restitusjon</string>
    <!-- END Of Training HUB -->
    <string name="suunto_coach_message_title">Suunto Coach</string>
    <string name="suunto_coach_message_default">Restitusjonen din er moderat, noe som kan indikere pågående tilpasning.</string>
    <string name="recovery_state_title">Restitusjonsstatus</string>
    <string name="recovery_state_date_time">I dag %s</string>
    <string name="recovery_daily_average">Daglig gj.sn.</string>
    <string name="recovery_average">Gjennomsnitt</string>

    <!-- HRV Display -->
    <string name="hrv_title">Pulsvariasjon</string>
    <string name="hrv_status_low">Lav</string>
    <string name="hrv_status_below">Nedenfor</string>
    <string name="hrv_status_normal">Normal</string>
    <string name="hrv_status_high">Høy</string>
    <string name="hrv_status_above">Ovenfor</string>
    <string name="hrv_status_no_data">Ingen data</string>
    <string name="hrv_seven_day_avg_short">7-d gj.sn.</string>
    <string name="info">Informasjon</string>

    <!-- Training Fatigue Resources -->
    <string name="tss_today">TSS i dag</string>
    <string name="tss_yesterday">TSS i går</string>
    <string name="tss">TSS</string>
    <string name="tsb">TSB</string>
    <string name="seven_day_avg_feelings">7-dagers gj.sn. følelser</string>
    <string name="seven_day_avg_min_hr">7-dagers gj.sn. hvilepuls</string>
    <string name="feeling_excellent">Utmerket</string>
    <string name="feeling_good">Bra</string>
    <string name="feeling_normal">Normal</string>
    <string name="feeling_poor">Dårlig</string>
    <string name="feeling_very_poor">Svært dårlig</string>

    <string name="daily_resources">Daglige ressurser</string>

    <!-- TSB -->
    <string name="tsb_rating_exhausted">Utmattet</string>
    <string name="tsb_rating_fatigued_gaining_fitness">Utmattet / Øker kondisjon</string>
    <string name="tsb_rating_balanced">Balansert</string>
    <string name="tsb_rating_ready_for_more">Klar for mer</string>

    <!-- Recovery Tips -->
    <string name="recovery_tips_optimal">Restitusjonen din er moderat, noe som kan indikere pågående tilpasning.</string>
    <string name="recovery_tips_good">Restitusjonen din er på et godt nivå, noe som indikerer en godt restituert tilstand for trening.</string>
    <string name="recovery_tips_fair">Restitusjonen din er mindre enn optimal, noe som tyder på at kroppen din fortsatt er i ferd med å komme seg.</string>
    <string name="recovery_tips_poor">Restitusjonen din er svært lav, noe som kan indikere er kroppen din er sliten. Bevissthet om dette kan bidra til å planlegge treningen din på en god måte.</string>
    <string name="recovery_tips_limited">Restitusjonen din er begrenset. Sørg for å få mer hvile før intens trening.</string>
    <string name="recovery_tips_excellent">Restitusjonen din er utmerket, noe som tyder på at du har kommet deg og er klar for høy ytelse.</string>
    <string name="recovery_tips_no_data">Ingen restitusjonsdata tilgjengelig. Bruk klokken under søvn for å spore restitusjon.</string>
    <string name="recovery_tips_empty_data">Bruk alltid klokken under søvn og aktiviteter for å få innsikt.</string>

    <string name="contributor_sleep_empty">Ingen innsikt. Aktiver sporing av søvn på klokken din</string>
    <string name="contributor_hrv_empty">Ingen innsikt. Aktiver sporing av pulsvariasjon og søvn på klokken din</string>
    <string name="contributor_resting_heart_rate_empty">Ingen innsikt. Aktiver sporing av puls på klokken din</string>
    <string name="contributor_training_fatigue_empty">Spor treningsøkter for å få innsikt</string>
    <string name="contributor_resource_empty">Ingen innsikt. Aktiver sporing av søvn på klokken din</string>
    <!-- Date Picker -->
    <string name="date_picker_today">I dag</string>
    <string name="date_picker_yesterday">I går</string>
    <string name="date_picker_this_week">Denne uken</string>
    <string name="date_picker_last_week">Siste uke</string>
    <string name="date_picker_this_month">Denne måneden</string>
    <string name="date_picker_last_month">Siste måned</string>
    <string name="date_picker_this_year">Dette året</string>
    <string name="date_picker_last_year">Siste år</string>
    <string name="date_picker_days_30">Siste 30 dager</string>
    <string name="date_picker_days_30_previous">Siste 30 dager</string>

    <string name="contributors_content_sleep">Søvn</string>
    <string name="contributors_content_resting_heart_rate">Hvilepuls</string>
    <string name="contributors_content_minimum_heart_rate">Minimumspuls</string>
    <string name="contributors_content_resources">Ressurser</string>
    <string name="recovery_hrv_info_sheet_title">Hva er pulsvariasjon?</string>
    <string name="recovery_hrv_info_sheet_intensity_md" formatted="false">Pulsvariasjon (HRV) måler tidsvariasjon i tidsintervallene mellom hjerteslag. Den sier noe om balansen i det autonome nervesystemet (ANS) og gir innsikt i generell helsetilstand og stressnivå. Pulsvariasjon er nyttig for å forstå autonom funksjon og for å fremme velvære.\n\n ## Hvordan leser jeg dataene?\n For optimal pulsvariasjon bør verdien din holde seg innenfor ditt normale område og ideelt sett litt nærmere «For høy»-grensen. Mens høyere HRV generelt er forbundet med bedre helse, bør det alltid tolkes i forhold til normalen din. Faktorer som avslapping, fysisk og mental anstrengelse eller sykdom (f.eks. influensa) kan forårsake svingninger i HRV.\n\n ## Hvordan måler jeg pulsvariasjonen min?\n 1. Suunto måler pulsvariasjonen din mens du sover. For å få HRV-data må du bruke klokken mens du sover og sørge for at søvnsporing er aktivert. Pulsvariasjon måles kontinuerlig gjennom hele søvnperioden for å beregne gjennomsnittlig RMSSD-verdi for natten. RMSSD (kvadratisk gjennomsnitt av suksessive forskjeller) er en mye brukt måling for å vurdere HRV.\n\n 2. Forklaring av verdiene\n\n Dagens pulsvariasjon bygger på målinger tatt forrige natt, mens gårsdagens pulsvariasjon stammer fra natten før.\n\n 7-dagers gjennomsnitt bygger på pulsvariasjonsmålinger fra de siste 7 nettene. For å fastslå dette gjennomsnittet kreves det minst 3 HRV-målinger innenfor en 7-dagers periode.\n\n For å finne normalintervallet ditt må du ha tatt totalt 14 HRV-målinger over 60 dager.\n\n</string>
    <string name="recovery_rhr_info_sheet_title">Om hvilepuls (RHR)</string>
    <string name="recovery_rhr_info_sheet_md" formatted="false">Hvilepuls (RHR) er puls målt når du er i ro, noe som reflekterer kardiovaskulær helse og kondisjon. En lavere RHR indikerer vanligvis bedre hjerteeffektivitet og kondisjon. Sporing av RHR kan også bidra til å vurdere restitusjon og justere trenings- og hvileperioder tilsvarende.</string>
    <string name="recovery_min_hr_info_sheet_title">Om minimumspuls dagtid</string>
    <string name="recovery_min_hr_info_sheet_md" formatted="false">Minimumspuls dagtid er den laveste pulsen som måles når du er våken, og er vanligvis høyere enn både hvilepuls og sovepuls. Den gjenspeiler generelt aktivitetsnivå og hjertehelse, og bidrar til å identifisere tretthet, stress og restitusjonsstatus når søvndata ikke er tilgjengelig.</string>
    <string name="recovery_training_fatigue_info_sheet_title">Treningsutmattelse</string>
    <string name="recovery_training_fatigue_info_sheet_md" formatted="false">Treningsutmattelse spiller en nøkkelrolle i restitusjon, da det bidrar til å stimulere tilpasning.\n\n ## Formstatus (TSB)\n Gjenspeiler ditt nåværende treningsnivå basert på treningsdata og innvirkningen på restitusjonen.\n\n ## Treningsbelastningsscore (TSS)\n Måler treningsinnsats fra i dag og i går, og påvirker restitusjon.\n\n ## Følelser\n Ærlig tilbakemelding om hvordan du følte deg under treningsøkten bidrar til å overvåke restitusjonsstatusen, da det gir kontekst utover de målte dataene. \n\n Velg hvordan du følte deg under treningsøkten:\n\n **Utmerket:** Det føltes uanstrengt og svært energigivende.\n\n **Veldig god:** Formen var positiv og samsvarte med forventningene.\n\n **God:** Det føltes håndterlig, men ikke uanstrengt.\n\n **Gj.sn.:** Jeg følte meg helt OK, ingen store oppturer eller nedturer.\n\n **Dårlig:** Det var slitsomt og vanskelig å håndtere.\n\n</string>
    <string name="recovery_resources_info_sheet_title">Om ressurser</string>
    <string name="recovery_resources_info_sheet_md" formatted="false">Ressurser gjenspeiler daglig restitusjon og energiforbruk, og hjelper deg med å følge med på den fysiske tilstanden din og justere aktivitetsnivået. Energi gjenopprettes vanligvis under søvn.\n\n ## Hvordan lese dataene?\n Diagrammet viser fire tilstander basert på ressursendringer:\n\n **1. Restituerer:** Ressursene øker raskt.\n\n **a. Scenario:** Dyp avslapning, spesielt kvalitetsøving, indikerer optimal restitusjon.\n\n **2. Inaktiv:** Ressursene endrer seg sakte og uforutsigbart.\n\n **a. Under søvn:** Forblir stabil eller øker gradvis (unntatt våkenperioder), med langsom restitusjon.\n\n **b. Under våkenhet:** Kan variere litt, avhengig av mindre aktiviteter.\n\n **3. Aktiv:** Ressursene avtar, og hastigheten er knyttet til treningsintensitet og restitusjon.\n\n **a. Scenario:** Daglige aktiviteter eller trening, hvor ressursene endres basert på aktivitetsintensitet og restitusjonstid.\n\n **4. Stresset:** Ressursene avtar raskt.\n\n **a. Scenario:** Stress under våkenhet fører til en rask nedgang i ressurser, og signaliserer en tilstand under høyt press.\n\n ## Hvordan måles ressursene?\n\n Ressursene måles basert på fysiologisk tilstand.\n\n **Aktiv tilstand:** Aktivitetsrestitusjonstid gjenspeiler restitusjonsbehovet. Lengre restitusjonstid antyder raskere ressursreduksjon.\n\n **Inaktiv tilstand:** Pulsvariabilitet (HRV) måler autonom balanse. Høy HRV indikerer høye ressurser; lav HRV signaliserer lave ressurser. Viktige HRV-målinger som RMSSD, stressindeks og SDNN, sammen med hjertefrekvens (HR), bidrar til å vurdere stressnivåer.\n\n</string>

    <string name="about_heart_rate_md" formatted="false">Puls (HR) refererer til antall hjerteslag per minutt og er en viktig indikator på hjertehelse og kondisjon. Å forstå variasjoner i pulsen kan bidra til å optimalisere trening og forbedre den generelle velværen din. Pulsen svinger under søvn, trening og daglige aktiviteter. Hvilepuls og minimum søvnpuls har en tendens til å være lavere, noe som kan indikere god restitusjon og brukes til å justere treningsbelastningen. Overvåking av daglig puls hjelper med å spore trender, oppdage avvik og opprettholde en bevissthet om den generelle helsen din.</string>

    <string name="about_calories_title">Om kalorier</string>
    <string name="about_calories_md" formatted="false">Totale kalorier representerer energien som forbrukes på en dag, inkludert basal forbrenningshastighet (BMR) og aktivitetskalorier. BMR er den minste energien som kreves for å opprettholde viktige fysiologiske funksjoner som pust, hjerteslag og temperaturregulering, mens du er i ro. Aktive kalorier står for energi brukt under trening og daglige aktiviteter. Du kan oppnå økende BMR ved å bygge muskelmasse, engasjere seg i regelmessig trening, holde seg hydrert og opprettholde gode søvnvaner.</string>
</resources>
