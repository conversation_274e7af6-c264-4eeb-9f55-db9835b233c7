<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/weekly_summary_day_range"
            style="@style/DiaryDividerText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:layout_marginBottom="@dimen/size_spacing_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Week 9.5.-16.5." />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/weekly_summary_average"
            style="@style/DiaryDividerText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:layout_marginBottom="@dimen/size_spacing_small"
            android:gravity="end"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/weekly_summary_day_range"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Average 7h 33min" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
