package com.stt.android.diary.summary

import com.stt.android.domain.workouts.SummaryWorkoutHeader
import javax.inject.Inject

class TrainingZoneSummaryGetTotalRowUseCase @Inject constructor(
    private val trainingZoneSummaryGroupingCalculatorUseCase: TrainingZoneSummaryGroupingCalculatorUseCase
) {
    suspend operator fun invoke(workouts: List<SummaryWorkoutHeader>): TableRowItem? =
        if (workouts.size > 1) { // no need to calculate total if we have only table row item
            trainingZoneSummaryGroupingCalculatorUseCase.calculateTotal(workouts)
        } else {
            null
        }
}
