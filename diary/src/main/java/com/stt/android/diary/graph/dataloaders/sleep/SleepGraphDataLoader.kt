package com.stt.android.diary.graph.dataloaders.sleep

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.TimeUtils
import com.stt.android.data.source.local.TABLE_RECOVERY_DATA
import com.stt.android.data.source.local.TABLE_SLEEP_SEGMENTS
import com.stt.android.diary.graph.dataloaders.base.GraphDataLoader
import com.stt.android.domain.diary.models.ChartPoint
import com.stt.android.domain.diary.models.GraphData
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.recovery.FetchRecoveryDataUseCase
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.sleep.Sleep
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.Duration
import java.time.Instant
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.roundToInt

class SleepGraphDataLoader @Inject constructor(
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val fetchRecoveryDataUseCase: FetchRecoveryDataUseCase
) : GraphDataLoader {

    private data class SleepGraphData(override val data: Map<GraphDataType, List<ChartPoint>>) :
        GraphData {
        companion object {
            fun create(sleepData: List<Sleep>, recoveryData: List<RecoveryData>): GraphData =
                SleepGraphData(createData(sleepData, recoveryData))

            private fun createData(
                sleepData: List<Sleep>,
                recoveryData: List<RecoveryData>
            ): Map<GraphDataType, List<ChartPoint>> {
                val lastSleeps = takeLastSleepForDay(sleepData)

                val sleepDurationGraphData = lastSleeps
                    .map { sleep ->
                        ChartPoint(
                            sleep.timestamp,
                            sleep.longSleep?.sleepDuration?.inWholeSeconds?.div(3600f), // seconds to hours
                        )
                    }

                val secondsInDay = TimeUnit.HOURS.toSeconds(24).toFloat()
                val sleepRegularityGraphData = lastSleeps
                    .mapNotNull {
                        val longSleep = it.longSleep
                        if (longSleep != null && longSleep.fellAsleep > 0L && longSleep.wokeUp > 0L) {
                            it.timestamp to longSleep
                        } else {
                            null
                        }
                    }.map { (timestamp, longSleep) ->
                        val sleepEndSeconds = secondsFromStartOfDay(longSleep.wokeUp)
                        val sleepStartSeconds = secondsFromStartOfDay(longSleep.fellAsleep)

                        // Move falling asleep time to next day with wake up time if the
                        // relative falling asleep time is lower than wake up time, as that means
                        // both times are past midnight. Otherwise only wake up time is rolled
                        // over to next day
                        val startAdjustment = if (sleepEndSeconds >= sleepStartSeconds) {
                            secondsInDay
                        } else {
                            0f
                        }

                        ChartPoint(
                            timestamp,
                            sleepEndSeconds + secondsInDay,
                            sleepStartSeconds + startAdjustment
                        )
                    }

                val spo2GraphData = lastSleeps.map { sleep ->
                    ChartPoint(
                        sleep.timestamp,
                        sleep.longSleep?.maxSpO2?.times(100)?.roundToInt()?.toFloat()
                    )
                }

                val sleepQualityGraphData = lastSleeps.map { sleep ->
                    ChartPoint(
                        sleep.timestamp,
                        sleep.longSleep?.qualityPerc?.toFloat()
                    )
                }

                val sleepHrGraphData = lastSleeps.map { sleep ->
                    ChartPoint(
                        sleep.timestamp,
                        sleep.longSleep?.avgHr?.inBpm?.toFloat(),
                    )
                }

                // we need morning resources, ie. last resources value before wake up, not more than 90 minutes before
                // https://amersportsdigital.atlassian.net/wiki/spaces/SLM/pages/2698379308/Morning+resources
                val morningResourcesGraphData = if (recoveryData.isNotEmpty()) {
                    val morningResources = mutableListOf<ChartPoint>()
                    val allowedDelta = Duration.of(90, ChronoUnit.MINUTES).toMillis()
                    var recoveryIteratorNextIndex = 0
                    // recovery data is from newest to oldest, so we reverse sleep to iterate in same order
                    // and optimise to make iteration as fast as possible
                    sleepRegularityIter@for (sleep in lastSleeps.asReversed()) {
                        val recoveryDataIterator = recoveryData.listIterator(recoveryIteratorNextIndex)
                        val wokeUp = sleep.longSleep?.wokeUp ?: continue
                        while (recoveryDataIterator.hasNext()) {
                            val recoveryDatum = recoveryDataIterator.next()
                            if (recoveryDatum.timestamp <= wokeUp) {
                                if (recoveryDatum.timestamp >= wokeUp - allowedDelta) {
                                    // found morning resource for this sleep entry
                                    morningResources.add(
                                        ChartPoint(
                                            recoveryDatum.timestamp,
                                            recoveryDatum.balance.times(100).roundToInt().toFloat()
                                        )
                                    )
                                }
                                recoveryIteratorNextIndex = recoveryDataIterator.nextIndex()
                                continue@sleepRegularityIter
                            }
                        }
                    }

                    morningResources
                } else {
                    emptyList()
                }

                // when only show nap graph, will use this data
                val sleepNapGraphData = lastSleeps.map {
                    // set napValue
                    ChartPoint(it.timestamp, it.getMergedNap()?.duration?.inWholeSeconds?.div(3600f))
                }

                val sleepTotalGraphData = lastSleeps.map {
                    // topValue: night sleep, bottomValue: nap
                    // todo this is really an hack, would be better to refactor this
                    ChartPoint(
                        timestamp = it.timestamp,
                        topValue = it.longSleep?.sleepDuration?.inWholeSeconds?.div(3600f),
                        bottomValue = it.getMergedNap()?.duration?.inWholeSeconds?.div(3600f),
                    )
                }

                return mapOf(
                    GraphDataType.SLEEP_DURATION to sleepDurationGraphData,
                    GraphDataType.SLEEP_REGULARITY to sleepRegularityGraphData,
                    GraphDataType.BLOOD_OXYGEN to spo2GraphData,
                    GraphDataType.SLEEP_QUALITY to sleepQualityGraphData,
                    GraphDataType.AVG_HR_DURING_SLEEP to sleepHrGraphData,
                    GraphDataType.MORNING_RESOURCES to morningResourcesGraphData,
                    GraphDataType.SLEEP_NAP to sleepNapGraphData,
                    GraphDataType.SLEEP_TOTAL to sleepTotalGraphData
                )
            }

            private fun takeLastSleepForDay(sleepList: List<Sleep>) = sleepList
                .groupBy { it.timestamp }
                .values
                .map { it.last() }

            private fun secondsFromStartOfDay(timestamp: Long): Float {
                val zonedDateTime = TimeUtils.epochToLocalZonedDateTime(timestamp)
                return (zonedDateTime.hour * 3600 + zonedDateTime.minute * 60 + zonedDateTime.second).toFloat()
            }
        }
    }

    override val observedRoomTables: Set<String> = setOf(TABLE_RECOVERY_DATA, TABLE_SLEEP_SEGMENTS)

    override suspend fun load(sinceMs: Long, untilMs: Long): GraphData =
        withContext(Dispatchers.Default) {
            runSuspendCatching {
                val currentZone = ZoneId.systemDefault()
                val from = Instant.ofEpochMilli(sinceMs).atZone(currentZone).toLocalDate()
                val to = Instant.ofEpochMilli(untilMs).atZone(currentZone).toLocalDate()
                val sleepData = fetchSleepUseCase.fetchSleeps(from, to).first()
                val recoveryData = fetchRecoveryDataUseCase.fetchRecoveryData(
                    from = from.minusDays(1L),
                    to = to.minusDays(1L),
                ).first()

                SleepGraphData.create(sleepData.filter { it.timestamp >= sinceMs }, recoveryData)
            }.onFailure {
                Timber.w(it, "Loading SleepGraphData failed.")
            }.getOrElse {
                SleepGraphData.create(emptyList(), emptyList())
            }
        }
}
