package com.stt.android.diary.trainingv2.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bottomSheetShape
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberViewInteropNestedScrollConnection
import com.stt.android.compose.widgets.DraggableBottomSheetHandle
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.core.domain.workouts.CoreActivityType

@Composable
internal fun TrainingActivitySheetPicker(
    selectedActivityTypes: List<CoreActivityType>,
    onSelectedActivityTypesChange: (Set<CoreActivityType>, Boolean) -> Unit,
    onAllSelected: () -> Unit,
    onSaveClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .nestedScroll(rememberViewInteropNestedScrollConnection())
            .clip(MaterialTheme.shapes.bottomSheetShape)
            .background(color = MaterialTheme.colors.surface),
    ) {
        DraggableBottomSheetHandle()

        ActivityListContent(
            selectedActivityTypes = selectedActivityTypes,
            onSelectedActivityTypesChange = onSelectedActivityTypesChange,
            onAllSelected = onAllSelected,
            modifier = Modifier.weight(1f),
        )

        PrimaryButton(
            text = stringResource(R.string.done),
            onClick = onSaveClick,
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.spacing.medium),
        )
    }
}

@Preview
@Composable
private fun TrainingActivitySheetPickerPreview() {
    AppTheme {
        TrainingActivitySheetPicker(
            selectedActivityTypes = emptyList(),
            onSelectedActivityTypesChange = { _, _ -> },
            onAllSelected = {},
            onSaveClick = {},
        )
    }
}
