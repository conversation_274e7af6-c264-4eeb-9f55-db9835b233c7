package com.stt.android.diary.summary

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.os.bundleOf
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.summary.composables.DialogListItem
import com.stt.android.diary.summary.composables.Footer
import com.stt.android.diary.summary.composables.Header
import com.stt.android.diary.summary.composables.SearchBar
import com.stt.android.diary.summary.composables.bottomBorder
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.home.diary.R
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import com.stt.android.R as BaseR

@AndroidEntryPoint
class TagsPickerDialogFragment : DialogFragment() {
    companion object {
        const val TAG = "TagsPickerDialogFragment"
        const val BUNDLE_SELECTED_SUUNTO_TAGS = "BUNDLE_SELECTED_SUUNTO_TAGS"
        const val BUNDLE_SELECTED_USER_TAGS = "BUNDLE_SELECTED_USER_TAGS"
        const val REQUEST_KEY_SELECTED_TAGS = "REQUEST_KEY_SELECTED_TAGS"
        const val REQUEST_KEY_SELECTED_USER_TAGS = "REQUEST_KEY_SELECTED_USER_TAGS"
    }

    private val tagsPickerDialogViewModel: TagsPickerDialogViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContent {
            AppTheme {
                val selectedSuuntoTags by tagsPickerDialogViewModel.selecteSuuntoTags.collectAsState()
                val selectedUserTags by tagsPickerDialogViewModel.selectedUserTags.collectAsState()
                val allSuuntoTags = tagsPickerDialogViewModel.allSuuntoTags
                val recentSummaryTags = tagsPickerDialogViewModel.recentSummaryTags
                val allUserTags by tagsPickerDialogViewModel.allUserTags.collectAsState()
                Body(
                    onSuuntoTagClick = tagsPickerDialogViewModel::onSuuntoTagClick,
                    onUserTagClick = tagsPickerDialogViewModel::onUserTagClick,
                    onCancelClick = {
                        dismiss()
                    },
                    onOkClick = {
                        setFragmentResult(
                            REQUEST_KEY_SELECTED_TAGS,
                            bundleOf(
                                BUNDLE_SELECTED_SUUNTO_TAGS to selectedSuuntoTags,
                                BUNDLE_SELECTED_USER_TAGS to selectedUserTags,
                            )
                        )
                        dismiss()
                    },
                    searchValue = tagsPickerDialogViewModel.searchValue,
                    onSearchValueChange = tagsPickerDialogViewModel::onSearchValueChange,
                    allSuuntoTags = allSuuntoTags.toImmutableList(),
                    allUserTags = allUserTags.toImmutableList(),
                    selectedSuuntoTags = selectedSuuntoTags.map { it.suuntoTag }.toImmutableList(),
                    selectedUserTags = selectedUserTags.map { it.userTag }.toImmutableList(),
                    recentSummaryTags = recentSummaryTags.toImmutableList()
                )
            }
        }
    }
}

@Composable
private fun Body(
    allSuuntoTags: ImmutableList<SuuntoTag>,
    allUserTags: ImmutableList<UserTag>,
    selectedSuuntoTags: ImmutableList<SuuntoTag>,
    selectedUserTags: ImmutableList<UserTag>,
    recentSummaryTags: ImmutableList<SummaryTag>,
    onSuuntoTagClick: (SuuntoTag, Boolean) -> Unit,
    onUserTagClick: (UserTag, Boolean) -> Unit,
    onCancelClick: () -> Unit,
    onOkClick: () -> Unit,
    searchValue: String,
    onSearchValueChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val selectedTagsCount by remember(selectedSuuntoTags, selectedUserTags) {
        mutableIntStateOf(selectedSuuntoTags.size + selectedUserTags.size)
    }
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(color = Color.White)
    ) {
        Header(
            text = stringResource(
                id = R.string.training_zone_summary_tags_picker_title,
                if (selectedTagsCount == 0) stringResource(R.string.training_zone_summary_filter_no_tags_selected) else selectedTagsCount.toString()
            )
        )
        SearchBar(
            text = searchValue,
            onSearchValueChange = onSearchValueChange
        )
        TagsList(
            modifier = Modifier.weight(1f),
            allSuuntoTags = allSuuntoTags,
            allUserTags = allUserTags,
            selectedSuuntoTags = selectedSuuntoTags,
            selectedUserTags = selectedUserTags,
            recentSummaryTags = recentSummaryTags,
            searchValue = searchValue,
            onSuuntoTagClick = onSuuntoTagClick,
            onUserTagClick = onUserTagClick,
        )
        Footer(onCancelClick = onCancelClick, onOkClick = onOkClick)
    }
}

@Composable
private fun TagsList(
    allSuuntoTags: ImmutableList<SuuntoTag>,
    allUserTags: ImmutableList<UserTag>,
    selectedSuuntoTags: ImmutableList<SuuntoTag>,
    selectedUserTags: ImmutableList<UserTag>,
    recentSummaryTags: ImmutableList<SummaryTag>,
    onSuuntoTagClick: (tag: SuuntoTag, isSelected: Boolean) -> Unit,
    onUserTagClick: (tag: UserTag, isSelected: Boolean) -> Unit,
    searchValue: String,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    val filteredRecentTags by remember(searchValue, recentSummaryTags) {
        mutableStateOf(recentSummaryTags.filter {
            when (it) {
                is SummaryTag.SummarySuuntoTag -> context.getString(it.suuntoTag.nameRes)
                is SummaryTag.SummaryUserTag -> it.userTag.name
            }.contains(searchValue, true)
        })
    }

    val filteredSuuntoTags by remember(searchValue, allSuuntoTags) {
        mutableStateOf(allSuuntoTags.filter {
            context.getString(it.nameRes).contains(searchValue, true)
        })
    }

    val filteredUserTags by remember(searchValue, allUserTags) {
        mutableStateOf(allUserTags.filter { it.name.contains(searchValue, true) })
    }

    val isEmpty by remember(filteredRecentTags, filteredSuuntoTags, filteredUserTags) {
        derivedStateOf { filteredRecentTags.isEmpty() && filteredSuuntoTags.isEmpty() && filteredUserTags.isEmpty() }
    }

    LazyColumn(
        modifier = modifier.fillMaxSize()
    ) {
        if (isEmpty) {
            item {
                Text(
                    text = stringResource(com.stt.android.core.R.string.no_search_results),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(MaterialTheme.spacing.large)
                )
            }
        } else {
            if (filteredRecentTags.isNotEmpty()) {
                item {
                    TagGroupListItem(
                        nameRes = R.string.training_zone_summary_tags_picker_recent_section_title,
                    )
                }
            }
            items(
                items = filteredRecentTags,
                key = { it.hashCode() }
            ) { tag ->
                when (tag) {
                    is SummaryTag.SummarySuuntoTag -> {
                        val isSelected = tag.suuntoTag in selectedSuuntoTags
                        DialogListItem(
                            nameRes = tag.suuntoTag.nameRes,
                            isSelected = isSelected,
                            modifier = Modifier.clickable {
                                onSuuntoTagClick(
                                    tag.suuntoTag,
                                    !isSelected
                                )
                            },
                        )
                    }

                    is SummaryTag.SummaryUserTag -> {
                        val isSelected = tag.userTag in selectedUserTags
                        DialogListItem(
                            text = tag.userTag.name,
                            isSelected = isSelected,
                            modifier = Modifier.clickable { onUserTagClick(tag.userTag, !isSelected) },
                        )
                    }
                }
            }

            item { Spacer(modifier = Modifier.height(MaterialTheme.spacing.small)) }

            item {
                TagGroupListItem(
                    iconRes = BaseR.drawable.ic_tag_chip_icon,
                    nameRes = R.string.training_zone_summary_tags_picker_automatic_tags_section_title,
                )
            }
            items(
                items = filteredSuuntoTags,
                key = { it.name }
            ) { tag ->
                val isSelected = tag in selectedSuuntoTags
                DialogListItem(
                    nameRes = tag.nameRes,
                    isSelected = isSelected,
                    modifier = Modifier.clickable { onSuuntoTagClick(tag, !isSelected) },
                )
            }

            item { Spacer(modifier = Modifier.height(MaterialTheme.spacing.small)) }

            if (filteredUserTags.isNotEmpty()) {
                item {
                    TagGroupListItem(
                        iconRes = null,
                        nameRes = R.string.training_zone_summary_tags_picker_custom_tags_section_title,
                    )
                }
                items(
                    items = filteredUserTags,
                    key = { it.hashCode() }
                ) { tag ->
                    val isSelected = tag in selectedUserTags
                    DialogListItem(
                        text = tag.name,
                        isSelected = isSelected,
                        modifier = Modifier.clickable { onUserTagClick(tag, !isSelected) },
                    )
                }
            }
        }
    }
}

@Composable
private fun TagGroupListItem(
    @StringRes nameRes: Int,
    modifier: Modifier = Modifier,
    @DrawableRes iconRes: Int? = null
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(50.dp)
            .background(color = Color.White)
            .padding(horizontal = MaterialTheme.spacing.medium)
            .bottomBorder(1.dp, MaterialTheme.colors.darkGrey),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmaller),
    ) {
        iconRes?.let {
            Icon(
                modifier = Modifier
                    .size(MaterialTheme.iconSizes.mini),
                painter = painterResource(it),
                tint = MaterialTheme.colors.onSurface,
                contentDescription = null
            )
        }
        Text(
            text = stringResource(nameRes),
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colors.onSurface,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun BodyPreview() {
    AppTheme {
        Body(
            onSuuntoTagClick = { _, _ -> },
            onUserTagClick = { _, _ -> },
            onCancelClick = {},
            onOkClick = {},
            searchValue = "",
            onSearchValueChange = {},
            allSuuntoTags = SuuntoTag.entries.take(3).toImmutableList(),
            allUserTags = persistentListOf(
                UserTag(id = 1, key = null, name = "Custom tag 1"),
                UserTag(id = 2, key = null, name = "Custom tag 2"),
                UserTag(id = 3, key = null, name = "This is a very very very very long tag name that probably no one will ever create something similar but this case should be handled"),
            ),
            selectedSuuntoTags = persistentListOf(SuuntoTag.COMMUTE),
            selectedUserTags = persistentListOf(UserTag(id = 2, key = null, name = "Custom tag 2")),
            recentSummaryTags = persistentListOf(
                SummaryTag.SummarySuuntoTag(SuuntoTag.COMMUTE, 10),
                SummaryTag.SummaryUserTag(UserTag(id = 2, key = null, name = "Custom tag 2"), 7),
                SummaryTag.SummarySuuntoTag(SuuntoTag.IMPACT_EASY_RECOVERY, 3),
            ),
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun EmptyPreview() {
    AppTheme {
        Body(
            onSuuntoTagClick = { _, _ -> },
            onUserTagClick = { _, _ -> },
            onCancelClick = {},
            onOkClick = {},
            searchValue = "fdfd",
            onSearchValueChange = {},
            allSuuntoTags = persistentListOf(),
            allUserTags = persistentListOf(),
            selectedSuuntoTags = persistentListOf(SuuntoTag.COMMUTE),
            selectedUserTags = persistentListOf(UserTag(id = 2, key = null, name = "Custom tag 2")),
            recentSummaryTags = persistentListOf(),
        )
    }
}
