package com.stt.android.diary.summary

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Switch
import androidx.compose.material.SwitchDefaults
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Info
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.DatePickerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.RangeSlider
import androidx.compose.material3.SelectableDates
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.attention
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.bottomSheetShape
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.darkestGrey
import com.stt.android.compose.theme.disabledColor
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberViewInteropNestedScrollConnection
import com.stt.android.compose.widgets.Chip
import com.stt.android.compose.widgets.DraggableBottomSheetHandle
import com.stt.android.core.domain.workouts.CoreActivityGrouping
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.diary.summary.TagsPickerDialogFragment.Companion.BUNDLE_SELECTED_SUUNTO_TAGS
import com.stt.android.diary.summary.TagsPickerDialogFragment.Companion.BUNDLE_SELECTED_USER_TAGS
import com.stt.android.diary.summary.TagsPickerDialogFragment.Companion.REQUEST_KEY_SELECTED_TAGS
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.home.diary.R
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.toEpochMilli
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import java.time.LocalDate
import java.util.Locale
import androidx.compose.material3.MaterialTheme as M3MaterialTheme
import androidx.compose.material3.Text as M3Text
import androidx.compose.material3.TextButton as M3TextButton

internal val SummaryDistanceFilterRange = 0f..101f

@AndroidEntryPoint
class TrainingZoneSummaryFilterBottomSheet : SmartBottomSheetDialogFragment() {
    companion object {
        const val TAG = "TrainingZoneSummaryFilterBottomSheet"
    }

    private val parentViewModel: TrainingZoneSummaryViewModel by viewModels(ownerProducer = { requireParentFragment() })

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        childFragmentManager.setFragmentResultListener(
            ActivityPickerDialogFragment.REQUEST_KEY_SELECTED_ACTIVITIES,
            this
        ) { _, bundle ->
            val ids =
                bundle.getIntegerArrayList(ActivityPickerDialogFragment.BUNDLE_SELECTED_ACTIVITIES)
                    ?: emptyList<Int>()
            CoreActivityType.entries.filter { it.id in ids }.let {
                parentViewModel.updateSports(it)
            }
        }

        childFragmentManager.setFragmentResultListener(
            REQUEST_KEY_SELECTED_TAGS,
            this
        ) { _, bundle ->
            val suuntoTags =
                bundle.getParcelableArrayList(BUNDLE_SELECTED_SUUNTO_TAGS)
                    ?: emptyList<SummaryTag.SummarySuuntoTag>()

            val userTags =
                bundle.getParcelableArrayList(BUNDLE_SELECTED_USER_TAGS)
                    ?: emptyList<SummaryTag.SummaryUserTag>()
            parentViewModel.updateTags(suuntoTags.toImmutableList(), userTags.toImmutableList())
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContent {
            AppTheme {
                val filterUiState by parentViewModel.filterUiState.collectAsState()
                val formattedStartDate by parentViewModel.formattedStartDate.collectAsState()
                val formattedEndDate by parentViewModel.formattedEndDate.collectAsState()
                val isShowEmptyRowsEnabled by parentViewModel.isShowEmptyRowsEnabled.collectAsState()
                val filterDatePickerState = rememberSummaryFilterDatePickerState(
                    initialSelectedStartDateMillis = filterUiState.selectedStartDateMillis,
                    initialSelectedEndDateMillis = filterUiState.selectedEndDateMillis
                )

                var dateSelectionType by rememberSaveable {
                    // Null means no selection
                    mutableStateOf<DateSelectionType?>(null)
                }

                Body(
                    sports = filterUiState.sportGrouping,
                    grouping = filterUiState.grouping,
                    distanceUiState = filterUiState.distance,
                    isDistanceSupported = filterUiState.isDistanceSupported,
                    distanceUnit = filterUiState.distanceUnit,
                    onGroupingChange = {
                        parentViewModel.updateGrouping(it)
                    },
                    onDistanceChange = { distance ->
                        parentViewModel.updateDistance(distance.start, distance.endInclusive)
                    },
                    onDoneClick = {
                        dismiss()
                    },
                    onResetClick = {
                        parentViewModel.resetFilters()
                    },
                    onSportsClick = {
                        if (childFragmentManager.findFragmentByTag(
                                ActivityPickerDialogFragment.TAG
                            ) == null
                        ) {
                            ActivityPickerDialogFragment().apply {
                                arguments = bundleOf(
                                    ActivityPickerDialogFragment.BUNDLE_SELECTED_ACTIVITIES to filterUiState.sports.map { it.id }
                                )
                            }.show(childFragmentManager, ActivityPickerDialogFragment.TAG)
                        }
                    },
                    onTagsClick = {
                        if (childFragmentManager.findFragmentByTag(
                                TagsPickerDialogFragment.TAG
                            ) == null
                        ) {
                            TagsPickerDialogFragment().apply {
                                arguments = bundleOf(
                                    BUNDLE_SELECTED_SUUNTO_TAGS to filterUiState.summarySuuntoTags.map { it },
                                    BUNDLE_SELECTED_USER_TAGS to filterUiState.summaryUserTags.map { it },
                                )
                            }.show(childFragmentManager, TagsPickerDialogFragment.TAG)
                        }
                    },
                    onStartDateClick = {
                        dateSelectionType = DateSelectionType.START
                    },
                    onEndDateClick = {
                        dateSelectionType = DateSelectionType.END
                    },
                    selectedStartDate = formattedStartDate,
                    selectedEndDate = formattedEndDate,
                    selectedSuuntoTags = filterUiState.suuntoTags,
                    selectedUserTags = filterUiState.userTags,
                    showEmptyRowsChecked = filterUiState.showEmptyRowsChecked,
                    isShowEmptyRowsEnabled = isShowEmptyRowsEnabled,
                    onShowEmptyRowsCheckedChange = parentViewModel::updateShowEmptyWeeksChecked,
                )

                if (dateSelectionType != null) {
                    FilterDatePicker(
                        dateSelectionType = dateSelectionType,
                        filterDatePickerState = filterDatePickerState,
                        onDismissRequest = { dateSelectionType = null },
                        onStartDateSet = parentViewModel::setStartDate,
                        onEndDateSet = parentViewModel::setEndDate,
                    )
                }
            }
        }
    }
}

@Composable
private fun Body(
    sports: ImmutableList<CoreActivityGrouping>,
    grouping: TrainingZoneSummaryGrouping,
    selectedStartDate: String?,
    selectedEndDate: String?,
    distanceUiState: DistanceUiState,
    isDistanceSupported: Boolean,
    distanceUnit: Int,
    selectedSuuntoTags: ImmutableList<SuuntoTag>,
    selectedUserTags: ImmutableList<UserTag>,
    showEmptyRowsChecked: Boolean,
    isShowEmptyRowsEnabled: Boolean,
    onShowEmptyRowsCheckedChange: (Boolean) -> Unit,
    onGroupingChange: (TrainingZoneSummaryGrouping) -> Unit,
    onDoneClick: () -> Unit,
    onResetClick: () -> Unit,
    onSportsClick: () -> Unit,
    onTagsClick: () -> Unit,
    onStartDateClick: () -> Unit,
    onEndDateClick: () -> Unit,
    onDistanceChange: (ClosedFloatingPointRange<Float>) -> Unit,
    modifier: Modifier = Modifier
) {
    val nestedScrollInterop = rememberViewInteropNestedScrollConnection()
    var scrollEnabled by remember { mutableStateOf(true) }

    Column(
        modifier = modifier
            .nestedScroll(nestedScrollInterop)
            .fillMaxSize()
            .clip(MaterialTheme.shapes.bottomSheetShape)
            .background(color = Color.White)
    ) {
        DraggableBottomSheetHandle(bottomPadding = 0.dp)
        TextButton(onClick = onDoneClick, modifier = Modifier.align(Alignment.End)) {
            Text(text = stringResource(R.string.training_zone_summary_filter_done))
        }
        Title(modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium))
        Column(
            modifier = Modifier
                .verticalScroll(
                    state = rememberScrollState(),
                    enabled = scrollEnabled,
                )
                .padding(bottom = MaterialTheme.spacing.medium)
        ) {
            Column {
                Divider()
                SportFilter(
                    sports = sports,
                    onClick = onSportsClick,
                )
                Divider()
                DateFilter(
                    title = stringResource(R.string.training_zone_summary_filter_date_start),
                    value = selectedStartDate,
                    onClick = onStartDateClick,
                )
                Divider()
                DateFilter(
                    title = stringResource(R.string.training_zone_summary_filter_date_end),
                    value = selectedEndDate,
                    onClick = onEndDateClick,
                )
                Divider()
                TagsFilter(
                    suuntoTags = selectedSuuntoTags,
                    userTags = selectedUserTags,
                    onClick = onTagsClick,
                )
                Divider()
                DistanceFilter(
                    distanceUiState = distanceUiState,
                    onDistanceChange = {
                        scrollEnabled = false
                        onDistanceChange(it)
                    },
                    isDistanceSupported = isDistanceSupported,
                    distanceUnit = distanceUnit,
                    onDistanceChangeFinished = { scrollEnabled = true },
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium)
                )
                Divider()
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
                GroupingFilter(
                    grouping = grouping,
                    onGroupingChange = onGroupingChange,
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium)
                )
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
                Divider()
                ShowEmptyRowsFilter(
                    showEmptyRowsChecked = showEmptyRowsChecked,
                    onShowEmptyRowsCheckedChange = onShowEmptyRowsCheckedChange,
                    isEnabled = isShowEmptyRowsEnabled,
                    modifier = Modifier.padding(
                        horizontal = MaterialTheme.spacing.medium,
                        vertical = MaterialTheme.spacing.small
                    )
                )
                Divider()
            }
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
            TextButton(onClick = onResetClick, modifier = Modifier.fillMaxWidth()) {
                Text(text = stringResource(R.string.training_zone_summary_filter_reset))
            }
        }
    }
}

@Composable
private fun Title(
    modifier: Modifier = Modifier
) {
    Text(
        text = stringResource(R.string.training_zone_summary_filtering).uppercase(Locale.getDefault()),
        style = MaterialTheme.typography.bodyXLargeBold,
        modifier = modifier.padding(bottom = MaterialTheme.spacing.medium)
    )
}

@Composable
private fun SportFilter(
    sports: ImmutableList<CoreActivityGrouping>,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(MaterialTheme.spacing.medium)
    ) {
        Text(
            text = stringResource(R.string.training_zone_summary_filter_sports_title),
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.weight(0.3f)
        )
        Text(
            text = if (sports.isEmpty()) {
                stringResource(R.string.training_zone_summary_filter_sports_all)
            } else {
                sports.map { stringResource(it.nameRes) }.joinToString(separator = ", ")
            },
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.End,
            modifier = Modifier.weight(0.7f),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            color = MaterialTheme.colors.primary
        )
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun GroupingFilter(
    grouping: TrainingZoneSummaryGrouping,
    onGroupingChange: (TrainingZoneSummaryGrouping) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
    ) {
        Text(
            text = stringResource(R.string.training_zone_summary_filter_grouping_title),
            style = MaterialTheme.typography.bodyLarge,
        )
        FlowRow(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
        ) {
            TrainingZoneSummaryGrouping.entries.forEach { value ->
                SelectionChip(
                    isSelected = value == grouping,
                    onChecked = { onGroupingChange(value) },
                    textResId = when (value) {
                        TrainingZoneSummaryGrouping.WEEKLY -> R.string.training_zone_summary_filter_grouping_weekly
                        TrainingZoneSummaryGrouping.MONTHLY -> R.string.training_zone_summary_filter_grouping_monthly
                        TrainingZoneSummaryGrouping.YEARLY -> R.string.training_zone_summary_filter_grouping_yearly
                        TrainingZoneSummaryGrouping.BY_ACTIVITY -> R.string.training_zone_summary_filter_grouping_by_activity
                    },
                )
            }
        }
    }
}

@Composable
private fun SelectionChip(
    isSelected: Boolean,
    onChecked: (Boolean) -> Unit,
    @StringRes textResId: Int,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        Chip(
            isSelected = isSelected,
            onChecked = onChecked,
            modifier = Modifier.heightIn(dimensionResource(com.stt.android.R.dimen.tag_editable_chip_min_height))
        ) {
            Text(
                text = stringResource(textResId),
                style = MaterialTheme.typography.body,
                color = if (isSelected) {
                    Color.White
                } else {
                    MaterialTheme.colors.nearBlack
                },
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.spacing.xsmall)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun FilterDatePicker(
    filterDatePickerState: SummaryFilterDatePickerState,
    dateSelectionType: DateSelectionType?,
    onDismissRequest: () -> Unit,
    onStartDateSet: (Long?) -> Unit,
    onEndDateSet: (Long?) -> Unit,
    modifier: Modifier = Modifier
) = M3AppTheme {
    DatePickerDialog(
        onDismissRequest = onDismissRequest,
        confirmButton = {
            M3TextButton(
                onClick = {
                    if (dateSelectionType == DateSelectionType.START) {
                        onStartDateSet(filterDatePickerState.startDatePickerState.selectedDateMillis)
                    } else {
                        onEndDateSet(filterDatePickerState.endDatePickerState.selectedDateMillis)
                    }
                    onDismissRequest()
                },
                enabled = if (dateSelectionType == DateSelectionType.START) {
                    filterDatePickerState.isStartDateConfirmEnabled
                } else {
                    filterDatePickerState.isEndDateConfirmEnabled
                }
            ) {
                M3Text(text = stringResource(com.stt.android.R.string.ok))
            }
        },
        dismissButton = {
            M3TextButton(
                onClick = onDismissRequest
            ) {
                M3Text(stringResource(com.stt.android.R.string.cancel))
            }
        },
        colors = DatePickerDefaults.colors().copy(
            containerColor = M3MaterialTheme.colorScheme.surface,
        ),
        modifier = modifier
    ) {
        DatePicker(
            state = if (dateSelectionType == DateSelectionType.START) {
                filterDatePickerState.startDatePickerState
            } else {
                filterDatePickerState.endDatePickerState
            },
            colors = DatePickerDefaults.colors().copy(
                containerColor = M3MaterialTheme.colorScheme.surface,
            ),
            showModeToggle = false,
        )
    }
}

@Composable
private fun DateFilter(
    title: String,
    value: String?,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(MaterialTheme.spacing.medium)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.weight(0.3f)
        )
        Text(
            text = value.orEmpty(),
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.End,
            modifier = Modifier.weight(0.7f),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            color = MaterialTheme.colors.primary
        )
    }
}

@Composable
private fun ShowEmptyRowsFilter(
    showEmptyRowsChecked: Boolean,
    onShowEmptyRowsCheckedChange: (Boolean) -> Unit,
    isEnabled: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(R.string.training_zone_summary_show_empty_rows),
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.weight(0.7f),
            color = if (isEnabled) MaterialTheme.colors.onSurface else MaterialTheme.colors.disabledColor
        )
        Switch(
            checked = showEmptyRowsChecked,
            onCheckedChange = onShowEmptyRowsCheckedChange,
            colors = SwitchDefaults.colors(
                checkedTrackColor = MaterialTheme.colors.primary,
                checkedThumbColor = MaterialTheme.colors.primary,
                disabledCheckedThumbColor = MaterialTheme.colors.disabledColor,
                disabledCheckedTrackColor = MaterialTheme.colors.disabledColor,
            ),
            enabled = isEnabled
        )
    }
}

@Composable
internal fun DistanceFilter(
    distanceUiState: DistanceUiState,
    onDistanceChange: (ClosedFloatingPointRange<Float>) -> Unit,
    isDistanceSupported: Boolean,
    distanceUnit: Int,
    onDistanceChangeFinished: () -> Unit,
    modifier: Modifier = Modifier
) {
    val distance by remember(distanceUiState) {
        mutableStateOf(
            run {
                val (min, max) = DistanceUiState.toFilterRange(distanceUiState)
                min..max
            }
        )
    }

    val formattedDistanceWithUnit = rememberFormattedDistanceWithUnit(
        distanceUiState = distanceUiState,
        distanceUnit = distanceUnit
    )
    val distanceTextColor = if (isDistanceSupported) MaterialTheme.colors.darkGrey else MaterialTheme.colors.cloudyGrey

    Column(
        modifier = modifier.padding(vertical = MaterialTheme.spacing.medium)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Text(
                text = stringResource(R.string.training_zone_summary_filter_distance),
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.weight(0.3f),
                color = if (isDistanceSupported) MaterialTheme.colors.nearBlack else MaterialTheme.colors.cloudyGrey
            )
            Text(
                text = buildAnnotatedString {
                    withStyle(style = SpanStyle(fontWeight = FontWeight.W600, fontSize = 16.sp)) {
                        append(formattedDistanceWithUnit.first)
                    }
                    formattedDistanceWithUnit.second?.let {
                        append(" ${stringResource(it)}")
                    }
                },
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.End,
                modifier = Modifier.weight(0.7f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                color = if (isDistanceSupported) MaterialTheme.colors.nearBlack else MaterialTheme.colors.cloudyGrey
            )
        }
        RangeSlider(
            value = distance,
            onValueChange = onDistanceChange,
            valueRange = SummaryDistanceFilterRange,
            colors = SliderDefaults.colors(
                activeTrackColor = MaterialTheme.colors.nearBlack,
                inactiveTrackColor = MaterialTheme.colors.cloudyGrey,
                thumbColor = MaterialTheme.colors.nearBlack,
                disabledActiveTrackColor = MaterialTheme.colors.lightGrey,
                disabledInactiveTrackColor = MaterialTheme.colors.lightGrey
            ),
            enabled = isDistanceSupported,
            onValueChangeFinished = onDistanceChangeFinished,
            modifier = Modifier
                .semantics { contentDescription = "DistanceRangeSlider" }
        )
        BoxWithConstraints(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = "0",
                modifier = Modifier.align(Alignment.CenterStart),
                color = distanceTextColor
            )
            MeasureViewWidth(
                viewToMeasure = {
                    Text(text = "25")
                }
            ) {
                Text(
                    text = "25",
                    modifier = Modifier.offset(
                        x = this.maxWidth.times(25)
                            .div(SummaryDistanceFilterRange.endInclusive)
                            .minus(it.div(2))
                    ), // To centralize the thumb with the text
                    color = distanceTextColor
                )
            }
            MeasureViewWidth(
                viewToMeasure = {
                    Text(text = "50")
                }
            ) {
                Text(
                    text = "50",
                    modifier = Modifier.offset(
                        x = this.maxWidth.times(50)
                            .div(SummaryDistanceFilterRange.endInclusive)
                            .minus(it.div(2))
                    ), // To centralize the thumb with the text
                    color = distanceTextColor
                )
            }
            Text(
                text = "100+",
                modifier = Modifier.align(Alignment.CenterEnd),
                color = distanceTextColor
            )
        }
        if (!isDistanceSupported) {
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Outlined.Info,
                    contentDescription = null,
                    tint = MaterialTheme.colors.attention
                )
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
                Text(
                    text = stringResource(R.string.training_zone_summary_filter_distance_select_sport_with_distance),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colors.darkestGrey,
                    modifier = Modifier.semantics {
                        contentDescription = "DistanceNotSupportedWarn"
                    }
                )
            }
        }
    }
}

@Composable
internal fun MeasureViewWidth(
    viewToMeasure: @Composable () -> Unit,
    content: @Composable (measuredWidth: Dp) -> Unit,
) {
    SubcomposeLayout { constraints ->
        val measuredWidth = subcompose("viewToMeasure", viewToMeasure)[0]
            .measure(Constraints()).width.toDp()

        val contentPlaceable = subcompose("content") {
            content(measuredWidth)
        }[0].measure(constraints)
        layout(contentPlaceable.width, contentPlaceable.height) {
            contentPlaceable.place(0, 0)
        }
    }
}

@Composable
private fun TagsFilter(
    suuntoTags: ImmutableList<SuuntoTag>,
    userTags: ImmutableList<UserTag>,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val noTagsIsSelected by remember(suuntoTags, userTags) {
        derivedStateOf {
            suuntoTags.isEmpty() && userTags.isEmpty()
        }
    }

    val allTags by remember(suuntoTags, userTags) {
        mutableStateOf(suuntoTags.map { context.getString(it.nameRes) } + userTags.map { it.name })
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(MaterialTheme.spacing.medium)
    ) {
        Text(
            text = stringResource(R.string.training_zone_summary_filter_tags_title),
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.weight(0.3f)
        )
        Text(
            text = if (noTagsIsSelected) {
                stringResource(R.string.training_zone_summary_filter_no_tags_selected)
            } else {
                allTags.joinToString(separator = ", ") { it }
            },
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.End,
            modifier = Modifier.weight(0.7f),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            color = MaterialTheme.colors.primary
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun BodyPreview() {
    AppTheme {
        Body(
            sports = persistentListOf(
                CoreActivityType.WALKING,
                CoreActivityType.RUNNING,
                CoreActivityType.MOUNTAIN_BIKING,
                CoreActivityType.SOCCER,
            ),
            grouping = TrainingZoneSummaryGrouping.WEEKLY,
            onGroupingChange = {},
            onDoneClick = {},
            onResetClick = {},
            onSportsClick = {},
            onTagsClick = {},
            selectedStartDate = null,
            selectedEndDate = null,
            onStartDateClick = {},
            onEndDateClick = {},
            isDistanceSupported = false,
            distanceUiState = DistanceUiState(
                minDistance = DistanceUiState.RangeValue.Exact(10),
                maxDistance = DistanceUiState.RangeValue.Exact(10)
            ),
            onDistanceChange = { _ -> },
            distanceUnit = com.stt.android.core.R.string.km,
            selectedSuuntoTags = persistentListOf(SuuntoTag.COMMUTE),
            selectedUserTags = persistentListOf(
                UserTag(1, null, "user tag 1"),
                UserTag(2, null, "user tag 2"),
            ),
            showEmptyRowsChecked = true,
            onShowEmptyRowsCheckedChange = {},
            isShowEmptyRowsEnabled = true,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun DistanceFilterPreview() {
    AppTheme {
        DistanceFilter(
            distanceUiState = DistanceUiState(
                minDistance = DistanceUiState.RangeValue.Exact(10),
                maxDistance = DistanceUiState.RangeValue.Exact(10)
            ),
            onDistanceChange = { _ -> },
            isDistanceSupported = true,
            distanceUnit = com.stt.android.core.R.string.km,
            onDistanceChangeFinished = {},
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun rememberSummaryFilterDatePickerState(
    initialSelectedStartDateMillis: Long? = null,
    initialSelectedEndDateMillis: Long? = null
): SummaryFilterDatePickerState {
    val currentYear by remember {
        mutableIntStateOf(LocalDate.now().year)
    }

    val selectableDates = remember {
        object : SelectableDates {
            override fun isSelectableDate(utcTimeMillis: Long): Boolean {
                return utcTimeMillis in LocalDate.of(
                    2010,
                    1,
                    1
                ).atStartOfDay().toEpochMilli()..LocalDate.now().atEndOfDay().toEpochMilli()
            }

            override fun isSelectableYear(year: Int): Boolean {
                return year >= 2010
            }
        }
    }
    val startDatePickerState = rememberDatePickerState(
        initialSelectedDateMillis = initialSelectedStartDateMillis,
        yearRange = IntRange(2010, currentYear),
        selectableDates = selectableDates
    )
    val endDatePickerState = rememberDatePickerState(
        initialSelectedDateMillis = initialSelectedEndDateMillis,
        yearRange = IntRange(2010, currentYear),
        selectableDates = selectableDates
    )
    return remember {
        SummaryFilterDatePickerState(
            startDatePickerState = startDatePickerState,
            endDatePickerState = endDatePickerState
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Stable
class SummaryFilterDatePickerState(
    val startDatePickerState: DatePickerState,
    val endDatePickerState: DatePickerState,
) {
    val isStartDateConfirmEnabled: Boolean
        get() {
            val selectedStartDateMillis = startDatePickerState.selectedDateMillis
            val selectedEndDateMillis = endDatePickerState.selectedDateMillis
            return selectedStartDateMillis != null && (selectedEndDateMillis == null || (selectedEndDateMillis >= selectedStartDateMillis))
        }

    val isEndDateConfirmEnabled: Boolean
        get() {
            val selectedStartDateMillis = startDatePickerState.selectedDateMillis
            val selectedEndDateMillis = endDatePickerState.selectedDateMillis
            return selectedEndDateMillis != null && (selectedStartDateMillis == null || (selectedEndDateMillis >= selectedStartDateMillis))
        }
}

@Composable
internal fun rememberFormattedDistanceWithUnit(
    distanceUiState: DistanceUiState,
    distanceUnit: Int,
    @StringRes anyStringResId: Int = R.string.training_zone_summary_filter_distance_all
): Pair<String, Int?> {
    val anyString = stringResource(anyStringResId)
    val overflowString = "100+"
    return remember(distanceUiState) {
        val min = when (distanceUiState.minDistance) {
            DistanceUiState.RangeValue.None -> 0
            is DistanceUiState.RangeValue.Exact -> distanceUiState.minDistance.value
            DistanceUiState.RangeValue.Overflow -> null
        }

        val max = when (distanceUiState.maxDistance) {
            DistanceUiState.RangeValue.None -> 0
            is DistanceUiState.RangeValue.Exact -> distanceUiState.maxDistance.value
            DistanceUiState.RangeValue.Overflow -> null
        }

        when {
            min == 0 && max == 0 -> anyString to null
            min == null && max == null -> overflowString to distanceUnit
            min == max -> (min?.toString() ?: "") to distanceUnit
            else -> {
                buildString {
                    if (min != null) {
                        append(min.toString())
                    } else {
                        append(overflowString)
                    }
                    append("-")
                    if (max != null) {
                        append(max.toString())
                    } else {
                        append(overflowString)
                    }
                } to distanceUnit
            }
        }
    }
}
