package com.stt.android.diary.common

import androidx.annotation.StyleRes
import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.R as BaseR

val DiaryPage.theme: Int
    @StyleRes get() = when (this) {
        DiaryPage.TRAINING -> BaseR.style.Theme_DiaryWorkouts
        DiaryPage.SCUBA_DIVING -> BaseR.style.Theme_DiaryScubaDiving
        DiaryPage.FREE_DIVING -> BaseR.style.Theme_DiaryFreeDiving
        DiaryPage.PROGRESS -> BaseR.style.Theme_DiaryTSS
        DiaryPage.RECOVERY -> BaseR.style.Theme_DiaryRecovery
        DiaryPage.DAILY_ACTIVITY -> BaseR.style.Theme_DiarySteps
        DiaryPage.SLEEP -> BaseR.style.Theme_DiarySleep
        DiaryPage.OVERVIEW -> BaseR.style.Theme_DiaryOverview
        DiaryPage.SUMMARY,
        DiaryPage.STATISTICS -> BaseR.style.Theme_DiarySummary
    }
