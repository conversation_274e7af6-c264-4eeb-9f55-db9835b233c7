package com.stt.android.diary.trainingv2

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.diary.summary.ActivityPickerDialogFragment
import com.stt.android.diary.trainingv2.composables.TrainingActivitySheetPicker
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class TrainingActivitySheetPickerFragment : SmartBottomSheetDialogFragment() {

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState).also { dialog ->
            (dialog as BottomSheetDialog).behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContentWithTheme {
            val selectedActivityTypeIds =
                arguments?.getIntegerArrayList(BUNDLE_SELECTED_ACTIVITIES) ?: arrayListOf()
            var selectedActivityTypes by rememberSaveable(selectedActivityTypeIds) {
                mutableStateOf(CoreActivityType.entries.filter { it.id in selectedActivityTypeIds })
            }

            TrainingActivitySheetPicker(
                selectedActivityTypes = selectedActivityTypes,
                onSelectedActivityTypesChange = { activityTypes, isSelect ->
                    selectedActivityTypes = with(selectedActivityTypes) {
                        if (isSelect) this + activityTypes else this - activityTypes
                    }
                },
                onAllSelected = {
                    selectedActivityTypes = emptyList()
                },
                onSaveClick = {
                    setResult(selectedActivityTypes)
                },
            )
        }
    }

    private fun setResult(activityTypes: List<CoreActivityType>) {
        setFragmentResult(
            REQUEST_KEY_SELECTED_ACTIVITIES,
            bundleOf(
                BUNDLE_SELECTED_ACTIVITIES to
                    activityTypes.sortedBy { it.id }.map { it.id }
            )
        )
        dismiss()
    }

    companion object {
        const val BUNDLE_SELECTED_ACTIVITIES = "BUNDLE_SELECTED_ACTIVITIES"
        const val REQUEST_KEY_SELECTED_ACTIVITIES = "REQUEST_KEY_SELECTED_ACTIVITIES"

        fun newInstance(selectedActivityTypes: List<CoreActivityType>) =
            TrainingActivitySheetPickerFragment().apply {
                arguments = bundleOf(
                    ActivityPickerDialogFragment.BUNDLE_SELECTED_ACTIVITIES to selectedActivityTypes.map { it.id }
                )
            }
    }
}
