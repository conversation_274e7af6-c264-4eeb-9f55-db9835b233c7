package com.stt.android.diary.common

import android.os.Handler
import android.os.Looper
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import com.github.mikephil.charting.charts.BarLineChartBase
import kotlin.math.abs

/**
 * A helper class to support "drag to highlight" on the chart.
 *
 * NOTE: This class does not support multi-touch events.
 */
open class DragToHighlightGraphHandler(
    private val graph: BarLineChartBase<*>,
    private val longTap: Boolean = true,
    private val enableScrubbingCount: Boolean = false,
) : View.OnTouchListener {
    private var isGestureResolved = true
    private var moveStartXRaw: Float = 0f
    private var moveStartYRaw: Float = 0f
    private var moveStartX: Float = 0f
    private var moveStartY: Float = 0f
    private val handler: Handler = Handler(Looper.getMainLooper())
    var scrubbingCount = 0
        private set

    private val runnable = Runnable {
        if (!isGestureResolved) {
            startHighLighting()
        }
    }

    override fun onTouch(
        v: View?,
        event: MotionEvent
    ): Bo<PERSON>an {
        // We don't support multi-touch, so ignore them.
        // https://suunto.tpondemand.com/entity/157614-the-cursor-can-not-be-slide
        if (event.pointerCount > 1) {
            return true
        }

        val action = event.action
        if (action == MotionEvent.ACTION_DOWN) {
            isGestureResolved = false
            moveStartXRaw = event.rawX
            moveStartYRaw = event.rawY
            moveStartX = event.x
            moveStartY = event.y
            handler.postDelayed(runnable, if (longTap) TAP_DOWN_TIME_THRESHOLD else 0L)
            return true
        } else if (action == MotionEvent.ACTION_UP) {
            endHighLighting()
            // Must be called as per ClickableViewAccessibility lint check
            graph.performClick()
            return true
        } else if (action == MotionEvent.ACTION_CANCEL) {
            endHighLighting()
        } else if (action == MotionEvent.ACTION_MOVE && !isGestureResolved) {
            val vc: ViewConfiguration = ViewConfiguration.get(graph.context)
            if (abs(moveStartXRaw - event.rawX) > vc.scaledTouchSlop) {
                endHighLighting()
            }

            if (abs(moveStartYRaw - event.rawY) > vc.scaledTouchSlop) {
                endHighLighting()
            }
        }
        return false
    }

    protected open fun startHighLighting() {
        graph.apply {
            parent?.requestDisallowInterceptTouchEvent(true)
            isHighlightPerDragEnabled = true
            highlightValue(graph.getHighlightByTouchPoint(moveStartX, moveStartY), true)
        }
        isGestureResolved = true
    }

    private fun endHighLighting() {
        if (longTap) {
            handler.removeCallbacks(runnable)
            graph.apply {
                parent?.requestDisallowInterceptTouchEvent(false)
                isHighlightPerDragEnabled = false
                highlightValue(null, true)
            }
            isGestureResolved = true
        }
        if (enableScrubbingCount) {
            scrubbingCount++
        }
    }

    companion object {
        private const val TAP_DOWN_TIME_THRESHOLD = 150L
    }
}
