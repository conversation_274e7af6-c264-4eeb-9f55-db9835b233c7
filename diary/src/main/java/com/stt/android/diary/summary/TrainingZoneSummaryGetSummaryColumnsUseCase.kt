package com.stt.android.diary.summary

import com.stt.android.mapping.getSummaryItemListByStId
import javax.inject.Inject

class TrainingZoneSummaryGetSummaryColumnsUseCase @Inject constructor() {

    operator fun invoke(rows: List<TableRowItemUiState>): List<TrainingZoneSummaryColumn> {
        return rows
            .flatMap { it.workouts }
            .map { getSummaryItemListByStId(it.activityTypeId) }
            .flatten()
            .toTrainingZoneSummaryValueItems()
            .distinct()
            .sorted()
    }
}
