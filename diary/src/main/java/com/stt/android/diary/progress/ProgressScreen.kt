package com.stt.android.diary.progress

import android.content.Context
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onLayoutRectChanged
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntOffset
import com.stt.android.analytics.AnalyticsPropertyValue.WidgetDetailPageButtonClickProperty.FITNESS_CHANGE_TIPS
import com.stt.android.analytics.AnalyticsPropertyValue.WidgetDetailPageButtonClickProperty.FITNESS_LEVEL_TIPS
import com.stt.android.analytics.AnalyticsPropertyValue.WidgetDetailPageButtonClickProperty.VO2MAX_TIPS
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.util.LazyColumnVisibilityTracker
import com.stt.android.compose.util.TrackLazyColumnVisibility
import com.stt.android.home.diary.R
import com.stt.android.utils.CustomTabsUtils

@Composable
internal fun ProgressScreen(
    viewModel: ProgressViewModel,
    modifier: Modifier = Modifier,
    isProgressDetail: Boolean = false,
) {
    LaunchedEffect(isProgressDetail) {
        viewModel.setIsProgressDetail(isProgressDetail)
    }
    
    ContentCenteringColumn(modifier = modifier) {
        val viewData = viewModel.viewData.collectAsState().value
        when (viewData) {
            ProgressViewData.Initial -> Unit

            is ProgressViewData.Loaded -> {
                ProgressLoaded(
                    viewData = viewData,
                    onEvent = { viewModel.onEvent(it) },
                )
            }
        }
    }
}

@Composable
private fun ProgressLoaded(
    viewData: ProgressViewData.Loaded,
    onEvent: (ProgressViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current

    val fitnessViewData by viewData.fitnessViewData.collectAsState()
    val ctlRampRateViewData by viewData.ctlRampRateViewData.collectAsState(initial = null)
    val vo2MaxProgressViewData by viewData.vo2MaxProgressViewData.collectAsState(initial = null)
    val fitnessHighlightedViewData by viewData.fitnessHighlightedViewData.collectAsState(initial = null)
    val vo2MaxHighlightedViewData by viewData.vo2MaxHighlightedViewData.collectAsState(initial = null)

    var showFitnessInfo by rememberSaveable { mutableStateOf(false) }
    var showRampRateInfo by rememberSaveable { mutableStateOf(false) }
    var showVo2MaxInfo by rememberSaveable { mutableStateOf(false) }

    var containerPositionInRootY by remember { mutableIntStateOf(0) }
    var fitnessChartPositionInRootY by remember { mutableIntStateOf(0) }
    var fitnessHighlightedHeight by remember { mutableIntStateOf(0) }
    var vo2MaxChartPositionInRootY by remember { mutableIntStateOf(0) }
    var vo2MaxHighlightedHeight by remember { mutableIntStateOf(0) }
    val listState = rememberLazyListState()
    
    val visibilityTracker = remember { LazyColumnVisibilityTracker() }
    
    TrackLazyColumnVisibility(
        listState = listState,
        tracker = visibilityTracker,
        onComponentVisible = { visibleComponents ->
            visibleComponents.forEach { componentKey ->
                val moduleName = ProgressViewModel.getModuleNameByComponentKey(componentKey)
                if (moduleName != null) {
                    onEvent(ProgressViewEvent.ModuleFullyVisible(moduleName))
                }
            }
        }
    )
    
    val showTimeRangeDivider by remember(listState) {
        derivedStateOf { listState.firstVisibleItemIndex > 2 } // Header, FitnessFatigueForm, Space
    }

    Box(
        modifier = modifier
            .onLayoutRectChanged(debounceMillis = 0L) {
                containerPositionInRootY = it.positionInRoot.y
            }
            .background(MaterialTheme.colorScheme.surface)
            .fillMaxSize(),
    ) {
        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize()
        ) {
            fitnessItems(
                timeRange = viewData.timeRange,
                fitnessViewData = fitnessViewData,
                chartViewDataMap = viewData.fitnessChartViewDataMap,
                chartPageIndex = viewData.fitnessChartPageIndex,
                chartPageCount = viewData.fitnessChartPageCount,
                chartPageStartDate = viewData.fitnessChartPageStartDate,
                chartPageEndDate = viewData.fitnessChartPageEndDate,
                highlightedIndex = viewData.fitnessHighlightedIndex,
                onEvent = onEvent,
                onInfoClicked = {
                    showFitnessInfo = true
                    onEvent(ProgressViewEvent.TrackButtonClick(FITNESS_LEVEL_TIPS))
                },
                onPositionInRootYChanged = { fitnessChartPositionInRootY = it },
                showTimeRangeDivider = showTimeRangeDivider,
            )
            ctlRampRateItems(
                ctlRampRateViewData,
                onInfoClicked = {
                    showRampRateInfo = true
                    onEvent(ProgressViewEvent.TrackButtonClick(FITNESS_CHANGE_TIPS))
                },
            )
            vo2MaxItems(
                timeRange = viewData.timeRange,
                progressViewData = vo2MaxProgressViewData,
                chartViewDataMap = viewData.vo2MaxChartViewDataMap,
                chartPageIndex = viewData.vo2MaxChartPageIndex,
                chartPageCount = viewData.vo2MaxChartPageCount,
                chartPageStartDate = viewData.vo2MaxChartPageStartDate,
                chartPageEndDate = viewData.vo2MaxChartPageEndDate,
                highlightedIndex = viewData.vo2MaxHighlightedIndex,
                onEvent = onEvent,
                onInfoClicked = {
                    showVo2MaxInfo = true
                    onEvent(ProgressViewEvent.TrackButtonClick(VO2MAX_TIPS))
                },
                onPositionInRootYChanged = { vo2MaxChartPositionInRootY = it },
            )
        }
        FitnessHighlightedView(
            modifier = Modifier
                .offset {
                    IntOffset(
                        x = 0,
                        y = (fitnessChartPositionInRootY - containerPositionInRootY - fitnessHighlightedHeight)
                            .coerceAtLeast(0),
                    )
                }
                .onSizeChanged { fitnessHighlightedHeight = it.height },
            viewData = fitnessHighlightedViewData,
        )
        Vo2MaxHighlightedView(
            modifier = Modifier
                .offset {
                    IntOffset(
                        x = 0,
                        y = (vo2MaxChartPositionInRootY - containerPositionInRootY - vo2MaxHighlightedHeight)
                            .coerceAtLeast(0),
                    )
                }
                .onSizeChanged { vo2MaxHighlightedHeight = it.height },
            viewData = vo2MaxHighlightedViewData,
        )
    }

    if (showFitnessInfo) {
        FitnessLevelInfoBottomSheet(
            onInfoProgressionClicked = {
                launchCustomTab(context, R.string.tss_info_progression_url)
            },
            onInfoValuesClicked = {
                launchCustomTab(context, R.string.tss_info_values_url)
            },
            onTrainingPeaksClick = {
                launchCustomTab(context, R.string.tss_info_training_peaks_url)
            },
            onDismiss = { showFitnessInfo = false },
        )
    }
    if (showRampRateInfo) {
        CTLRampRateInfoBottomSheet(onDismiss = { showRampRateInfo = false })
    }
    if (showVo2MaxInfo) {
        Vo2MaxInfoBottomSheet(onDismiss = { showVo2MaxInfo = false })
    }
}

private fun launchCustomTab(context: Context, @StringRes urlResId: Int) {
    CustomTabsUtils.launchCustomTab(context, context.getString(urlResId))
}
