package com.stt.android.diary.common

import android.content.res.Resources
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.home.diary.R
import com.stt.android.ui.utils.EpoxyConditionalDividerItemDecoration
import com.stt.android.ui.utils.WideScreenPaddingDecoration
import com.stt.android.R as BaseR

interface WideScreenDecorator {
    fun setupForWideScreen(
        resources: Resources,
        list: RecyclerView,
        theme: Resources.Theme
    )
}

class WideScreenDecoratorDelegate : WideScreenDecorator {
    override fun setupForWideScreen(
        resources: Resources,
        list: RecyclerView,
        theme: Resources.Theme
    ) {
        list.addItemDecoration(WideScreenPaddingDecoration(resources, theme))
        val divider = resources.getDimensionPixelSize(R.dimen.top_bottom_divider)
        val dividerColor = resources.getColor(BaseR.color.light_grey, theme)
        list.addItemDecoration(
            EpoxyConditionalDividerItemDecoration(
                dividerColor = dividerColor
            ) { item: Any?, nextItem: Any? ->
                if (item == null || nextItem == null) {
                    divider
                } else {
                    null
                }
            }
        )
    }
}
