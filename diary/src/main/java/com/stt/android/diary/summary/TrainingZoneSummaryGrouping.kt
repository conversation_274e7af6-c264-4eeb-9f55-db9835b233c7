package com.stt.android.diary.summary

import com.stt.android.home.diary.R

enum class TrainingZoneSummaryGrouping {
    BY_ACTIVITY,
    WEEKLY,
    MONTHLY,
    YEARLY,
    ;

    fun getLabelResId() =
        when (this) {
            WEEKLY -> R.string.training_zone_summary_filter_grouping_weekly
            MONTHLY -> R.string.training_zone_summary_filter_grouping_monthly
            YEARLY -> R.string.training_zone_summary_filter_grouping_yearly
            BY_ACTIVITY -> R.string.training_zone_summary_filter_grouping_by_activity
        }
}
