package com.stt.android.diary.graph.data

import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.data.CandleEntry
import com.github.mikephil.charting.data.Entry
import com.stt.android.domain.diary.models.ChartData

data class ChartPage(
    val primaryChartData: ChartData,
    val secondaryChartData: ChartData?,
    val barData: List<BarEntry>,
    val candleData: List<CandleEntry>,
    val lineData: List<List<Entry>>?
) {
    val timestamps get() = primaryChartData.chartPoints.map { it.timestamp }

    val pageNumber get() = this.primaryChartData.timeFrame.pageNumber
}
