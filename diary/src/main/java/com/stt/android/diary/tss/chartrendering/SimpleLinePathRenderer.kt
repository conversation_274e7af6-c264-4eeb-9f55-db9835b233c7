package com.stt.android.diary.tss.chartrendering

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.animation.ChartAnimator
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.interfaces.dataprovider.LineDataProvider
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.github.mikephil.charting.renderer.LineChartRenderer
import com.github.mikephil.charting.utils.Utils
import com.github.mikephil.charting.utils.ViewPortHandler
import com.stt.android.core.R as CR

// Simple line renderer that renders a data set as a single path instead of rendering individual
// line segments like the default LineChartRenderer. This allows for rendering proper joins for the
// line.
// Multi-color data sets or animations are not supported.
class SimpleLinePathRenderer(
    context: Context,
    chart: Line<PERSON><PERSON><PERSON><PERSON><PERSON>,
    animator: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    viewPortHandler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    private val drawTriangle: Boolean,
    private val topOffsetDp: Float,
    private val bottomOffsetDp: Float,
) : LineChartRenderer(chart, animator, viewPortHandler) {

    private val highlightPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = Utils.convertDpToPixel(1f)
        color = ContextCompat.getColor(context, CR.color.near_black)
    }
    private val highlightTrianglePath = Path()

    override fun drawLinear(canvas: Canvas, dataSet: ILineDataSet) {
        mRenderPaint.style = Paint.Style.STROKE
        mRenderPaint.strokeJoin = Paint.Join.ROUND
        mRenderPaint.strokeCap = Paint.Cap.SQUARE
        mRenderPaint.color = dataSet.color

        mXBounds.set(mChart, dataSet)
        val entryCount = dataSet.entryCount
        val transformer = mChart.getTransformer(dataSet.axisDependency)

        // if drawing filled is enabled
        if (dataSet.isDrawFilledEnabled && entryCount > 0) {
            drawLinearFill(canvas, dataSet, transformer, mXBounds)
        }

        val path = Path().apply {
            if (entryCount > 0) {
                val firstEntry = dataSet.getEntryForIndex(0)
                moveTo(firstEntry.x, firstEntry.y)

                if (entryCount > 1) {
                    for (i in 1 until entryCount) {
                        val entry = dataSet.getEntryForIndex(i)
                        lineTo(entry.x, entry.y)
                    }
                } else {
                    // Render single point
                    lineTo(firstEntry.x, firstEntry.y)
                }
            }
        }

        transformer.pathValueToPixel(path)
        canvas.drawPath(path, mRenderPaint)
    }

    override fun drawHighlighted(c: Canvas?, indices: Array<out Highlight?>?) {
        // clipped by contentRect, do nothing
    }

    fun drawHighlightedFreely(c: Canvas, indices: Array<out Highlight>) {
        val lineData = mChart.lineData

        for (high in indices) {
            val set = lineData.getDataSetByIndex(high.dataSetIndex)

            if (set == null || !set.isHighlightEnabled) continue

            val e = set.getEntryForXValue(high.x, high.y)

            if (!isInBoundsX(e, set)) continue

            val pix = mChart.getTransformer(set.axisDependency)
                .getPixelForValues(e.x, e.y * mAnimator.phaseY)

            high.setDraw(pix.x.toFloat(), pix.y.toFloat())

            // Compose doesn't clip by default, so we are free.
            val topY = 0f + Utils.convertDpToPixel(topOffsetDp)
            val bottomY = mViewPortHandler.contentBottom() + Utils.convertDpToPixel(bottomOffsetDp)
            if (drawTriangle) {
                val triangleWidth = Utils.convertDpToPixel(5f)
                val triangleHeight = Utils.convertDpToPixel(4f)

                highlightTrianglePath.reset()
                highlightTrianglePath.moveTo(pix.x.toFloat() - triangleWidth / 2f, topY)
                highlightTrianglePath.lineTo(pix.x.toFloat() + triangleWidth / 2f, topY)
                highlightTrianglePath.lineTo(pix.x.toFloat(), topY + triangleHeight)
                highlightTrianglePath.close()
                highlightPaint.style = Paint.Style.FILL
                c.drawPath(highlightTrianglePath, highlightPaint)
            }

            highlightPaint.style = Paint.Style.STROKE
            c.drawLine(
                pix.x.toFloat(),
                topY,
                pix.x.toFloat(),
                bottomY,
                highlightPaint
            )
        }
    }
}
