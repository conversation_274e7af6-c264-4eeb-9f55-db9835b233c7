package com.stt.android.diary.tss.chartrendering

import android.graphics.Canvas
import android.graphics.Rect
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.renderer.YAxisRenderer
import com.github.mikephil.charting.utils.Transformer
import com.github.mikephil.charting.utils.ViewPortHandler
import kotlin.math.abs

class FlexibleYAxisLabelRenderer(
    private val labels: Map<Float, String>,
    viewPortHandler: ViewPortHandler,
    yAxis: YAxis,
    transformer: Transformer,
) : YAxisRenderer(viewPortHandler, yAxis, transformer) {

    private val textBounds = Rect()

    private val floatComparator = Comparator<Float> { a, b -> b.compareTo(a) }

    override fun drawYLabels(
        c: Canvas?,
        fixedPosition: Float,
        positions: FloatArray?,
        offset: Float
    ) {
        var prevY: Float? = null
        for ((yValue, label) in labels.toSortedMap(floatComparator)) {
            val y = transformer.getPixelForValues(0f, yValue).y.toFloat()
            mAxisLabelPaint.getTextBounds(label, 0, label.length, textBounds)
            val textHeight = textBounds.height()
            if (prevY == null || abs(y - prevY) > textHeight) {
                c?.drawText(label, fixedPosition, y + offset, mAxisLabelPaint)
                prevY = y
            }
        }
    }
}
