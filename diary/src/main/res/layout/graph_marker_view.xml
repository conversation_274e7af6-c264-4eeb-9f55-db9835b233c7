<?xml version="1.0" encoding="UTF-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/marker_container"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minWidth="300dp"
    android:background="@drawable/border_shadow"
    android:paddingTop="@dimen/size_spacing_small"
    android:paddingBottom="@dimen/size_spacing_small">

    <TextView
        android:id="@+id/diaryGraphPrimaryValue"
        style="@style/Body.Medium.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        app:layout_constraintBottom_toTopOf="@id/diaryGraphPrimaryLabel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="01:49'12" />

    <TextView
        android:id="@+id/diaryGraphPrimaryLabel"
        style="@style/Body.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/diaryGraphPrimaryValue"
        tools:text="Duration" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:barrierDirection="end"
        app:constraint_referenced_ids="diaryGraphPrimaryValue,diaryGraphPrimaryLabel" />

    <TextView
        android:id="@+id/diaryGraphSecondaryValue"
        style="@style/Body.Medium.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/diaryGraphSecondaryLabel"
        app:layout_constraintStart_toEndOf="@id/barrier"
        app:layout_constraintTop_toTopOf="@id/diaryGraphPrimaryValue"
        tools:text="95%"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/diaryGraphSecondaryLabel"
        style="@style/Body.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:maxWidth="96dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/barrier"
        tools:text="Ressenti de l’exercice"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/secondaryBarrier"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:barrierDirection="end"
        app:constraint_referenced_ids="diaryGraphSecondaryValue,diaryGraphSecondaryLabel" />

    <TextView
        android:id="@+id/diaryGraphTime"
        style="@style/Body.Small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:gravity="end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/secondaryBarrier"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="04/01/2019 - 11/01/2019" />

</androidx.constraintlayout.widget.ConstraintLayout>
