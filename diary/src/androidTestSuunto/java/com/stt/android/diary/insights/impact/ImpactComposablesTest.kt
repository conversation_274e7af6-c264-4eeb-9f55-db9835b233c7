package com.stt.android.diary.insights.impact

import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithTag
import com.stt.android.diary.insights.common.DummyTrainingHubUiStates
import kotlinx.collections.immutable.persistentListOf
import org.junit.Rule
import org.junit.Test

class ImpactComposablesTest {
    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun trainingHubImpactShouldBeRendered() {
        composeTestRule.setContent {
            TrainingHubImpact(
                impactUiState = DummyTrainingHubUiStates.impactUiState,
                onWorkoutImpactTypeClick = {},
                onViewMoreOrLessClick = {},
                onShowInfoClicked = {},
                onTrainingModelInfoClicked = {}
            )
        }
        composeTestRule.onNodeWithTag("TrainingHubImpact").assertIsDisplayed()
    }

    @Test
    fun trainingHubImpactShouldBeRenderedEvenIfImpactCountIsEmpty() {
        composeTestRule.setContent {
            TrainingHubImpact(
                impactUiState = DummyTrainingHubUiStates.impactUiState.copy(_workoutsImpactCount = persistentListOf()),
                onWorkoutImpactTypeClick = {},
                onViewMoreOrLessClick = {},
                onShowInfoClicked = {},
                onTrainingModelInfoClicked = {}
            )
        }
        composeTestRule.onNodeWithTag("TrainingHubImpact").assertIsDisplayed()
    }
}
