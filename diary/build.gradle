plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.compose"
    id "stt.android.plugin.epoxy"
    id "stt.android.plugin.moshi"
}

android {
    namespace 'com.stt.android.home.diary'

    buildFeatures {
        dataBinding true
        buildConfig = true
    }
}

dependencies {
    implementation project(Deps.core)
    implementation project(Deps.appBase)
    implementation project(Deps.analytics)
    implementation project(Deps.domain)
    implementation project(Deps.diaryDomain)
    implementation project(Deps.workoutsDomain)
    implementation project(Deps.datasource)
    implementation project(Deps.persistence)
    implementation project(Deps.infoModel)
    implementation project(Deps.composeUi)
    implementation project(Deps.chartImpl)
    suuntoImplementation project(Deps.timeline)

    implementation libs.soy.algorithms

    implementation libs.material
    implementation libs.androidx.compose.material3
    implementation libs.androidx.room.ktx
    implementation libs.androidx.corektx
    implementation libs.mpandroid
    implementation libs.androidx.constraintlayout
    implementation libs.androidx.browser
    implementation libs.commonmark
    implementation(libs.sim.formatter) {
        exclude group: 'org.javolution', module: 'javolution'
    }
    implementation libs.vico

    implementation libs.lazytable
    testImplementation libs.truth
    implementation libs.vico
}
