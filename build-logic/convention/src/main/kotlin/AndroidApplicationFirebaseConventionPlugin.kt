import com.android.build.api.dsl.ApplicationExtension
import com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsExtension
import com.stt.android.invoke
import com.stt.android.isRunningOnCi
import com.stt.android.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.provideDelegate

/**
 * This plugin applies firebase crashlytics, analytics and performance (only playstore build)
 */
class AndroidApplicationFirebaseConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.google.gms.google-services")
                apply("com.google.firebase.crashlytics")
            }

            if (isRunningOnCi) {
                // fixme this is ugly, figure out if there is a better way
                val gradleTaskForChina = gradle.startParameter.taskRequests.toString()
                    .contains("China", ignoreCase = true)
                if (!gradleTaskForChina) {
                    pluginManager.apply("com.google.firebase.firebase-perf")
                }
            }

            dependencies {
                val bom = libs("firebase-bom")
                add("implementation", platform(bom))
                "implementation"(libs("firebase.analytics"))
                "playstoreImplementation"(libs("firebase.perf"))
                "implementation"(libs("firebase.crashlytics"))
            }

            extensions.configure<ApplicationExtension> {
                buildTypes {
                    debug {
                        configure<CrashlyticsExtension> {
                            mappingFileUploadEnabled = false
                        }
                    }
                    release {
                        // mapping files upload controlled by gradle.properties
                        val uploadMappingFiles: String? by project
                        configure<CrashlyticsExtension> {
                            mappingFileUploadEnabled = uploadMappingFiles.toBoolean()
                        }
                    }

                }
            }
        }
    }
}
