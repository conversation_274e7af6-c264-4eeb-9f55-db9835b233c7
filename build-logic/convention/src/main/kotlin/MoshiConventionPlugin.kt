import com.stt.android.invoke
import com.stt.android.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class MoshiConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            pluginManager.apply("com.google.devtools.ksp")

            dependencies {
                "implementation"(libs("moshi"))
                "implementation"(libs("moshi.kotlin"))
                "implementation"(libs("moshi.adapters"))
                "ksp"(libs("moshi.codegen"))
            }
        }
    }
}
