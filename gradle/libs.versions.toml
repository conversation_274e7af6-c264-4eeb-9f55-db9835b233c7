[versions]
activityModes = "0.19.0"
#noinspection GradleDependency
androidFab = "2.5.0"
androidGradlePlugin = "8.12.2"
androidWearable = "2.9.0"
androidxActivity = "1.11.0"
androidxAnnotations = "1.9.1"
androidxAppcompat = "1.7.1"
androidxArch = "2.2.0"
androidxBenchmark = "1.4.1"
androidxBrowser = "1.9.0"
androidxCamera2 = "1.5.0"
androidxCardview = "1.0.0"
androidxCollection = "1.5.0"
androidxConstraintLayout = "2.2.1"
androidxConstraintlayoutCompose = "1.1.1"
androidxCorektx = "1.17.0"
androidxCustomview = "1.2.0"
androidxCustomviewPoolingcontainer = "1.1.0"
androidxDocumentfile = "1.1.0"
androidxDynamicAnimation = "1.1.0"
androidxExif = "1.4.1"
androidxFragment = "1.8.9"
androidxGlance = "1.1.1"
androidxGridLayout = "1.1.0"
androidxHilt = "1.3.0"
androidxLifecycle = "2.9.3"
androidxLocalBroadcastManager = "1.1.0"
androidxMedia = "1.7.1"
androidxNavigation = "2.7.7"
androidxPaging = "3.3.6"
androidxPercentLayout = "1.0.0"
androidxPreference = "1.2.1"
androidxRecyclerview = "1.4.0"
androidxRoom = "2.8.0"
androidxSplashscreen = "1.0.1"
androidxSqlitektx = "2.6.0"
androidxSwiperefreshlayout = "1.1.0"
androidxTestCore = "1.7.0"
androidxTestJunit = "1.3.0"
androidxTestTruth = "1.7.0"
androidxTestOrchestrator = "1.6.1"
androidxTestRules = "1.7.0"
androidxTestRunner = "1.7.0"
androidxTracing = "1.2.0" # Do not update to 1.3.0, report 'Method beginSection in android.os.Trace not mocked.' from AdvancedLapsViewModelTest
androidxViewPager2 = "1.1.0"
androidxWindow = "1.4.0"
androidxWorkManager = "2.10.4"
assertj = "3.27.4"
barcodeScanning = "17.3.0"
betterLinkMovementMethod = "2.2.0"
circleIndicator = "2.1.6"
coil = "3.3.0"
commonmark = "0.25.1"
composeAccompanist = "0.36.0" # System ui controller removed in 0.37.0
composeBom = "2025.09.00"
composeRevealSwipe = "1.2.0"
coroutine = "1.10.2"
dagger = "2.57.1"
desugar = "2.1.5"
dexmaker = "2.28.6"
discreteScrollView = "1.5.1"
duktape = "1.3.0" # // Updating Duktape to version 1.4.0 causes crashes in the device view on Android 7.
easypermissions = "3.0.0"
emarsys = "3.10.2"
epoxy = "5.1.4"
eventbus = "3.3.1"
exoplayer = "2.19.1"
facebookSdk = "18.1.3"
firebaseBom = "34.2.0"
firebaseCrashlyticsPlugin = "3.0.6"
firebasePerfPlugin = "2.0.1"
flexbox = "3.0.0"
glide = "5.0.4"
gmsPlugin = "4.4.3"
googleOssPlugin = "0.10.7"
gpxparser = "2.3.1"
gradleVersionsPlugin = "0.52.0"
gravitySnapHelper = "2.2.2"
groupie = "2.10.1"
gson = "2.13.1"
helpshift = "7.11.2" # not androidx compatible yet
javaOtp = "0.4.0" # do not change before verifying with backend
javaXInject = "1"
jdeferred = "1.2.6"
jsbridge = "1.0.4"
jsonUnit = "4.1.1"
junit = "4.13.2"
koptional = "1.7.0"
kotlin = "2.2.10"
kotlinPoet = "2.2.0"
kotlinter = "5.2.0"
kotlinxCollectionsImmutable = "0.4.0"
ksp = "2.2.10-2.0.2" # keep aligned to kotlin version https://github.com/google/ksp/releases
ktlintPlugin = "0.4.27"
lazytable = "1.10.0"
leakcanaryAndroid = "2.14"
lottie = "6.6.7"
mapbox = "11.14.1"
mapboxSearch = "2.14.1"
mapboxServices = "7.6.0"
mapboxTurf = "7.6.0"
mapsUtils = "3.16.2"
material = "1.13.0"
mcumgrBle = "2.7.2"
mockito5 = "5.19.0"
mockito5Kotlin = "6.0.0"
mockk = "1.14.5"
moshi = "1.15.2"
mpandroid = "3.1.0"
nordicsemiBle = "1.6.0"
nordicsemiBleKtx = "2.10.2"
okhttp = "5.1.0"
opensdkChinaExternal = "0.2.0.2"
opensdkCommon = "0.2.0.2"
ormlite = "4.48" # do not update ormlite, just get rid of it
phoneNumber = "9.0.13"
photoview = "2.3.0"
playBilling = "7.1.1"
playInAppReview = "2.0.2"
playInstallReferrer = "2.2"
playPublisher = "3.12.1"
playServicesAuth = "21.4.0"
playServicesBase = "18.7.2"
playServicesLocation = "21.3.0"
playServicesMaps = "19.2.0"
playServicesOsslicenses = "17.2.2"
playServicesWearable = "19.0.0"
protobufLite = "3.25.5"
protobufPlugin = "0.9.4"
rajawali = "1.2.1970"
rangeSeekBar = "3.0.0"
retrofit = "3.0.0"
rxandroid = "1.2.1"
rxjava = "1.3.8"
rxjava2 = "2.2.21"
rxjava2Android = "2.1.1"
rxjava2Interop = "0.13.7"
rxjava2Kotlin = "2.4.0"
rxjavaProguard = "*******"
rxrelay = "2.1.1"
simFormatter = "1.19.0"
simpleToolTip = "1.0.3"
soyAlgorithms = "1.2.66"
sshPlugin = "2.12.0"
stateless4J = "2.5.0"
tencentJg = "1.1"
tencentSDK = "*******-release"
timber = "5.0.1"
timberJunitRule = "1.0.1"
tinypinyin = "2.0.3"
transcoder = "0.11.2"
truth = "1.4.4"
turbine = "1.2.1"
vico = "2.1.3"
viewbindingpropertydelegateNoreflection = "1.5.9"
volcenginertc = "3.58.1.19400"
wechat = "6.8.34"
owasp-dependencycheck = "12.1.3"
telephoto = "0.16.0"

[libraries]
accompanist-drawablepainter = { group = "com.google.accompanist", name = "accompanist-drawablepainter", version.ref = "composeAccompanist" }
accompanist-systemuicontroller = { group = "com.google.accompanist", name = "accompanist-systemuicontroller", version.ref = "composeAccompanist" }
activitymodes = { group = "com.soy.infomodel.activitymodes", name = "activitymodes", version.ref = "activityModes" }
android-desugar-nio = { group = "com.android.tools", name = "desugar_jdk_libs_nio", version.ref = "desugar" }
android-wearable = { group = "com.google.android.support", name = "wearable", version.ref = "androidWearable" }
androidfab = { group = "uk.co.markormesher", name = "android-fab", version.ref = "androidFab" }
androidx-activity = { group = "androidx.activity", name = "activity-ktx", version.ref = "androidxActivity" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "androidxActivity" }
androidx-annotations = { group = "androidx.annotation", name = "annotation", version.ref = "androidxAnnotations" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "androidxAppcompat" }
androidx-arch-core-testing = { group = "androidx.arch.core", name = "core-testing", version.ref = "androidxArch" }
androidx-benchmark-junit4 = { group = "androidx.benchmark", name = "benchmark-junit4", version.ref = "androidxBenchmark" }
androidx-browser = { group = "androidx.browser", name = "browser", version.ref = "androidxBrowser" }
androidx-camera-camera2 = { group = "androidx.camera", name = "camera-camera2", version.ref = "androidxCamera2" }
androidx-camera-lifecycle = { group = "androidx.camera", name = "camera-lifecycle", version.ref = "androidxCamera2" }
androidx-camera-view = { group = "androidx.camera", name = "camera-view", version.ref = "androidxCamera2" }
androidx-cardview = { group = "androidx.cardview", name = "cardview", version.ref = "androidxCardview" }
androidx-collection = { group = "androidx.collection", name = "collection", version.ref = "androidxCollection" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-compose-foundation = { group = "androidx.compose.foundation", name = "foundation" }
androidx-compose-material = { group = "androidx.compose.material", name = "material" }
androidx-compose-material3 = { group = "androidx.compose.material3", name = "material3" }
androidx-compose-runtime = { group = "androidx.compose.runtime", name = "runtime" }
androidx-compose-runtime-livedata = { group = "androidx.compose.runtime", name = "runtime-livedata" }
androidx-compose-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-compose-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-compose-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-compose-ui-viewbinding = { group = "androidx.compose.ui", name = "ui-viewbinding" }
androidx-compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "androidxConstraintLayout" }
androidx-constraintlayout-compose = { group = "androidx.constraintlayout", name = "constraintlayout-compose", version.ref = "androidxConstraintlayoutCompose" }
androidx-corektx = { group = "androidx.core", name = "core-ktx", version.ref = "androidxCorektx" }
androidx-customview = { group = "androidx.customview", name = "customview", version.ref = "androidxCustomview" }
androidx-customview-poolingcontainer = { group = "androidx.customview", name = "customview-poolingcontainer", version.ref = "androidxCustomviewPoolingcontainer" }
androidx-dagger-hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "androidxHilt" }
androidx-documentfile = { group = "androidx.documentfile", name = "documentfile", version.ref = "androidxDocumentfile" }
androidx-dynamicanimation = { group = "androidx.dynamicanimation", name = "dynamicanimation", version.ref = "androidxDynamicAnimation" }
androidx-exif = { group = "androidx.exifinterface", name = "exifinterface", version.ref = "androidxExif" }
androidx-fragment = { group = "androidx.fragment", name = "fragment-ktx", version.ref = "androidxFragment" }
androidx-fragment-compose = { group = "androidx.fragment", name = "fragment-compose", version.ref = "androidxFragment" }
androidx-fragment-testing = { group = "androidx.fragment", name = "fragment-testing", version.ref = "androidxFragment" }
androidx-glance-appwidget = { group = "androidx.glance", name = "glance-appwidget", version.ref = "androidxGlance" }
androidx-gridlayout = { group = "androidx.gridlayout", name = "gridlayout", version.ref = "androidxGridLayout" }
androidx-lifecycle-common-java8 = { group = "androidx.lifecycle", name = "lifecycle-common-java8", version.ref = "androidxLifecycle" }
androidx-lifecycle-livedata-ktx = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "androidxLifecycle" }
androidx-lifecycle-process = { group = "androidx.lifecycle", name = "lifecycle-process", version.ref = "androidxLifecycle" }
androidx-lifecycle-reactive-streams-ktx = { group = "androidx.lifecycle", name = "lifecycle-reactivestreams-ktx", version.ref = "androidxLifecycle" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "androidxLifecycle" }
androidx-lifecycle-service = { group = "androidx.lifecycle", name = "lifecycle-service", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "androidxLifecycle" }
androidx-lifecycle-lifecycle-runtime-compose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "androidxLifecycle" }
androidx-localbroadcastmanager = { group = "androidx.localbroadcastmanager", name = "localbroadcastmanager", version.ref = "androidxLocalBroadcastManager" }
androidx-media = { module = "androidx.media:media", version.ref = "androidxMedia" }
androidx-navigation-common-ktx = { group = "androidx.navigation", name = "navigation-common-ktx", version.ref = "androidxNavigation" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "androidxNavigation" }
androidx-navigation-fragment-ktx = { group = "androidx.navigation", name = "navigation-fragment-ktx", version.ref = "androidxNavigation" }
androidx-navigation-runtime-ktx = { group = "androidx.navigation", name = "navigation-runtime-ktx", version.ref = "androidxNavigation" }
androidx-navigation-safe-args = { group = "androidx.navigation", name = "navigation-safe-args-gradle-plugin", version.ref = "androidxNavigation" }
androidx-navigation-ui-ktx = { group = "androidx.navigation", name = "navigation-ui-ktx", version.ref = "androidxNavigation" }
androidx-paging = { group = "androidx.paging", name = "paging-runtime-ktx", version.ref = "androidxPaging" }
androidx-paging-compose = { group = "androidx.paging", name = "paging-compose", version.ref = "androidxPaging" }
androidx-percentlayout = { group = "androidx.percentlayout", name = "percentlayout", version.ref = "androidxPercentLayout" }
androidx-preference = { group = "androidx.preference", name = "preference-ktx", version.ref = "androidxPreference" }
androidx-recyclerview = { group = "androidx.recyclerview", name = "recyclerview", version.ref = "androidxRecyclerview" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "androidxRoom" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "androidxRoom" }
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "androidxRoom" }
androidx-room-rxjava2 = { group = "androidx.room", name = "room-rxjava2", version.ref = "androidxRoom" }
androidx-room-testing = { group = "androidx.room", name = "room-testing", version.ref = "androidxRoom" }
androidx-savedstate = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-savedstate", version.ref = "androidxLifecycle" }
androidx-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "androidxSplashscreen" }
androidx-sqlitektx = { group = "androidx.sqlite", name = "sqlite-ktx", version.ref = "androidxSqlitektx" }
androidx-swiperefreshlayout = { group = "androidx.swiperefreshlayout", name = "swiperefreshlayout", version.ref = "androidxSwiperefreshlayout" }
androidx-test-core = { group = "androidx.test", name = "core", version.ref = "androidxTestCore" }
androidx-test-ext = { group = "androidx.test.ext", name = "junit", version.ref = "androidxTestJunit" }
androidx-test-ext-ktx = { group = "androidx.test.ext", name = "junit-ktx", version.ref = "androidxTestJunit" }
androidx-test-ext-truth = { group = "androidx.test.ext", name = "truth", version.ref = "androidxTestTruth" }
androidx-test-orchestrator = { group = "androidx.test", name = "orchestrator", version.ref = "androidxTestOrchestrator" }
androidx-test-rules = { group = "androidx.test", name = "rules", version.ref = "androidxTestRules" }
androidx-test-runner = { group = "androidx.test", name = "runner", version.ref = "androidxTestRunner" }
androidx-tracing = { group = "androidx.tracing", name = "tracing-ktx", version.ref = "androidxTracing" }
androidx-viewpager2 = { group = "androidx.viewpager2", name = "viewpager2", version.ref = "androidxViewPager2" }
androidx-window = { group = "androidx.window", name = "window", version.ref = "androidxWindow" }
androidx-work = { group = "androidx.work", name = "work-runtime", version.ref = "androidxWorkManager" }
androidx-work-test = { group = "androidx.work", name = "work-testing", version.ref = "androidxWorkManager" }
assertj = { group = "org.assertj", name = "assertj-core", version.ref = "assertj" }
barcode-scanning = { group = "com.google.mlkit", name = "barcode-scanning", version.ref = "barcodeScanning" }
better-link-movement-method = { group = "me.saket", name = "better-link-movement-method", version.ref = "betterLinkMovementMethod" }
circleindicator = { group = "me.relex", name = "circleindicator", version.ref = "circleIndicator" }
coil = { group = "io.coil-kt.coil3", name = "coil", version.ref = "coil" }
coil-compose = { group = "io.coil-kt.coil3", name = "coil-compose", version.ref = "coil" }
coil-gif = { group = "io.coil-kt.coil3", name = "coil-gif", version.ref = "coil" }
coil-okhttp = { group = "io.coil-kt.coil3", name = "coil-network-okhttp", version.ref = "coil" }
coil-video = { group = "io.coil-kt.coil3", name = "coil-video", version.ref = "coil" }
commonmark = { group = "org.commonmark", name = "commonmark", version.ref = "commonmark" }
dagger = { group = "com.google.dagger", name = "dagger", version.ref = "dagger" }
dagger-compiler = { group = "com.google.dagger", name = "dagger-compiler", version.ref = "dagger" }
dagger-hilt = { group = "com.google.dagger", name = "hilt-android", version.ref = "dagger" }
dagger-hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "dagger" }
dagger-hilt-gradle-plugin = { group = "com.google.dagger", name = "hilt-android-gradle-plugin", version.ref = "dagger" }
dagger-hilt-testing = { group = "com.google.dagger", name = "hilt-android-testing", version.ref = "dagger" }
dexmaker = { group = "com.linkedin.dexmaker", name = "dexmaker-mockito-inline-extended", version.ref = "dexmaker" }
discrete-scrollview = { group = "com.yarolegovich", name = "discrete-scrollview", version.ref = "discreteScrollView" }
duktape = { group = "com.squareup.duktape", name = "duktape-android", version.ref = "duktape" } # // Updating Duktape to version 1.4.0 causes crashes in the device view on Android 7.
easypermissions = { group = "pub.devrel", name = "easypermissions", version.ref = "easypermissions" }
emarsys = { group = "com.emarsys", name = "emarsys-sdk", version.ref = "emarsys" }
emarsys-firebase = { group = "com.emarsys", name = "emarsys-firebase", version.ref = "emarsys" }
epoxy = { group = "com.airbnb.android", name = "epoxy", version.ref = "epoxy" }
epoxy-compose = { group = "com.airbnb.android", name = "epoxy-compose", version.ref = "epoxy" }
epoxy-databinding = { group = "com.airbnb.android", name = "epoxy-databinding", version.ref = "epoxy" }
epoxy-paging = { group = "com.airbnb.android", name = "epoxy-paging3", version.ref = "epoxy" }
epoxy-processor = { group = "com.airbnb.android", name = "epoxy-processor", version.ref = "epoxy" }
epoxy-viewbinder = { group = "com.airbnb.android", name = "epoxy-viewbinder", version.ref = "epoxy" }
eventbus = { group = "org.greenrobot", name = "eventbus", version.ref = "eventbus" }
exoplayer = { group = "com.google.android.exoplayer", name = "exoplayer", version.ref = "exoplayer" }
facebook-sdk = { group = "com.facebook.android", name = "facebook-android-sdk", version.ref = "facebookSdk" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics" }
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebaseBom" }
firebase-config = { group = "com.google.firebase", name = "firebase-config" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics" }
firebase-in-app-messaging = { group = "com.google.firebase", name = "firebase-inappmessaging-display" }
firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging" }
firebase-perf = { group = "com.google.firebase", name = "firebase-perf" }
flexbox = { group = "com.google.android.flexbox", name = "flexbox", version.ref = "flexbox" }
glide-disklrucache = { group = "com.github.bumptech.glide", name = "disklrucache", version.ref = "glide" } # keeping this dependency to use DiskLruCache
gpxparser = { group = "com.github.ticofab", name = "android-gpx-parser", version.ref = "gpxparser" }
gravity-snap-helper = { group = "com.github.rubensousa", name = "gravitysnaphelper", version.ref = "gravitySnapHelper" }
groupie = { group = "com.github.lisawray.groupie", name = "groupie", version.ref = "groupie" }
groupie-databinding = { group = "com.github.lisawray.groupie", name = "groupie-databinding", version.ref = "groupie" }
groupie-kotlin = { group = "com.github.lisawray.groupie", name = "groupie-kotlin-android-extensions", version.ref = "groupie" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }
helpshift = { group = "com.helpshift", name = "android-helpshift-aar", version.ref = "helpshift" } # not androidx compatible yet
java-otp = { group = "com.eatthepath", name = "java-otp", version.ref = "javaOtp" } # doNotChangeBeforeVerifyingWithBackend
javax-inject = { group = "javax.inject", name = "javax.inject", version.ref = "javaXInject" }
jdeferred = { group = "org.jdeferred", name = "jdeferred-android-aar", version.ref = "jdeferred" }
jsbridge = { module = "com.github.lzyzsd:jsbridge", version.ref = "jsbridge" }
json-unit = { group = "net.javacrumbs.json-unit", name = "json-unit-assertj", version.ref = "jsonUnit" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
koptional = { group = "com.gojuno.koptional", name = "koptional", version.ref = "koptional" }
kotlin-coroutines = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-core", version.ref = "coroutine" }
kotlin-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "coroutine" }
kotlin-coroutines-play-services = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-play-services", version.ref = "coroutine" }
kotlin-coroutines-rxjava = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-rx2", version.ref = "coroutine" }
kotlin-coroutines-test = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "coroutine" }
kotlin-poet = { group = "com.squareup", name = "kotlinpoet", version.ref = "kotlinPoet" }
kotlin-poet-ksp = { group = "com.squareup", name = "kotlinpoet-ksp", version.ref = "kotlinPoet" }
kotlin-reflect = { group = "org.jetbrains.kotlin", name = "kotlin-reflect", version.ref = "kotlin" }
ksp-api = { group = "com.google.devtools.ksp", name = "symbol-processing-api", version.ref = "ksp" }
kotlin-stdlib = { group = "org.jetbrains.kotlin", name = "kotlin-stdlib-jdk8", version.ref = "kotlin" }
kotlin-test = { group = "org.jetbrains.kotlin", name = "kotlin-test", version.ref = "kotlin" }
kotlinx-collections-immutable = { group = "org.jetbrains.kotlinx", name = "kotlinx-collections-immutable", version.ref = "kotlinxCollectionsImmutable" }
lazytable = { group = "io.github.oleksandrbalan", name = "lazytable", version.ref = "lazytable" }
leakcanary-android = { group = "com.squareup.leakcanary", name = "leakcanary-android", version.ref = "leakcanaryAndroid" }
lottie = { group = "com.airbnb.android", name = "lottie", version.ref = "lottie" }
lottie-compose = { group = "com.airbnb.android", name = "lottie-compose", version.ref = "lottie" }
mapbox = { group = "com.mapbox.maps", name = "android-ndk27", version.ref = "mapbox" }
mapbox-search = { group = "com.mapbox.search", name = "mapbox-search-android-ui-ndk27", version.ref = "mapboxSearch" }
mapbox-services = { group = "com.mapbox.mapboxsdk", name = "mapbox-sdk-services", version.ref = "mapboxServices" }
mapbox-turf = { group = "com.mapbox.mapboxsdk", name = "mapbox-sdk-turf", version.ref = "mapboxTurf" }
maps-utils = { group = "com.google.maps.android", name = "android-maps-utils", version.ref = "mapsUtils" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
mcumgr-ble = { module = "no.nordicsemi.android:mcumgr-ble", version.ref = "mcumgrBle" }
mockito5 = { group = "org.mockito", name = "mockito-core", version.ref = "mockito5" }
mockito5-kotlin = { group = "org.mockito.kotlin", name = "mockito-kotlin", version.ref = "mockito5Kotlin" }
mockk = { group = "io.mockk", name = "mockk", version.ref = "mockk" }
mockwebserver = { group = "com.squareup.okhttp3", name = "mockwebserver", version.ref = "okhttp" }
moshi = { group = "com.squareup.moshi", name = "moshi", version.ref = "moshi" }
moshi-adapters = { group = "com.squareup.moshi", name = "moshi-adapters", version.ref = "moshi" }
moshi-codegen = { group = "com.squareup.moshi", name = "moshi-kotlin-codegen", version.ref = "moshi" }
moshi-kotlin = { group = "com.squareup.moshi", name = "moshi-kotlin", version.ref = "moshi" } # Moshi Kotlin dep should only be used in debug builds i.e. debugImplementation!
mpandroid = { group = "com.github.PhilJay", name = "MPAndroidChart", version.ref = "mpandroid" }
nordicsemi-ble = { group = "no.nordicsemi.android.support.v18", name = "scanner", version.ref = "nordicsemiBle" }
nordicsemi-ble-ktx = { group = "no.nordicsemi.android", name = "ble-ktx", version.ref = "nordicsemiBleKtx" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
okhttp-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
opensdk-common = { module = "com.bytedance.ies.ugc.aweme:opensdk-common", version.ref = "opensdkCommon" }
opensdk-china-external = { module = "com.bytedance.ies.ugc.aweme:opensdk-china-external", version.ref = "opensdkChinaExternal" }
ormlite = { group = "com.j256.ormlite", name = "ormlite-android", version.ref = "ormlite" }
phone-number = { group = "com.googlecode.libphonenumber", name = "libphonenumber", version.ref = "phoneNumber" }
photoview = { group = "com.github.chrisbanes", name = "PhotoView", version.ref = "photoview" }
play-auth = { group = "com.google.android.gms", name = "play-services-auth", version.ref = "playServicesAuth" }
play-base = { group = "com.google.android.gms", name = "play-services-base", version.ref = "playServicesBase" }
play-billing = { group = "com.android.billingclient", name = "billing", version.ref = "playBilling" }
play-billing-ktx = { group = "com.android.billingclient", name = "billing-ktx", version.ref = "playBilling" }
play-in-app-review = { group = "com.google.android.play", name = "review-ktx", version.ref = "playInAppReview" }
play-install-referrer = { group = "com.android.installreferrer", name = "installreferrer", version.ref = "playInstallReferrer" }
play-location = { group = "com.google.android.gms", name = "play-services-location", version.ref = "playServicesLocation" }
play-maps = { group = "com.google.android.gms", name = "play-services-maps", version.ref = "playServicesMaps" }
play-osslicenses = { group = "com.google.android.gms", name = "play-services-oss-licenses", version.ref = "playServicesOsslicenses" }
play-wearable = { group = "com.google.android.gms", name = "play-services-wearable", version.ref = "playServicesWearable" }
protobuf-converter = { group = "com.squareup.retrofit2", name = "converter-protobuf", version.ref = "retrofit" }
protobuf-lite = { group = "com.google.protobuf", name = "protobuf-javalite", version.ref = "protobufLite" }
protobuf-protoc = { group = "com.google.protobuf", name = "protoc", version.ref = "protobufLite" }
rangeseekbar = { group = "com.github.Jay-Goo", name = "RangeSeekBar", version.ref = "rangeSeekBar" }
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-moshi = { group = "com.squareup.retrofit2", name = "converter-moshi", version.ref = "retrofit" }
retrofit-rxjava = { group = "com.squareup.retrofit2", name = "adapter-rxjava2", version.ref = "retrofit" }
retrofit-scalars = { group = "com.squareup.retrofit2", name = "converter-scalars", version.ref = "retrofit" }
revealswipe = { group = "de.charlex.compose", name = "revealswipe", version.ref = "composeRevealSwipe" }
rxandroid = { group = "io.reactivex", name = "rxandroid", version.ref = "rxandroid" }
rxjava = { group = "io.reactivex", name = "rxjava", version.ref = "rxjava" }
rxjava-proguard = { group = "com.artemzin.rxjava", name = "proguard-rules", version.ref = "rxjavaProguard" }
rxjava2 = { group = "io.reactivex.rxjava2", name = "rxjava", version.ref = "rxjava2" }
rxjava2-android = { group = "io.reactivex.rxjava2", name = "rxandroid", version.ref = "rxjava2Android" }
rxjava2-interop = { group = "com.github.akarnokd", name = "rxjava2-interop", version.ref = "rxjava2Interop" }
rxjava2-kotlin = { group = "io.reactivex.rxjava2", name = "rxkotlin", version.ref = "rxjava2Kotlin" }
rxrelay = { group = "com.jakewharton.rxrelay2", name = "rxrelay", version.ref = "rxrelay" }
sim-formatter = { group = "com.amersports.formatter", name = "format", version.ref = "simFormatter" }
simple-tooltip = { group = "com.github.SportsTrackingTechnologies", name = "simple-tool-tip", version.ref = "simpleToolTip" }
soy-algorithms = { group = "com.soy.algorithms", name = "sttalg-android", version.ref = "soyAlgorithms" }
stateless4j = { group = "com.github.oxo42", name = "stateless4j", version.ref = "stateless4J" }
tencend-huawei-sdk = { group = "com.tencent.tpns", name = "huawei", version.ref = "tencentSDK" }
tencent-jg = { group = "com.tencent.jg", name = "jg", version.ref = "tencentJg" }
tencent-oppo-sdk = { group = "com.tencent.tpns", name = "oppo", version.ref = "tencentSDK" }
tencent-sdk = { group = "com.tencent.tpns", name = "tpns", version.ref = "tencentSDK" }
tencent-xiaomi-sdk = { group = "com.tencent.tpns", name = "xiaomi", version.ref = "tencentSDK" }
timber = { group = "com.jakewharton.timber", name = "timber", version.ref = "timber" }
timber-junit-rule = { group = "net.lachlanmckee", name = "timber-junit-rule", version.ref = "timberJunitRule" }
tinypinyin = { module = "me.majiajie:tinypinyin", version.ref = "tinypinyin" }
transcoder = { group = "com.otaliastudios", name = "transcoder", version.ref = "transcoder" }
truth = { group = "com.google.truth", name = "truth", version.ref = "truth" }
turbine = { group = "app.cash.turbine", name = "turbine", version.ref = "turbine" }
vico = { group = "com.patrykandpatrick.vico", name = "compose-m3", version.ref = "vico" }
viewbindingpropertydelegate-noreflection = { module = "com.github.kirich1409:viewbindingpropertydelegate-noreflection", version.ref = "viewbindingpropertydelegateNoreflection" }
volcenginertc = { module = "com.volcengine:VolcEngineRTC", version.ref = "volcenginertc" }
wearable = { group = "com.google.android.wearable", name = "wearable", version.ref = "androidWearable" }
wechat = { group = "com.tencent.mm.opensdk", name = "wechat-sdk-android", version.ref = "wechat" }
rajawali = { group = "org.rajawali3d", name = "rajawali", version.ref = "rajawali" }
telephoto = { group = "me.saket.telephoto", name = "zoomable-image-coil3", version.ref = "telephoto" }

# Dependencies of build-logic
android-gradle-plugin = { group = "com.android.tools.build", name = "gradle", version.ref = "androidGradlePlugin" }
firebase-crashlytics-gradle-plugin = { group = "com.google.firebase", name = "firebase-crashlytics-gradle", version.ref = "firebaseCrashlyticsPlugin" }
firebase-perf-gradle-plugin = { group = "com.google.firebase", name = "perf-plugin", version.ref = "firebasePerfPlugin" }
kotlin-allopen-plugin = { group = "org.jetbrains.kotlin", name = "kotlin-allopen", version.ref = "kotlin" }
kotlin-gradle-plugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlin" }
ksp-gradle-plugin = { group = "com.google.devtools.ksp", name = "com.google.devtools.ksp.gradle.plugin", version.ref = "ksp" }
play-publisher-plugin = { group = "com.github.triplet.gradle", name = "play-publisher", version.ref = "playPublisher" }
protobuf-plugin = { group = "com.google.protobuf", name = "protobuf-gradle-plugin", version.ref = "protobufPlugin" }

# Temporary added as runtime dependency until migrating to convention plugins
google-oss-licenses-plugin = { group = "com.google.android.gms", name = "oss-licenses-plugin", version.ref = "googleOssPlugin" }
ktlint-plugin = { group = "io.nlopez.compose.rules", name = "ktlint", version.ref = "ktlintPlugin" }

[plugins]
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
android-library = { id = "com.android.library", version.ref = "androidGradlePlugin" }
android-test = { id = "com.android.test", version.ref = "androidGradlePlugin" }
androidx-benchmark = { id = "androidx.benchmark", version.ref = "androidxBenchmark" }
androidx-navigation-safeargs = { id = "androidx.navigation.safeargs", version.ref = "androidxNavigation" }
androidx-navigation-safeargs-kotlin = { id = "androidx.navigation.safeargs.kotlin", version.ref = "androidxNavigation" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebaseCrashlyticsPlugin" }
firebase-perf = { id = "com.google.firebase.firebase-perf", version.ref = "firebasePerfPlugin" }
gms = { id = "com.google.gms.google-services", version.ref = "gmsPlugin" }
gradle-versions = { id = "com.github.ben-manes.versions", version.ref = "gradleVersionsPlugin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "dagger" }
kotlin-allopen = { id = "org.jetbrains.kotlin.plugin.allopen", version.ref = "kotlin" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
kotlin-ksp = { id = "com.google.devtools.ksp", version.ref = "ksp"}
kotlinter = { id = "org.jmailen.kotlinter", version.ref = "kotlinter" }
play-publisher = { id = "com.github.triplet.play", version.ref = "playPublisher" }
protobuf = { id = "com.google.protobuf", version.ref = "protobufPlugin" }
ssh = { id = "org.hidetake.ssh", version.ref = "sshPlugin" }
owasp-dependencycheck = { id = "org.owasp.dependencycheck", version.ref = "owasp-dependencycheck" }
