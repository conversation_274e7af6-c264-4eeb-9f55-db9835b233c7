#!/bin/bash

# Test script for MDS API KSP code generation

echo "=== MDS API KSP Code Generation Test ==="
echo "Starting KSP compilation test..."

# Change to project directory
cd "$(dirname "$0")"

# Clean previous builds
echo "Cleaning previous builds..."
./gradlew clean

# Build SuuntoConnectivity module with KSP
echo "Building SuuntoConnectivity with KSP..."
./gradlew :SuuntoConnectivity:kspKotlin

# Check if KSP generated files exist
echo "Checking for generated files..."

KSP_OUTPUT_DIR="SuuntoConnectivity/build/generated/ksp/main/kotlin"

if [ -d "$KSP_OUTPUT_DIR" ]; then
    echo "✓ KSP output directory exists: $KSP_OUTPUT_DIR"
    
    # List generated files
    echo "Generated files:"
    find "$KSP_OUTPUT_DIR" -name "*.kt" | while read file; do
        echo "  - $(basename "$file")"
    done
    
    # Check for specific expected files
    EXPECTED_FILES=(
        "RunSportModesMdsApiV2Consumer.kt"
        "RunSportModesMdsApiV2Producer.kt"
        "RunSportModesMdsApiV2MessageConstants.kt"
    )
    
    for expected_file in "${EXPECTED_FILES[@]}"; do
        if find "$KSP_OUTPUT_DIR" -name "$expected_file" | grep -q .; then
            echo "✓ Found expected file: $expected_file"
        else
            echo "✗ Missing expected file: $expected_file"
        fi
    done
else
    echo "✗ KSP output directory not found: $KSP_OUTPUT_DIR"
fi

# Try to compile the project
echo "Attempting full project compilation..."
if ./gradlew :SuuntoConnectivity:compileKotlin; then
    echo "✓ Project compilation successful"
else
    echo "✗ Project compilation failed"
    exit 1
fi

# Run tests if available
echo "Running MDS API tests..."
if ./gradlew :SuuntoConnectivity:test --tests "*MdsApi*"; then
    echo "✓ MDS API tests passed"
else
    echo "⚠ MDS API tests failed or not found"
fi

echo "=== KSP Code Generation Test Complete ==="
