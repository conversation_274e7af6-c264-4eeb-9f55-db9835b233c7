package com.stt.android.remote.user

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Same as [com.stt.android.domain.user.BackendPublicUser] in appbase module,
 * but with Moshi support
 */
@JsonClass(generateAdapter = true)
data class RemotePublicUser(
    @<PERSON><PERSON>(name = "key") val key: String,
    @<PERSON><PERSON>(name = "username") val username: String,
    @<PERSON><PERSON>(name = "profileImageUrl") val profileImageUrl: String?,
    @<PERSON><PERSON>(name = "imageKey") val profileImageKey: String?,
    @<PERSON><PERSON>(name = "realName") val realName: String?,
    @<PERSON><PERSON>(name = "lastModified") val lastModified: Long,
    @<PERSON><PERSON>(name = "city") val city: String?,
    @<PERSON><PERSON>(name = "country") val country: String?,
    @<PERSON><PERSON>(name = "website") val website: String?
)
