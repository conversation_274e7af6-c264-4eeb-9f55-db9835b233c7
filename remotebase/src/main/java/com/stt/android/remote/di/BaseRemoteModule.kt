package com.stt.android.remote.di

import android.content.Context
import android.content.SharedPreferences
import com.stt.android.baseremote.BuildConfig
import com.stt.android.remote.SharedOkHttpClient
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object BaseRemoteModule {

    const val PREFS_NAME = "UrlConfigurationPreferences"
    const val CONFIGURATION_KEY = "URL_CONFIGURATION_KEY"

    /**
     * A base instance of [OkHttpClient] should be shared to improve memory and performance.
     * https://square.github.io/okhttp/4.x/okhttp/okhttp3/-ok-http-client/#okhttpclients-should-be-shared
     */
    @Provides
    @SharedOkHttpClient
    @Singleton
    fun provideSharedOkHttpClient(): OkHttpClient {
        return OkHttpClient()
    }

    @Provides
    @Named(PREFS_NAME)
    fun provideUrlConfigurationPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    /**
     * Singleton guarantees this configuration will not change unless app process is restarted
     */
    @Provides
    @Singleton
    fun provideUrlConfiguration(
        @Named(PREFS_NAME) preferences: SharedPreferences
    ): BaseUrlConfiguration {
        val customConfigurationKey = preferences.getString(CONFIGURATION_KEY, null)
        return BaseUrlConfiguration.entries.firstOrNull { it.key == customConfigurationKey }
            ?: provideDefaultConfiguration()
    }

    @Suppress("KotlinConstantConditions")
    private fun provideDefaultConfiguration(): BaseUrlConfiguration = when {
        BuildConfig.FLAVOR_store == "china" -> BaseUrlConfiguration.PROD_CHINA
        BuildConfig.DEBUG -> BaseUrlConfiguration.TEST
        else -> BaseUrlConfiguration.PROD_GLOBAL
    }
}
