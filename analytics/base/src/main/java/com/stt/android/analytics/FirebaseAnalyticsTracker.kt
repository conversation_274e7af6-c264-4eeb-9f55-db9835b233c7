package com.stt.android.analytics

import android.content.Context
import android.os.Bundle
import androidx.annotation.Size

interface FirebaseAnalyticsTracker {

    fun init(context: Context)

    fun trackEvent(
        @AnalyticsEvent.EventName
        @Size(min = 1L, max = 40L)
        eventName: String,
        propertyKey: String,
        propertyValue: Any?
    )

    fun trackEvent(
        @AnalyticsEvent.EventName
        @Size(min = 1L, max = 40L)
        eventName: String,
        analyticsProperties: AnalyticsProperties
    )

    fun trackEvent(
        @AnalyticsEvent.EventName
        @Size(min = 1L, max = 40L)
        eventName: String,
        bundle: Bundle?
    )

    fun trackEvent(
        @AnalyticsEvent.EventName
        @Size(min = 1L, max = 40L)
        eventName: String
    ) = trackEvent(eventName, null)

    fun setUserId(userAnalyticsUUID: String)

    /**
     * There are max 25 custom user properties in Firebase Analytics, let's be careful which one we choose to send!
     */
    fun trackUserProperty(userProperty: String, @Size(min = 0L, max = 36L) value: String)

    fun logout()

    companion object {
        /**
         * Checking it matches the specs at
         * https://developers.google.com/android/reference/com/google/firebase/analytics/FirebaseAnalytics.UserProperty
         */
        fun isValidUserProperty(userProperty: String): Boolean {
            if (userProperty.isBlank()) return false
            if (!Character.isLetter(userProperty[0].code)) return false
            for (c in userProperty) {
                if (!Character.isLetterOrDigit(c.code) && c != '_') return false
            }
            if (userProperty.startsWith("firebase_") ||
                userProperty.startsWith("google_") ||
                userProperty.startsWith("ga_")
            ) {
                return false
            }
            if (userProperty.length > 24) return false
            return true
        }
    }
}
