package com.stt.android.analytics

import android.content.Context
import android.content.pm.PackageManager
import timber.log.Timber

object MovescountUtils {

    private const val MOVESCOUNT_APP_PACKAGE_NAME = "com.suunto.movescount.android"

    @JvmStatic
    fun isMovescountInstalled(context: Context): <PERSON><PERSON>an {
        return try {
            context.packageManager.getPackageInfo(MOVESCOUNT_APP_PACKAGE_NAME, PackageManager.GET_META_DATA)
            Timber.d("Movescount Android App is present in phone")
            true
        } catch (exception: PackageManager.NameNotFoundException) {
            Timber.d("Movescount Android App is not present in phone")
            false
        }
    }
}
