package com.stt.android.analytics

import com.google.common.truth.Truth.assertWithMessage
import com.stt.android.analytics.FirebaseAnalyticsTracker.Companion.isValidUserProperty
import org.junit.Assert
import org.junit.Test
import java.lang.reflect.Modifier.isStatic
import kotlin.reflect.full.memberProperties
import kotlin.reflect.javaType

class AnalyticsPropertiesTest {
    @Test
    fun put() {
        val properties = AnalyticsProperties()
            .put("StringProperty", "StringValue")
            .put("IntProperty", 42)
            .put("FloatProperty", 12.3f)
        val map = properties.map
        Assert.assertEquals(3, map.size.toLong())
        Assert.assertEquals("StringValue", map["StringProperty"])
        Assert.assertEquals(42, map["IntProperty"])
        Assert.assertEquals(12.3f, map["FloatProperty"] as Float, 0.0001f)
    }

    @Test
    fun putOnOff() {
        val properties = AnalyticsProperties()
            .putOnOff("OnProperty", true)
            .putOnOff("OffProperty", false)
        val map = properties.map
        Assert.assertEquals(2, map.size.toLong())
        Assert.assertEquals(AnalyticsPropertyValue.ON, map["OnProperty"])
        Assert.assertEquals(AnalyticsPropertyValue.OFF, map["OffProperty"])
    }

    @Test
    fun putYesNo() {
        val properties = AnalyticsProperties()
            .putYesNo("YesProperty", true)
            .putYesNo("NoProperty", false)
        val map = properties.map
        Assert.assertEquals(2, map.size.toLong())
        Assert.assertEquals(AnalyticsPropertyValue.YES, map["YesProperty"])
        Assert.assertEquals(AnalyticsPropertyValue.NO, map["NoProperty"])
    }

    @Test
    fun putTrueFalse() {
        val properties = AnalyticsProperties()
            .putTrueFalse("TrueProperty", true)
            .putTrueFalse("FalseProperty", false)
        val map = properties.map
        Assert.assertEquals(2, map.size.toLong())
        Assert.assertEquals(AnalyticsPropertyValue.TRUE, map["TrueProperty"])
        Assert.assertEquals(AnalyticsPropertyValue.FALSE, map["FalseProperty"])
    }

    @Test
    fun testUserPropertiesForFirebase() {
        val userPropertyNames = FirebaseUserProperty::class.memberProperties
            .filter { it.isConst && it.returnType.javaType == String::class.java }
            .mapNotNull { it.call()?.toString() }
        for (propertyName in userPropertyNames) {
            assertWithMessage("propertyField: $propertyName is too long for firebase")
                .that(propertyName.length).isAtMost(24)
            assertWithMessage("propertyField: $propertyName is not valid for firebase")
                .that(isValidUserProperty(propertyName)).isTrue()
        }
    }

    @Test
    fun testEventsForFirebase() {
        val eventNames = AnalyticsEvent::class.java.declaredFields
            .filter { isStatic(it.modifiers) && it.type == String::class.java }
            .mapNotNull { it.get(null)!!.toString() }
        val knownTooLongNames = setOf(
            "LocationConfirmAutomaticLocationCompleted",
            "LocationAutomaticLocationPermissionToggled",
            "OnboardingAskEmailForUnverifiedUserScreen",
            "ConnectedServiceConnectionSuccessfulOnClient"
        )
        for (eventName in eventNames) {
            if (eventName in knownTooLongNames) continue
            assertWithMessage("eventName: $eventName is too long for firebase")
                .that(eventName.length).isAtMost(40)
        }
    }
}
