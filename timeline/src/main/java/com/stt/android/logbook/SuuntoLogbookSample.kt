package com.stt.android.logbook

import com.stt.android.sim.Cylinder
import com.stt.android.sim.DiveRoute
import com.stt.android.sim.Marks
import com.stt.android.sim.ZappSample
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime

/**
 * SuuntoLogbookSample
 *
 * This interface should be extended when more data from the samples are required
 *
 * The properties are nullable here but the internal implementation may use non-nullable
 * primitive types to save some memory
 *
 * A good reference for which values go into the SML samples is the latest SBEM descriptor
 * https://bitbucket.org/suunto/nextgen/src/develop/nea/logger/descriptors.sbemdesc
 */
sealed interface SuuntoLogbookSample {
    // Timestamp as milliseconds since epoch UTC
    // it's editable for performance reasons only
    var timestamp: Long

    // Timestamp as zonedDateTime
    val zonedDateTime: ZonedDateTime
        get() = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.of("UTC"))

    data class Events(
        override var timestamp: Long,
        val events: List<Marks>
    ) : SuuntoLogbookSample

    data class DiveEvent(
        override var timestamp: Long,
        val event: Marks
    ) : SuuntoLogbookSample

    sealed class HR : SuuntoLogbookSample {
        // Heart rate, unit Hz (60 /min = 1 Hz)
        abstract val hr: Float

        data class Optical(override var timestamp: Long, override val hr: Float) : HR()
        data class Ibi(override var timestamp: Long, override var hr: Float) : HR()

        // The difference with Ibi is that this contains the raw data, above Ibi is used to filter HR data
        // So at the end, using Ibi will end up having less than values than what the belt recorded
        data class RawIbi(
            override var timestamp: Long,
            override var hr: Float,
            val values: List<Int>
        ) : HR()

        data class RecoveryInThreeMins(override var timestamp: Long, override val hr: Float) : HR()
    }

    data class Zapp(
        override var timestamp: Long,
        val zappSample: ZappSample
    ) : SuuntoLogbookSample

    data class Location(
        override var timestamp: Long,
        // Latitude in radians
        val latitude: Float,
        // Longitude in radians
        val longitude: Float
    ) : SuuntoLogbookSample

    data class VerticalOscillation(
        override var timestamp: Long,
        val verticalOscillation: Float
    ) : SuuntoLogbookSample

    data class GroundContactTime(
        override var timestamp: Long,
        val groundContactTime: Float
    ) : SuuntoLogbookSample

    // the name periodic comes from the SBEM definition for this sample
    data class Periodic(
        override var timestamp: Long,
        private val _power: Float,
        private val _distance: Float,
        private val _altitude: Float,
        private val _speed: Float,
        private val _cadence: Float,
        private val _verticalSpeed: Float,
        private val _temperature: Float,
        private val _duration: Float,
        // unit s
        val breaststrokeGlideTime: Int?,
        // unit degree
        val freestyleAvgBreathAngle: Int?,
        // unit degree
        val breaststrokeAvgBreathAngle: Int?,
        // unit /min
        val breathingRate: Int?,
        // unit degree
        val breaststrokeHeadAngle: Int?,
        // unit degree
        val freestyleHeadAngle: Int?,
        // unit /round
        val skipsPerRound: Int?,
    ) : SuuntoLogbookSample {
        // Power, unit W
        val power: Float? get() = _power.floatIfFinite

        // Distance from start, unit m (source is told in distance event)
        val distance: Float? get() = _distance.floatIfFinite

        // Altitude, unit m (source is told in altitude event)
        val altitude: Float? get() = _altitude.floatIfFinite

        // Speed, unit m/s
        val speed: Float? get() = _speed.floatIfFinite

        // Cadence, unit Hz (source is told in cadence event)
        val cadence: Float? get() = _cadence.floatIfFinite

        // Vertical speed, unit m/s
        val verticalSpeed: Float? get() = _verticalSpeed.floatIfFinite

        // Temperature, unit K (0 C = 273.15 K)
        val temperature: Float? get() = _temperature.floatIfFinite

        // unit s
        val duration: Float? get() = _duration.floatIfFinite
    }

    data class Dive(
        override var timestamp: Long,
        private val _depth: Float,
        val cylinders: List<Cylinder>?,
        private val _ventilation: Float,
        // Temperature, unit K (0 C = 273.15 K)
        private val _temperature: Float,
        val diveRoute: DiveRoute?,
        val diveRouteOrigin: NgDiveRouteOrigin?,
        val diveRouteQuality: Double?,
    ) : SuuntoLogbookSample {
        val depth: Float? get() = _depth.floatIfFinite
        val ventilation: Float? get() = _ventilation.floatIfFinite
        val temperature: Float? get() = _temperature.floatIfFinite
    }
}

internal val Float.floatIfFinite: Float?
    get() = takeIf { it.isFinite() }
