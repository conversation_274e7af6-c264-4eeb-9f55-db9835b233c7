package com.stt.android.logbook

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.suunto.algorithms.data.HeartRate.Companion.hz
import kotlinx.parcelize.Parcelize
import java.time.ZonedDateTime
import kotlin.math.roundToInt

/**
 * Class for reading device information within a suunto logbook
 */
@JsonClass(generateAdapter = true)
data class SuuntoLogbookDevice(
    @param:<PERSON><PERSON>(name = "Info") val deviceInfo: SuuntoLogbookDeviceInfo?,
    @param:<PERSON><PERSON>(name = "Name") val name: String,
    @param:<PERSON><PERSON>(name = "SerialNumber") val serialNumber: String
)

@JsonClass(generateAdapter = true)
data class SuuntoLogbookDeviceInfo(
    @param:Json(name = "HW") val hwName: String?,
    @param:Json(name = "SW") val swVersion: String?,
    @param:<PERSON><PERSON>(name = "BSL") val bslVersion: String?,
    @param:Json(name = "BatteryAtStart") val batteryAtStart: String?, // example "Charge: 87%, Voltage: 4.054V"
    @param:Json(name = "BatteryAtEnd") val batteryAtEnd: String? // example "Charge: 87%, Voltage: 4.054V"
)

/**
 * Model for sml MinMaxType
 */
@JsonClass(generateAdapter = true)
data class SuuntoLogbookMinMax(
    @param:Json(name = "Min") val min: Float?,
    @param:Json(name = "MinTime") val minTime: Float?,
    @param:Json(name = "Max") val max: Float?,
    @param:Json(name = "MaxTime") val maxTime: Float?,
    @param:Json(name = "Avg") val avg: Float?
)

/**
 * Model for settings in header
 */
@JsonClass(generateAdapter = true)
data class Settings(
    @param:Json(name = "AltiBaroProfile") val altiBaroProfile: String?,
    @param:Json(name = "AutoLap") val autoLap: AutoLap?,
    @param:Json(name = "AutoPause") val autoPause: AutoPause?,
    @param:Json(name = "BikePodUsed") val bikePodUsed: Boolean?,
    @param:Json(name = "EnabledNavigationSystems") val enabledNavSystem: String?,
    @param:Json(name = "FootPodAutoCalib") val footPodAutoCalib: FootPodAutoCalib?,
    @param:Json(name = "FootPodUsed") val footPodUsed: Boolean?,
    @param:Json(name = "FusedAltiUsed") val fusedAltiUsed: Boolean?,
    @param:Json(name = "HrUsed") val hrUsed: Boolean?,
    @param:Json(name = "PowerPodUsed") val powerPodUsed: Boolean?,
    @param:Json(name = "SgeeEpoTimestamp") val sgeeEpoTimestamp: ZonedDateTime?
)

@JsonClass(generateAdapter = true)
@Parcelize
data class CompetitionResult(
    @param:Json(name = "WorkoutId") val workoutId: String?,
    @param:Json(name = "Username") val username: String?,
    @param:Json(name = "Distance") val distance: Double?,
    @param:Json(name = "Result") val result: Int?,
    @param:Json(name = "FinishDuration") val finishDuration: Long?,
    @param:Json(name = "TargetDuration") val targetDuration: Long?,
    @param:Json(name = "WorkoutOwner") val workoutOwner: String?,
) : Parcelable

@JsonClass(generateAdapter = true)
data class AutoLap(
    @param:Json(name = "Distance") val distance: Float?,
    @param:Json(name = "Duration") val duration: Float?,
    @param:Json(name = "Enabled") val enabled: Boolean?
)

@JsonClass(generateAdapter = true)
data class AutoPause(
    @param:Json(name = "Enabled") val enabled: Boolean?
)

@JsonClass(generateAdapter = true)
data class FootPodAutoCalib(
    @param:Json(name = "Coeff") val coeff: Float?,
    @param:Json(name = "Used") val used: Boolean?
)

/**
 * SuuntoLogbookFeeling field from device contains an Integer number [0, 5], where 0 means
 * that the feeling is not defined while numbers from 1 to 5 are respectively
 * Poor, Average, Good, Very good and Excellent
 */
enum class SuuntoLogbookFeeling {
    // Other values
    Undefined,

    // Defined values
    Poor,
    Average,
    Good,
    VeryGood,
    Excellent
}

data class SuuntoLogbookSamples(
    // samples are guaranteed sorted by timestamp in the right way in [MoshiSmlParser]
    @param:Json(name = "Samples") val samples: List<SuuntoLogbookSample>,
    val statistics: SmlSampleStatistics
)

/**
 * Zone information that comes within a suunto logbook.
 *
 * Duration fields unit: seconds
 * Limits fields unit: Hz (60 /min = 1 Hz)
 */
@JsonClass(generateAdapter = true)
data class SuuntoLogbookZone(
    @param:Json(name = "Zone1Duration") val zone1Duration: Float?,
    @param:Json(name = "Zone2Duration") val zone2Duration: Float?,
    @param:Json(name = "Zone2LowerLimit") val zone2LowerLimit: Float?,
    @param:Json(name = "Zone3Duration") val zone3Duration: Float?,
    @param:Json(name = "Zone3LowerLimit") val zone3LowerLimit: Float?,
    @param:Json(name = "Zone4Duration") val zone4Duration: Float?,
    @param:Json(name = "Zone4LowerLimit") val zone4LowerLimit: Float?,
    @param:Json(name = "Zone5Duration") val zone5Duration: Float?,
    @param:Json(name = "Zone5LowerLimit") val zone5LowerLimit: Float?
)

/**
 * Personal information about user
 * MaxHR set by user in Settings -> General -> Personal. Unit: Hz (60 /min = 1 Hz)
 */
@JsonClass(generateAdapter = true)
data class SuuntoLogbookPersonal(
    @param:Json(name = "MaxHR") val personalMaxHr: Float?
) {
    val personalMaxHrInBpm: Int? get() = personalMaxHr?.hz?.inBpm?.roundToInt()
}

/**
 * Can contain null if sml zip is missing
 */
class SmlZip(val sml: ByteArray?)

data class SuuntoLogbookSummaryContent(
    val summary: SuuntoLogbookSummary,
    val windows: List<SuuntoLogbookWindow>,
    val zapps: List<SuuntoLogbookZapp>,
    val diveHeader: NgDiveHeader?,
    val diveFooter: NgDiveFooter?
)

data class SmlFromJson(
    @param:Json(name = "Summary") val summaryContent: SuuntoLogbookSummaryContent,
    @param:Json(name = "Data") val data: SuuntoLogbookSamples
)

data class LogbookGasData(
    val gasIndex: Int,
    val gasName: String,
    val startPressure: Float?, // measured in kPa
    val endPressure: Float? = null, // measured in kPa
    val avgVentilation: Float? = null,
    val transmitterId: Int? = null,
    val startPressure2: Float? = null, // For side mount
    val endPressure2: Float? = null, // For side mount
    val transmitterId2: Int? = null, // For side mount
) {
    val usedPressure
        get() =
            if (startPressure != null && endPressure != null) startPressure - endPressure else null

    val usedPressure2
        get() =
            if (startPressure2 != null && endPressure2 != null) startPressure2 - endPressure2 else null
}

// Statistics about how many samples of different kinds of data we have in the SML file
data class SmlSampleStatistics(
    val hrCount: Int, // "HR"
    val powerCount: Int, // "Power"
    val distanceCount: Int, // "Distance"
    val altitudeCount: Int, // "Altitude"
    val speedCount: Int, // "Speed"
    val cadenceCount: Int, // "Cadence"
    val latLngCount: Int, // "Latitude" and "Longitude"
    val headingCount: Int, // "Heading"
    val verticalSpeedCount: Int, // "VerticalSpeed"
    val temperatureCount: Int, // "Temperature"
    val ibiDataCount: Int, // "Data" or "R-R"
    val depthCount: Int, // "Depth"
    val cylinderInfoCount: Int, // "Cylinders"
    val ventilationCount: Int, // "Ventilation"
    val diveRouteCount: Int, // "DiveRoute"
    val zappSampleCount: Map<Int, Int>, // "ZappSample" count by channelId
    val recoveryHRInThreeMinsCount: Int, // "HRInThreeMins"
    val verticalOscillationCount: Int, // "VerticalOscillation"
    val groundContactTimeCount: Int, // "GroundContactTime"
    val diveRouteOriginCount: Int, // "DiveRouteOrigin"
    val diveRouteQualityCount: Int, // "DiveRouteQuality"
)
