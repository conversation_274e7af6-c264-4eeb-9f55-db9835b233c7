package com.stt.android.sim

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class Cylinder(
    @param:<PERSON>son(name = "GasNumber") val gasNumber: Int? = null, // Gas number, index of gas array
    @param:Json(name = "Pressure") val pressure: Float? = null, // Tank pressure, unit Pa (1 bar = 100000 Pa)
    @param:Json(name = "Pressure2") val pressure2: Float? = null, // Used for side mount, unit Pa (1 bar = 100000 Pa)
    @param:Json(name = "Ventilation") val ventilation: Float? = null // Gas consumption, Ng dive devices
) {
    val pressureKPa: Float?
        get() = pressure?.div(1000f)

    val pressure2KPa: Float?
        get() = pressure2?.div(1000f)
}
