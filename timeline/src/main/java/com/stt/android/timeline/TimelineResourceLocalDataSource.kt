package com.stt.android.timeline

import com.stt.android.timeline.entity.SleepSampleAttributesEntity
import com.suunto.connectivity.recovery.SuuntoRecoveryDataEntry
import com.suunto.connectivity.trenddata.SuuntoTrendDataEntry

/**
 * To be implemented app side to save 247 data directly to room DB.
 */
interface TimelineResourceLocalDataSource {

    /**
     * [RemoteTimelineHeaderJson.attributes] are of type [SleepSampleAttributesEntity]
     *
     * @return If any of the sleep entries were stored. False if all of entries had been stored already.
     */
    suspend fun storeSleepEntries(entries: List<RemoteTimelineJson>): Boolean

    /**
     * @return If any trend data entries were stored. False if all of entries had been stored already.
     */
    suspend fun storeTrendDataEntries(entries: List<SuuntoTrendDataEntry>): Bo<PERSON>an

    /**
     * @return If any recovery entries were stored. False if all of entries had been stored already.
     */
    suspend fun storeRecoveryEntries(entries: List<SuuntoRecoveryDataEntry>): Boolean
}
