package com.suunto.connectivity.poi

interface POIWatchAPI {

    /**
     * Returns list of POI creations dates ordered with same indexes that are used on watch.
     * Use this indexes in [getPOIAt], [removePOIAt] and [editPOIAt] APIs.
     * If indexes are invalidated, this method should be called again to get a new list.
     */
    @Throws(POIMdsException::class)
    suspend fun getCreationIndexedList(serial: String): List<Long>

    @Throws(POIMdsException::class)
    suspend fun getPOIAt(serial: String, index: Int): MdsPOI

    /**
     * Invalidates all indexes
     */
    @Throws(POIMdsException::class)
    suspend fun addPOI(serial: String, mdsPOI: MdsPOI)

    /**
     * Invalidates all indexes greater than [index]
     */
    @Throws(POIMdsException::class)
    suspend fun removePOIAt(serial: String, index: Int): Bo<PERSON>an

    @Throws(POIMdsException::class)
    suspend fun editPOIAt(serial: String, index: Int, mdsPOI: MdsPOI)
}
