package com.suunto.connectivity.poi

sealed class POIMdsException(
    mdsMessage: String?,
    val code: Int,
    cause: Throwable? = null
) : RuntimeException(
    "MDS returned error during POI sync. Code: $code. Message: $mdsMessage",
    cause
) {

    class ContinueWithEmptyListException :
        POIMdsException("MDS returned STATUS_CONTINUE but poiList is empty", 500)

    class MdsPOIParseException(json: String) : POIMdsException("Error parsing POI: $json", 500)

    class MdsPOINotFoundException(index: Int, code: Int, cause: Throwable? = null) :
        POIMdsException("POI not found at index: $index", code, cause)

    class MdsPOIListIsFull(code: Int, cause: Throwable) :
        POIMdsException("POI list is full", code, cause)

    class GenericException(cause: Throwable, code: Int? = null) :
        POIMdsException(cause.message, code ?: 500)
}
