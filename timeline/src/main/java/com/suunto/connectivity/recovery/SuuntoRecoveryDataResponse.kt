package com.suunto.connectivity.recovery

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Container
 */
@JsonClass(generateAdapter = true)
data class SuuntoRecoveryDataResponse(
    @Json(name = "Content") val content: SuuntoRecoveryDataEntries?
)

/**
 * Container
 */
@JsonClass(generateAdapter = true)
data class SuuntoRecoveryDataEntries(
    @Json(name = "continueTimestamp") val continueTimestamp: Long?,
    @Json(name = "moments") val entries: List<SuuntoRecoveryDataEntry>?
)

/**
 * Used to deserialize recovery data (e.g. moments) MDS JSON response
 * @param serial The serial number of the device
 * @param balance Stress balance value (0-100)
 * @param stressState Stress state
 * @param timestampSeconds Timestamp in seconds UTC
 */
@JsonClass(generateAdapter = true)
data class SuuntoRecoveryDataEntry(
    @Json(name = "serial") val serial: String = "", // serial is missing from json API
    @Json(name = "balance") val balance: Int,
    @Json(name = "stressState") val stressState: Int,
    @Json(name = "timestamp") val timestampSeconds: Long
)

@JsonClass(generateAdapter = true)
data class SuuntoRecoveryDataContract(
    @Json(name = "requestIndex") val requestTimestamp: Long // Named index but actually timestamp
)
