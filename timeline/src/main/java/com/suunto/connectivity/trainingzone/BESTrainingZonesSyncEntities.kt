package com.suunto.connectivity.trainingzone

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class BESTrainingZoneSyncContract(
    @Json(name = "Data")
    val data: String
)

@JsonClass(generateAdapter = true)
data class BESTrainingZoneSyncData(
    @Json(name = "tssDaily56d") val tssDaily56d: List<Float>, // TSS/day for the last 56 days
    @Json(name = "tsbDaily42d") val tsbDaily42d: List<Float>, // TSB/day for the last 42 days
    @Json(name = "ctlDaily42d") val ctlDaily42d: List<Float>, // CTL/day for the last 42 days
    @Json(name = "atlDaily2d") val atlDaily2d: List<Float>, // ATL/day for the last 2 days
    @Json(name = "sportSummary14d") val sportSummary14d: List<SportSummary>, // 14 days sports Summary
    @Json(name = "sportTime56d") val sportTime56d: List<Long>, // 56 days sport time (seconds)
    @Json(name = "hrIntensityZone") val hrIntensityZone: List<List<Int>>, // 14 days maximum heart rate intensity zones (seconds)
    @Json(name = "vo2Max42d") val vo2Max42d: List<Float>, // 42 days vo2Max
    @Json(name = "fitnessAge") val latestFitnessAge: Int?, // Fitness age (The most recent valid value)
    @Json(name = "lacticThHr") val latestLacticThHr: Float?, // Lactate threshold heart rate (The most recent valid value)
    @Json(name = "lacticThPace") val latestLacticThPace: Float?, // Lactate threshold Pace (The most recent valid value)
    @Json(name = "recoveryPercent") val recoveryPercent: Int?, // Percent of recovery
    @Json(name = "hrvRange") val hrvRange: HrvRange?, // HRV normal range [0-999]-[0-999]
    @Json(name = "lastNightHrv") val lastNightHrv: Int?, // Last night hrv [0-999]
    @Json(name = "sleep60d") val sleep60d: List<Sleep>, // Most recent sleep data for 14 of the 60 days.
    @Json(name = "firstMoveTime") val oldestWorkoutStartTime: Long?, // The oldest workout start time (seconds)
    @Json(name = "restHr7d") val restHr7d: List<Int>, // Rest heart rate for the last 7 days
    @Json(name = "feeling42d") val feeling42d: List<Float>, // [feelings count * 10000 + total feelings] for the last 42 days
)

@JsonClass(generateAdapter = true)
data class SportSummary(
    @Json(name = "day") val day: Int, // 0-13
    @Json(name = "sportType") val sportType: Int,
    @Json(name = "tssValue") val tssValue: Float,
    @Json(name = "sportTime") val sportTime: Long, // sport time in seconds
    @Json(name = "sportDistance") val sportDistance: Long, // sport distance in meters
    @Json(name = "sportCalorie") val sportCalorie: Int, // sport calorie (kcal)
)

@JsonClass(generateAdapter = true)
data class HrvRange(
    @Json(name = "start") val start: Int,
    @Json(name = "end") val end: Int,
)

@JsonClass(generateAdapter = true)
data class Sleep(
    @Json(name = "day") val day: Int, // 0-6
    @Json(name = "longSleep") val longSleep: Int, // long sleep in minute
    @Json(name = "shortSleep") val shortSleep: Int, // short sleep in minute
    @Json(name = "hrv") val hrv: Float, // hrv
    @Json(name = "hr") val hr: Int, // Average heart rate
)
