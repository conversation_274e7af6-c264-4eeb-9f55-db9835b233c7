package com.suunto.connectivity.capabilities

import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

// Augment capabilities with "ui_screensize_*" capability unless reported by the watch.
// This allows size optimization for UI resources in Zapp files when using watches
// with old SW builds, which do not yet support reporting screen size.
fun SuuntoWatchCapabilities.augmentedWithUIScreenSize(variant: String?): SuuntoWatchCapabilities =
    if (!hasExplicitScreenSize) {
        val screenSize = when (SuuntoDeviceType.fromVariantName(variant)) {
            SuuntoDeviceType.Suunto3,
            SuuntoDeviceType.Suunto3Fitness,
            SuuntoDeviceType.Suunto5,
            SuuntoDeviceType.Suunto5Peak -> "ui_screensize_small"
            SuuntoDeviceType.Suunto9,
            SuuntoDeviceType.Suunto9Lima -> "ui_screensize_large"
            SuuntoDeviceType.Suunto9Peak -> "ui_screensize_medium"
            else -> null
        }
        SuuntoWatchCapabilities(capabilities + listOfNotNull(screenSize))
    } else {
        this
    }
