package com.suunto.connectivity.deviceid

import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

internal class SuuntoGtCapability : ISuuntoDeviceCapabilityInfo {

    override val suuntoDeviceType = SuuntoDeviceType.SuuntoGT
    override val isWhiteboard = true
    override val isWatch = true

    override fun hasGpsSensor(): Boolean {
        return true
    }

    override fun supportsBarometricAltitude(): Boolean {
        return false
    }

    override fun supportsNotifications(firmwareVersion: String?): Boolean {
        return false
    }

    override fun supportsTrendData(mdsDeviceInfo: MdsDeviceInfo?): <PERSON><PERSON>an {
        return false
    }

    override fun supportsSleepData(mdsDeviceInfo: MdsDeviceInfo?): <PERSON><PERSON>an {
        return false
    }

    override fun supportsRoutesSync(mdsDeviceInfo: MdsDeviceInfo?): <PERSON><PERSON>an {
        return false
    }

    override fun supportsPOISync(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsRecoveryData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsSystemEvents(): Boolean {
        return true
    }

    override fun supportsOtaUpdate(firmwareVersion: String): Boolean {
        return true
    }

    override fun supportsMediaAndNotificationControls(firmwareVersion: String): Boolean {
        return false
    }

    override fun toString(): String {
        return SuuntoDeviceType.SuuntoGT.toString()
    }
}
