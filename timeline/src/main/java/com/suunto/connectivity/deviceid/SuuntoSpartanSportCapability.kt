package com.suunto.connectivity.deviceid

import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

/**
 * Device capabilities for SpartanSport
 *
 *
 * Created by <PERSON><PERSON> on 9/1/2016.
 */
internal class SuuntoSpartanSportCapability : ISuuntoDeviceCapabilityInfo {

    override val suuntoDeviceType = SuuntoDeviceType.SpartanSport
    override val isWhiteboard = true
    override val isWatch = true

    override fun hasGpsSensor(): Boolean {
        return true
    }

    override fun supportsBarometricAltitude(): Boolean {
        return false
    }

    override fun supportsNotifications(firmwareVersion: String?): Boolean {
        return true
    }

    override fun supportsTrendData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsSleepData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsRoutesSync(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsPOISync(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return true
    }

    override fun supportsRecoveryData(mdsDeviceInfo: MdsDeviceInfo?): Boolean {
        return false
    }

    override fun supportsSystemEvents(): Boolean {
        return true
    }

    override fun supportsOtaUpdate(firmwareVersion: String): Boolean {
        return false
    }

    override fun supportsMediaAndNotificationControls(firmwareVersion: String): Boolean {
        return false
    }

    override fun toString(): String {
        return SuuntoDeviceType.SpartanSport.toString()
    }
}
