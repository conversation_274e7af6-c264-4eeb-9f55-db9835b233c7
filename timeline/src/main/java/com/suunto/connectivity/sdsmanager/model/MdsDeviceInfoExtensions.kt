@file:JvmName("MdsDeviceInfoExtensions")

package com.suunto.connectivity.sdsmanager.model

private const val SKU = "SKU"
private const val SIM = "SIM"
private const val APP = "APP"
private const val WASS = "WASS"
private const val MDSP = "MDSP"

fun MdsDeviceInfo?.getSKUVersion(): String = queryAdditionalVersionInfo(SKU)

fun MdsDeviceInfo?.getSIMVersion(): String = queryAdditionalVersionInfo(SIM)

fun MdsDeviceInfo?.getWearAppVersion(): String = queryAdditionalVersionInfo(APP)

fun MdsDeviceInfo?.getWASSVersion(): String = queryAdditionalVersionInfo(WASS)

fun MdsDeviceInfo?.getMDSPVersion(): String = queryAdditionalVersionInfo(MDSP)

private fun MdsDeviceInfo?.queryAdditionalVersionInfo(name: String) =
    this?.additionalVersionInfoExtension
        ?.firstOrNull { it.name == name }
        ?.versionHash ?: ""

fun MdsDeviceInfo?.getSerialNumber(): String = this?.serial ?: ""
