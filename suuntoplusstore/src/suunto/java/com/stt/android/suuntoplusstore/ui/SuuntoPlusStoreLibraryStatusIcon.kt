package com.stt.android.suuntoplusstore.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.device.R as DeviceR

@Composable
internal fun SuuntoPlusStoreLibraryStatusIcon(
    modifier: Modifier = Modifier,
    contentDescription: String? = null,
) {
    Icon(
        painter = painterResource(id = DeviceR.drawable.save_bookmark_fill),
        contentDescription = contentDescription,
        tint = MaterialTheme.colors.onPrimary,
        modifier = modifier
            .size(MaterialTheme.iconSizes.small)
            .clip(CircleShape)
            .background(MaterialTheme.colors.primary)
            .padding(5.dp),
    )
}

@Preview
@Composable
private fun PreviewSuuntoPlusStoreLibraryStatusIcon() {
    AppTheme {
        SuuntoPlusStoreLibraryStatusIcon()
    }
}
