package com.stt.android.suuntoplusstore.partners

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.suuntoplusstore.R
import com.stt.android.suuntoplusstore.ui.SuuntoPlusStoreGridScreenBanner
import com.stt.android.device.R as DeviceR

@Composable
internal fun SuuntoPlusStorePartnersScreenBanner(modifier: Modifier = Modifier) {
    val backgroundImage = painterResource(DeviceR.drawable.suunto_plus_store_partners_screen_banner)
    val titleText = stringResource(R.string.suunto_plus_store_partners_screen_banner_title)
    val descriptionText = stringResource(
        R.string.suunto_plus_store_partners_screen_banner_description
    )
    SuuntoPlusStoreGridScreenBanner(backgroundImage, titleText, descriptionText, modifier)
}

@Preview("Narrow")
@Preview("Wide", widthDp = 900)
@Composable
private fun PreviewSuuntoPlusStorePartnersScreenBanner() {
    AppTheme {
        SuuntoPlusStorePartnersScreenBanner()
    }
}
