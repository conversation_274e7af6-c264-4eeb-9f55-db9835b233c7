package com.stt.android.suuntoplusstore.home

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.widgets.CategoryItem
import com.stt.android.suuntoplusstore.R

@Composable
fun SuuntoPlusStoreFeaturesCategoryItem(
    onClick: (ItemType) -> Unit,
    modifier: Modifier = Modifier,
) {
    CategoryItem(
        title = R.string.suunto_plus_store_home_screen_item_type_action_features,
        icon = R.drawable.ic_activity_outline,
        onClick = { onClick(ItemType.FEATURES) },
        modifier = modifier,
    )
}

@Composable
fun SuuntoPlusStorePartnersCategoryItem(
    onClick: (ItemType) -> Unit,
    modifier: Modifier = Modifier,
) {
    CategoryItem(
        title = R.string.suunto_plus_store_home_screen_item_type_action_partners,
        icon = R.drawable.ic_connected_services_outline,
        onClick = { onClick(ItemType.PARTNERS) },
        modifier = modifier,
    )
}

@Composable
fun SuuntoPlusStoreGuidesCategoryItem(
    onClick: (ItemType) -> Unit,
    modifier: Modifier = Modifier,
) {
    CategoryItem(
        title = R.string.suunto_plus_store_home_screen_item_type_action_guides,
        icon = R.drawable.ic_suunto_plus_store_home_screen_item_type_action_guides,
        onClick = { onClick(ItemType.GUIDES) },
        modifier = modifier,
    )
}

@Composable
fun SuuntoPlusStoreWatchfaceCategoryItem(
    onClick: (ItemType) -> Unit,
    modifier: Modifier = Modifier,
) {
    CategoryItem(
        title = R.string.suunto_plus_store_home_screen_item_type_action_watch_face,
        icon = com.stt.android.device.R.drawable.ic_suunto_plus_watch_face,
        onClick = { onClick(ItemType.WATCHFACE) },
        modifier = modifier,
    )
}

@Preview(widthDp = 300)
@Composable
private fun PreviewSuuntoPlusStoreFeaturesCategoryItem() {
    AppTheme {
        SuuntoPlusStoreFeaturesCategoryItem(onClick = {})
    }
}

@Preview(widthDp = 300)
@Composable
private fun PreviewSuuntoPlusStorePartnersCategoryItem() {
    AppTheme {
        SuuntoPlusStorePartnersCategoryItem(onClick = {})
    }
}

@Preview(widthDp = 300)
@Composable
private fun PreviewSuuntoPlusStoreGuidesCategoryItem() {
    AppTheme {
        SuuntoPlusStoreGuidesCategoryItem(onClick = {})
    }
}

@Preview(widthDp = 300)
@Composable
private fun PreviewSuuntoPlusStoreWatchfaceCategoryItem() {
    AppTheme {
        SuuntoPlusStoreWatchfaceCategoryItem(onClick = {})
    }
}
