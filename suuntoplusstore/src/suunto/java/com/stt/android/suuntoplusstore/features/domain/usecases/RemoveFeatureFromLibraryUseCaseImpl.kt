package com.stt.android.suuntoplusstore.features.domain.usecases

import com.stt.android.device.datasource.suuntoplusfeature.SuuntoPlusFeaturesLocalDataSource
import com.stt.android.device.domain.suuntoplusfeature.RemoveFeatureFromLibraryUseCase
import com.stt.android.device.remote.suuntoplusfeature.SuuntoPlusFeaturesRemoteDataSource
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreFeatureId
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteDataSource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class RemoveFeatureFromLibraryUseCaseImpl @Inject constructor(
    private val remoteFeaturesDataSource: SuuntoPlusFeaturesRemoteDataSource,
    private val remoteStoreDataSource: SuuntoPlusStoreRemoteDataSource,
    private val featuresLocalDataSource: SuuntoPlusFeaturesLocalDataSource
) : RemoveFeatureFromLibraryUseCase {
    override suspend fun removeFeatureFromWatch(featureId: String) {
        runCatching {
            featuresLocalDataSource.updateEnabledState(featureId, false)
            remoteFeaturesDataSource.updateEnabledState(featureId, false)
        }.onFailure {
            Timber.w(it, "Failed to disable feature $featureId")
        }
    }

    override suspend fun removeFeatureFromLibrary(featureId: String): Unit =
        withContext(Dispatchers.IO) {
            remoteStoreDataSource.removeFeatureFromLibrary(SuuntoPlusStoreFeatureId(featureId))
            featuresLocalDataSource.deleteByIds(listOf(featureId))
        }
}
