package com.stt.android.suuntoplusstore.guides

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.ScaffoldState
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.lifecycle.LifecycleOwner
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.lifecycle.DisposableEffectOnLifecycleStart
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.suuntoplusstore.Category
import com.stt.android.suuntoplusstore.Item.Guide
import com.stt.android.suuntoplusstore.R
import com.stt.android.suuntoplusstore.ui.ShowSnackbarWithRetryOnError
import com.stt.android.compose.widgets.CategoryHeader
import com.stt.android.suuntoplusstore.ui.SuuntoPlusStoreInternetConnectionSnackbar
import com.stt.android.suuntoplusstore.ui.SuuntoPlusStoreTopBar
import com.stt.android.ui.utils.WindowInfo
import com.stt.android.ui.utils.WindowSizeClass
import com.stt.android.utils.takeIfNotEmpty
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import com.stt.android.core.R as CR

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun SuuntoPlusStoreGuidesScreen(
    screenViewState: ViewState<GuidesScreenViewState>,
    showNoInternetSnackbar: Boolean,
    onItemSelected: (Guide) -> Unit,
    onGuidesRefresh: () -> Unit,
    onUpPressed: () -> Unit,
    onStart: () -> Unit,
    windowInfo: WindowInfo,
    modifier: Modifier = Modifier,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    scaffoldState: ScaffoldState = rememberScaffoldState(),
) {
    val currentOnStart by rememberUpdatedState(onStart)
    DisposableEffectOnLifecycleStart(ignoreChangingConfigurations = true) {
        currentOnStart()
    }

    val listState = rememberLazyListState()
    val isRefreshing = screenViewState is ViewState.Loading

    DisposableEffectOnLifecycleStart(lifecycleOwner) { onGuidesRefresh() }

    ShowSnackbarWithRetryOnError(screenViewState, scaffoldState, onGuidesRefresh)

    Scaffold(
        scaffoldState = scaffoldState,
        topBar = {
            SuuntoPlusStoreTopBar(
                titleText = stringResource(id = R.string.suunto_plus_store_guides_title),
                onUpPressed = onUpPressed
            )
        },
        modifier = modifier
    ) { internalPadding ->
        Box(
            modifier = Modifier
                .padding(internalPadding)
                .fillMaxWidth()
                .background(Color.White)
        ) {
            val pullRefreshState = rememberPullRefreshState(
                refreshing = isRefreshing,
                onRefresh = onGuidesRefresh
            )

            GuidesContent(
                listState = listState,
                viewState = screenViewState,
                onItemSelected = onItemSelected,
                windowInfo = windowInfo,
                modifier = Modifier
                    .fillMaxHeight()
                    .pullRefresh(pullRefreshState, true)
            )

            PullRefreshIndicator(
                refreshing = isRefreshing,
                state = pullRefreshState,
                modifier = Modifier.align(Alignment.TopCenter)
            )
        }
    }

    SuuntoPlusStoreInternetConnectionSnackbar(showNoInternetSnackbar, scaffoldState)
}

@Composable
private fun GuidesContent(
    listState: LazyListState,
    viewState: ViewState<GuidesScreenViewState>,
    onItemSelected: (Guide) -> Unit,
    windowInfo: WindowInfo,
    modifier: Modifier = Modifier
) {
    val columnCount = remember(windowInfo) {
        when (windowInfo.windowWidthSizeClass) {
            WindowSizeClass.Medium,
            WindowSizeClass.Expanded -> 3

            else -> 2
        }
    }

    val gridHorizontalPadding = MaterialTheme.spacing.medium

    // TODO: 30.5.2022 Use LazyVerticalGrid once it supports item specific paddings
    ContentCenteringColumn {
        LazyColumn(
            state = listState,
            modifier = modifier,
        ) {
            item(key = "banner") {
                SuuntoPlusStoreGuidesScreenBanner()
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
            }

            for (category in viewState.data?.categories.orEmpty()) {
                item(key = "${category.id} header") {
                    ContentCenteringColumn(
                        modifier = Modifier.padding(horizontal = gridHorizontalPadding),
                    ) {
                        CategoryHeader(title = category.title)
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                    }
                }

                items(
                    items = category.items
                        .filterIsInstance<Guide>()
                        .chunked(columnCount)
                        .map(Iterable<Guide>::toImmutableList)
                        .toImmutableList(),
                    key = { rowItems -> "${category.id} ${rowItems.joinToString { it.id }}" }
                ) { guideRow: ImmutableList<Guide> ->
                    GuideRow(guideRow, columnCount, gridHorizontalPadding, onItemSelected)
                }
                item(key = "${category.id} end spacer") {
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                }
            }

            // FIXME: 29.7.2022 Remove this category once the backend returns all item in categories
            viewState.data?.guides?.takeIfNotEmpty()?.let { guides ->
                item(key = "all-items header") {
                    ContentCenteringColumn(
                        modifier = Modifier.padding(horizontal = gridHorizontalPadding),
                    ) {
                        CategoryHeader(title = stringResource(id = R.string.suunto_plus_store_category_all))
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                    }
                }
                items(
                    items = guides
                        .chunked(columnCount)
                        .map(Iterable<Guide>::toImmutableList)
                        .toImmutableList(),
                    key = { rowItems -> rowItems.joinToString { it.id } }
                ) { rowGuides: ImmutableList<Guide> ->
                    GuideRow(rowGuides, columnCount, gridHorizontalPadding, onItemSelected)
                }
            }
        }
    }
}

@Composable
internal fun GuideRow(
    rowGuides: ImmutableList<Guide>,
    columnCount: Int,
    gridHorizontalPadding: Dp,
    onItemSelected: (Guide) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
    ) {
        Box(modifier = Modifier.fillMaxWidth()) {
            Row(
                modifier = Modifier
                    .padding(horizontal = gridHorizontalPadding)
                    .widthIn(max = dimensionResource(CR.dimen.content_max_width))
                    .align(Alignment.Center),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            ) {
                for (index in 0 until columnCount) {
                    if (index <= rowGuides.lastIndex) {
                        SuuntoPlusStoreGuideItem(
                            guide = rowGuides[index],
                            onItemSelected = onItemSelected,
                            modifier = Modifier.weight(1f),
                        )
                    } else {
                        // The empty grid items on the last row
                        Box(modifier = Modifier.weight(1f))
                    }
                }
            }
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
    }
}

private val previewCategories = (1..10).map { c ->
    Category(
        id = "category_$c,",
        title = "first category",
        items = (1..c).map { i ->
            Guide(
                id = "guide_$i",
                name = "Guide $i",
                shortDescription = "short description",
                richDescription = null,
                description = "long description, ".repeat(5),
                detailScreenImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/57949619c7cd556bc347cd987b816217.png",
            )
        }.toImmutableList(),
    )
}.toImmutableList()

private val previewScreenViewState = loaded(
    GuidesScreenViewState(
        categories = previewCategories,
        guides = persistentListOf(
            Guide(
                id = "running-form-drills",
                name = "Running form drills",
                shortDescription = "Improve your technique",
                richDescription = null,
                description = "",
                labels = persistentListOf("NEW"),
                detailScreenImageUrl = "",
            ),
            Guide(
                id = "12-strength-exercises",
                name = "12 strength exercises",
                shortDescription = "Improve your strength",
                richDescription = null,
                description = "Improve your strength",
                detailScreenImageUrl = "",
            ),
            Guide(
                id = "5-key-swim-drills",
                name = "5 Key swim drills",
                shortDescription = "Improve your technique",
                richDescription = null,
                description = "",
                labels = persistentListOf("NEW"),
                detailScreenImageUrl = "",
            ),
            Guide(
                id = "5-key-scuba-drills",
                name = "5 Key scuba drills",
                shortDescription = "Improve your technique",
                richDescription = null,
                description = "",
                detailScreenImageUrl = "",
            ),
            Guide(
                id = "5-key-hiking-drills",
                name = "5 Key hiking drills",
                shortDescription = "Improve your technique",
                richDescription = null,
                description = "",
                detailScreenImageUrl = "",
            ),
        )
    )
)

@Preview("Medium width WindowSizeClass", widthDp = 840)
@Composable
private fun PreviewSuuntoPlusStoreGuidesScreenMedium() {
    AppTheme {
        SuuntoPlusStoreGuidesScreen(
            screenViewState = previewScreenViewState,
            showNoInternetSnackbar = false,
            onItemSelected = {},
            onGuidesRefresh = {},
            onUpPressed = {},
            onStart = {},
            windowInfo = WindowInfo(
                windowWidthSizeClass = WindowSizeClass.Medium,
                windowSize = DpSize(width = 840.dp, 700.dp)
            ),
        )
    }
}

@Preview("Compact width WindowSizeClass")
@Composable
private fun PreviewSuuntoPlusStoreGuidesScreenCompact() {
    AppTheme {
        SuuntoPlusStoreGuidesScreen(
            screenViewState = previewScreenViewState,
            showNoInternetSnackbar = false,
            onItemSelected = {},
            onGuidesRefresh = {},
            onUpPressed = {},
            onStart = {},
            windowInfo = WindowInfo(WindowSizeClass.Compact, DpSize(width = 500.dp, 900.dp)),
        )
    }
}

@Preview("Loading")
@Composable
private fun PreviewSuuntoPlusStoreGuidesScreenLoading() {
    AppTheme {
        SuuntoPlusStoreGuidesScreen(
            screenViewState = loading(),
            showNoInternetSnackbar = false,
            onItemSelected = {},
            onGuidesRefresh = {},
            onUpPressed = {},
            onStart = {},
            windowInfo = WindowInfo(WindowSizeClass.Compact, DpSize(width = 500.dp, 900.dp)),
        )
    }
}
