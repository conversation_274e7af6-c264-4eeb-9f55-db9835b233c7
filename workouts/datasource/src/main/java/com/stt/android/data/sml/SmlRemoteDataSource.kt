package com.stt.android.data.sml

import com.stt.android.domain.sml.SmlDataSource
import com.stt.android.domain.workouts.extensions.SMLExtension
import com.stt.android.remote.smlzip.SmlRemoteApi
import java.io.InputStream
import javax.inject.Inject

class SmlRemoteDataSource @Inject constructor(
    private val smlRemoteApi: SmlRemoteApi
) : SmlDataSource {
    override suspend fun fetchSmlExtension(
        workoutId: Int,
        workoutKey: String?,
    ): SMLExtension? = workoutKey?.let { smlRemoteApi.fetchSmlZip(it) }
        ?.inputStream()
        ?.use(InputStream::readBytes)
        ?.let { SMLExtension(workoutId, it) }

    override suspend fun saveSmlExtension(smlExtension: SMLExtension) {
        throw UnsupportedOperationException(
            "Remote api does not support saving SML extension. " +
                "SML extension is stored separately to the backend"
        )
    }
}
