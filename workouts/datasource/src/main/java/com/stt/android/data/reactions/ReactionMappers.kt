package com.stt.android.data.reactions

import com.stt.android.domain.workouts.reactions.Reaction
import com.stt.android.remote.reactions.RemoteReaction

fun RemoteReaction.toDomainEntity(workoutKey: String): Reaction {
    return Reaction(
        key = this.key,
        workoutKey = workoutKey,
        reaction = this.reaction,
        userName = this.userName,
        userRealOrUsername = this.userRealOrUserName,
        userProfilePictureUrl = this.userProfilePictureUrl,
        timestamp = this.timestamp
    )
}

fun Reaction.toRemoteEntity(): RemoteReaction {
    return RemoteReaction(
        key = this.key,
        reaction = this.reaction,
        userName = this.userName,
        userRealOrUserName = this.userRealOrUsername,
        userProfilePictureUrl = this.userProfilePictureUrl,
        timestamp = this.timestamp
    )
}
