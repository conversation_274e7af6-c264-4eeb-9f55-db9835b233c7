package com.stt.android.data.workout

import com.stt.android.data.Local
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.suunto.algorithms.data.Length
import javax.inject.Inject

/**
 * Manages the state of workouts in local database
 */
class WorkoutHeaderRepository @Inject constructor(
    @Local private val workoutHeaderDataSource: WorkoutHeaderDataSource // TODO replace with Room DAO interface once migrated TP #87987
) : WorkoutHeaderDataSource {
    override suspend fun storeWorkout(workoutHeader: WorkoutHeader) {
        workoutHeaderDataSource.storeWorkout(workoutHeader)
    }

    override suspend fun getDeletedWorkoutsKeys(): List<String> {
        return workoutHeaderDataSource.getDeletedWorkoutsKeys()
    }

    override suspend fun getLocallyModifiedWorkouts(): List<WorkoutHeader> {
        return workoutHeaderDataSource.getLocallyModifiedWorkouts()
    }

    override suspend fun markWorkoutsAsSynced(workoutKeys: Set<String>) {
        workoutHeaderDataSource.markWorkoutsAsSynced(workoutKeys)
    }

    override suspend fun findManuallyCreatedWorkouts(): List<WorkoutHeader> {
        return workoutHeaderDataSource.findManuallyCreatedWorkouts()
    }

    override suspend fun findNewUnsyncedWorkouts(): List<WorkoutHeader> {
        return workoutHeaderDataSource.findNewUnsyncedWorkouts()
    }

    override fun syncWorkouts() {
        workoutHeaderDataSource.syncWorkouts()
    }

    /**
     * Deletes a workout if it has never been synced to server otherwise marks as deleted
     * @param id the workout header id
     * @return true if the workout was deleted or false if it is marked for deletion
     */
    override suspend fun markDeletedOrPermanentlyDelete(id: Int): Boolean {
        return workoutHeaderDataSource.markDeletedOrPermanentlyDelete(id)
    }

    override suspend fun markExtensionsFetched(id: Int): Boolean {
        return workoutHeaderDataSource.markExtensionsFetched(id)
    }

    /**
     * Permanently removes a workout from local database
     * @param key workout key
     */
    override suspend fun remove(key: String) {
        workoutHeaderDataSource.remove(key)
    }

    override suspend fun findByKey(key: String): WorkoutHeader? {
        return workoutHeaderDataSource.findByKey(key)
    }

    override suspend fun findById(id: Int): WorkoutHeader? {
        return workoutHeaderDataSource.findById(id)
    }

    override suspend fun findByIds(ids: List<Int>): List<WorkoutHeader> {
        return workoutHeaderDataSource.findByIds(ids)
    }

    override suspend fun findPagedOfType(
        ownerUsername: String,
        activityTypeId: Int,
        page: Int
    ): List<WorkoutHeader> {
        return workoutHeaderDataSource.findPagedOfType(ownerUsername, activityTypeId, page)
    }

    override suspend fun findPagedExcludingTypes(
        ownerUsername: String,
        excludedTypes: Set<Int>,
        page: Int
    ): List<WorkoutHeader> {
        return workoutHeaderDataSource.findPagedExcludingTypes(ownerUsername, excludedTypes, page)
    }

    override suspend fun findByStartTime(
        ownerUsername: String,
        minimumStartTime: Long,
        maximumStartTime: Long
    ): List<WorkoutHeader> {
        return workoutHeaderDataSource.findByStartTime(
            ownerUsername,
            minimumStartTime,
            maximumStartTime
        )
    }

    override suspend fun findNotDeletedByRange(
        ownerUsername: String,
        activityTypeIds: Collection<Int>,
        sinceMs: Long,
        untilMs: Long
    ): List<WorkoutHeader> {
        return workoutHeaderDataSource.findNotDeletedByRange(
            ownerUsername,
            activityTypeIds,
            sinceMs,
            untilMs
        )
    }

    override suspend fun findPagedByTimeRange(
        ownerUsername: String,
        sinceMs: Long,
        untilMs: Long,
        includeActivityTypeId: Int?,
        excludeActivityTypeIds: Set<Int>,
        page: Int,
        firstPageSize: Int,
        pageSize: Int,
        filterWithAscent: Boolean
    ): List<WorkoutHeader> {
        return workoutHeaderDataSource.findPagedByTimeRange(
            ownerUsername,
            sinceMs,
            untilMs,
            includeActivityTypeId,
            excludeActivityTypeIds,
            page,
            firstPageSize,
            pageSize,
            filterWithAscent
        )
    }

    override suspend fun loadActivityTypeCount(id: Int): Long {
        return workoutHeaderDataSource.loadActivityTypeCount(id)
    }

    override suspend fun loadFastestOfActivityType(id: Int, since: Long, till: Long): WorkoutHeader? {
        return workoutHeaderDataSource.loadFastestOfActivityType(id, since, till)
    }

    override suspend fun loadFarthestOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderDataSource.loadFarthestOfActivityType(id, since, till)
    }

    override suspend fun loadShortestTimeOfActivityTypeWithDistanceRange(
        id: Int,
        distanceRange: ClosedRange<Length>,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderDataSource.loadShortestTimeOfActivityTypeWithDistanceRange(
            id,
            distanceRange,
            since,
            till
        )
    }

    override suspend fun loadLatestOfActivityType(id: Int, till: Long): WorkoutHeader? {
        return workoutHeaderDataSource.loadLatestOfActivityType(id, till)
    }

    override suspend fun loadLongestTimeOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderDataSource.loadLongestTimeOfActivityType(id, since, till)
    }

    override suspend fun loadHighestClimbOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderDataSource.loadHighestClimbOfActivityType(id, since, till)
    }

    override suspend fun loadHighestAltitudeOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderDataSource.loadHighestAltitudeOfActivityType(id, since, till)
    }

    override suspend fun loadFastestPaceOfActivityTypeInPeriod(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return workoutHeaderDataSource.loadFastestPaceOfActivityTypeInPeriod(id, since, till)
    }

    override suspend fun findWithUserTagsById(id: Int): WorkoutHeader? {
        return workoutHeaderDataSource.findWithUserTagsById(id)
    }

    override suspend fun findWorkoutsWithUnsyncedUserTags(): List<WorkoutHeader> {
        return workoutHeaderDataSource.findWorkoutsWithUnsyncedUserTags()
    }

    override suspend fun loadTotalActivityCount(): Long {
        return workoutHeaderDataSource.loadTotalActivityCount()
    }

    override suspend fun loadActivityCountInPeriod(since: Long, till: Long): Long {
        return workoutHeaderDataSource.loadActivityCountInPeriod(since, till)
    }

    override suspend fun loadActivityTypeCountInPeriod(id: Int, since: Long, till: Long): Long {
        return workoutHeaderDataSource.loadActivityTypeCountInPeriod(id, since, till)
    }

    override suspend fun findOldestWorkout(ownerUsername: String, since: Long): WorkoutHeader? {
        return workoutHeaderDataSource.findOldestWorkout(ownerUsername, since)
    }

    override suspend fun findActivityTypesInTimeRange(
        username: String,
        fromTimeMs: Long,
        toTimeMs: Long
    ): List<ActivityType> {
        return workoutHeaderDataSource.findActivityTypesInTimeRange(username, fromTimeMs, toTimeMs)
    }
    
    override suspend fun findActivityTypesWithAscentInTimeRange(
        username: String,
        fromTimeMs: Long,
        toTimeMs: Long
    ): List<ActivityType> {
        return workoutHeaderDataSource.findActivityTypesWithAscentInTimeRange(username, fromTimeMs, toTimeMs)
    }
}
