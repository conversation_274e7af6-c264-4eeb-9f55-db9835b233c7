package com.stt.android.data.workout.extensions

import com.stt.android.data.EntityMapper
import com.stt.android.data.source.local.jumpropeextension.LocalJumpRopeExtension
import com.stt.android.data.source.local.swimmingextension.LocalSwimmingExtension
import com.stt.android.domain.workouts.extensions.JumpRopeExtension
import com.stt.android.domain.workouts.extensions.SwimmingExtension
import javax.inject.Inject

class JumpRopeExtensionLocalMapper
@Inject constructor() : EntityMapper<LocalJumpRopeExtension, JumpRopeExtension> {

    override fun toDomainEntity(): Function1<LocalJumpRopeExtension, JumpRopeExtension> = {
        JumpRopeExtension(
            it.workoutId,
            it.rounds,
            it.avgSkipsPerRound,
            it.maxConsecutiveSkips,
        )
    }

    override fun toDataEntity(): Function1<JumpRopeExtension, LocalJumpRopeExtension> = {
        LocalJumpRopeExtension(
            it.workoutId ?: throw IllegalStateException("Workout ID cannot be null"),
            it.rounds,
            it.avgSkipsPerRound,
            it.maxConsecutiveSkips,
        )
    }
}
