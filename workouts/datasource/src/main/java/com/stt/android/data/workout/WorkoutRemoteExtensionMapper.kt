package com.stt.android.data.workout

import com.soy.algorithms.intensity.IntensityZones
import com.soy.algorithms.intensity.IntensityZonesData
import com.stt.android.domain.user.workoutextension.FitnessExtension
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.JumpRopeExtension
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.SwimmingExtension
import com.stt.android.domain.workouts.extensions.WeatherExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.logbook.SuuntoLogbookZapp
import com.stt.android.remote.extensions.RemoteIntensityExtensionIntensityZones
import com.stt.android.remote.extensions.RemoteIntensityExtensionZone
import com.stt.android.remote.extensions.RemoteIntensityExtensionZones
import com.stt.android.remote.extensions.RemoteSlopeSkiSummaryExtensionStatistics
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import java.time.Duration
import javax.inject.Inject

/**
 * A mapper class for mapping remote data [RemoteWorkoutExtension] entity to domain [WorkoutExtension] entity
 */
class WorkoutRemoteExtensionMapper @Inject constructor() {
    fun toDomainEntity(workoutId: Int): (RemoteWorkoutExtension) -> WorkoutExtension? {
        return { remoteExtension ->
            when (remoteExtension) {
                is RemoteWorkoutExtension.RemoteSummaryExtension -> {
                    SummaryExtension(
                        workoutId = workoutId,
                        pte = remoteExtension.pte ?: 0f,
                        feeling = remoteExtension.feeling ?: 0,
                        avgTemperature = remoteExtension.avgTemperature ?: 0f,
                        peakEpoc = remoteExtension.peakEpoc ?: 0f,
                        avgPower = remoteExtension.avgPower ?: 0f,
                        avgCadence = remoteExtension.avgCadence ?: 0f,
                        avgSpeed = remoteExtension.avgSpeed,
                        ascentTime = remoteExtension.ascentTime ?: 0f,
                        descentTime = remoteExtension.descentTime ?: 0f,
                        performanceLevel = remoteExtension.performanceLevel ?: 0f,
                        recoveryTime = remoteExtension.recoveryTime,
                        ascent = remoteExtension.ascent,
                        descent = remoteExtension.descent,
                        deviceHardwareVersion = remoteExtension.gear?.deviceHardwareVersion,
                        deviceSoftwareVersion = remoteExtension.gear?.deviceSoftwareVersion,
                        productType = remoteExtension.gear?.productType,
                        displayName = remoteExtension.gear?.displayName,
                        deviceName = remoteExtension.gear?.deviceName,
                        deviceSerialNumber = remoteExtension.gear?.deviceSerialNumber,
                        deviceManufacturer = remoteExtension.gear?.deviceManufacturer,
                        exerciseId = remoteExtension.exerciseId,
                        zapps = remoteExtension.zapps.toDomainType(),
                        repetitionCount = remoteExtension.repetitionCount ?: 0,
                        maxCadence = remoteExtension.maxCadence ?: 0f,
                        avgStrideLength = remoteExtension.avgStrideLength,
                        fatConsumption = remoteExtension.fatConsumption,
                        carbohydrateConsumption = remoteExtension.carbohydrateConsumption,
                        avgGroundContactTime = remoteExtension.avgGroundContactTime,
                        avgVerticalOscillation = remoteExtension.avgVerticalOscillation,
                        avgLeftGroundContactBalance = remoteExtension.avgLeftGroundContactBalance,
                        avgRightGroundContactBalance = remoteExtension.avgRightGroundContactBalance,
                        lacticThHr = remoteExtension.lacticThHr,
                        lacticThPace = remoteExtension.lacticThPace,
                        avgAscentSpeed = remoteExtension.avgAscentSpeed,
                        maxAscentSpeed = remoteExtension.maxAscentSpeed,
                        avgDescentSpeed = remoteExtension.avgDescentSpeed,
                        maxDescentSpeed = remoteExtension.maxDescentSpeed,
                        avgDistancePerStroke = remoteExtension.avgDistancePerStroke,
                    )
                }

                is RemoteWorkoutExtension.RemoteFitnessExtension -> with(remoteExtension) {
                    maxHeartRate?.let {
                        FitnessExtension(
                            workoutId = workoutId,
                            maxHeartRate = it,
                            vo2Max = vo2Max ?: 0.0f,
                            fitnessAge = fitnessAge,
                        )
                    }
                }

                is RemoteWorkoutExtension.RemoteSwimmingHeaderExtension -> {
                    val isValid = remoteExtension.run {
                        listOf(
                            avgSwolf,
                            avgStrokeRate,
                            breaststrokeGlideTime,
                            ventilationFrequency,
                            avgFreestyleBreathAngle,
                            maxFreestyleBreathAngle,
                            freestyleGlideAngle,
                            avgBreaststrokeBreathAngle,
                            maxBreaststrokeBreathAngle,
                            freestyleDuration,
                            breaststrokeDuration,
                            freestylePercentage,
                            breaststrokePercentage,
                            breaststrokeHeadAngle,
                        )
                    }.any { it != null }
                    if (isValid) {
                        SwimmingExtension(
                            workoutId = workoutId,
                            avgSwolf = remoteExtension.avgSwolf ?: 0,
                            avgStrokeRate = remoteExtension.avgStrokeRate ?: 0.0f,
                            breathingRate = remoteExtension.ventilationFrequency,
                            breaststrokeDuration = remoteExtension.breaststrokeDuration,
                            breaststrokePercentage = remoteExtension.breaststrokePercentage,
                            breaststrokeGlideTime = remoteExtension.breaststrokeGlideTime,
                            breaststrokeMaxBreathAngle = remoteExtension.maxBreaststrokeBreathAngle,
                            breaststrokeAvgBreathAngle = remoteExtension.avgBreaststrokeBreathAngle,
                            freestyleDuration = remoteExtension.freestyleDuration,
                            freestylePercentage = remoteExtension.freestylePercentage,
                            freestyleMaxBreathAngle = remoteExtension.maxFreestyleBreathAngle,
                            freestyleAvgBreathAngle = remoteExtension.avgFreestyleBreathAngle,
                            freestylePitchAngle = remoteExtension.freestyleGlideAngle,
                            breaststrokeHeadAngle = remoteExtension.breaststrokeHeadAngle,
                        )
                    } else {
                        // not supported extension
                        null
                    }
                }

                is RemoteWorkoutExtension.RemoteIntensityExtension -> {
                    IntensityExtension(
                        workoutId = workoutId,
                        intensityZones = remoteExtension.zones.toDomain(),
                    )
                }

                is RemoteWorkoutExtension.RemoteSlopeSkiSummaryExtension -> {
                    SlopeSkiSummary(
                        workoutId = workoutId,
                        totalRuns = remoteExtension.statistics.numberOfRuns,
                        descentDurationInMilliseconds = Duration.ofSeconds(
                            remoteExtension.statistics.descentDurationSeconds
                        ).toMillis(),
                        descentsInMeters = remoteExtension.statistics.descentMeters,
                        descentDistanceInMeters = remoteExtension.statistics.descentDistanceMeters,
                        maxSpeedMetersPerSecond = remoteExtension.statistics.maxSpeed
                    )
                }

                is RemoteWorkoutExtension.RemoteDiveHeaderExtension -> {
                    DiveExtension(
                        workoutId = workoutId,
                        maxDepth = remoteExtension.maxDepth,
                        algorithm = remoteExtension.algorithm,
                        personalSetting = remoteExtension.personalSetting,
                        diveNumberInSeries = remoteExtension.diveNumberInSeries,
                        cns = remoteExtension.cns,
                        algorithmLock = remoteExtension.algorithmLock,
                        diveMode = remoteExtension.diveMode,
                        otu = remoteExtension.otu,
                        pauseDuration = remoteExtension.pauseDuration,
                        gasConsumption = remoteExtension.gasConsumption,
                        altitudeSetting = remoteExtension.altitudeSetting,
                        gasQuantities = remoteExtension.gasQuantities,
                        surfaceTime = remoteExtension.surfaceTime,
                        diveTime = remoteExtension.diveTime,
                        gasesUsed = remoteExtension.gasesUsed,
                        maxDepthTemperature = remoteExtension.maxDepthTemperature,
                        avgDepth = remoteExtension.avgDepth,
                        minGF = remoteExtension.minGF,
                        maxGF = remoteExtension.maxGF
                    )
                }

                is RemoteWorkoutExtension.RemoteWeatherExtension -> {
                    WeatherExtension(
                        workoutId = workoutId,
                        airPressure = remoteExtension.airPressure,
                        cloudiness = remoteExtension.cloudiness,
                        groundLevelAirPressure = remoteExtension.groundLevelAirPressure,
                        humidity = remoteExtension.humidity,
                        rainVolume1h = remoteExtension.rainVolume1h,
                        rainVolume3h = remoteExtension.rainVolume3h,
                        seaLevelAirPressure = remoteExtension.seaLevelAirPressure,
                        snowVolume1h = remoteExtension.snowVolume1h,
                        snowVolume3h = remoteExtension.snowVolume3h,
                        temperature = remoteExtension.temperature,
                        weatherIcon = remoteExtension.weatherIcon,
                        windDirection = remoteExtension.windDirection,
                        windSpeed = remoteExtension.windSpeed
                    )
                }

                is RemoteWorkoutExtension.RemoteJumpRopeExtension -> {
                    JumpRopeExtension(
                        workoutId = workoutId,
                        rounds = remoteExtension.rounds,
                        avgSkipsPerRound = remoteExtension.avgSkipsPerRound,
                        maxConsecutiveSkips = remoteExtension.maxConsecutiveSkips
                    )
                }

                else -> {
                    // not supported extension
                    null
                }
            }
        }
    }

    fun toDataEntity(): (WorkoutExtension) -> RemoteWorkoutExtension {
        return { domainExtension ->
            when (domainExtension) {
                is SummaryExtension -> {
                    RemoteWorkoutExtension.RemoteSummaryExtension(
                        pte = domainExtension.pte,
                        feeling = domainExtension.feeling,
                        avgTemperature = domainExtension.avgTemperature,
                        peakEpoc = domainExtension.peakEpoc,
                        avgPower = domainExtension.avgPower,
                        avgCadence = domainExtension.avgCadence,
                        avgSpeed = domainExtension.avgSpeed,
                        ascentTime = domainExtension.ascentTime,
                        descentTime = domainExtension.descentTime,
                        performanceLevel = domainExtension.performanceLevel,
                        recoveryTime = domainExtension.recoveryTime,
                        ascent = domainExtension.ascent,
                        descent = domainExtension.descent,
                        gear = RemoteWorkoutExtension.RemoteSummaryGear(
                            domainExtension.deviceHardwareVersion,
                            domainExtension.deviceSoftwareVersion,
                            domainExtension.productType,
                            domainExtension.displayName,
                            domainExtension.deviceName,
                            domainExtension.deviceSerialNumber,
                            domainExtension.deviceManufacturer
                        ),
                        exerciseId = domainExtension.exerciseId,
                        zapps = domainExtension.zapps.map { it.toRemoteType() },
                        maxCadence = domainExtension.maxCadence ?: 0f,
                        repetitionCount = domainExtension.repetitionCount ?: 0,
                        avgStrideLength = domainExtension.avgStrideLength,
                        fatConsumption = domainExtension.fatConsumption,
                        carbohydrateConsumption = domainExtension.carbohydrateConsumption,
                        avgGroundContactTime = domainExtension.avgGroundContactTime,
                        avgVerticalOscillation = domainExtension.avgVerticalOscillation,
                        avgLeftGroundContactBalance = domainExtension.avgLeftGroundContactBalance,
                        avgRightGroundContactBalance = domainExtension.avgRightGroundContactBalance,
                        lacticThHr = domainExtension.lacticThHr,
                        lacticThPace = domainExtension.lacticThPace,
                        avgAscentSpeed = domainExtension.avgAscentSpeed,
                        maxAscentSpeed = domainExtension.maxAscentSpeed,
                        avgDescentSpeed = domainExtension.avgDescentSpeed,
                        maxDescentSpeed = domainExtension.maxDescentSpeed,
                        avgDistancePerStroke = domainExtension.avgDistancePerStroke,
                    )
                }

                is FitnessExtension -> {
                    RemoteWorkoutExtension.RemoteFitnessExtension(
                        maxHeartRate = domainExtension.maxHeartRate,
                        vo2Max = domainExtension.vo2Max,
                        fitnessAge = domainExtension.fitnessAge
                    )
                }

                is IntensityExtension -> {
                    RemoteWorkoutExtension.RemoteIntensityExtension(
                        zones = domainExtension.intensityZones.toRemote(),
                        physiologicalThresholds = null
                    )
                }

                is SlopeSkiSummary -> {
                    RemoteWorkoutExtension.RemoteSlopeSkiSummaryExtension(
                        statistics = RemoteSlopeSkiSummaryExtensionStatistics(
                            numberOfRuns = domainExtension.totalRuns,
                            descentDurationSeconds = Duration.ofMillis(
                                domainExtension.descentDurationInMilliseconds
                            ).seconds,
                            descentMeters = domainExtension.descentsInMeters,
                            descentDistanceMeters = domainExtension.descentDistanceInMeters,
                            maxSpeed = domainExtension.maxSpeedMetersPerSecond
                        ),
                        runs = emptyList()
                    )
                }

                is SwimmingExtension -> {
                    RemoteWorkoutExtension.RemoteSwimmingHeaderExtension(
                        avgSwolf = domainExtension.avgSwolf,
                        avgStrokeRate = domainExtension.avgStrokeRate,
                        breaststrokeGlideTime = domainExtension.breaststrokeGlideTime,
                        ventilationFrequency = domainExtension.breathingRate,
                        avgFreestyleBreathAngle = domainExtension.freestyleAvgBreathAngle,
                        maxFreestyleBreathAngle = domainExtension.freestyleMaxBreathAngle,
                        freestyleGlideAngle = domainExtension.freestylePitchAngle,
                        avgBreaststrokeBreathAngle = domainExtension.breaststrokeAvgBreathAngle,
                        maxBreaststrokeBreathAngle = domainExtension.breaststrokeMaxBreathAngle,
                        freestyleDuration = domainExtension.freestyleDuration,
                        breaststrokeDuration = domainExtension.breaststrokeDuration,
                        freestylePercentage = domainExtension.freestylePercentage,
                        breaststrokePercentage = domainExtension.freestylePercentage,
                        breaststrokeHeadAngle = domainExtension.breaststrokeHeadAngle,
                    )
                }

                is DiveExtension -> {
                    RemoteWorkoutExtension.RemoteDiveHeaderExtension(
                        maxDepth = domainExtension.maxDepth,
                        algorithm = domainExtension.algorithm,
                        personalSetting = domainExtension.personalSetting,
                        diveNumberInSeries = domainExtension.diveNumberInSeries,
                        cns = domainExtension.cns,
                        algorithmLock = domainExtension.algorithmLock,
                        diveMode = domainExtension.diveMode,
                        otu = domainExtension.otu,
                        pauseDuration = domainExtension.pauseDuration,
                        gasConsumption = domainExtension.gasConsumption,
                        altitudeSetting = domainExtension.altitudeSetting,
                        gasQuantities = domainExtension.gasQuantities,
                        surfaceTime = domainExtension.surfaceTime,
                        diveTime = domainExtension.diveTime,
                        gasesUsed = domainExtension.gasesUsed,
                        maxDepthTemperature = domainExtension.maxDepthTemperature,
                        avgDepth = domainExtension.avgDepth,
                        minGF = domainExtension.minGF,
                        maxGF = domainExtension.maxGF
                    )
                }

                is WeatherExtension -> {
                    RemoteWorkoutExtension.RemoteWeatherExtension(
                        airPressure = domainExtension.airPressure,
                        cloudiness = domainExtension.cloudiness,
                        groundLevelAirPressure = domainExtension.groundLevelAirPressure,
                        humidity = domainExtension.humidity,
                        rainVolume1h = domainExtension.rainVolume1h,
                        rainVolume3h = domainExtension.rainVolume3h,
                        seaLevelAirPressure = domainExtension.seaLevelAirPressure,
                        snowVolume1h = domainExtension.snowVolume1h,
                        snowVolume3h = domainExtension.snowVolume3h,
                        temperature = domainExtension.temperature,
                        weatherIcon = domainExtension.weatherIcon,
                        windDirection = domainExtension.windDirection,
                        windSpeed = domainExtension.windSpeed
                    )
                }

                is JumpRopeExtension -> {
                    RemoteWorkoutExtension.RemoteJumpRopeExtension(
                        rounds = domainExtension.rounds,
                        avgSkipsPerRound = domainExtension.avgSkipsPerRound,
                        maxConsecutiveSkips = domainExtension.maxConsecutiveSkips
                    )
                }

                else -> throw IllegalArgumentException("No mapper configured for $domainExtension")
            }
        }
    }

    private fun IntensityZones.toRemote(): RemoteIntensityExtensionIntensityZones =
        RemoteIntensityExtensionIntensityZones(
            heartRate = hr?.hzToBpm()?.toRemote(),
            power = power?.toRemote(),
            speed = speed?.toRemote(),
        )

    private fun IntensityZonesData.toRemote(): RemoteIntensityExtensionZones {
        return RemoteIntensityExtensionZones(
            RemoteIntensityExtensionZone(
                this.zone1Duration,
                0f
            ),
            RemoteIntensityExtensionZone(
                this.zone2Duration,
                this.zone2LowerLimit
            ),
            RemoteIntensityExtensionZone(
                this.zone3Duration,
                this.zone3LowerLimit
            ),
            RemoteIntensityExtensionZone(
                this.zone4Duration,
                this.zone4LowerLimit
            ),
            RemoteIntensityExtensionZone(
                this.zone5Duration,
                this.zone5LowerLimit
            )
        )
    }
}

fun RemoteIntensityExtensionIntensityZones?.toDomain(): IntensityZones = IntensityZones(
    hr = this?.heartRate?.toDomain(),
    power = this?.power?.toDomain(),
    speed = this?.speed?.toDomain(),
)

fun RemoteIntensityExtensionZones.toDomain(): IntensityZonesData {
    return IntensityZonesData(
        this.zone1.totalTime,
        this.zone2.totalTime,
        this.zone3.totalTime,
        this.zone4.totalTime,
        this.zone5.totalTime,
        this.zone2.lowerLimit,
        this.zone3.lowerLimit,
        this.zone4.lowerLimit,
        this.zone5.lowerLimit
    )
}

fun RemoteWorkoutExtension.Type.toDomainType(): Int {
    return when (this) {
        RemoteWorkoutExtension.Type.SUMMARY_EXTENSION -> WorkoutExtension.TYPE_WORKOUT_SUMMARY
        RemoteWorkoutExtension.Type.FITNESS_EXTENSION -> WorkoutExtension.TYPE_FITNESS
        RemoteWorkoutExtension.Type.SKI_EXTENSION -> WorkoutExtension.TYPE_SLOPE_SKI
        RemoteWorkoutExtension.Type.INTENSITY_EXTENSION -> WorkoutExtension.TYPE_WORKOUT_INTENSITY
        RemoteWorkoutExtension.Type.DIVE_HEADER_EXTENSION -> WorkoutExtension.TYPE_DIVE
        RemoteWorkoutExtension.Type.SWIMMING_HEADER_EXTENSION -> WorkoutExtension.TYPE_SWIMMING
        RemoteWorkoutExtension.Type.WEATHER_EXTENSION -> WorkoutExtension.TYPE_WEATHER
        RemoteWorkoutExtension.Type.JUMP_ROPE_EXTENSION -> WorkoutExtension.TYPE_JUMP_ROPE
    }
}

fun List<RemoteWorkoutExtension.RemoteZapp>?.toDomainType(): List<SuuntoLogbookZapp> =
    this?.map { it.toDomainType() } ?: listOf()

fun RemoteWorkoutExtension.RemoteZapp.toDomainType(): SuuntoLogbookZapp = SuuntoLogbookZapp(
    id = id,
    authorId = authorId,
    externalId = externalId,
    name = name,
    summaryOutputs = summaryOutputs?.map { it.toDomainType() },
    channels = channels?.map { it.toDomainType() }
)

fun RemoteWorkoutExtension.RemoteSummaryOutput.toDomainType(): SuuntoLogbookZapp.SummaryOutput =
    SuuntoLogbookZapp.SummaryOutput(
        format = format,
        id = id,
        name = name,
        postfix = postfix,
        summaryValue = summaryValue
    )

fun RemoteWorkoutExtension.RemoteZappChannel.toDomainType(): SuuntoLogbookZapp.ZappChannel =
    SuuntoLogbookZapp.ZappChannel(
        channelId = channelId,
        format = format,
        inverted = inverted,
        name = name,
        variableId = variableId,
    )

fun SuuntoLogbookZapp.toRemoteType(): RemoteWorkoutExtension.RemoteZapp =
    RemoteWorkoutExtension.RemoteZapp(
        id = id,
        authorId = authorId,
        externalId = externalId,
        name = name,
        summaryOutputs = summaryOutputs?.mapNotNull { it.toRemoteType() },
        channels = channels?.map { it.toRemoteType() }
    )

fun SuuntoLogbookZapp.SummaryOutput.toRemoteType(): RemoteWorkoutExtension.RemoteSummaryOutput? =
    if (summaryValue != null) {
        RemoteWorkoutExtension.RemoteSummaryOutput(
            format = format,
            id = id,
            name = name,
            postfix = postfix,
            summaryValue = summaryValue!!
        )
    } else {
        null
    }

fun SuuntoLogbookZapp.ZappChannel.toRemoteType(): RemoteWorkoutExtension.RemoteZappChannel =
    RemoteWorkoutExtension.RemoteZappChannel(
        channelId = channelId,
        format = format,
        inverted = inverted,
        name = name,
        variableId = variableId,
    )
