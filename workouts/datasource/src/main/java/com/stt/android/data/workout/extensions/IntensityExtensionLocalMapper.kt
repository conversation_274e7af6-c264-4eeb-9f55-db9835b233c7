package com.stt.android.data.workout.extensions

import com.soy.algorithms.intensity.IntensityZones
import com.soy.algorithms.intensity.IntensityZonesData
import com.stt.android.data.EntityMapper
import com.stt.android.data.source.local.intensityextension.LocalIntensityExtension
import com.stt.android.data.source.local.intensityextension.LocalWorkoutIntensityZone
import com.stt.android.domain.user.workoutextension.IntensityExtension
import javax.inject.Inject

class IntensityExtensionLocalMapper
@Inject constructor() : EntityMapper<LocalIntensityExtension, IntensityExtension> {

    override fun toDomainEntity(): Function1<LocalIntensityExtension, IntensityExtension> = {
        IntensityExtension(
            workoutId = it.workoutId,
            intensityZones = IntensityZones(
                hr = it.hrZones?.toIntensityZoneData(),
                speed = it.speedZones?.toIntensityZoneData(),
                power = it.powerZones?.toIntensityZoneData(),
            ),
        )
    }

    override fun toDataEntity(): Function1<IntensityExtension, LocalIntensityExtension> = {
        it.toLocalIntensityExtension()
    }

    private fun IntensityExtension.toLocalIntensityExtension(): LocalIntensityExtension {
        return LocalIntensityExtension(
            workoutId = workoutId,
            hrZones = intensityZones.hr?.toLocalWorkoutIntensityZone(),
            speedZones = intensityZones.speed?.toLocalWorkoutIntensityZone(),
            powerZones = intensityZones.power?.toLocalWorkoutIntensityZone(),

        )
    }

    private fun IntensityZonesData.toLocalWorkoutIntensityZone(): LocalWorkoutIntensityZone {
        return LocalWorkoutIntensityZone(
            zone1Duration = zone1Duration,
            zone2Duration = zone2Duration,
            zone3Duration = zone3Duration,
            zone4Duration = zone4Duration,
            zone5Duration = zone5Duration,
            zone2LowerLimit = zone2LowerLimit,
            zone3LowerLimit = zone3LowerLimit,
            zone4LowerLimit = zone4LowerLimit,
            zone5LowerLimit = zone5LowerLimit
        )
    }

    private fun LocalWorkoutIntensityZone.toIntensityZoneData(): IntensityZonesData {
        return IntensityZonesData(
            zone1Duration ?: 0f,
            zone2Duration ?: 0f,
            zone3Duration ?: 0f,
            zone4Duration ?: 0f,
            zone5Duration ?: 0f,
            zone2LowerLimit ?: 0f,
            zone3LowerLimit ?: 0f,
            zone4LowerLimit ?: 0f,
            zone5LowerLimit ?: 0f
        )
    }
}
