package com.stt.android.data.reactions;

import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory;
import com.stt.android.backgroundwork.WorkerKey;
import dagger.Binds;
import dagger.Module;
import dagger.multibindings.IntoMap;

@Module
public abstract class ReactionRemoteSyncJobModule {
    @Binds
    @IntoMap
    @WorkerKey(ReactionRemoteSyncJob.class)
    public abstract CoroutineWorkerAssistedFactory bindFactory(ReactionRemoteSyncJob.Factory factory);
}
