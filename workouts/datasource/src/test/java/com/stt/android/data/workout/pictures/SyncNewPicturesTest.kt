package com.stt.android.data.workout.pictures

import com.stt.android.data.session.CurrentUser
import com.stt.android.domain.sync.AggregatedSyncException
import com.stt.android.remote.workout.picture.PictureRemoteApi
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Spy
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
@OptIn(ExperimentalCoroutinesApi::class)
class SyncNewPicturesTest {
    @Mock
    private lateinit var currentUser: CurrentUser

    @Spy
    private lateinit var picturesDataSource: FakePicturesDataSource

    @Spy
    private lateinit var pictureRestApi: FakePictureRestApi

    @Spy
    private lateinit var pictureFileRepository: FakePictureFileRepository

    private lateinit var syncNewPictures: SyncNewPictures

    @Before
    fun setup() = runTest {
        whenever(currentUser.getUsername()).thenReturn("foo")
        syncNewPictures = SyncNewPictures(
            picturesDataSource,
            currentUser,
            PictureRemoteApi(pictureRestApi),
            pictureFileRepository
        )
    }

    @Test
    fun `skip uploading pictures if an error occurs while finding unsynced pictures`() =
        runTest {
            whenever(picturesDataSource.findUnsyncedPictures(any())).thenThrow(RuntimeException::class.java)

            assertThat(syncNewPictures()).isEqualTo(0)
        }

    @Test
    fun `no pictures to upload should return 0 uploaded pictures`() = runTest {
        whenever(picturesDataSource.findUnsyncedPictures(any())).thenReturn(emptyList())

        assertThat(syncNewPictures()).isEqualTo(0)
    }

    @Test
    fun `one picture to upload should return 1 uploaded picture`() = runTest {
        assertThat(syncNewPictures()).isEqualTo(1)
    }

    @Test(expected = AggregatedSyncException::class)
    fun `first picture fails due to null filename should continue and finally throw AggregatedSyncException`() =
        runTest {
            whenever(picturesDataSource.findUnsyncedPictures(any()))
                .thenReturn(
                    listOf(
                        createPicture().copy(fileName = null),
                        createPicture()
                    )
                )
            assertThat(syncNewPictures()).isEqualTo(1)
        }

    @Test(expected = AggregatedSyncException::class)
    fun `first picture fails due to null workoutKey should continue and finally throw AggregatedSyncException`() =
        runTest {
            whenever(picturesDataSource.findUnsyncedPictures(any()))
                .thenReturn(
                    listOf(
                        createPicture().copy(workoutKey = null),
                        createPicture()
                    )
                )

            syncNewPictures()
        }

    @Test(expected = AggregatedSyncException::class)
    fun `first picture fails due to null md5hash should continue and finally throw AggregatedSyncException`() =
        runTest {
            whenever(picturesDataSource.findUnsyncedPictures(any()))
                .thenReturn(
                    listOf(
                        createPicture().copy(md5Hash = null),
                        createPicture()
                    )
                )

            syncNewPictures()
        }
}
