package com.stt.android.data.workout

import com.stt.android.data.workout.binary.BinaryFileRepository
import com.stt.android.domain.workouts.WorkoutHeader
import java.io.File

class FakeBinaryFileRepository : BinaryFileRepository {
    override suspend fun create(workoutHeader: WorkoutHeader) {
    }

    override suspend fun update(workoutHeader: WorkoutHeader) {
    }

    override suspend fun get(workoutHeader: WorkoutHeader): File {
        return File("foo")
    }
}
