package com.stt.android.domain.workouts.extensions

import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.firstOfType
import kotlin.reflect.KClass

class FakeExtensionsDataSource : ExtensionsDataSource {

    var extensions: MutableMap<Int, List<WorkoutExtension>> = mutableMapOf()

    override suspend fun getExtensionsForWorkout(workoutHeader: WorkoutHeader): List<WorkoutExtension> =
        extensions[workoutHeader.id] ?: emptyList()

    override suspend fun upsertExtension(workoutId: Int, extension: WorkoutExtension) {
        val foundList = (extensions[workoutId] ?: emptyList()).toMutableList()
        extensions[workoutId] = foundList.apply {
            removeIf { it::class == extension::class }
        } + extension
    }

    override suspend fun findExtensionsByType(extensionClass: KClass<out WorkoutExtension>): List<WorkoutExtension> =
        extensions.values.flatten().filter { it::class == extensionClass }

    override suspend fun loadExtensions(workoutId: Int): List<WorkoutExtension> =
        extensions[workoutId] ?: emptyList()

    override suspend fun loadIntensityExtension(workoutId: Int): IntensityExtension? =
        extensions[workoutId]?.firstOfType()

    override suspend fun fetchExtensions(
        workoutId: Int,
        workoutKey: String
    ): List<WorkoutExtension> = extensions[workoutId] ?: emptyList()
}
