package com.stt.android.domain.workouts.attributes

import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class DomainWorkoutAttributesUpdateTest {
    @Test
    fun `updateWith does not change workoutId or ownerUsername`() {
        val original = allUpdate
        val updated = original.updateWith(emptyUpdate)

        assertThat(updated.workoutId).isEqualTo(original.workoutId)
        assertThat(updated.ownerUsername).isEqualTo(original.ownerUsername)
    }

    @Test
    fun `updateWith empty does no changes`() {
        val original = allUpdate.copy(fieldsToDelete = allDeletion.fieldsToDelete)
        val updated = original.updateWith(emptyUpdate)

        assertThat(updated).isEqualTo(original)
    }

    @Test
    fun `updateWith equal does no changes`() {
        val allUpdatesReapplied = allUpdate.updateWith(allUpdate)
        val allDeletionsReapplied = allDeletion.updateWith(allDeletion)

        assertThat(allUpdatesReapplied).isEqualTo(allUpdate)
        assertThat(allDeletionsReapplied).isEqualTo(allDeletion)
    }

    @Test
    fun `updateWith properly changes location update values`() {
        val updated = locationUpdate1.updateWith(locationUpdate2)

        assertThat(updated.workoutLocation).isEqualTo(locationUpdate2.workoutLocation)
        assertThat(updated.fieldsToDelete).doesNotContain("startPosition")
    }

    @Test
    fun `updateWith properly changes location deletion to update`() {
        val updated = allDeletion.updateWith(locationUpdate1)

        assertThat(updated.workoutLocation).isEqualTo(locationUpdate1.workoutLocation)
        assertThat(updated.fieldsToDelete).doesNotContain("startPosition")
    }

    @Test
    fun `updateWith properly changes location update to deletion`() {
        val updated = locationUpdate1.updateWith(allDeletion)

        assertThat(updated.workoutLocation).isNull()
        assertThat(updated.fieldsToDelete).contains("startPosition")
    }

    @Test
    fun `updateWith location change doesn't affect other attributes`() {
        val updated = allUpdate.updateWith(locationUpdate2)

        assertThat(updated.tss).isEqualTo(allUpdate.tss)
        assertThat(updated.maxSpeed).isEqualTo(allUpdate.maxSpeed)
        assertThat(updated.totalAscent).isEqualTo(allUpdate.totalAscent)
        assertThat(updated.totalDescent).isEqualTo(allUpdate.totalDescent)
        assertThat(updated.fieldsToDelete).isEqualTo(allUpdate.fieldsToDelete)
    }

    @Test
    fun `updateWith properly changes tss update values`() {
        val updated = tssUpdate1.updateWith(tssUpdate2)

        assertThat(updated.tss).isEqualTo(tssUpdate2.tss)
        assertThat(updated.fieldsToDelete).doesNotContain("tss")
    }

    @Test
    fun `updateWith properly changes tss deletion to update`() {
        val updated = allDeletion.updateWith(tssUpdate1)

        assertThat(updated.tss).isEqualTo(tssUpdate1.tss)
        assertThat(updated.fieldsToDelete).doesNotContain("tss")
    }

    @Test
    fun `updateWith properly changes tss update to deletion`() {
        val updated = tssUpdate1.updateWith(allDeletion)

        assertThat(updated.tss).isNull()
        assertThat(updated.fieldsToDelete).contains("tss")
    }

    @Test
    fun `updateWith tss change doesn't affect other attributes`() {
        val updated = allUpdate.updateWith(tssUpdate2)

        assertThat(updated.workoutLocation).isEqualTo(allUpdate.workoutLocation)
        assertThat(updated.maxSpeed).isEqualTo(allUpdate.maxSpeed)
        assertThat(updated.totalAscent).isEqualTo(allUpdate.totalAscent)
        assertThat(updated.totalDescent).isEqualTo(allUpdate.totalDescent)
        assertThat(updated.fieldsToDelete).isEqualTo(allUpdate.fieldsToDelete)
    }

    @Test
    fun `updateWith properly changes maxSpeed update values`() {
        val updated = maxSpeedUpdate1.updateWith(maxSpeedUpdate2)

        assertThat(updated.maxSpeed).isEqualTo(maxSpeedUpdate2.maxSpeed)
        assertThat(updated.fieldsToDelete).doesNotContain("maxSpeed")
    }

    @Test
    fun `updateWith properly changes maxSpeed deletion to update`() {
        val updated = allDeletion.updateWith(maxSpeedUpdate1)

        assertThat(updated.maxSpeed).isEqualTo(maxSpeedUpdate1.maxSpeed)
        assertThat(updated.fieldsToDelete).doesNotContain("maxSpeed")
    }

    @Test
    fun `updateWith properly changes maxSpeed update to deletion`() {
        val updated = maxSpeedUpdate1.updateWith(allDeletion)

        assertThat(updated.maxSpeed).isNull()
        assertThat(updated.fieldsToDelete).contains("maxSpeed")
    }

    @Test
    fun `updateWith maxSpeed change doesn't affect other attributes`() {
        val updated = allUpdate.updateWith(maxSpeedUpdate2)

        assertThat(updated.workoutLocation).isEqualTo(allUpdate.workoutLocation)
        assertThat(updated.tss).isEqualTo(allUpdate.tss)
        assertThat(updated.totalAscent).isEqualTo(allUpdate.totalAscent)
        assertThat(updated.totalDescent).isEqualTo(allUpdate.totalDescent)
        assertThat(updated.fieldsToDelete).isEqualTo(allUpdate.fieldsToDelete)
    }

    @Test
    fun `updateWith properly changes ascent update values`() {
        val updated = ascentUpdate1.updateWith(ascentUpdate2)

        assertThat(updated.totalAscent).isEqualTo(ascentUpdate2.totalAscent)
        assertThat(updated.fieldsToDelete).doesNotContain("totalAscent")
    }

    @Test
    fun `updateWith properly changes ascent deletion to update`() {
        val updated = allDeletion.updateWith(ascentUpdate1)

        assertThat(updated.totalAscent).isEqualTo(ascentUpdate1.totalAscent)
        assertThat(updated.fieldsToDelete).doesNotContain("totalAscent")
    }

    @Test
    fun `updateWith properly changes ascent update to deletion`() {
        val updated = ascentUpdate1.updateWith(allDeletion)

        assertThat(updated.totalAscent).isNull()
        assertThat(updated.fieldsToDelete).contains("totalAscent")
    }

    @Test
    fun `updateWith ascent change doesn't affect other attributes`() {
        val updated = allUpdate.updateWith(ascentUpdate2)

        assertThat(updated.workoutLocation).isEqualTo(allUpdate.workoutLocation)
        assertThat(updated.tss).isEqualTo(allUpdate.tss)
        assertThat(updated.maxSpeed).isEqualTo(allUpdate.maxSpeed)
        assertThat(updated.totalDescent).isEqualTo(allUpdate.totalDescent)
        assertThat(updated.fieldsToDelete).isEqualTo(allUpdate.fieldsToDelete)
    }

    @Test
    fun `updateWith properly changes descent update values`() {
        val updated = descentUpdate1.updateWith(descentUpdate2)

        assertThat(updated.totalDescent).isEqualTo(descentUpdate2.totalDescent)
        assertThat(updated.fieldsToDelete).doesNotContain("totalDescent")
    }

    @Test
    fun `updateWith properly changes descent deletion to update`() {
        val updated = allDeletion.updateWith(descentUpdate1)

        assertThat(updated.totalDescent).isEqualTo(descentUpdate1.totalDescent)
        assertThat(updated.fieldsToDelete).doesNotContain("totalDescent")
    }

    @Test
    fun `updateWith properly changes descent update to deletion`() {
        val updated = descentUpdate1.updateWith(allDeletion)

        assertThat(updated.totalDescent).isNull()
        assertThat(updated.fieldsToDelete).contains("totalDescent")
    }

    @Test
    fun `updateWith descent change doesn't affect other attributes`() {
        val updated = allUpdate.updateWith(descentUpdate2)

        assertThat(updated.workoutLocation).isEqualTo(allUpdate.workoutLocation)
        assertThat(updated.tss).isEqualTo(allUpdate.tss)
        assertThat(updated.maxSpeed).isEqualTo(allUpdate.maxSpeed)
        assertThat(updated.totalAscent).isEqualTo(allUpdate.totalAscent)
        assertThat(updated.fieldsToDelete).isEqualTo(allUpdate.fieldsToDelete)
    }

    @Test
    fun `updateWith should correctly update requiresUserConfirmation based on the new value`() {
        val originalHadIt = allUpdate.copy(requiresUserConfirmation = true)
            .updateWith(allDeletion) // false originally
        val updatesHadIt = allUpdate.updateWith(allDeletion.copy(requiresUserConfirmation = true))

        assertThat(originalHadIt.requiresUserConfirmation).isFalse
        assertThat(updatesHadIt.requiresUserConfirmation).isTrue
    }

    @Test
    fun `updateWith properly changes suunto tags values`() {
        pendingSuuntoTagsUpdate = pendingSuuntoTagsUpdate.copy(
            attributes = pendingSuuntoTagsUpdate.attributes.copy(
                suuntoTags = listOf(SuuntoTag.COMMUTE)
            )
        )
        currentSuuntoTagsUpdate = currentSuuntoTagsUpdate.copy(
            attributes = currentSuuntoTagsUpdate.attributes.copy(
                suuntoTags = listOf()
            )
        )

        val updated = pendingSuuntoTagsUpdate.updateWith(currentSuuntoTagsUpdate)

        assertThat(updated.suuntoTags).isEqualTo(currentSuuntoTagsUpdate.suuntoTags)
        assertThat(updated.fieldsToDelete).doesNotContain("suuntoTags")
    }

    companion object {
        private val emptyUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(null, null, null, null, null, null),
            emptyList(),
            false
        )
        private val allUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                DomainWorkoutLocation(10.0, 10.0),
                TSS(60f, TSSCalculationMethod.MANUAL),
                13.7,
                44.5,
                26.6,
                suuntoTags = listOf(SuuntoTag.COMMUTE)
            ),
            emptyList(),
            false
        )
        private val allDeletion = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(null, null, null, null, null, null),
            listOf("startPosition", "tss", "maxSpeed", "totalAscent", "totalDescent", "suuntoTags"),
            false
        )
        private val locationUpdate1 = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(DomainWorkoutLocation(10.0, 10.0), null, null, null, null, null),
            emptyList(),
            false
        )
        private val locationUpdate2 = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(DomainWorkoutLocation(20.0, 20.0), null, null, null, null, null),
            emptyList(),
            false
        )
        private val tssUpdate1 = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                null,
                TSS(50f, TSSCalculationMethod.HR, 2f, 3f, 4f),
                null,
                null,
                null,
                null
            ),
            emptyList(),
            false
        )
        private val tssUpdate2 = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(null, TSS(60f, TSSCalculationMethod.MANUAL), null, null, null, null),
            emptyList(),
            false
        )
        private val maxSpeedUpdate1 = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(null, null, 12.7, null, null, null),
            emptyList(),
            false
        )
        private val maxSpeedUpdate2 = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(null, null, 11.4, null, null, null),
            emptyList(),
            false
        )
        private val ascentUpdate1 = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(null, null, null, 42.0, null, null),
            emptyList(),
            false
        )
        private val ascentUpdate2 = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(null, null, null, 40.4, null, null),
            emptyList(),
            false
        )
        private val descentUpdate1 = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(null, null, null, null, 24.0, null),
            emptyList(),
            false
        )
        private val descentUpdate2 = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(null, null, null, null, 22.1, null),
            emptyList(),
            false
        )

        private var pendingSuuntoTagsUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(null, null, null, null, null, listOf(SuuntoTag.COMMUTE)),
            emptyList(),
            false
        )

        private var currentSuuntoTagsUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(null, null, null, null, null, emptyList()),
            emptyList(),
            false
        )
    }
}
