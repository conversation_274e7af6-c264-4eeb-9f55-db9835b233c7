package com.stt.android.domain.sml.dive

import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.DomainWindow
import com.stt.android.logbook.LogbookGasData
import com.stt.android.logbook.NgDiveData
import com.stt.android.logbook.NgDiveFooter
import com.stt.android.logbook.NgDiveHeader
import com.stt.android.logbook.convertToLogbookGasData
import com.stt.android.logbook.filterValidGases

object NgDiveDataBuilder {
    fun build(
        diveFooter: NgDiveFooter?,
        diveHeader: NgDiveHeader?,
        activityWindow: DomainWindow?,
        moveWindow: DomainWindow?,
        activityType: ActivityType
    ): NgDiveData {
        val gases = diveFooter?.gases?.filterValidGases()
            ?.convertToLogbookGasData() ?: emptyList()
        val gasQuantities: Map<String, LogbookGasData?> =
            gases.associateBy { it.gasIndex.toString() }
        val gasesUsed = gases.map { it.gasName }
        val isScubaDiving = activityType == ActivityType.SCUBADIVING
        val avgDepth = if (isScubaDiving) {
            activityWindow?.depthAverage?.toFloat() ?: moveWindow?.depthAverage?.toFloat()
        } else {
            moveWindow?.maxDepthAverage?.toFloat()
        }
        val maxDepth = activityWindow?.depth?.max?.toFloat()
        val cns = diveFooter?.endTissue?.cns?.let { (it / 100).toFloat() }
        val otu = diveFooter?.endTissue?.otu?.toFloat()
        val surfaceTime =
            if (isScubaDiving) diveHeader?.surfaceTime?.toFloat() else activityWindow?.diveRecoveryTime?.toFloat()
        val diveTime = activityWindow?.diveTime?.toFloat()
        val algorithm = diveHeader?.algorithm
        val highGF = diveHeader?.highGF?.toFloat()?.div(100)
        val lowGF = diveHeader?.lowGF?.toFloat()?.div(100)
        val diveInSeries = diveHeader?.diveInSeries

        return NgDiveData(
            maxDepth = maxDepth,
            algorithm = algorithm,
            diveNumberInSeries = diveInSeries,
            cns = cns,
            otu = otu,
            gasQuantities = gasQuantities,
            surfaceTime = surfaceTime,
            diveTime = diveTime,
            gasesUsed = gasesUsed,
            avgDepth = avgDepth,
            minGF = lowGF,
            maxGF = highGF
        )
    }
}
