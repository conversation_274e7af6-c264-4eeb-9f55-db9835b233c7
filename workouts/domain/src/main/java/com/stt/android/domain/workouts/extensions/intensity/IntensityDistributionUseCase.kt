package com.stt.android.domain.workouts.extensions.intensity

import com.stt.android.core.domain.workouts.CoreActivityGroup
import com.stt.android.core.domain.workouts.CoreActivityGroupByType
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.workouts.BasicWorkoutHeader
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import javax.inject.Inject

class IntensityDistributionUseCase @Inject constructor(
    private val intensityExtensionUseCase: GetIntensityExtensionUseCase,
) {
    suspend fun calculate(
        workouts: List<BasicWorkoutHeader>,
        avgFactor: Float
    ): IntensityDistribution = withContext(IO) {
        workouts.mapNotNull {
            val activityGroup = CoreActivityGroupByType[CoreActivityType.valueOf(it.activityTypeId)]
            val intensityExtension = intensityExtensionUseCase.loadLocalExtension(workoutId = it.id)
            intensityExtension?.let { ext -> ext to activityGroup }
        }.fold(IntensityDistribution()) { distribution, (intensity, activityGroup) ->
            IntensityDistribution(
                hrZones = distribution.hrZones + intensity.intensityZones.hr,
                speedZones = distribution.speedZones + intensity.intensityZones.speed,
                runningPowerZones = if (activityGroup == CoreActivityGroup.RUN) {
                    distribution.runningPowerZones + intensity.intensityZones.power
                } else {
                    distribution.runningPowerZones
                },
                cyclingPowerZones = if (activityGroup == CoreActivityGroup.RIDE) {
                    distribution.cyclingPowerZones + intensity.intensityZones.power
                } else {
                    distribution.cyclingPowerZones
                },
            )
        }.run {
            copy(
                hrZones = hrZones / avgFactor,
                speedZones = speedZones / avgFactor,
                runningPowerZones = runningPowerZones / avgFactor,
                cyclingPowerZones = cyclingPowerZones / avgFactor,
            )
        }
    }
}
