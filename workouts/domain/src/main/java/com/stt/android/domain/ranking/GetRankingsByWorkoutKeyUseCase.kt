package com.stt.android.domain.ranking

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject

class GetRankingsByWorkoutKeyUseCase @Inject constructor(
    private val rankingDataSource: RankingDataSource,
) {
    operator fun invoke(workoutKey: String?): Flow<List<Ranking>> {
        if (workoutKey.isNullOrBlank()) {
            return flowOf(emptyList())
        }
        return rankingDataSource.fetchRankings(workoutKey)
    }
}
