package com.stt.android.domain.workouts

import com.stt.android.domain.workouts.extensions.SummaryExtension

interface WeChatWorkoutDataSource {

    fun store(workoutHeader: WorkoutHeader, extension: SummaryExtension)

    suspend fun findUnSyncData(): List<WeChatWorkoutData>

    suspend fun updateStatus(id: Int, sync: <PERSON><PERSON><PERSON>)

    suspend fun findSyncedData(): List<WeChatWorkoutData>

    suspend fun deleteSynced()

    fun deleteAll()
}
