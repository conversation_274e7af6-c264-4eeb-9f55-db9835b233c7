package com.stt.android.domain.workouts.videos

import com.stt.android.domain.Point
import com.stt.android.domain.review.ReviewState

data class Video(
    val id: Int? = null,
    val key: String?,
    val workoutId: Int?,
    val workoutKey: String?,
    val username: String?,
    val totalTime: Long,
    val timestamp: Long,
    val description: String?,
    val location: Point?,
    val url: String?,
    val thumbnailUrl: String?,
    val width: Int,
    val height: Int,
    val filename: String?,
    val thumbnailFilename: String?,
    val locallyChanged: Boolean,
    val reviewState: ReviewState = ReviewState.PASS
)
