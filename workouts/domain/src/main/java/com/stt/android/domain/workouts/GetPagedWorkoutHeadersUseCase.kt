package com.stt.android.domain.workouts

import com.stt.android.domain.CoroutineUseCase
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetPagedWorkoutHeadersUseCase
@Inject constructor(
    private val workoutHeaderDataSource: WorkoutHeaderDataSource
) : CoroutineUseCase<List<WorkoutHeader>, GetPagedWorkoutHeadersUseCase.Params> {

    override suspend fun run(params: Params): List<WorkoutHeader> = withContext(IO) {
        when (params) {
            is Params.OnlyActivityType -> {
                workoutHeaderDataSource.findPagedOfType(
                    params.username,
                    params.activityTypeId,
                    params.page
                )
            }
            is Params.ExcludeActivityTypes -> {
                workoutHeaderDataSource.findPagedExcludingTypes(
                    params.username,
                    params.excludedActivityTypeIds,
                    params.page
                )
            }
        }
    }

    sealed class Params {
        data class OnlyActivityType(
            val username: String,
            val activityTypeId: Int,
            val page: Int
        ) : Params()

        data class ExcludeActivityTypes(
            val username: String,
            val excludedActivityTypeIds: Set<Int>,
            val page: Int
        ) : Params()
    }
}
