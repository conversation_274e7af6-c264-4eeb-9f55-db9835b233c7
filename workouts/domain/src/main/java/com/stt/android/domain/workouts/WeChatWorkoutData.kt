package com.stt.android.domain.workouts

import com.stt.android.domain.Point
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS

data class WeChatWorkoutData(
    /**
     * If the workout is local the ID is based on hashed {@link #startTime}. If the workout comes
     * from the backend we use a hash based on the {@link #key}. The hash is used to simplify
     * updates to a workout coming from the backend. We are able to update the workout in the
     * database directly (based on the hashed ID).
     */
    val id: Int,
    /**
     * Database unique {@code Key}. Null if the workout is only local (i.e. has not yet been synced)
     */
    val key: String?,
    val totalDistance: Double,
    val maxSpeed: Double,
    /**
     * Value of {@link ActivityType#getId()}
     */
    val activityTypeId: Int,
    val avgSpeed: Double,
    val description: String?,
    val startPosition: Point?,
    val stopPosition: Point?,
    val centerPosition: Point?,
    val startTime: Long, // ms UTC
    val stopTime: Long, // ms UTC
    val totalTime: Double, // seconds
    val energyConsumption: Double, // kCal
    val username: String,
    val heartRateAverage: Double, // bpm
    /**
     * The percentage of the average heart rate value for this workout compared
     * to the user set max heart rate value. For example 78% means that the
     * heart rate average was 78% of the maximum.
     */
    val heartRateAvgPercentage: Double, // 0..100
    val heartRateMax: Double, // bpm
    /**
     * The percentage of the maximum heart rate value for this workout compared
     * to the user set max heart rate value. For example 50% means that the
     * maximum value reached was half of that set by the user as maximum.
     */
    val heartRateMaxPercentage: Double, // 0..100
    val heartRateUserSetMax: Double, // bpm
    val averageCadence: Int,
    val maxCadence: Int,
    val pictureCount: Int,
    val viewCount: Int,
    val commentCount: Int,
    val sharingFlags: Int,
    val stepCount: Int,
    val polyline: String?,
    val manuallyAdded: Boolean,
    val reactionCount: Int,
    val totalAscent: Double, // meters
    val totalDescent: Double, // meters
    val recoveryTime: Long, // seconds
    val locallyChanged: Boolean = false,
    val deleted: Boolean = false,
    val seen: Boolean = false,
    val maxAltitude: Double?, // decimeters !!!
    val minAltitude: Double?, // decimeters !!!
    val extensionsFetched: Boolean = false,
    val tss: TSS? = null,
    /**
     * List of all TSS values that have been calculated for the workout.
     * The TSS value in use is stored in tss field, and in case of it being manually set
     * then it might not be part of the tssList.
     * Null and empty list should be handled as being equal, and the order doesn't matter.
     * A List is used instead of Set to for consistency with the name from backend and possible
     * future uses for the order
     */
    val tssList: List<TSS> = emptyList(),
    val suuntoTags: List<SuuntoTag>,
    val userTags: List<UserTag>,
    val deviceName: String?,
    val deviceSerial: String?,
    val deviceDisplayName: String?,
    val deviceHardwareVersion: String?,
    val deviceSoftwareVersion: String?,
    val productType: String?,
    val deviceManufacturer: String?,
    val syncStatus: Int,
    val skipsCount: Int?,
)
