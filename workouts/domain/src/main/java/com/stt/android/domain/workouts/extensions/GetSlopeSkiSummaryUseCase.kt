package com.stt.android.domain.workouts.extensions

import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import javax.inject.Inject

class GetSlopeSkiSummaryUseCase
@Inject constructor(
    private val slopeSkiExtensionDataSource: SlopeSkiExtensionDataSource
) {
    suspend operator fun invoke(workoutId: Int): SlopeSkiSummary? {
        return slopeSkiExtensionDataSource.findByWorkoutId(workoutId)
    }
}
