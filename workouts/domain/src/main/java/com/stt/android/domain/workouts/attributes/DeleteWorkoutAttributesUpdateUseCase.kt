package com.stt.android.domain.workouts.attributes

import com.stt.android.domain.CoroutineUseCase
import javax.inject.Inject

/**
 * Use case for deleting pending workout attributes updates
 */
class DeleteWorkoutAttributesUpdateUseCase @Inject constructor(
    private val workoutAttributesUpdateRepository: WorkoutAttributesUpdateDataSource,
) : CoroutineUseCase<Unit, DeleteWorkoutAttributesUpdateUseCase.Params> {
    fun getDeleteStartPositionParams(
        workoutId: Int,
        username: String,
    ) = Params(workoutId, username, listOf(DomainWorkoutAttributesUpdate.FIELD_START_POSITION))

    fun getDeleteTssParams(
        workoutId: Int,
        username: String,
    ) = Params(workoutId, username, listOf(DomainWorkoutAttributesUpdate.FIELD_TSS))

    fun getDeleteMaxSpeedParams(
        workoutId: Int,
        username: String,
    ) = Params(workoutId, username, listOf(DomainWorkoutAttributesUpdate.FIELD_MAX_SPEED))

    fun getDeleteTotalAscentParams(
        workoutId: Int,
        username: String,
    ) = Params(workoutId, username, listOf(DomainWorkoutAttributesUpdate.FIELD_TOTAL_ASCENT))

    fun getDeleteTotalDescentParams(
        workoutId: Int,
        username: String,
    ) = Params(workoutId, username, listOf(DomainWorkoutAttributesUpdate.FIELD_TOTAL_DESCENT))

    override suspend fun run(params: Params) {
        val paramsUpdate = DomainWorkoutAttributesUpdate(
            params.workoutId,
            params.username,
            DomainWorkoutAttributes(null, null, null, null, null, null),
            params.attributesToDelete,
            false
        )
        val pendingUpdate = workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(
            params.workoutId,
            params.username
        )
        val newUpdate = pendingUpdate?.updateWith(paramsUpdate) ?: paramsUpdate
        workoutAttributesUpdateRepository.addWorkoutAttributesUpdate(newUpdate)
    }

    data class Params(
        val workoutId: Int,
        val username: String,
        val attributesToDelete: List<String>
    )
}
