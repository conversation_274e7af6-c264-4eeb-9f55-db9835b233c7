package com.stt.android.remote.workout

import com.squareup.moshi.FromJson
import com.squareup.moshi.JsonReader
import com.squareup.moshi.ToJson
import com.stt.android.moshi.buildBasicMoshi
import com.stt.android.remote.di.RestApiFactory.createMoshi
import com.stt.android.remote.extensions.RemoteWorkoutExtension

/**
 * Moshi adapter for serialising and deserializing [RemoteWorkoutExtension]
 */
class WorkoutExtensionAdapter {

    private val moshi = buildBasicMoshi()

    @FromJson
    fun fromJson(reader: JsonReader): RemoteWorkoutExtension? {
        val jsonValue = reader.readJsonValue()
        val map = (jsonValue as Map<*, *>)
        val type = try {
            RemoteWorkoutExtension.Type.fromValue(
                map["type"] as? String ?: return RemoteWorkoutExtension.RemoteUnknownExtension
            )
        } catch (e: NoSuchElementException) {
            return RemoteWorkoutExtension.RemoteUnknownExtension
        }

        // Parse with detected type
        return when (type) {
            RemoteWorkoutExtension.Type.SKI_EXTENSION ->
                moshi.adapter(RemoteWorkoutExtension.RemoteSlopeSkiSummaryExtension::class.java).fromJsonValue(jsonValue)
            RemoteWorkoutExtension.Type.FITNESS_EXTENSION ->
                moshi.adapter(RemoteWorkoutExtension.RemoteFitnessExtension::class.java).fromJsonValue(jsonValue)
            RemoteWorkoutExtension.Type.INTENSITY_EXTENSION ->
                moshi.adapter(RemoteWorkoutExtension.RemoteIntensityExtension::class.java).fromJsonValue(jsonValue)
            RemoteWorkoutExtension.Type.SUMMARY_EXTENSION ->
                moshi.adapter(RemoteWorkoutExtension.RemoteSummaryExtension::class.java).fromJsonValue(jsonValue)
            RemoteWorkoutExtension.Type.DIVE_HEADER_EXTENSION ->
                moshi.adapter(RemoteWorkoutExtension.RemoteDiveHeaderExtension::class.java).fromJsonValue(jsonValue)
            RemoteWorkoutExtension.Type.SWIMMING_HEADER_EXTENSION ->
                moshi.adapter(RemoteWorkoutExtension.RemoteSwimmingHeaderExtension::class.java).fromJsonValue(jsonValue)
            RemoteWorkoutExtension.Type.WEATHER_EXTENSION ->
                moshi.adapter(RemoteWorkoutExtension.RemoteWeatherExtension::class.java).fromJsonValue(jsonValue)
            RemoteWorkoutExtension.Type.JUMP_ROPE_EXTENSION ->
                moshi.adapter(RemoteWorkoutExtension.RemoteJumpRopeExtension::class.java).fromJsonValue(jsonValue)
        }
    }

    @ToJson
    fun toJson(remoteWorkoutExtension: RemoteWorkoutExtension): Any {
        return when (remoteWorkoutExtension) {
            is RemoteWorkoutExtension.RemoteSlopeSkiSummaryExtension,
            is RemoteWorkoutExtension.RemoteFitnessExtension,
            is RemoteWorkoutExtension.RemoteIntensityExtension,
            is RemoteWorkoutExtension.RemoteSummaryExtension,
            is RemoteWorkoutExtension.RemoteDiveHeaderExtension,
            is RemoteWorkoutExtension.RemoteSwimmingHeaderExtension,
            is RemoteWorkoutExtension.RemoteWeatherExtension -> remoteWorkoutExtension
            else -> throw IllegalArgumentException("Serializing ${remoteWorkoutExtension::class.java} is not supported")
        }
    }
}

internal fun createMoshiWithWorkoutExtensionAdapter() = createMoshi(setOf(WorkoutExtensionAdapter()))
