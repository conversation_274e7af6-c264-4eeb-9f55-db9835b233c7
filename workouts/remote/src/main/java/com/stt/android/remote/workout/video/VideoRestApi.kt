package com.stt.android.remote.workout.video

import com.stt.android.remote.response.AskoResponse
import okhttp3.MultipartBody
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query

interface VideoRestApi {
    @Multipart
    @POST("workouts/{workoutKey}/video")
    suspend fun uploadVideo(
        @Path("workoutKey") workoutKey: String,
        @Query("timestamp") timestamp: Long,
        @Query("lat") latitude: Double?,
        @Query("lon") longitude: Double?,
        @Query("totaltime") totaltime: Long,
        @Query("hash") md5hash: String,
        @Query("width") width: Int,
        @Query("height") height: Int,
        @Part videoFile: MultipartBody.Part,
        @Part thumbnail: MultipartBody.Part
    ): AskoResponse<RemoteVideo>
}
