package com.suunto.connectivity.mdsapi.annotations

import kotlinx.coroutines.flow.Flow
import java.lang.reflect.Method
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type

/**
 * Parsed service method information
 */
data class ServiceMethod(
    val httpMethod: HttpMethod,
    val urlTemplate: String,
    val parameters: List<ParameterInfo>,
    val returnType: Type,
    val isFlow: Boolean,
    val isSuspend: Boolean
) {
    
    companion object {
        fun parse(method: Method): ServiceMethod {
            val httpMethod = parseHttpMethod(method)
            val urlTemplate = parseUrlTemplate(method, httpMethod)
            val parameters = parseParameters(method)
            val returnType = parseReturnType(method)
            val isFlow = isFlowReturnType(method)
            val isSuspend = method.parameterTypes.lastOrNull()?.name == "kotlin.coroutines.Continuation"
            
            return ServiceMethod(
                httpMethod = httpMethod,
                urlTemplate = urlTemplate,
                parameters = parameters,
                returnType = returnType,
                isFlow = isFlow,
                isSuspend = isSuspend
            )
        }
        
        private fun parseHttpMethod(method: Method): HttpMethod {
            return when {
                method.isAnnotationPresent(GET::class.java) -> HttpMethod.GET
                method.isAnnotationPresent(POST::class.java) -> HttpMethod.POST
                method.isAnnotationPresent(PUT::class.java) -> HttpMethod.PUT
                method.isAnnotationPresent(DELETE::class.java) -> HttpMethod.DELETE
                method.isAnnotationPresent(SUBSCRIBE::class.java) -> HttpMethod.SUBSCRIBE
                else -> throw IllegalArgumentException("Method ${method.name} must have HTTP method annotation")
            }
        }
        
        private fun parseUrlTemplate(method: Method, httpMethod: HttpMethod): String {
            return when (httpMethod) {
                HttpMethod.GET -> method.getAnnotation(GET::class.java).value
                HttpMethod.POST -> method.getAnnotation(POST::class.java).value
                HttpMethod.PUT -> method.getAnnotation(PUT::class.java).value
                HttpMethod.DELETE -> method.getAnnotation(DELETE::class.java).value
                HttpMethod.SUBSCRIBE -> method.getAnnotation(SUBSCRIBE::class.java).value
            }
        }
        
        private fun parseParameters(method: Method): List<ParameterInfo> {
            val parameters = mutableListOf<ParameterInfo>()
            val parameterAnnotations = method.parameterAnnotations
            
            parameterAnnotations.forEachIndexed { index, annotations ->
                for (annotation in annotations) {
                    when (annotation) {
                        is Path -> parameters.add(ParameterInfo.Path(annotation.value, index))
                        is Query -> parameters.add(ParameterInfo.Query(annotation.value, index))
                        is Body -> parameters.add(ParameterInfo.Body(index))
                        is Serial -> parameters.add(ParameterInfo.Serial(index))
                    }
                }
            }
            
            return parameters
        }
        
        private fun parseReturnType(method: Method): Type {
            val returnType = method.genericReturnType
            
            // Handle suspend functions (return type is Object for suspend functions)
            if (method.parameterTypes.lastOrNull()?.name == "kotlin.coroutines.Continuation") {
                // For suspend functions, we need to extract the actual return type from Continuation
                val continuationType = method.genericParameterTypes.lastOrNull() as? ParameterizedType
                return continuationType?.actualTypeArguments?.firstOrNull() ?: returnType
            }
            
            return returnType
        }
        
        private fun isFlowReturnType(method: Method): Boolean {
            val returnType = method.genericReturnType
            return when (returnType) {
                is ParameterizedType -> returnType.rawType == Flow::class.java
                is Class<*> -> Flow::class.java.isAssignableFrom(returnType)
                else -> false
            }
        }
    }
}
