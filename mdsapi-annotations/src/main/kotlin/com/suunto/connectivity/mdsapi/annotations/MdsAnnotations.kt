package com.suunto.connectivity.mdsapi.annotations

/**
 * HTTP method annotations for MDS API
 */
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class GET(val value: String)

@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class POST(val value: String)

@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class PUT(val value: String)

@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class DELETE(val value: String)

@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class SUBSCRIBE(val value: String)

/**
 * Parameter annotations for MDS API
 */
@Target(AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
annotation class Path(val value: String)

@Target(AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
annotation class Query(val value: String)

@Target(AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
annotation class Body

/**
 * Special annotation for serial parameter
 */
@Target(AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
annotation class Serial

/**
 * Annotation to mark interfaces for automatic Consumer/Producer generation
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.SOURCE)
annotation class GenerateMdsConsumerProducer

/**
 * Annotation to specify timeout for MDS API calls
 */
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class Timeout(val value: Long = 30000L) // Default 30 seconds

/**
 * HTTP methods supported by MDS
 */
enum class HttpMethod {
    GET, POST, PUT, DELETE, SUBSCRIBE
}

/**
 * Annotation to mark Query classes for automatic Consumer/Producer generation
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.SOURCE)
annotation class MdsQuery

/**
 * Annotation to mark Response classes for automatic Consumer/Producer generation
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.SOURCE)
annotation class MdsResponse

/**
 * Annotation to specify message type for Query classes
 * If not specified, a unique message type will be auto-generated
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.SOURCE)
annotation class MessageType(val value: Int)

/**
 * Parameter information for MDS API calls
 */
sealed class ParameterInfo {
    data class Path(val name: String, val index: Int) : ParameterInfo()
    data class Query(val name: String, val index: Int) : ParameterInfo()
    data class Body(val index: Int) : ParameterInfo()
    data class Serial(val index: Int) : ParameterInfo()
}
