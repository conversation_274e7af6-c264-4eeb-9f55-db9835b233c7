<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="androidx.core.content.ContextCompat" />

        <variable
            name="infoModelFormatter"
            type="com.stt.android.mapping.InfoModelFormatter" />

        <variable
            name="name"
            type="java.lang.String" />

        <variable
            name="onSaveClicked"
            type="android.view.View.OnClickListener" />

        <variable
            name="viewModel"
            type="com.stt.android.home.explore.pois.POIDetailsViewModel" />

        <import type="android.view.View" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/poi_details_edit_button"
                style="@style/Fab.Small"
                android:layout_marginTop="@dimen/size_spacing_xlarge"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:contentDescription="@string/edit"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/edit_poi" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/copy_coordinates_button"
                style="@style/Fab.Small"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:contentDescription="@null"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/poi_details_edit_button"
                app:srcCompat="@drawable/copy_coordinates" />

            <TextView
                android:id="@+id/poi_details_name_text"
                style="@style/Body.Large.Bold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_small"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@{name}"
                app:layout_constraintEnd_toStartOf="@id/poi_details_edit_button"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/poi_details_edit_button"
                tools:text="Montana del Filo, Santa Cruz de Tenerife" />

            <TextView
                android:id="@+id/poi_details_coordinates_text"
                style="@style/Body.Medium"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_small"
                android:maxLines="2"
                android:text="@{viewModel.coordinatesText}"
                android:textIsSelectable="true"
                app:layout_constraintEnd_toStartOf="@id/copy_coordinates_button"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/copy_coordinates_button"
                tools:text="28°25'08.1&quot;N 16°24'19.8&quot;W" />

            <TextView
                android:id="@+id/poi_details_altitude_text"
                style="@style/Body.Medium"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/size_spacing_medium"
                android:text="@{@string/altitude_at_location(viewModel.altitudeText)}"
                app:layout_constraintEnd_toStartOf="@id/copy_coordinates_button"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/poi_details_coordinates_text"
                tools:text="Altitude: 1280 m" />

            <TextView
                android:id="@+id/poi_details_creation_date_text"
                style="@style/Body.Medium"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/size_spacing_medium"
                android:text="@{@string/poi_created_on_date(viewModel.creationDateText)}"
                android:visibility="@{viewModel.creationDateText != null ? View.VISIBLE : View.GONE}"
                app:layout_constraintEnd_toStartOf="@id/copy_coordinates_button"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/poi_details_altitude_text"
                tools:text="Created: 01/01/2020" />

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/weatherInfoFragment"
                android:name="com.stt.android.home.explore.weather.WeatherInfoFragment"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:elevation="@dimen/elevation_navbar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/poi_details_creation_date_text" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/poi_details_type_and_actions_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:elevation="@dimen/elevation_navbar"
                android:paddingBottom="@dimen/size_spacing_medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/weatherInfoFragment">

                <View
                    android:id="@+id/poi_details_type_row"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/list_item_two_lines_height"
                    android:background="?selectableItemBackground"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/poi_details_type_label"
                    style="@style/Body.Larger"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:text="@string/waypoint_details_type_label"
                    app:layout_constraintBottom_toBottomOf="@id/poi_details_type_row"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/poi_details_type_row"
                    tools:text="Type" />

                <ImageView
                    android:id="@+id/poi_details_type_icon"
                    android:layout_width="@dimen/poi_icon_size"
                    android:layout_height="@dimen/poi_icon_size"
                    android:layout_marginEnd="20dp"
                    android:src="@{viewModel.poiType != null &amp;&amp; viewModel.poiType.listIconResId != 0 ? ContextCompat.getDrawable(context, viewModel.poiType.listIconResId) : null}"
                    app:layout_constraintBottom_toBottomOf="@id/poi_details_type_row"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/poi_details_type_row"
                    tools:ignore="ContentDescription"
                    tools:src="@drawable/poi_big_game_fill" />

                <TextView
                    android:id="@+id/poi_details_type_text"
                    style="@style/Body.Larger"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:text="@{viewModel.poiType != null &amp;&amp; viewModel.poiType.nameResId != 0 ? context.getString(viewModel.poiType.nameResId) : null}"
                    app:layout_constraintBottom_toBottomOf="@id/poi_details_type_row"
                    app:layout_constraintEnd_toStartOf="@id/poi_details_type_icon"
                    app:layout_constraintTop_toTopOf="@id/poi_details_type_row"
                    tools:ignore="HardcodedText"
                    tools:text="@string/poi_type_biggame" />

                <View
                    android:id="@+id/divider0"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    android:visibility="@{viewModel.showWatchToggle ? View.VISIBLE : View.GONE}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/poi_details_type_row" />

                <LinearLayout
                    android:id="@+id/use_in_watch_layout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/size_spacing_medium"
                    android:paddingVertical="@dimen/size_spacing_medium"
                    android:visibility="@{viewModel.showWatchToggle ? View.VISIBLE : View.GONE}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider0"
                    tools:ignore="UseCompoundDrawables">

                    <ImageView
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        android:src="@drawable/use_in_watch" />

                    <TextView
                        style="@style/Body.Larger"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/size_spacing_medium"
                        android:layout_weight="1"
                        android:text="@string/add_poi_to_watch" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/poi_details_add_to_watch_switch"
                        style="@style/RadioCheckSwitchStyle"
                        android:enabled="@{!viewModel.syncOngoing}" />
                </LinearLayout>

                <View
                    android:id="@+id/divider"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/use_in_watch_layout" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/poi_details_navigate_now"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/size_spacing_medium"
                    android:paddingVertical="@dimen/size_spacing_medium"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider"
                    tools:ignore="UseCompoundDrawables">

                    <ImageView
                        android:id="@+id/iv_poi_details_navigate_now"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/watch_navigate_outline" />

                    <TextView
                        android:id="@+id/tv_poi_details_navigate_now"
                        style="@style/Body.Larger"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/size_spacing_medium"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_poi_details_navigate_now"
                        app:layout_constraintEnd_toStartOf="@id/iv_poi_details_navigate_now_arrow"
                        android:text="@string/navigate_on_watch_now" />

                    <ImageView
                        android:id="@+id/iv_poi_details_navigate_now_arrow"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/ic_right_arrow"
                        app:tint="@color/near_black" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/divider3"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/poi_details_navigate_now" />


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/poi_details_start_route"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/size_spacing_medium"
                    android:paddingVertical="@dimen/size_spacing_medium"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider3"
                    tools:ignore="UseCompoundDrawables">

                    <ImageView
                        android:id="@+id/iv_location_start_here"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/route_start_button_a" />

                    <TextView
                        android:id="@+id/tv_location_start_here"
                        style="@style/Body.Larger"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/size_spacing_medium"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_location_start_here"
                        app:layout_constraintEnd_toStartOf="@id/iv_location_start_here_arrow"
                        android:text="@string/route_to_destination" />

                    <ImageView
                        android:id="@+id/iv_location_start_here_arrow"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/ic_right_arrow"
                        app:tint="@color/near_black" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/divider1"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/poi_details_start_route" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/poi_details_end_route"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/size_spacing_medium"
                    android:paddingVertical="@dimen/size_spacing_medium"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider1"
                    tools:ignore="UseCompoundDrawables">

                    <ImageView
                        android:id="@+id/iv_location_end_here"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/route_end_button_b" />

                    <TextView
                        android:id="@+id/tv_location_end_here"
                        style="@style/Body.Larger"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/size_spacing_medium"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_location_end_here"
                        app:layout_constraintEnd_toStartOf="@id/iv_location_end_here_arrow"
                        android:text="@string/route_from_destination" />

                    <ImageView
                        android:id="@+id/iv_location_end_here_arrow"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/ic_right_arrow"
                        app:tint="@color/near_black" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/divider2"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/poi_details_end_route" />

                <Button
                    android:id="@+id/poi_details_save_button"
                    style="@style/Button.Primary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/size_spacing_medium"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:onClick="@{onSaveClicked}"
                    android:text="@{(!viewModel.isEditing() || viewModel.isModified()) ? @string/save : @string/done}"
                    android:visibility="@{viewModel.showSyncingMessage ? View.INVISIBLE : View.VISIBLE}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider2"
                    tools:text="@string/save" />

                <Button
                    android:id="@+id/poi_details_cancel_button"
                    style="@style/ButtonFlat"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:text="@string/cancel"
                    android:visibility="@{viewModel.editing ? View.GONE : View.VISIBLE}"
                    app:layout_constraintBottom_toBottomOf="@id/poi_details_save_button"
                    app:layout_constraintEnd_toStartOf="@id/poi_details_save_button"
                    app:layout_constraintTop_toTopOf="@id/poi_details_save_button"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/poi_details_syncing_indicator"
                    style="@style/Body.Medium"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/near_black"
                    android:padding="@dimen/size_spacing_medium"
                    android:text="@string/poi_sync_in_progress"
                    android:textColor="@color/primary_text"
                    app:fadeVisible="@{viewModel.showSyncingMessage}"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</layout>
