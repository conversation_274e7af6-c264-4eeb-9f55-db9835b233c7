package com.stt.android.home.explore.offlinemaps.domain

import androidx.annotation.Size
import androidx.work.WorkManager
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.AppCoroutineScopeProvider
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.home.explore.offlinemaps.datasource.OfflineRegionRepository
import com.stt.android.home.explore.offlinemaps.entities.OfflineMapDownloadTarget
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionResult
import com.stt.android.offlinemaps.domain.DownloadOfflineMapWorker
import com.stt.android.watch.offlinemaps.domain.NotifyAreaSelectionChangedUseCase
import com.stt.android.watch.offlinemaps.domain.NotifyAreaUnderDownloadDeletedUseCase
import com.suunto.connectivity.repository.SuuntoRepositoryException
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

internal class OfflineRegionDownloadOperators @Inject constructor(
    private val notifyAreaSelectionChangedUseCase: NotifyAreaSelectionChangedUseCase,
    private val notifyAreaUnderDownloadDeletedUseCase: NotifyAreaUnderDownloadDeletedUseCase,
    private val repository: OfflineRegionRepository,
    private val workManager: WorkManager,
    private val appCoroutineScopeProvider: AppCoroutineScopeProvider,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    suspend fun downloadOfflineRegion(
        downloadTo: OfflineMapDownloadTarget,
        regionId: String,
        groupName: String? = null,
    ): OfflineRegionResult.OfflineRegion = withContext(coroutinesDispatchers.computation) {
        repository.downloadOfflineRegion(
            downloadTarget = downloadTo,
            regionId = regionId,
            groupName = groupName,
        ).also {
            when (downloadTo) {
                OfflineMapDownloadTarget.WATCH -> notifyWatch(regionId = regionId)
                OfflineMapDownloadTarget.MOBILE -> DownloadOfflineMapWorker.schedule(workManager)
            }
        }
    }

    suspend fun deleteDownload(
        @Size(min = 1L) deleteFrom: Set<OfflineMapDownloadTarget>,
        region: OfflineRegionResult.OfflineRegion,
    ): OfflineRegionResult.OfflineRegion = withContext(coroutinesDispatchers.computation) {
        val regionForWatchAsync = if (deleteFrom.contains(OfflineMapDownloadTarget.WATCH)) {
            async {
                repository.deleteDownload(
                    deleteFrom = OfflineMapDownloadTarget.WATCH,
                    region = region,
                ).also {
                    notifyWatch(
                        regionId = region.id,
                        ongoingDownloadCancelled = region.downloading,
                    )
                }
            }
        } else {
            null
        }

        val regionForMobile = if (deleteFrom.contains(OfflineMapDownloadTarget.MOBILE)) {
            repository.deleteDownload(
                deleteFrom = OfflineMapDownloadTarget.MOBILE,
                region = region,
            ).also {
                DownloadOfflineMapWorker.schedule(workManager)
            }
        } else {
            null
        }

        merge(regionForWatchAsync?.await(), regionForMobile)
    }

    private fun merge(
        regionForWatch: OfflineRegionResult.OfflineRegion?,
        regionForMobile: OfflineRegionResult.OfflineRegion?,
    ): OfflineRegionResult.OfflineRegion = when {
        regionForWatch != null && regionForMobile != null -> regionForWatch.copy(
            downloadOrders = (regionForWatch.downloadOrders + regionForMobile.downloadOrders).toPersistentList(),
        )

        regionForWatch != null -> regionForWatch
        regionForMobile != null -> regionForMobile
        else -> throw IllegalStateException("No download order put. Should not reach here.")
    }

    suspend fun getMapStorageSize(): Long? = repository.getMapStorageSize()

    private fun notifyWatch(
        regionId: String? = null,
        ongoingDownloadCancelled: Boolean = false,
    ) {
        // Breaking structured concurrency letting the download operations and app execution to
        // continue. We should always try to notify the watch, but the operation is not critical and
        // the UI should not wait for these to complete.
        appCoroutineScopeProvider.appCoroutineScope.launch {
            if (ongoingDownloadCancelled && regionId != null) {
                runSuspendCatching {
                    notifyAreaUnderDownloadDeletedUseCase(regionId)
                }.onSuccess {
                    Timber.d("AreaUnderDownloadDeleted sent to watch")
                }.onFailure { e ->
                    if (e is SuuntoRepositoryException && e.message?.contains("Failed status: 404") == true) {
                        Timber.d("Failed to send AreaUnderDownloadDeleted to the watch. API not supported or watch not connected.")
                    } else {
                        Timber.w(e, "Failed to send AreaUnderDownloadDeleted to the watch")
                    }
                }
            }

            runSuspendCatching {
                notifyAreaSelectionChangedUseCase()
            }.onSuccess {
                Timber.d("AreaSelectionChanged sent to watch")
            }.onFailure { e ->
                if (e is SuuntoRepositoryException && e.message?.contains("Failed status: 404") == true) {
                    Timber.d("Failed to send AreaSelectionChanged to the watch. Watch is not connected.")
                } else {
                    Timber.w(e, "Failed to send AreaSelectionChanged to the watch")
                }
            }
        }
    }
}
