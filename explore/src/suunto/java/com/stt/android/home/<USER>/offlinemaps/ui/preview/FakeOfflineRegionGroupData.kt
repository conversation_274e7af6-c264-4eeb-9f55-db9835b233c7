package com.stt.android.home.explore.offlinemaps.ui.preview

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.home.explore.offlinemaps.entities.DownloadOrder
import com.stt.android.home.explore.offlinemaps.entities.OfflineAreaSourceTileType
import com.stt.android.home.explore.offlinemaps.entities.OfflineMapDownloadTarget
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionResult
import com.stt.android.home.explore.offlinemaps.entities.OfflineRegionStatus
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import com.stt.android.core.R as CR

internal object FakeOfflineRegionGroupData {
    private val finlandBounds: LatLngBounds = LatLngBounds(
        LatLng(59.2508, 15.4372),
        LatLng(70.1446, 33.3072),
    )

    val wholeFinland: OfflineRegionResult.OfflineRegion = OfflineRegionResult.OfflineRegion(
        id = "1",
        name = "Whole Finland",
        sizes = persistentMapOf(
            OfflineMapDownloadTarget.WATCH to OfflineRegionResult.OfflineRegion.Size(
                storageSizeInBytes = 19375000,
                transferSizeInBytes = 19375000
            )
        ),
        area = 24583.412,
        areaUnitRes = CR.string.square_kilometers,
        bounds = finlandBounds,
        boundaryUrl = "",
        maskUrl = "",
        description = "",
        downloadOrders = persistentListOf(
            DownloadOrder(
                deviceSerialNumber = "022401200117",
                downloadCompletedAt = 1672208623000,
                status = OfflineRegionStatus.FINISHED,
                downloadedSize = 19375000,
                sourceTileType = OfflineAreaSourceTileType.SMTF_V1,
            ),
        ),
        styleIds = persistentListOf("outdoorstyle"),
    )

    val kainuu: OfflineRegionResult.OfflineRegion = OfflineRegionResult.OfflineRegion(
        id = "2",
        name = "Kainuu",
        sizes = persistentMapOf(
            OfflineMapDownloadTarget.WATCH to OfflineRegionResult.OfflineRegion.Size(
                storageSizeInBytes = 1936000,
                transferSizeInBytes = 1936000
            )
        ),
        area = 24583.412,
        areaUnitRes = CR.string.square_kilometers,
        bounds = finlandBounds,
        boundaryUrl = "",
        maskUrl = "",
        description = "",
        downloadOrders = persistentListOf(
            DownloadOrder(
                deviceSerialNumber = "022401200117",
                downloadCompletedAt = null,
                status = OfflineRegionStatus.REQUESTED,
                downloadedSize = 0,
                sourceTileType = OfflineAreaSourceTileType.SMTF_V1,
            ),
        ),
        styleIds = persistentListOf("outdoorstyle"),
    )

    val lappi: OfflineRegionResult.OfflineRegion = OfflineRegionResult.OfflineRegion(
        id = "3",
        name = "Lappi",
        sizes = persistentMapOf(
            OfflineMapDownloadTarget.WATCH to OfflineRegionResult.OfflineRegion.Size(
                storageSizeInBytes = 8368000,
                transferSizeInBytes = 8368000
            )
        ),
        area = 24583.412,
        areaUnitRes = CR.string.square_kilometers,
        bounds = finlandBounds,
        boundaryUrl = "",
        maskUrl = "",
        description = "",
        downloadOrders = persistentListOf(
            DownloadOrder(
                deviceSerialNumber = "022401200117",
                downloadCompletedAt = null,
                status = OfflineRegionStatus.FAILED,
                downloadedSize = 0,
                sourceTileType = OfflineAreaSourceTileType.SMTF_V1,
            ),
        ),
        styleIds = persistentListOf("outdoorstyle"),
    )

    val uusimaa: OfflineRegionResult.OfflineRegion = OfflineRegionResult.OfflineRegion(
        id = "4",
        name = "Uusimaa",
        sizes = persistentMapOf(
            OfflineMapDownloadTarget.WATCH to OfflineRegionResult.OfflineRegion.Size(
                storageSizeInBytes = 7238000,
                transferSizeInBytes = 7238000
            )
        ),
        area = 24583.412,
        areaUnitRes = CR.string.square_kilometers,
        bounds = finlandBounds,
        boundaryUrl = "",
        maskUrl = "",
        description = "",
        downloadOrders = persistentListOf(
            DownloadOrder(
                deviceSerialNumber = "022401200117",
                downloadCompletedAt = 1750853092000,
                status = OfflineRegionStatus.FAILED,
                downloadedSize = 0,
                sourceTileType = OfflineAreaSourceTileType.SMTF_V1,
            ),
        ),
        styleIds = persistentListOf("outdoorstyle"),
    )

    val pirkanmaa: OfflineRegionResult.OfflineRegion = OfflineRegionResult.OfflineRegion(
        id = "5",
        name = "Pirkanmaa",
        sizes = persistentMapOf(
            OfflineMapDownloadTarget.WATCH to OfflineRegionResult.OfflineRegion.Size(
                storageSizeInBytes = 4123000,
                transferSizeInBytes = 4123000
            )
        ),
        area = 24583.412,
        areaUnitRes = CR.string.square_kilometers,
        bounds = finlandBounds,
        boundaryUrl = "",
        maskUrl = "",
        description = "",
        downloadOrders = persistentListOf(
            DownloadOrder(
                deviceSerialNumber = "022401200117",
                downloadCompletedAt = 1,
                status = OfflineRegionStatus.FINISHED,
                downloadedSize = 4123000,
                sourceTileType = OfflineAreaSourceTileType.SMTF_V1,
            )
        ),
        styleIds = persistentListOf(),
    )

    val pohjanmaa: OfflineRegionResult.OfflineRegion = OfflineRegionResult.OfflineRegion(
        id = "6",
        name = "Pohjanmaa",
        sizes = persistentMapOf(
            OfflineMapDownloadTarget.WATCH to OfflineRegionResult.OfflineRegion.Size(
                storageSizeInBytes = 5873000,
                transferSizeInBytes = 5873000
            )
        ),
        area = 24583.412,
        areaUnitRes = CR.string.square_kilometers,
        bounds = finlandBounds,
        boundaryUrl = "",
        maskUrl = "",
        description = "",
        downloadOrders = persistentListOf(
            DownloadOrder(
                deviceSerialNumber = "022401200117",
                downloadCompletedAt = 1,
                status = OfflineRegionStatus.IN_PROGRESS,
                downloadedSize = 3600,
                sourceTileType = OfflineAreaSourceTileType.SMTF_V1,
            ),
        ),
        styleIds = persistentListOf("outdoorstyle"),
    )

    val janka: OfflineRegionResult.OfflineRegion = OfflineRegionResult.OfflineRegion(
        id = "7",
        name = "Janka",
        sizes = persistentMapOf(
            OfflineMapDownloadTarget.WATCH to OfflineRegionResult.OfflineRegion.Size(
                storageSizeInBytes = 5873000,
                transferSizeInBytes = 5873000
            )
        ),
        area = 24583.412,
        areaUnitRes = CR.string.square_kilometers,
        bounds = finlandBounds,
        boundaryUrl = "",
        maskUrl = "",
        description = "",
        downloadOrders = persistentListOf(
            DownloadOrder(
                deviceSerialNumber = "022401200117",
                downloadCompletedAt = 1,
                status = OfflineRegionStatus.DELETE_REQUESTED,
                downloadedSize = 3600,
                sourceTileType = OfflineAreaSourceTileType.SMTF_V1,
            ),
        ),
        styleIds = persistentListOf("outdoorstyle")
    )

    val austria: OfflineRegionResult.OfflineRegionGroup = OfflineRegionResult.OfflineRegionGroup(
        id = "1",
        name = "Austria",
        size = 18732000,
        regions = persistentListOf(),
    )

    val france: OfflineRegionResult.OfflineRegionGroup = OfflineRegionResult.OfflineRegionGroup(
        id = "2",
        name = "France",
        size = 11193000,
        regions = persistentListOf(),
    )

    val finland: OfflineRegionResult.OfflineRegionGroup = OfflineRegionResult.OfflineRegionGroup(
        id = "3",
        name = "Finland",
        size = 19375000,
        regions = persistentListOf(wholeFinland, kainuu, lappi, uusimaa, pirkanmaa, pohjanmaa, janka),
    )

    val germany: OfflineRegionResult.OfflineRegionGroup = OfflineRegionResult.OfflineRegionGroup(
        id = "4",
        name = "Germany",
        size = 10364000,
        regions = persistentListOf(),
    )

    val italy: OfflineRegionResult.OfflineRegionGroup = OfflineRegionResult.OfflineRegionGroup(
        id = "5",
        name = "Italy",
        size = 15873000,
        regions = persistentListOf(),
    )

    val spain: OfflineRegionResult.OfflineRegionGroup = OfflineRegionResult.OfflineRegionGroup(
        id = "6",
        name = "Spain",
        size = 12360000,
        regions = persistentListOf(),
    )

    val sweden: OfflineRegionResult.OfflineRegionGroup = OfflineRegionResult.OfflineRegionGroup(
        id = "7",
        name = "Sweden",
        size = 18223000,
        regions = persistentListOf(),
    )

    val regionGroups: ImmutableList<OfflineRegionResult.OfflineRegionGroup> =
        persistentListOf(austria, france, finland, germany, italy, spain, sweden)
}
