package com.stt.android.home.explore.offlinemaps

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.analytics.AnalyticsPropertyValue.DownloadMapsScreenSource
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.home.explore.offlinemaps.ui.OfflineMapsScreen
import com.stt.android.offlinemaps.OfflineMapsNavigator
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
internal class OfflineMapsFragment : Fragment() {
    private val viewModel: OfflineMapsViewModel by viewModels()

    @Inject
    lateinit var offlineMapsNavigator: OfflineMapsNavigator

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContentWithM3Theme {
            val viewState by viewModel.viewState.collectAsState()
            OfflineMapsScreen(
                viewState = viewState,
                eventHandler = { event ->
                    when (event) {
                        is OfflineMapsViewEvent.OpenRegion -> openRegion(
                            regionId = event.regionId,
                        )
                        is OfflineMapsViewEvent.OpenDownloadMaps -> openDownloadMaps()
                        is OfflineMapsViewEvent.MarkBatteryTipAsShown -> viewModel.markBatteryTipAsShown()
                        is OfflineMapsViewEvent.MarkFailedToDownloadRegionAsShown -> viewModel.markFailedToDownloadRegionAsShown()
                        is OfflineMapsViewEvent.CancelDownloading -> viewModel.cancelDownload(event.region)
                        is OfflineMapsViewEvent.CancelDeleting -> viewModel.cancelDeleting(event.region)
                        is OfflineMapsViewEvent.RetryDownloading -> viewModel.retryDownload(event.region)
                    }
                },
            )
        }
    }

    private fun openRegion(
        regionId: String,
    ) {
        startActivity(
            DownloadedMapDetailsActivity.newStartIntent(
                context = requireContext(),
                regionId = regionId,
            )
        )
    }

    private fun openDownloadMaps() {
        viewModel.setPendingReload()

        offlineMapsNavigator.openOfflineMapSelection(
            context = requireContext(),
            analyticsSource = DownloadMapsScreenSource.WATCH_MAPS_LIBRARY_SCREEN,
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP,
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.pollOfflineRegionGroups()
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                viewModel.checkPendingReload()
            }
        }
    }

    companion object {
        fun newInstance(): OfflineMapsFragment = OfflineMapsFragment()
    }
}
