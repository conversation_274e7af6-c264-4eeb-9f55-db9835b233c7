package com.stt.android.home.explore.routes.popular

import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import com.stt.android.compose.theme.dividerColor
import com.stt.android.domain.routes.RouteWatchSyncState
import com.stt.android.home.explore.routes.addtowatch.AddToWatchView
import com.stt.android.home.explore.routes.details.TopRouteDetailsViewData

@Suppress("UnusedReceiverParameter")
@Composable
internal fun ColumnScope.AddToWatchView(viewData: TopRouteDetailsViewData) {
    AddToWatchView(
        watchEnabled = viewData.isWatchEnabled,
        watchSyncState = RouteWatchSyncState.PENDING,
        watchSyncResponseCode = 0,
        watchRouteListFull = viewData.watchRouteListFull,
        onCheckedChanged = viewData.onWatchEnableChanged,
    )

    HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
}
