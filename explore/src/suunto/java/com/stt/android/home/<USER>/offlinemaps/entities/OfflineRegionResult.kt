package com.stt.android.home.explore.offlinemaps.entities

import androidx.compose.runtime.Immutable
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList

internal sealed interface OfflineRegionResult {
    val id: String

    @Immutable
    data class OfflineRegion(
        override val id: String,
        val name: String,
        val sizes: ImmutableMap<OfflineMapDownloadTarget, Size>,
        val area: Double?,
        val areaUnitRes: Int?,
        val description: String?,
        val bounds: LatLngBounds?,
        val boundaryUrl: String?,
        val maskUrl: String?,
        val downloadOrders: ImmutableList<DownloadOrder>,
        val styleIds: ImmutableList<String>,
        val groupName: String? = null,
        val adjacentRegions: ImmutableList<String> = persistentListOf(),
        val adjacentMaskUrl: String? = null,
        val centerPoint: LatLng? = null,
        val batchDownloadAllowed: Boolean? = null,
    ) : OfflineRegionResult {
        data class Size(
            val storageSizeInBytes: Long?,
            val transferSizeInBytes: Long?,
        )

        val downloadOrderForWatch: DownloadOrder? get() =
            downloadOrders.firstOrNull { it.sourceTileType?.isForWatch() == true }

        val downloadOrderForMobile: DownloadOrder? get() =
            downloadOrders.firstOrNull { it.sourceTileType?.isForMobile() == true }

        val sizeForWatch: Size? get() = sizes[OfflineMapDownloadTarget.WATCH]

        val sizeForMobile: Size? get() = sizes[OfflineMapDownloadTarget.MOBILE]

        val downloadRequested: Boolean get() = downloadOrders.any { it.status == OfflineRegionStatus.REQUESTED }

        val downloading: Boolean get() = downloadOrders.any { downloadOrder -> downloadOrder.downloading }

        val downloadingForWatch: Boolean get() = downloadOrders.any { downloadOrder ->
            downloadOrder.downloading && downloadOrder.sourceTileType?.isForWatch() == true
        }

        val downloadingForMobile: Boolean get() = downloadOrders.any { downloadOrder ->
            (downloadOrder.downloading || downloadOrder.status == OfflineRegionStatus.REQUESTED) &&
                downloadOrder.sourceTileType?.isForMobile() == true
        }

        val downloadingUpdate: Boolean get() = downloadOrders.any { it.status == OfflineRegionStatus.UPDATE_IN_PROGRESS }

        val downloaded: Boolean get() = downloadOrders.any { downloadOrder -> downloadOrder.downloaded }

        val downloadedForMobile: Boolean get() = downloadOrders.any { downloadOrder ->
            downloadOrder.downloaded && downloadOrder.sourceTileType?.isForMobile() == true
        }

        val downloadedForWatch: Boolean get() = downloadOrders.any { downloadOrder ->
            downloadOrder.downloaded && downloadOrder.sourceTileType?.isForWatch() == true
        }

        val downloadFailed: Boolean get() = downloadOrders.any { it.status == OfflineRegionStatus.FAILED }

        val downloadFailedForMobile: Boolean get() = downloadOrders.any { downloadOrder ->
            downloadOrder.status == OfflineRegionStatus.FAILED && downloadOrder.sourceTileType?.isForMobile() == true
        }

        val downloadFailedForWatch: Boolean get() = downloadOrders.any { downloadOrder ->
            downloadOrder.status == OfflineRegionStatus.FAILED && downloadOrder.sourceTileType?.isForWatch() == true
        }

        val deleteRequested: Boolean get() = downloadOrders.any { it.status == OfflineRegionStatus.DELETE_REQUESTED }

        val downloadAvailable: Boolean get() = downloadOrderForWatch?.downloadAvailable != false ||
            downloadOrderForMobile?.downloadAvailable != false

        val updateAvailable: Boolean get() = downloadOrders.any { it.status == OfflineRegionStatus.UPDATE_AVAILABLE }

        val cancellable: Boolean get() = downloadRequested || (downloading && !downloadingUpdate) || deleteRequested

        val downloadProgressForWatch: Float get() {
            val downloaded = downloadOrderForWatch?.downloadedSize ?: return 0.0F
            val transferSize = sizeForWatch?.transferSizeInBytes ?: return 0.0F
            return if (transferSize == 0L) {
                0.0F
            } else {
                (downloaded / transferSize.toFloat()).coerceIn(0.0F, 1.0F)
            }
        }

        fun addDownloadOrders(
            newDownloadOrders: List<DownloadOrder>,
        ): OfflineRegion {
            val updatedDownloadOrders = buildMap {
                newDownloadOrders.forEach { downloadOrder ->
                    put(downloadOrder.deviceSerialNumber, downloadOrder)
                }

                downloadOrders.forEach { downloadOrder ->
                    putIfAbsent(downloadOrder.deviceSerialNumber, downloadOrder)
                }
            }.values.toImmutableList()

            return copy(downloadOrders = updatedDownloadOrders)
        }

        fun addDownloadOrdersIfAbsent(
            downloadOrdersToAdd: List<DownloadOrder>,
        ): OfflineRegion {
            val updatedDownloadOrders = buildMap {
                downloadOrders.forEach { downloadOrder ->
                    put(downloadOrder.deviceSerialNumber, downloadOrder)
                }

                downloadOrdersToAdd.forEach { downloadOrder ->
                    putIfAbsent(downloadOrder.deviceSerialNumber, downloadOrder)
                }
            }.values.toImmutableList()

            return copy(downloadOrders = updatedDownloadOrders)
        }
    }

    @Immutable
    data class OfflineRegionGroup(
        override val id: String,
        val name: String,
        val size: Long,
        val regions: ImmutableList<OfflineRegion>,
        val batchDownloadAllowed: Boolean? = null,
    ) : OfflineRegionResult
}
