package com.stt.android.home.explore.routes.planner.waypoints.waypointutils

import com.stt.android.domain.Point
import com.stt.android.domain.routes.RouteSegment
import com.stt.android.home.explore.routes.planner.waypoints.WaypointUtils
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointType
import org.assertj.core.api.Assertions
import org.junit.Test

class EditWaypointsTest {
    @Test
    fun `edit waypoint name and type`() {
        val actions = WaypointUtils.editWaypoints(
            segments,
            listOf(original),
            expected.name ?: "",
            WaypointType.from(expected.type ?: 26),
            null
        ).actions
        Assertions.assertThat(actions).isNotEmpty
        actions.forEach { action ->
            action.original.routePoints.forEachIndexed { index, point ->
                val actual = action.updated.routePoints[index]
                if (point == original) {
                    Assertions.assertThat(actual).isEqualTo(expected)
                } else {
                    Assertions.assertThat(actual).isEqualTo(point)
                }
            }
        }
    }

    @Test
    fun `try edit a waypoint that does not exist`() {
        val actions = WaypointUtils.editWaypoints(
            segments,
            listOf(invalid),
            expected.name ?: "",
            WaypointType.from(expected.type ?: 26),
            null
        ).actions
        Assertions.assertThat(actions).isEmpty()
    }

    private val original = Point(
        longitude = 23.81611247233824,
        latitude = 61.42932724232318,
        altitude = 119.18499755859375,
        relativeDistance = 0.0,
        name = "Custom",
        type = 26
    )
    private val invalid = Point(
        longitude = 1.0,
        latitude = 61.42932724232318,
        altitude = 119.18499755859375,
        relativeDistance = 0.0,
        name = "Custom",
        type = 26
    )
    private val expected = Point(
        longitude = 23.81611247233824,
        latitude = 61.42932724232318,
        altitude = 119.18499755859375,
        relativeDistance = 0.0,
        name = "New name",
        type = 7
    )
    private val segments: List<RouteSegment> = listOf(
        RouteSegment(
            startPoint = Point(
                longitude = 23.817602157592773,
                latitude = 61.429405212402344,
                altitude = 0.0,
                relativeDistance = 0.0,
                name = null,
                type = null
            ),
            endPoint = Point(
                longitude = 23.81611247233824,
                latitude = 61.42932724232318,
                altitude = 119.18499755859375,
                relativeDistance = 0.0,
                name = null,
                type = null
            ),
            position = 0,
            routePoints = listOf(
                Point(
                    longitude = 23.81760025024414,
                    latitude = 61.429405212402344,
                    altitude = 118.16999816894531,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                original
            ),
            ascent = 0.0
        ),
        RouteSegment(
            startPoint = Point(
                longitude = 23.81611247233824,
                latitude = 61.42932724232318,
                altitude = 119.18499755859375,
                relativeDistance = 0.0,
                name = null,
                type = null
            ),
            endPoint = Point(
                longitude = 23.814075469970703,
                latitude = 61.42981719970703,
                altitude = 0.0,
                relativeDistance = 0.0,
                name = null,
                type = null
            ),
            position = 1,
            routePoints = listOf(
                Point(
                    longitude = 23.81611247233824,
                    latitude = 61.42932724232318,
                    altitude = 119.18499755859375,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.814470291137695,
                    latitude = 61.42924118041992,
                    altitude = 120.19999694824219,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.81442642211914,
                    latitude = 61.429534912109375,
                    altitude = 120.19999694824219,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.814327239990234,
                    latitude = 61.42969512939453,
                    altitude = 120.19999694824219,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.81417465209961,
                    latitude = 61.42984390258789,
                    altitude = 120.19999694824219,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                )
            ),
            ascent = 0.0
        ),
        RouteSegment(
            startPoint = Point(
                longitude = 23.814075469970703,
                latitude = 61.42981719970703,
                altitude = 0.0,
                relativeDistance = 0.0,
                name = null,
                type = null
            ),
            endPoint = Point(
                longitude = 23.81455825335572,
                latitude = 61.43057266265318,
                altitude = 119.2599983215332,
                relativeDistance = 0.0,
                name = null,
                type = null
            ),
            position = 2,
            routePoints = listOf(
                Point(
                    longitude = 23.81417465209961,
                    latitude = 61.42984390258789,
                    altitude = 120.19999694824219,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.81390380859375,
                    latitude = 61.4300651550293,
                    altitude = 120.19999694824219,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.813325881958008,
                    latitude = 61.430355072021484,
                    altitude = 120.19999694824219,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.813583374023438,
                    latitude = 61.43047332763672,
                    altitude = 120.19999694824219,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.813886642456055,
                    latitude = 61.43056106567383,
                    altitude = 120.19999694824219,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.814157485961914,
                    latitude = 61.43058776855469,
                    altitude = 120.19999694824219,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.81450080871582,
                    latitude = 61.43057632446289,
                    altitude = 120.19999694824219,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.81455825335572,
                    latitude = 61.43057266265318,
                    altitude = 119.2599983215332,
                    relativeDistance = 0.0,
                    name = "Waypoint",
                    type = 26
                )
            ),
            ascent = 0.0
        ),
        RouteSegment(
            startPoint = Point(
                longitude = 23.81455825335572,
                latitude = 61.43057266265318,
                altitude = 119.2599983215332,
                relativeDistance = 0.0,
                name = null,
                type = null
            ),
            endPoint = Point(
                longitude = 23.81675148010254,
                latitude = 61.430419921875,
                altitude = 0.0,
                relativeDistance = 0.0,
                name = null,
                type = null
            ),
            position = 3,
            routePoints = listOf(
                Point(
                    longitude = 23.81455825335572,
                    latitude = 61.43057266265318,
                    altitude = 119.2599983215332,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.816415786743164,
                    latitude = 61.43045425415039,
                    altitude = 118.31999969482422,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                ),
                Point(
                    longitude = 23.816747665405273,
                    latitude = 61.43041229248047,
                    altitude = 118.30000305175781,
                    relativeDistance = 0.0,
                    name = null,
                    type = null
                )
            ),
            ascent = -0.01999664306640625
        )
    )
}
