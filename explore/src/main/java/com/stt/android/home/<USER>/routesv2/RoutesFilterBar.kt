package com.stt.android.home.explore.routesv2

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.home.explore.R
import com.stt.android.home.explore.toproutes.filter.ResetButton
import com.stt.android.home.explore.toproutes.filter.TopRouteFilterChip

@Composable
fun RoutesFilterBar(
    currentRoutesPage: RoutesPage,
    isFilterExpand: Boolean,
    showResetIcon: Boolean,
    onQueryChange: (String) -> Unit,
    onFilterReset: () -> Unit,
    onFilterClick: () -> Unit,
    onRoutePageChanged: (RoutesPage) -> Unit,
    modifier: Modifier = Modifier,
    showFilterIcon: Boolean = false,
) {
    Column(
        modifier = modifier
    ) {
        RouteSearchBox(
            searchHiltResId = R.string.enter_route_name_or_activity_type,
            onQueryChange = onQueryChange
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))

        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.medium)
        ) {
            FlowRow(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
            ) {
                RoutesPage.entries.forEach { routePage ->
                    TopRouteFilterChip(
                        selected = currentRoutesPage == routePage,
                        onChecked = {
                            onRoutePageChanged(routePage)
                        },
                        chipText = stringResource(routePage.resId),
                        showBorder = true,
                        modifier = Modifier
                            .heightIn(dimensionResource(com.stt.android.R.dimen.tag_default_chip_min_height))
                    )
                }
            }

            if (showResetIcon) {
                ResetButton(
                    onClick = onFilterReset,
                )

                Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
            }

            if (showFilterIcon) {
                Icon(
                    painter = if (isFilterExpand) {
                        painterResource(R.drawable.ic_filter_collapse)
                    } else {
                        painterResource(R.drawable.ic_filter_expand)
                    },
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.nearBlack,
                    modifier = Modifier
                        .size(MaterialTheme.iconSizes.small)
                        .clip(CircleShape)
                        .clickableThrottleFirst(onClick = onFilterClick)
                )
            }
        }
    }
}

@Composable
internal fun RouteSearchBox(
    @StringRes searchHiltResId: Int,
    onQueryChange: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    var query by rememberSaveable { mutableStateOf("") }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.spacing.medium)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = MaterialTheme.colorScheme.nearWhite,
                    shape = RoundedCornerShape(40.dp)
                )
                .padding(
                    horizontal = MaterialTheme.spacing.smaller,
                    vertical = MaterialTheme.spacing.small
                ),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_map_search),
                tint = MaterialTheme.colorScheme.darkGrey,
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.small)
            )

            Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))

            BasicTextField(
                value = query,
                onValueChange = { newText ->
                    query = newText
                    onQueryChange(newText)
                },
                textStyle = MaterialTheme.typography.bodyMedium.merge(
                    color = MaterialTheme.colorScheme.nearBlack
                ),
                cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                decorationBox = { innerTextField ->
                    Box(
                        contentAlignment = Alignment.CenterStart
                    ) {
                        if (query.isEmpty()) {
                            Text(
                                text = stringResource(searchHiltResId),
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.darkGrey,
                            )
                        }
                        innerTextField()
                    }
                },
                singleLine = true,
                modifier = Modifier.weight(1f),
            )

            if (query.isNotEmpty()) {
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))

                Icon(
                    painter = painterResource(R.drawable.ic_clear),
                    tint = MaterialTheme.colorScheme.darkGrey,
                    contentDescription = null,
                    modifier = Modifier
                        .size(MaterialTheme.iconSizes.small)
                        .clickable {
                            query = ""
                            onQueryChange("")
                        }
                )
            }
        }
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun RoutesFilterBarPreview() {
    M3AppTheme {
        RoutesFilterBar(
            currentRoutesPage = RoutesPage.MINE,
            showFilterIcon = true,
            isFilterExpand = false,
            showResetIcon = false,
            onQueryChange = {},
            onFilterReset = {},
            onFilterClick = {},
            onRoutePageChanged = {}
        )
    }
}
