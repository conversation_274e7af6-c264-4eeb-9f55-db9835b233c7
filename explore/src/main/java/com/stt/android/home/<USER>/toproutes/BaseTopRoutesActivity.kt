package com.stt.android.home.explore.toproutes

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.findNavController
import com.google.android.gms.maps.model.LatLng
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.R
import com.stt.android.utils.firstOfType
import io.reactivex.disposables.CompositeDisposable

abstract class BaseTopRoutesActivity : AppCompatActivity(R.layout.activity_top_routes) {
    private lateinit var navController: NavController

    protected val disposables = CompositeDisposable()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        navController = findNavController(R.id.nav_host_fragment_container)
    }

    override fun onDestroy() {
        disposables.clear()
        super.onDestroy()
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        val topRoutesFragment = getTopRoutesFragment()
        if (topRoutesFragment?.isBottomSheetVisible() == true) {
            topRoutesFragment.hideBottomSheet()
        } else {
            super.onBackPressed()
        }
    }

    private fun getTopRoutesFragment(): TopRoutesFragment? {
        return supportFragmentManager.findFragmentByClass()
    }

    private inline fun <reified T : Fragment> FragmentManager.findFragmentByClass(): T? {
        return primaryNavigationFragment?.childFragmentManager?.fragments?.firstOfType()
    }

    protected fun showAddToWatchView() {
        getTopRoutesFragment()?.showAddToWatchView()
    }

    protected fun hideAddToWatch() {
        getTopRoutesFragment()?.hideAddToWatch()
    }

    companion object {
        const val EXTRA_LAT_LNG = "com.stt.android.home.explore.toproutes.EXTRA_LAT_LNG"
        const val EXTRA_ACTIVITY_TYPE = "com.stt.android.home.explore.toproutes.EXTRA_ACTIVITY_TYPE"
        const val EXTRA_ZOOM = "com.stt.android.home.explore.toproutes.EXTRA_ZOOM"
        const val EXTRA_SHOW_MARKER = "com.stt.android.home.explore.toproutes.EXTRA_SHOW_MARKER"
        const val EXTRA_SELECT_ROUTE_LAT_LNG = "com.stt.android.home.explore.toproutes.EXTRA_SELECT_ROUTE_LAT_LNG"

        @JvmStatic
        fun newStartIntent(
            context: Context,
            latLng: LatLng,
            selectedLatLng: LatLng?,
            activityType: ActivityType?,
            zoom: Float,
            showMarker: Boolean
        ) = Intent(context, TopRoutesActivity::class.java).apply {
            putExtra(EXTRA_LAT_LNG, latLng)
            putExtra(EXTRA_ACTIVITY_TYPE, activityType)
            putExtra(EXTRA_SELECT_ROUTE_LAT_LNG, selectedLatLng)
            putExtra(EXTRA_ZOOM, zoom)
            putExtra(EXTRA_SHOW_MARKER, showMarker)
        }
    }
}
