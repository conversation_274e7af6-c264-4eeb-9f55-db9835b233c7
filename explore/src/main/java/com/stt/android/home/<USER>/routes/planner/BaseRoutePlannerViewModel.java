package com.stt.android.home.explore.routes.planner;

import android.content.SharedPreferences;
import android.location.Location;
import android.net.Uri;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.SavedStateHandle;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.soy.algorithms.ascent.VerticalDelta;
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance;
import com.stt.android.analytics.DatahubAnalyticsTracker;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.analytics.AnalyticsEventProperty;
import com.stt.android.analytics.AnalyticsProperties;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.analytics.EmarsysAnalytics;
import com.stt.android.common.coroutines.CoroutinesDispatcherProvider;
import com.stt.android.common.ui.RxViewModel;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.core.domain.workouts.CoreActivityType;
import com.stt.android.domain.Point;
import com.stt.android.domain.di.IoThread;
import com.stt.android.domain.di.MainThread;
import com.stt.android.domain.explore.pois.GetAllPOIsUseCase;
import com.stt.android.domain.explore.pois.POI;
import com.stt.android.domain.routes.DefaultActivitiesUseCase;
import com.stt.android.domain.routes.DeleteRouteUseCase;
import com.stt.android.domain.routes.DeleteRoutesInProgressUseCase;
import com.stt.android.domain.routes.ImportRouteException;
import com.stt.android.domain.routes.Route;
import com.stt.android.domain.routes.RouteSegment;
import com.stt.android.domain.routes.RouteVerticalDeltaCalc;
import com.stt.android.domain.routes.SaveRouteUseCase;
import com.stt.android.domain.routes.WaypointTools;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.user.RoadSurfaceType;
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.home.explore.routes.RouteAltitudeChartData;
import com.stt.android.home.explore.routes.RouteAnalytics;
import com.stt.android.home.explore.routes.RouteUtils;
import com.stt.android.home.explore.routes.WaypointDetails;
import com.stt.android.home.explore.routes.WaypointDetailsItem;
import static com.stt.android.home.explore.routes.planner.BaseRoutePlannerActivity.ROUTE_BUTTON_IMPORT_URI;
import static com.stt.android.home.explore.routes.planner.RoutingApiModel.MAX_TURN_BY_TURN_WAYPOINT_COUNT;
import static com.stt.android.home.explore.routes.planner.RoutingApiModel.MAX_WAYPOINT_COUNT;
import com.stt.android.home.explore.routes.planner.actionresult.AddWaypointsActionResult;
import com.stt.android.home.explore.routes.planner.actionresult.EditWaypointActionResult;
import com.stt.android.home.explore.routes.planner.actionresult.MovePointResult;
import com.stt.android.home.explore.routes.planner.actionresult.ReverseRouteResult;
import com.stt.android.home.explore.routes.planner.addtoroute.AddToRouteType;
import com.stt.android.home.explore.routes.planner.waypoints.NearestPoints;
import com.stt.android.home.explore.routes.planner.waypoints.NearestPointsResult;
import com.stt.android.home.explore.routes.planner.waypoints.PointsWithDistances;
import com.stt.android.home.explore.routes.planner.waypoints.WaypointUtils;
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointType;
import com.stt.android.infomodel.SummaryItem;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.maps.MapFloatingActionButtonsState;
import com.stt.android.maps.SuuntoCameraOptions;
import com.stt.android.maps.SuuntoCameraPosition;
import com.stt.android.maps.SuuntoMapOptions;
import com.stt.android.maps.location.SuuntoLocationCallback;
import com.stt.android.maps.location.SuuntoLocationListener;
import com.stt.android.maps.location.SuuntoLocationRequest;
import com.stt.android.maps.location.SuuntoLocationSource;
import com.stt.android.models.MapSelectionModel;
import com.stt.android.network.interfaces.ANetworkProvider;
import com.stt.android.routes.PointExtKt;
import com.stt.android.ui.utils.TextFormatter;
import com.stt.android.utils.STTConstants;
import io.reactivex.Completable;
import io.reactivex.Flowable;
import io.reactivex.Maybe;
import io.reactivex.Scheduler;
import io.reactivex.Single;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import java.util.ArrayList;
import com.stt.android.workouts.details.values.WorkoutValue;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import kotlin.Pair;
import kotlin.Triple;
import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import timber.log.Timber;

public abstract class BaseRoutePlannerViewModel extends RxViewModel
    implements SuuntoLocationListener {
    private static final int DEFAULT_ZOOM_LEVEL = 15;
    private static final int DEFAULT_CAMERA_MOVE_DELAY_MS = 300;
    @SuppressWarnings("WeakerAccess") // Used in inner classes
    private final SavedStateHandle savedStateHandle;
    final RoutePlannerModel routePlannerModel;
    private final SuuntoLocationSource suuntoLocationSource;
    private final UserSettingsController userSettingsController;
    private final CurrentUserController currentUserController;
    private final RouteAnalytics routeAnalytics;
    private final SharedPreferences sharedPreferences;

    private final SharedPreferences featureTogglePreferences;
    private final WaypointTools waypointTools;
    private boolean speedChanged = false;
    private @NonNull RoutingMode routingMode = RoutingMode.FOOT;
    private final EmarsysAnalytics emarsysAnalytics;
    private final DatahubAnalyticsTracker datahubAnalyticsTracker;
    private final InfoModelFormatter infoModelFormatter;
    private final CompositeDisposable getPOIsDisposable = new CompositeDisposable();
    private final CompositeDisposable initRouteDisposable = new CompositeDisposable();
    private final SaveRouteUseCase saveRouteUseCase;
    private final DeleteRouteUseCase deleteRouteUseCase;
    private final DeleteRoutesInProgressUseCase deleteRoutesInProgressUseCase;
    private final DefaultActivitiesUseCase defaultActivitiesUseCase;
    private final GetAllPOIsUseCase getAllPOIsUseCase;
    private final MapSelectionModel mapSelectionModel;
    private final IsSubscribedToPremiumUseCase isSubscribedToPremiumUseCase;
    private List<POI> currentPOIs = new ArrayList<>();

    /**
     * Set of used modes used when creating a route. Used for analytics to determine what modes
     * users are using when creating a route
     */
    private final Set<String> routingModesUsed = new HashSet<>();

    /**
     * Undo count used only by analytics
     */
    private int undoCountForAnalytics;
    /**
     * Manually added planning points for analytics
     */
    private int planningPointCountForAnalytics;
    /**
     * Manually dragged planning points for analytics
     */
    private int planningPointMoveCountForAnalytics;

    private String pendingSavedRouteName = null;
    private boolean pendingRouteSaveError = false;
    /**
     * Holds data when adding new waypoints
     */
    NearestPoints pendingNearestPoints = null;
    /**
     * Holds data when editing existing waypoints
     */
    PointsWithDistances pendingWaypoints = null;

    private SuuntoCameraOptions restoredCameraPosition = null;
    private boolean cameraHasMoved;

    private boolean trackUserLocation;

    private boolean isWatchRouteListFull = false;

    private boolean isInitialized = false;

    private final MutableLiveData<MapFloatingActionButtonsState> _mapFloatingActionButtonState;
    private final MutableLiveData<String> _routeName;

    public BaseRoutePlannerViewModel(
        SavedStateHandle savedStateHandle,
        RoutePlannerModel routePlannerModel,
        SuuntoLocationSource locationSource,
        UserSettingsController userSettingsController,
        CurrentUserController currentUserController,
        EmarsysAnalytics emarsysAnalytics,
        DatahubAnalyticsTracker datahubAnalyticsTracker,
        @IoThread Scheduler ioThread,
        @MainThread Scheduler mainThread,
        RouteAnalytics routeAnalytics,
        SharedPreferences sharedPreferences,
        CoroutinesDispatcherProvider dispatcherProvider,
        InfoModelFormatter infoModelFormatter,
        GetAllPOIsUseCase getAllPOIsUseCase,
        SaveRouteUseCase saveRouteUseCase,
        DeleteRouteUseCase deleteRouteUseCase,
        DeleteRoutesInProgressUseCase deleteRoutesInProgressUseCase,
        DefaultActivitiesUseCase defaultActivitiesUseCase,
        MapSelectionModel mapSelectionModel,
        IsSubscribedToPremiumUseCase isSubscribedToPremiumUseCase,
        SharedPreferences featureTogglePreferences,
        WaypointTools waypointTools
    ) {
        super(ioThread, mainThread, dispatcherProvider);

        this.savedStateHandle = savedStateHandle;
        this.routePlannerModel = routePlannerModel;
        this.suuntoLocationSource = locationSource;
        this.userSettingsController = userSettingsController;
        this.currentUserController = currentUserController;
        this.emarsysAnalytics = emarsysAnalytics;
        this.datahubAnalyticsTracker = datahubAnalyticsTracker;
        this.routeAnalytics = routeAnalytics;
        this.infoModelFormatter = infoModelFormatter;
        this.sharedPreferences = sharedPreferences;
        this.saveRouteUseCase = saveRouteUseCase;
        this.deleteRouteUseCase = deleteRouteUseCase;
        this.deleteRoutesInProgressUseCase = deleteRoutesInProgressUseCase;
        this.defaultActivitiesUseCase = defaultActivitiesUseCase;
        this.getAllPOIsUseCase = getAllPOIsUseCase;
        this.mapSelectionModel = mapSelectionModel;
        this.isSubscribedToPremiumUseCase = isSubscribedToPremiumUseCase;
        this.featureTogglePreferences = featureTogglePreferences;
        this.waypointTools = waypointTools;

        _mapFloatingActionButtonState = new MutableLiveData<>(new MapFloatingActionButtonsState(
            false,
            false,
            this.mapSelectionModel.getShow3dOption(),
            this.mapSelectionModel.getMap3dEnabled(),
            true,
            true,
            true,
            false
        ));

        _routeName = new MutableLiveData<>(routePlannerModel.getRouteName());
    }

    @NonNull
    public LiveData<Boolean> hasPremium() {
        return isSubscribedToPremiumUseCase.hasPremium();
    }

    public LiveData<MapFloatingActionButtonsState> getMapFloatingActionButtonState() {
        return _mapFloatingActionButtonState;
    }

    public LiveData<String> getRouteName() {
        return _routeName;
    }

    public void handle3dOptionToggled(boolean enable3D) {
        MapFloatingActionButtonsState currentState = _mapFloatingActionButtonState.getValue();
        if (currentState != null) {
            _mapFloatingActionButtonState.setValue(new MapFloatingActionButtonsState(
                currentState.getShowInfo(),
                currentState.getShowSearch(),
                currentState.getShow3D(),
                enable3D,
                true,
                currentState.getShowMapLayers(),
                currentState.getShowLocation(),
                currentState.getLocationEnabled()
            ));
        }
    }

    public void initializeViewModel(
        @Nullable Uri importUri // URI from intent data
    ) {
        ImportFileInfo importFileInfo = getImportFileInfo(savedStateHandle, importUri);
        initRoutePlannerModel(savedStateHandle, importFileInfo);

        // Remember last used routing mode
        int routingModeIndex = sharedPreferences.getInt(
            STTConstants.DefaultPreferences.LAST_USED_ROUTING_MODE, -1);
        RoutingMode mode = RoutingModeKt.routingModeFromIndex(routingModeIndex);
        if (mode != null) {
            routingMode = mode;
        } else {
            routingMode = RoutingMode.FOOT;
        }

        getPOIsDisposable.add(
            getAllPOIsUseCase.getPOIsAsFlowable()
                .subscribeOn(getIoThread())
                .observeOn(getMainThread())
                .subscribe(
                    (pois) -> {
                        currentPOIs = pois;
                        if (view != null) {
                            view.showPOIs(pois);
                        }
                    },
                    (error) -> Timber.w(error, "Failed to get POIs")
                )
        );

        getDisposables().add(
            checkIsWatchRouteListFull()
                .subscribeOn(getIoThread())
                .observeOn(getMainThread())
                .subscribe(
                    (isFull) -> {
                        isWatchRouteListFull = isFull;
                        if (view != null) {
                            restoreAddToWatchSwitch();
                        }
                    },
                    (error) -> Timber.w(error, "Failed to check if watch route list is full")
                )
        );
    }

    private void initRoutePlannerModel(SavedStateHandle savedStateHandle, ImportFileInfo importFileInfo) {
        routePlannerModel.init(
            savedStateHandle.get(STTConstants.ExtraKeys.ROUTE_ID),
            savedStateHandle.get(STTConstants.ExtraKeys.CAMERA_POSITION),
            savedStateHandle.get(STTConstants.ExtraKeys.ROUTE_START_POINT),
            savedStateHandle.get(STTConstants.ExtraKeys.ROUTE_END_POINT),
            savedStateHandle.get(STTConstants.ExtraKeys.ROUTING_MODE),
            importFileInfo,
            savedStateHandle.get(STTConstants.ExtraKeys.WORKOUT_HEADER),
            savedStateHandle.get(STTConstants.ExtraKeys.ROUTE_ACTION_COPY),
            savedStateHandle.get(STTConstants.ExtraKeys.IS_WATCH_ROUTE_LIST_FULL),
            savedStateHandle.get(STTConstants.ExtraKeys.TOP_ROUTE_ID),
            savedStateHandle.get(STTConstants.ExtraKeys.ROUTE_ACTIVITY_TYPE)
        );
    }

    @Nullable
    private ImportFileInfo getImportFileInfo(
        SavedStateHandle savedStateHandle,
        @Nullable Uri importUri
    ) {
        Uri uri = savedStateHandle.get(ROUTE_BUTTON_IMPORT_URI); // URI from intent extras
        if (uri == null) {
            uri = importUri; // URI from intent data
        }
        ImportFileInfo importFileInfo = null;
        if (uri != null) {
            Boolean buttonImport = savedStateHandle.get(BaseRoutePlannerActivity.ROUTE_BUTTON_IMPORT);
            if (uri.getLastPathSegment() != null) {
                importFileInfo = new ImportFileInfo(
                    uri.getLastPathSegment(),
                    uri,
                    buttonImport != null ? buttonImport : false);
            }
        }
        return importFileInfo;
    }

    @Override
    protected void onCleared() {
        super.onCleared();

        getPOIsDisposable.clear();
        initRouteDisposable.clear();
    }

    public List<POI> getCurrentPOIs() {
        return currentPOIs;
    }

    // TODO: Legacy MVP - remove when refactored into ViewModel

    protected RoutePlannerView view;

    public final void takeView(RoutePlannerView view) {
        if (this.view != view) {
            initRouteDisposable.clear();
            this.view = view;
        }
        onViewTaken();
    }

    public final void dropView() {
        this.view = null;
        onViewDropped();
    }

    protected final RoutePlannerView getView() {
        return view;
    }

    //

    private void addStartPoint(double latitude, double longitude) {
        routePlannerModel.addStartPoint(latitude, longitude);
        RoutePlannerView view = getView();
        if (view != null) {
            view.showStartPoint(latitude, longitude);
            // We don't show ascent info until there is a segment
            view.hideAscentDescentInfo();
        }
    }

    /**
     * Adds new segments at the end of the current route
     */
    private void appendRoutePoint(double latitude, double longitude) {
        getDisposables().clear();
        getDisposables().add(
            routePlannerModel.appendRoutePoint(latitude, longitude, routingMode)
                .subscribeOn(getMainThread())
                .observeOn(getMainThread())
                .subscribe((segments) -> {
                    showNewSegments(segments, true);
                    updateRouteDistanceAndVerticalDeltaTextsOnView();
                    updateRouteDurationAndSpeed(routePlannerModel.getDuration());
                    //Add used mode to analytics
                    routingModesUsed.add(getRoutingModeNameForAnalytics());
                    informFetchingRouteCompleted();
                    if (routePlannerModel.getTurnByTurnWaypointCount()
                        >= MAX_TURN_BY_TURN_WAYPOINT_COUNT) {
                        informTurnByTurnWaypointMaxCountReached();
                    }
                    if (routePlannerModel.getWaypointCount(true) >= MAX_WAYPOINT_COUNT) {
                        informWaypointMaxCountReached();
                    }

                    simplifyRouteIfNeeded();

                    try {
                        saveChangesInProgress();
                    } catch (Exception e) {
                        // Don't inform UI about error as route model was already updated
                        Timber.w(e, "Failed to save route after appending point");
                    }
                }, (throwable) -> {
                    Timber.w(throwable, "Can't append point to route");
                    informAddPointFailed();
                }));
    }

    public void findNearestPointsOnRoute(
        LatLng from,
        AddToRouteType type
    ) {
        ArrayList<RouteSegment> segments = routePlannerModel.getSegments();
        getDisposables().add(
            Single.fromCallable(() -> {
                    List<Point> routePoints = segments.stream()
                        .flatMap(segment -> segment.getRoutePoints().stream())
                        .collect(Collectors.toList());

                    boolean backTraceEnabled = isBackTraceEnabled(routePoints);

                    return WaypointUtils.findNearestPointOnRoute(
                        from.latitude,
                        from.longitude,
                        segments,
                        type,
                        backTraceEnabled
                    );
                })
                .subscribeOn(getIoThread())
                .observeOn(getMainThread())
                .subscribe((result) -> {
                    if (result instanceof NearestPointsResult.Success) {
                        NearestPointsResult.Success success = (NearestPointsResult.Success) result;
                        pendingNearestPoints = success.getResult();
                        informFindNearestPointsCompleted();
                    } else {
                        NearestPointsResult.Failure failure = (NearestPointsResult.Failure) result;
                        Timber.w(failure.getThrowable());
                    }
                }, (throwable) -> {
                    Timber.w(throwable, "Finding nearest points failed");
                }));
    }

    private boolean isBackTraceEnabled(List<Point> routePoints) {
        return !routePoints.isEmpty() && routePoints.get(0).isSamePosition(
            routePoints.get(routePoints.size() - 1));
    }

    public void findWaypointsOnRoute(LatLng from) {
        getDisposables().add(
            Maybe.fromCallable(() -> WaypointUtils.findWaypointsOnRoute(
                from,
                routePlannerModel.getSegments())
            )
                .subscribeOn(getIoThread())
                .observeOn(getMainThread())
                .subscribe((waypoints) -> {
                    pendingWaypoints = waypoints;
                    informFindWaypointsCompleted();
                }, (throwable) -> {
                    Timber.w(throwable, "Finding waypoints failed");
                }));
    }

    void addWaypoints(
        String waypointName,
        WaypointType waypointType,
        String waypointDescription
    ) {
        if (pendingNearestPoints == null) return;

        getDisposables().clear();
        getDisposables().add(
            routePlannerModel.addWaypoints(pendingNearestPoints.getResults(), waypointName,
                waypointType, waypointDescription)
                .subscribeOn(getMainThread())
                .observeOn(getMainThread())
                .subscribe((results) -> {
                    informWaypointsAdded(results);
                    pendingNearestPoints = null;
                    updateRouteDistanceAndVerticalDeltaTextsOnView();
                    saveChangesInProgress();
                }, Timber::w)
        );
    }

    void addPlanningPoints() {
        ++planningPointCountForAnalytics;
        addWaypoints(null, null, null);
    }

    void editWaypoints(
        @NonNull String waypointName,
        @NonNull WaypointType waypointType,
        @Nullable String waypointDescription
    ) {
        if (pendingWaypoints == null) return;

        getDisposables().clear();
        getDisposables().add(
            routePlannerModel.editWaypoints(pendingWaypoints.getPoints(), waypointName,
                    waypointType, waypointDescription)
                .subscribeOn(getMainThread())
                .observeOn(getMainThread())
                .subscribe((results) -> {
                    informWaypointEdited(results);
                    updateWaypointDetailList();
                    pendingWaypoints = null;
                    saveChangesInProgress();
                }, Timber::w)
        );
    }

    void deleteWaypoints() {
        if (pendingWaypoints == null) return;

        getDisposables().clear();
        getDisposables().add(
            routePlannerModel.deleteWaypoints(pendingWaypoints.getPoints())
                .subscribeOn(getMainThread())
                .observeOn(getMainThread())
                .subscribe((results) -> {
                    informWaypointEdited(results);
                    pendingWaypoints = null;
                    updateAltitudeChartData();
                    updateWaypointDetailList();
                    saveChangesInProgress();
                }, Timber::w)
        );
    }

    private void updateRouteFully(List<RouteSegment> updatedSegments) {
        // Route segments have dependencies like the action stack (RoutePlannerModel),
        // polylines, markers, positions (BaseRoutePlannerView). To keep them in sync with
        // segments we need to recreate the route.
        // Remove segments and clear action stack, polylines and markers
        ArrayList<RouteSegment> segments = routePlannerModel.getSegments();
        informSegmentsRemoved(segments, true);
        routePlannerModel.removeAllSegments();
        // Recreate the start point as removeAllSegments clears the action stack
        LatLng startPoint = routePlannerModel.getStartPoint();
        if (startPoint == null) return;
        routePlannerModel.removeStartPoint();
        undoStartPoint();
        addStartPoint(startPoint.latitude, startPoint.longitude);
        // Recreate the route
        routePlannerModel.addSegments(updatedSegments);
        showNewSegments(updatedSegments, true);
        updateRouteDistanceAndVerticalDeltaTextsOnView();
        updateRouteDurationAndSpeed(routePlannerModel.getDuration());
        informRouteUpdatedFully();
    }

    private void informRouteUpdatedFully() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.onRouteUpdatedFully();
        }
    }

    Flowable<Pair<Double, Double>> calculateDistanceByPoints(List<Point> routePoints) {
        return Flowable.just(routePoints)
            .map((points) -> RouteUtils.calculateDistanceByPoints(routePoints))
            .map((distance) -> new Pair<>(distance, distance / routePlannerModel.getAvgSpeed()));
    }

    Flowable<VerticalDelta> calculateVerticalDeltaByPoints(List<Point> routePoints) {
        return Flowable.just(routePoints)
            .map((points) -> Objects.requireNonNullElse(RouteVerticalDeltaCalc.calculateCumulativeVerticalDelta(routePoints), new VerticalDelta(0.0,0.0)));
    }

    void convertRoutePointsByIndex(
        RouteAltitudeChartData chartData,
        ClimbGuidance climbGuidance,
        int index
    ) {
        getDisposables().add(
            Flowable.fromCallable(() -> {
                    int indexSelect = CollectionsKt.indexOfLast(chartData.getEntries(),
                        entry -> entry.getX() <= index);
                    int endIndex = chartData.getIndexList().get(indexSelect);
                    List<Point> routePoints = routePlannerModel.getSegments()
                        .stream()
                        .flatMap(segment -> segment.getRoutePoints().stream())
                        .collect(Collectors.toList());
                    List<Point> points = routePoints.subList(0, endIndex + 1);
                    int segmentIndex;
                    int pointIndex = 0;
                    int pointCount = 0;
                    for (segmentIndex = 0; segmentIndex < climbGuidance.getSegments().size(); segmentIndex++) {
                        int count = climbGuidance.getSegments().get(segmentIndex).getPoints().size();
                        if (pointCount + count > endIndex) {
                            pointIndex = endIndex - pointCount;
                            break;
                        }
                        // segment's last point is the same as next segment's first point
                        pointCount += (count - 1);
                    }
                    return new Triple<>(points, segmentIndex, pointIndex);
                })
                .subscribeOn(getIoThread())
                .subscribe(selectPointsAndCurrentPoint -> {
                    updateAllDataDisplayed(selectPointsAndCurrentPoint.getFirst());
                    updateRouteByIndex(climbGuidance,
                        selectPointsAndCurrentPoint.getSecond(),
                        selectPointsAndCurrentPoint.getThird());
                })
        );
    }

    void convertRoutePointsByIndex(RouteAltitudeChartData chartData, int index) {
        List<RouteSegment> routeSegments = routePlannerModel.getSegments();
        getDisposables().add(
            Flowable.fromCallable(() -> {
                    int indexSelect = CollectionsKt.indexOfLast(chartData.getEntries(),
                        entry -> entry.getX() <= index);
                    int endIndex = chartData.getIndexList().get(indexSelect);
                    List<Point> routePoints = routeSegments
                        .stream()
                        .flatMap(segment -> segment.getRoutePoints().stream())
                        .collect(Collectors.toList());
                    List<Point> points = routePoints.subList(0, endIndex + 1);
                    int segmentIndex;
                    int pointIndex = 0;
                    int pointCount = 0;
                    for (segmentIndex = 0; segmentIndex < routeSegments.size(); segmentIndex++) {
                        int count = routeSegments.get(segmentIndex).getRoutePoints().size();
                        if (pointCount + count > endIndex) {
                            pointIndex = endIndex - pointCount;
                            break;
                        }
                        pointCount += count;
                    }
                    return new Triple<>(points, segmentIndex, pointIndex);
                })
                .subscribeOn(getIoThread())
                .subscribe(selectPointsAndCurrentPoint -> {
                    updateAllDataDisplayed(selectPointsAndCurrentPoint.getFirst());
                    updateRouteByIndex(routeSegments,
                        selectPointsAndCurrentPoint.getSecond(),
                        selectPointsAndCurrentPoint.getThird());
                })
        );
    }

    void updateRouteByIndex(ClimbGuidance climbGuidance, int segmentIndex, int pointIndex) {
        getDisposables().add(Flowable.just(climbGuidance)
            .subscribeOn(getMainThread())
            .subscribe(obj -> {
                view.highlightRouteByIndex(climbGuidance, segmentIndex, pointIndex);
            })
        );
    }

    void updateRouteByIndex(List<RouteSegment> routeSegments, int segmentIndex, int pointIndex) {
        getDisposables().add(Flowable.just(routeSegments)
            .subscribeOn(getMainThread())
            .subscribe(obj -> {
                view.highlightRouteByIndex(routeSegments, segmentIndex, pointIndex);
            })
        );
    }

    void resetRouteToDefault() {
        getDisposables().add(Flowable.fromCallable(routePlannerModel::getSegments)
            .subscribeOn(getMainThread())
            .subscribe(obj -> {
                view.resetRouteToDefault(obj);
            })
        );
    }

    void updateAllDataDisplayed(List<Point> routePoints){
        getDisposables().add(
            Flowable.zip(calculateDistanceByPoints(routePoints), calculateVerticalDeltaByPoints(routePoints), Pair::new)
                .subscribeOn(getIoThread())
                .observeOn(getMainThread())
                .subscribe(distanceAndDurationAndVerticalDelta -> {
                    Pair<Double, Double> distanceAndDuration =
                        distanceAndDurationAndVerticalDelta.getFirst();
                    Double distance = distanceAndDuration.getFirst();
                    Double duration = distanceAndDuration.getSecond();
                    updateRouteDistanceTextOnView(distance);
                    updateRouteDurationAndSpeed(duration);

                    updateRouteAscentAndDescentTextOnView(distanceAndDurationAndVerticalDelta.getSecond());
                })
        );
    }

    @SuppressWarnings("WeakerAccess")
        // Used in inner classes
    void updateRouteDistanceAndVerticalDeltaTextsOnView() {
        updateRouteDistanceTextOnView(routePlannerModel.getDistance());
        updateRouteAscentAndDescentTextOnView(routePlannerModel.getVerticalDelta());
        updateAltitudeChartData();
        updateWaypointDetailList();
        resetRouteInputName();
    }

    private void resetRouteInputName() {
        if (view == null) {
            return;
        }
        view.resetEditViewsVisibleForRouteCreation();
    }

    private void updateWaypointDetailList() {
        if (view == null) {
            return;
        }
        List<RouteSegment> routeSegments = new ArrayList<>(routePlannerModel.getSegments());
        Single<List<WaypointDetailsItem>> calculateWaypointDetails =
            Single.fromCallable(() -> RouteUtils.splitAtWaypointsInclusive(routeSegments, waypointTools))
                .subscribeOn(getIoThread())
                .map(list -> list.stream()
                    .map(this::transformWaypointDetails)
                    .collect(Collectors.toList())
                );

        Disposable disposable = calculateWaypointDetails
            .observeOn(getMainThread())
            .subscribe(view::updateWaypointDetailsList, Timber::w);

        getDisposables().add(disposable);
    }

    private WaypointDetailsItem transformWaypointDetails(WaypointDetails details) {
        WorkoutValue formattedAscent = formatIfNonNull(details.getAscentSummary(), SummaryItem.ASCENTALTITUDE);
        WorkoutValue formattedDescent = formatIfNonNull(details.getDescentSummary(), SummaryItem.ASCENTALTITUDE);
        WorkoutValue formattedDistance = formatIfNonNull(details.getDistanceSummary(), SummaryItem.DISTANCE);

        WaypointType waypointType = details.getWaypointType() != null
            ? WaypointType.from(details.getWaypointType())
            : null;

        return new WaypointDetailsItem(
            infoModelFormatter.formatValue(SummaryItem.DISTANCE, details.getCurrentDistance()),
            details.getLatLng(),
            waypointType,
            details.getWaypointName(),
            formattedDistance,
            formattedAscent,
            formattedDescent
        );
    }

    private WorkoutValue formatIfNonNull(Double value, SummaryItem item) {
        return value != null ? infoModelFormatter.formatValue(item, value) : null;
    }

    private void updateAltitudeChartData() {
        if (view == null) {
            return;
        }

        MeasurementUnit measurementUnit = userSettingsController.getSettings().getMeasurementUnit();
        List<RouteSegment> routeSegments = new ArrayList<>(routePlannerModel.getSegments());
        Single<RouteAltitudeChartData> calculateAltitudeChartData = Single.fromCallable(
            () -> RouteUtils.calculateAltitudeChartData(routeSegments, measurementUnit))
            .subscribeOn(getIoThread());
        Single<ClimbGuidance> calculateClimbGuidance = Single.defer(
            () -> {
                List<Point> points = routeSegments.stream()
                    .flatMap((Function<RouteSegment, Stream<Point>>) routeSegment
                        -> routeSegment.getRoutePoints().stream())
                    .collect(Collectors.toList());
                return calculateClimbGuidance(points);
            }).subscribeOn(getIoThread());
        Disposable disposable = Single.zip(calculateAltitudeChartData, calculateClimbGuidance, Pair::new)
            .observeOn(getMainThread())
            .subscribe(result -> {
                if (view != null) {
                    view.updateAltitudeChart(result.getFirst(), result.getSecond());
                }
            }, Timber::w);
        getDisposables().add(disposable);
    }

    abstract protected Single<ClimbGuidance> calculateClimbGuidance(@NonNull List<Point> points);

    private void updateRouteDistanceTextOnView(double distance) {
        MeasurementUnit measurementUnit = userSettingsController.getSettings().getMeasurementUnit();
        RoutePlannerView view = getView();
        if (view != null) {
            String formattedDistance =
                TextFormatter.formatDistance(measurementUnit.toDistanceUnit(distance));
            view.setRouteDistance(formattedDistance, measurementUnit.getDistanceUnit());
        }
    }

    private void updateRouteAscentAndDescentTextOnView(@Nullable VerticalDelta verticalDelta) {
        MeasurementUnit measurementUnit = userSettingsController.getSettings().getMeasurementUnit();
        RoutePlannerView view = getView();
        if (view != null) {
            if (verticalDelta == null && routePlannerModel.getDistance() <= 0.0) {
                view.hideAscentDescentInfo();
                return;
            }

            String formattedAscentAltitude =
                TextFormatter.formatAltitude(measurementUnit.toAltitudeUnit(verticalDelta != null ? verticalDelta.getAscent() : 0.0));
            String formattedDescentAltitude =
                TextFormatter.formatAltitude(measurementUnit.toAltitudeUnit(verticalDelta != null ? verticalDelta.getDescent() : 0.0));
            view.setRouteAscent(formattedAscentAltitude, measurementUnit.getAltitudeUnit());
            view.setRouteDescent(formattedDescentAltitude, measurementUnit.getAltitudeUnit());
        }
    }

    @SuppressWarnings("WeakerAccess")
        // Used in inner classes
    void updateRouteDurationAndSpeed(double duration) {
        RoutePlannerView view = getView();
        if (view != null) {
            MeasurementUnit measurementUnit =
                userSettingsController.getSettings().getMeasurementUnit();

            view.setRouteDurationAndSpeed(
                infoModelFormatter.formatEstimatedRouteDuration((long) duration),
                getFormattedAvgSpeed(),
                measurementUnit.getSpeedUnit()
            );
        }
    }

    @SuppressWarnings("WeakerAccess")
        // Used in inner classes
    void showNewSegments(@NonNull List<RouteSegment> newSegments, boolean updatePreviousSegment) {
        RoutePlannerView view = getView();
        if (view != null) {
            view.showSegments(newSegments, updatePreviousSegment);
        }
    }

    @SuppressWarnings("WeakerAccess")
        // Used in inner classes
    void informFetchingRouteCompleted() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.onFetchingRouteCompleted();
        }
    }

    void informFetchingRouteStarted() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.onFetchingRouteStarted();
        }
    }

    void informFindNearestPointsCompleted() {
        RoutePlannerView view = getView();
        if(view != null) {
            view.onFindNearestPointsCompleted();
        }
    }

    void informFindWaypointsCompleted() {
        RoutePlannerView view = getView();
        if(view != null) {
            view.onFindWaypointsCompleted();
        }
    }

    private void updateRouteName(@NonNull String routeName) {
        setRouteName(routeName);
        _routeName.setValue(routeName);
    }

    protected void onViewTaken() {
        RoutePlannerView view = getView();
        if (view == null) {
            return;
        }
        view.onFetchingRouteStarted();

        initRouteDisposable.add(
            routePlannerModel.initializeRoute()
                .subscribeOn(getMainThread())
                .observeOn(getMainThread())
                .subscribe(
                    this::onModelInitialized,
                    this::handleRouteInitError
                )
        );

        if (pendingSavedRouteName != null) {
            informRouteSaved(pendingSavedRouteName);
        }

        if (pendingRouteSaveError) {
            informSaveRouteError();
        }
    }

    private void handleRouteInitError(Throwable e) {
        if (e instanceof ImportRouteException) {
            Timber.w(e, "Failed to import route");
            trackRouteImportErrorAnalytics();
            if (view != null) {
                view.errorImportingRoute();
            }
        } else if (e instanceof RoutePlannerModel.EmptyRouteException) {
            Timber.w(e, "Route is empty");
        } else if (routePlannerModel.haveInitialRoutePoints()) {
            handleInitialRouteSegmentError(e);
        } else {
            Timber.w(e, "Failed to initialize route");
        }
    }

    private void handleInitialRouteSegmentError(Throwable e) {
        Timber.w(e, "Failed to create route between given initial points");
        if (view != null) {
            restoreState(); // Make sure that route name and other views are updated
            boolean internetConnectionNeeded = !ANetworkProvider.isOnline();
            view.failedToAddInitialSegment(internetConnectionNeeded);
            view.onFetchingRouteCompleted();
            if (hasStartPoint() && routePlannerModel.getStartPoint() != null) {
                // Start point is there even if creating the segment failed.
                // Center map on start point.
                view.moveMapTo(routePlannerModel.getStartPoint().latitude,
                    routePlannerModel.getStartPoint().longitude,
                    DEFAULT_ZOOM_LEVEL);
            }
        }
    }

    @SuppressWarnings("WeakerAccess")
    void onModelInitialized() {
        Timber.d("onModelInitialized() called");
        if (!isInitialized) {
            if (routePlannerModel.originalRoute != null) {
                informEditRoutePlanningStarted();
                boolean turnWaypointsEnabled = routePlannerModel.originalRoute.getTurnWaypointsEnabled();
                informTurnWaypointsEnabledUpdate(turnWaypointsEnabled, false);
            }
        }
        updateRouteDistanceAndVerticalDeltaTextsOnView();
        updateRouteDurationAndSpeed(routePlannerModel.getDuration());
        updateActivities(routePlannerModel.getActivityTypes(), false);
        informRoutingModeChanged(routingMode);
        restoreState();
        RoutePlannerView view = getView();
        if (view == null) {
            return;
        }
        view.onFetchingRouteCompleted();
        view.showWaypointsIgnoredIfNeeded(routePlannerModel.countImportedWaypointsIgnored);
        requestLocationUpdates();
        moveMapToInitialPosition();
    }

    protected void onViewDropped() {
        removeLocationUpdates();
    }

    SuuntoLocationSource getLocationSource() {
        return suuntoLocationSource;
    }

    void requestLocationUpdates() {
        RoutePlannerView view = getView();
        if (view != null) {
            if (view.hasAnyLocationPermission()) {
                suuntoLocationSource.requestLocationUpdates(SuuntoLocationRequest.DEFAULT_LOCATION_REQUEST, this);
            }
        }
    }

    private void removeLocationUpdates() {
        try {
            suuntoLocationSource.removeLocationUpdates(this);
        } catch (SecurityException ignore) {
        }
    }

    @Override
    public void onLocationChanged(@NonNull Location location, @NonNull SuuntoLocationSource source) {
        // No action
    }

    @Override
    public void onLocationAvailability(boolean locationAvailable,
        @NonNull SuuntoLocationSource source) {
        // No action
    }

    private void restoreState() {
        resetViewState();
        restoreStartPoint();
        restoreSegments();
        updateRouteDistanceAndVerticalDeltaTextsOnView();
        updateRouteDurationAndSpeed(routePlannerModel.getDuration());
        restoreRouteName();
        if (!isInitialized) {
            restoreAddToWatchSwitch();
            isInitialized = true;
        }
        restorePendingWaypoint();
    }

    private void restoreAddToWatchSwitch() {
        view.toggleAddToWatch(routePlannerModel.originalRoute, isWatchRouteListFull);
    }

    public void moveMapToInitialPosition() {
        // Move to previously saved position
        if (cameraHasMoved && restoredCameraPosition != null) {
            view.moveMapTo(restoredCameraPosition, DEFAULT_CAMERA_MOVE_DELAY_MS);
            restoredCameraPosition = null;
            cameraHasMoved = false;
            return;
        }

        // Move to route bounds
        ArrayList<RouteSegment> segments = routePlannerModel.getSegments();
        if (!segments.isEmpty()) {
            LatLngBounds.Builder boundsBuilder = LatLngBounds.builder();
            boolean hasPoints = false;
            for (RouteSegment segment : segments) {
                for (Point point : segment.getRoutePoints()) {
                    LatLng latLng = new LatLng(point.getLatitude(), point.getLongitude());
                    boundsBuilder.include(latLng);
                    hasPoints = true;
                }
            }

            if (hasPoints) {
                LatLngBounds bounds = boundsBuilder.build();
                view.moveMapTo(bounds, DEFAULT_CAMERA_MOVE_DELAY_MS);
                return;
            }
        }

        // Move to initial position
        SuuntoCameraOptions initialCamera = routePlannerModel.getInitialCameraPosition();
        if (initialCamera != null) {
            view.moveMapTo(initialCamera, DEFAULT_CAMERA_MOVE_DELAY_MS);
            return;
        }

        // If user has not moved camera, move to restored camera position
        if (restoredCameraPosition != null) {
            view.moveMapTo(restoredCameraPosition, DEFAULT_CAMERA_MOVE_DELAY_MS);
            restoredCameraPosition = null;
            return;
        }

        // Move to the current location
        suuntoLocationSource.getLastKnownLocation(new SuuntoLocationCallback() {
            @Override
            public void onSuccess(@NonNull Location location) {
                RoutePlannerView view = getView();
                if (view != null) {
                    view.moveMapTo(location.getLatitude(), location.getLongitude(),
                        DEFAULT_ZOOM_LEVEL);
                }
            }

            @Override
            public void onFailure(@NonNull Exception exception) {
                RoutePlannerView view = getView();
                if (view != null) {
                    view.moveMapTo(
                        SuuntoMapOptions.Companion.getDEFAULTS().getCameraPosition(),
                        DEFAULT_CAMERA_MOVE_DELAY_MS
                    );

                    view.myLocationNotAvailable();
                }
            }
        });
    }

    void onMyLocationClicked(boolean moveCamera) {
        if (!moveCamera) return;

        suuntoLocationSource.getLastKnownLocation(
            location -> {
                RoutePlannerView view = getView();
                if (view != null) {
                    view.moveMapTo(location.getLatitude(), location.getLongitude());
                }
                return Unit.INSTANCE;
            },
            error -> {
                RoutePlannerView view = getView();
                if (view != null) {
                    view.myLocationNotAvailable();
                }
                return Unit.INSTANCE;
            }
        );
    }

    public void setFetchingRoute(boolean fetchingRoute) {
        routePlannerModel.setFetchingRoute(fetchingRoute);
    }

    private void restoreStartPoint() {
        if (routePlannerModel.hasStartPoint()) {
            RoutePlannerView view = getView();
            if (view != null) {
                LatLng startPoint = routePlannerModel.getStartPoint();
                view.showStartPoint(startPoint.latitude, startPoint.longitude);
            }
        }
    }

    private void restorePendingWaypoint() {
        if (routePlannerModel.hasPendingWaypoint()) {
            RoutePlannerView view = getView();
            if (view != null) {
                LatLng waypoint = routePlannerModel.getPendingWaypoint();
                view.showPendingWaypoint(waypoint);
            }
        }
    }

    private void resetViewState() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.resetState();
        }
    }

    private void restoreSegments() {
        ArrayList<RouteSegment> segments = routePlannerModel.getSegments();
        showNewSegments(segments, true);
        updateRouteDistanceAndVerticalDeltaTextsOnView();
        updateRouteDurationAndSpeed(routePlannerModel.getDuration());
    }

    private void restoreRouteName() {
        updateRouteName(routePlannerModel.getRouteName());
    }

    void undoLastAction() {
        undoCountForAnalytics++;
        RoutePlannerOperation op = routePlannerModel.undoLastAction();
        if (op instanceof AddStartPointOperation) {
            undoStartPoint();
        } else if (op instanceof AppendSegmentsOperation) {
            AppendSegmentsOperation operation = (AppendSegmentsOperation) op;
            undoAppendSegments(operation.getSegments());
            saveChanges();
        } else if (op instanceof MovePointOperation) {
            MovePointOperation operation = (MovePointOperation) op;
            operation.getMoveRoutePointResult();
            undoMove(operation.getMoveRoutePointResult());
        } else if (op instanceof MoveStartPointOperation) {
            MoveStartPointOperation operation = (MoveStartPointOperation) op;
            undoStartPointMove(operation.getMoveRoutePointResult());
        } else if (op instanceof AddWaypointOperation) {
            AddWaypointOperation operation = (AddWaypointOperation) op;
            informWaypoinstRemoved(operation.getAddWaypointResult());
        } else if (op instanceof EditWaypointOperation) {
            EditWaypointOperation operation = (EditWaypointOperation) op;
            informUndoEditWaypoint(operation.getEditWaypointResult());
        } else if (op instanceof ReversedRouteOperation) {
            ReversedRouteOperation operation = (ReversedRouteOperation) op;
            informUndoRouteReverse(operation.getReverseRouteResult());
        }
        updateRouteDistanceAndVerticalDeltaTextsOnView();
        updateRouteDurationAndSpeed(routePlannerModel.getDuration());
    }

    private void undoStartPointMove(
        MovePointResult moveRoutePointResult
    ) {
        undoMove(moveRoutePointResult);
        RoutePlannerView view = getView();
        if (view != null) {
            routePlannerModel.setStartPoint(moveRoutePointResult.getOriginalStartPoint());
            view.moveStartPoint(moveRoutePointResult.getOriginalStartPoint());
        }
    }

    private void undoStartPoint() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.removeStartPoint();
        }
    }

    private void undoMove(MovePointResult result) {
        updateSegmentsAfterMove(result.getNewSegments(), result.getOriginalSegments());
        saveChangesInProgress();
    }

    private void undoAppendSegments(List<RouteSegment> removedSegments) {
        getDisposables().clear();
        informSegmentsRemoved(removedSegments, true);
    }

    private void saveChanges() {
        if (!routePlannerModel.getSegments().isEmpty()) {
            saveChangesInProgress();
        } else {
            deleteRouteInProgress();
        }
    }

    private void deleteRouteInProgress() {
        Route route = routePlannerModel.getRouteInProgress();
        if (route != null) {
            routePlannerModel.resetRouteInProgress();
            getDisposables().add(
                deleteRouteUseCase.deleteRouteRx(route)
                    .subscribeOn(getMainThread())
                    .observeOn(getMainThread())
                    .subscribe(() -> {
                        },
                        throwable ->
                            Timber.w(throwable, "Deleting route failed")));
        }
    }

    private void informSegmentsRemoved(
        @NonNull List<RouteSegment> removedSegments,
        boolean updateLatestSegment
    ) {
        RoutePlannerView view = getView();
        if (view != null) {
            view.removeSegments(removedSegments, updateLatestSegment);
        }
    }

    private void informWaypointsAdded(
        @NonNull AddWaypointsActionResult result
    ) {
        if (view != null) {
            view.addWaypoint(result);
        }
    }

    private void informWaypointEdited(
        @NonNull EditWaypointActionResult result
    ) {
        if (view != null) {
            view.editWaypoint(result);
        }
    }

    private void informWaypoinstRemoved(
        @NonNull AddWaypointsActionResult result
    ) {
        if (view != null) {
            view.removeWaypoint(result);
        }
    }

    private void informUndoEditWaypoint(
        @NonNull EditWaypointActionResult result
    ) {
        if (view != null) {
            view.undoEditWaypoint(result);
        }
    }

    public void setAvgSpeed(double speedInMetersPerSecond) {
        speedChanged = true;
        routePlannerModel.updateAvgSpeed(speedInMetersPerSecond);
        updateRouteDurationAndSpeed(routePlannerModel.getDuration());
    }

    @SuppressWarnings("WeakerAccess")
        // Used in inner classes
    void informAddPointFailed() {
        RoutePlannerView view = getView();
        if (view != null) {
            boolean internetConnectionNeeded = !ANetworkProvider.isOnline();
            view.addPointFailed(internetConnectionNeeded);
            informFetchingRouteCompleted();
        }
    }

    @SuppressWarnings("WeakerAccess")
        // Used in inner classes
    void informMovePointFailed(Throwable throwable) {
        Timber.w(throwable, "Can't move point on route");
        RoutePlannerView view = getView();
        if (view != null) {
            view.movePointFailed();
            informFetchingRouteCompleted();
        }
    }

    protected void informInvalidRouteName() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.invalidRouteName();
        }
    }

    private void informCreatingNewRoute() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.creatingNewRoute();
        }
    }

    private void informCreatingCopyRoute() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.creatingCopyRoute();
        }
    }

    private void informTopRouteEditing() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.topRouteEditing();
        }
    }

    private void informEditingRoute(String routeName) {
        RoutePlannerView view = getView();
        if (view != null) {
            view.editingRoute(routeName);
        }
    }

    private void informTurnByTurnWaypointMaxCountReached() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.onMaxTurnByTurnWaypointCount();
        }
    }

    private void informWaypointMaxCountReached() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.onMaxWaypointCount();
        }
    }

    String getFormattedAvgSpeed() {
        MeasurementUnit measurementUnit = userSettingsController.getSettings().getMeasurementUnit();
        return TextFormatter.formatSpeed(
            measurementUnit.toSpeedUnit(routePlannerModel.getAvgSpeed()));
    }

    @StringRes public int getSpeedUnitRes() {
        return userSettingsController.getSettings().getMeasurementUnit().getSpeedUnit();
    }

    public void saveOrUpdateRoute(int scrubbingCount, Boolean isNavigate) {
        getDisposables().add(
            Single.fromCallable(() -> routePlannerModel.getNewOrUpdatedRoute(true, true))
                .subscribeOn(getIoThread())
                .flatMap(route -> saveRouteCompletable(route, routePlannerModel.isWorkoutToRoute(), isNavigate)
                    .andThen(routeAnalytics.trackRouteSaved(
                            routePlannerModel.isExistingRoute(),
                            routePlannerModel.isWorkoutToRoute(),
                            getRouteAnalytics(false),
                            currentUserController.getUsername(),
                            scrubbingCount)
                        .onErrorComplete(error -> {
                            Timber.w(error, "Unable to track analytics for save route");
                            return true;
                        }))
                    .toSingleDefault(route)
                )
                .observeOn(getMainThread())
                .subscribe(
                    route -> informRouteSaved(route.getName()),
                    throwable -> {
                        Timber.w(throwable, "Error while saving route");
                        if (throwable instanceof RoutePlannerModel.InvalidRouteNameException) {
                            informInvalidRouteName();
                        } else {
                            informSaveRouteError();
                        }
                    }));
        informSavingRoute();
    }

    private void saveChangesInProgress() {
        if (routePlannerModel.getSegments().isEmpty()) return;
        // If this is first save of this route, we can now safely delete all temporally saved
        // routes. Let's do it to avoid their count growing unlimited.
        Completable deleteOldRoutesInProgress =
            routePlannerModel.getRouteInProgress() == null
                ? deleteRoutesInProgressUseCase.deleteRoutesInProgress()
                : Completable.complete();
        getDisposables().add(
            Single.fromCallable(() -> routePlannerModel.getNewOrUpdatedRoute(false, true))
                .subscribeOn(getIoThread())
                .flatMapCompletable(route ->
                    deleteOldRoutesInProgress
                        .andThen(saveRouteUseCase.saveRouteRx(route))
                )
                .observeOn(getMainThread())
                .subscribe(
                    () -> {
                    },
                    throwable -> {
                        Timber.w(throwable, "Error while saving route");
                        if (throwable instanceof RoutePlannerModel.InvalidRouteNameException) {
                            informInvalidRouteName();
                        } else {
                            informSaveRouteError();
                        }
                    })
        );
    }

    public void setRouteInProgressId(@Nullable String id) {
        routePlannerModel.setRouteInProgressId(id);
    }

    @Nullable
    public String getRouteInProgressId() {
        return routePlannerModel.getRouteInProgressId();
    }

    public void setTurnByTurnEnabled(boolean enabled) {
        routePlannerModel.turnByTurnEnabled = enabled;
        updateWaypointDetailList();
    }

    public void setRouteInProgressStartPoint(@Nullable LatLng routeInProgressStartPoint) {
        routePlannerModel.setRouteInProgressStartPoint(routeInProgressStartPoint);
    }

    @Nullable
    public LatLng getStartPoint() {
        return routePlannerModel.getStartPoint();
    }

    protected abstract Completable saveRouteCompletable(Route route, boolean isWorkoutToRoute, boolean isNavigate);

    protected abstract Single<Boolean> checkIsWatchRouteListFull();

    public boolean isEdited() {
        return routePlannerModel.isEdited();
    }

    @SuppressWarnings("WeakerAccess")
        // Used in inner classes
    void informRouteSaved(String routeName) {
        RoutePlannerView view = getView();
        if (view != null) {
            pendingSavedRouteName = null;
            view.routeSaved(routeName);
        } else {
            pendingSavedRouteName = routeName;
        }

        pendingRouteSaveError = false;
    }

    private void informSavingRoute() {
        RoutePlannerView view = getView();
        if (view != null) {
            view.savingRoute();
        }
    }

    @SuppressWarnings("WeakerAccess")
        // Used in inner classes
    void informSaveRouteError() {
        RoutePlannerView view = getView();
        if (view != null) {
            pendingRouteSaveError = false;
            view.errorSavingRoute();
        } else {
            pendingRouteSaveError = true;
        }

        pendingSavedRouteName = null;
    }

    void routingMode(@NonNull RoutingMode routingMode) {
        this.routingMode = routingMode;
        if (!speedChanged) {
            routePlannerModel.updateAvgSpeed(RoutePlannerModel.getDefaultSpeed(routingMode));
            updateRouteDurationAndSpeed(routePlannerModel.getDuration());
        }
        informRoutingModeChanged(routingMode);

        sharedPreferences.edit()
            .putInt(STTConstants.DefaultPreferences.LAST_USED_ROUTING_MODE, routingMode.getIndex())
            .apply();
    }

    private void informRoutingModeChanged(@NonNull RoutingMode routingMode) {
        RoutePlannerView view = getView();
        if (view != null) {
            view.onRoutingChanged(routingMode);
        }
    }

    private void informEditRoutePlanningStarted() {
        if (view != null) {
            view.onEditRoutePlanningStarted();
        }
    }

    private void informTurnWaypointsEnabledUpdate(boolean enabled, boolean byTheUser) {
        if (view != null) {
            view.onTurnByTurnWaypointsEnabledChanged(enabled, byTheUser);
        }
    }

    void moveSegmentEndPoint(
        int segmentHash,
        double newEndPointLatitude,
        double newEndPointLongitude,
        boolean isWaypoint) {
        getDisposables().add(
            routePlannerModel.moveSegmentEndPoint(segmentHash, newEndPointLatitude,
                newEndPointLongitude, routingMode)
                .subscribeOn(getIoThread())
                .observeOn(getMainThread())
                .subscribe(
                    result -> {
                        if (!isWaypoint) ++planningPointMoveCountForAnalytics;
                        updateSegmentsAfterMove(result.getOriginalSegments(), result.getNewSegments());
                        informFetchingRouteCompleted();
                        try {
                            saveChangesInProgress();
                        } catch (Exception e) {
                            // Don't inform UI about error as route model was already updated
                            Timber.w(e, "Failed to save route after moving segment endpoint");
                        }
                    },
                    this::informMovePointFailed
                ));
    }

    void moveStartPointTo(double latitude, double longitude) {
        Single<MovePointResult> moveRoutePointResultObservable =
            routePlannerModel.moveStartPointTo(latitude, longitude, routingMode);
        if (moveRoutePointResultObservable != null) {
            getDisposables().add(moveRoutePointResultObservable
                .subscribeOn(getIoThread())
                .observeOn(getMainThread())
                .subscribe(
                    result -> {
                        updateSegmentsAfterMove(result.getOriginalSegments(), result.getNewSegments());
                        informFetchingRouteCompleted();
                        try {
                            saveChangesInProgress();
                        } catch (Exception e) {
                            // Don't inform UI about error as route model was already updated
                            Timber.w(e, "Failed to save route after moving start point");
                        }
                    },
                    this::informMovePointFailed
                ));
        }
    }

    private void updateSegmentsAfterMove(
        List<RouteSegment> removed,
        List<RouteSegment> added
    ) {
        informSegmentsRemoved(removed, false);
        showNewSegments(added, false);
        updateRouteDistanceAndVerticalDeltaTextsOnView();
        updateRouteDurationAndSpeed(routePlannerModel.getDuration());
    }

    private void informUndoRouteReverse(ReverseRouteResult result) {
        ReverseRouteResult reverseResult =
            new ReverseRouteResult(result.getNewSegments(), result.getOriginalSegments());
        updateSegmentsAfterReversed(reverseResult);
    }

    private void updateSegmentsAfterReversed(ReverseRouteResult result) {
        informFetchingRouteStarted();
        changeStartPointOnReversed();
        informSegmentsRemoved(result.getOriginalSegments(), true);
        showNewSegments(result.getNewSegments(), true);
        updateRouteDistanceAndVerticalDeltaTextsOnView();
        updateRouteDurationAndSpeed(routePlannerModel.getDuration());
        informFetchingRouteCompleted();
        if (routePlannerModel.getTurnByTurnWaypointCount()
            >= MAX_TURN_BY_TURN_WAYPOINT_COUNT) {
            informTurnByTurnWaypointMaxCountReached();
        }
        if (routePlannerModel.getWaypointCount(true) >= MAX_WAYPOINT_COUNT) {
            informWaypointMaxCountReached();
        }
    }

    boolean hasStartPoint() {
        return routePlannerModel.hasStartPoint();
    }

    void updateActivities(List<ActivityType> newActivities, boolean userChoice) {
        if (userChoice && !newActivities.equals(routePlannerModel.getActivityTypes())) {
            routeAnalytics.trackRoutePlanningActivitiesChanged(newActivities);
        }
        routePlannerModel.updateActivities(newActivities);
        informActivitiesChanged(newActivities);
    }

    private void informActivitiesChanged(List<ActivityType> newActivities) {
        RoutePlannerView view = getView();
        if (view != null) {
            view.activitiesChanged(newActivities);
        }
    }

    @NonNull
    RoutingMode getRoutingMode() {
        return routingMode;
    }

    public void setRouteName(@NonNull String routeName) {
        if (routePlannerModel.updateRouteName(routeName)) {
            informCreatingNewRoute();
        } else if (routePlannerModel.isActionCopyRoute()) {
            informCreatingCopyRoute();
        } else if (routePlannerModel.isFromTopRoute()) {
            informTopRouteEditing();
        } else {
            informEditingRoute(routeName);
        }
    }

    public void addRoutePoint(LatLng latLng) {
        if (!routePlannerModel.hasStartPoint()) {
            addStartPoint(latLng.latitude, latLng.longitude);
        } else {
            //notify view that route is being fetched
            informFetchingRouteStarted();
            appendRoutePoint(latLng.latitude, latLng.longitude);
        }
    }

    public void addPendingWaypoint(LatLng latLng) {
        routePlannerModel.addPendingWaypoint(latLng);
        RoutePlannerView view = getView();
        if (view != null) {
            view.showPendingWaypoint(latLng);
        }
    }

    public void removePendingWaypoint() {
        routePlannerModel.removePendingWaypoint();
    }

    public void setStartingPointLocation(LatLng originalPosition) {
        routePlannerModel.setStartPoint(originalPosition);
    }

    private void simplifyRouteIfNeeded() {
        if (routePlannerModel.isSimplificationThresholdExceeded()) {
            Timber.d("Route simplification needed");
            getDisposables().add(
                Single.fromCallable(routePlannerModel::getSimplifiedSegments)
                    .subscribeOn(getIoThread())
                    .observeOn(getMainThread())
                    .subscribe(
                        this::updateRouteFully,
                        error -> Timber.w(error, "Failed to simplify route")
                    )
            );
        }
    }

    public void trackRoutePlanningInstructionsButtonTapped() {
        getDisposables().add(
            routeAnalytics.trackRoutePlanningInstructionsButtonTapped()
                .subscribeOn(getIoThread())
                .observeOn(getMainThread())
                .subscribe(() -> {
                    },
                    throwable -> Timber.w(throwable,
                        "Error while sending trackRoutePlanningInstructions event")));
    }

    private void trackRouteImportErrorAnalytics() {
        AnalyticsProperties properties = new AnalyticsProperties();
        properties.put(AnalyticsEventProperty.ROUTE_IMPORT_SOURCE,
            routePlannerModel.isWorkoutToRoute()
                ? AnalyticsPropertyValue.RouteImportProperty.WORKOUT
                : AnalyticsPropertyValue.RouteImportProperty.FILE_SYSTEM);
        datahubAnalyticsTracker.trackEvent(AnalyticsEvent.ROUTE_IMPORT_ERROR, properties);
        emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.ROUTE_IMPORT_ERROR,
            properties.getMap());
    }

    @NonNull
    AnalyticsProperties getRouteAnalytics(boolean waypointCountFromOriginalRoute) {
        AnalyticsProperties properties = new AnalyticsProperties()
            .put(AnalyticsEventProperty.ROUTES_MODES, routingModesUsed.toString())
            .put(AnalyticsEventProperty.ROUTES_POINTS, routePlannerModel.getRoutePointsForAnalytics())
            .put(AnalyticsEventProperty.ROUTES_UNDO_COUNT, undoCountForAnalytics)
            .put(AnalyticsEventProperty.ROUTES_PLANNING_POINTS_ADDED_MANUALLY, planningPointCountForAnalytics)
            .put(AnalyticsEventProperty.ROUTES_PLANNING_POINTS_MOVED_MANUALLY, planningPointMoveCountForAnalytics)
            .put(AnalyticsEventProperty.DISTANCE_IN_METERS, routePlannerModel.getDistance())
            .put(AnalyticsEventProperty.DURATION_IN_SECONDS, routePlannerModel.getDuration())
            .put(AnalyticsEventProperty.IMPORTED_ROUTE, routePlannerModel.isImportedRoute() ? "Yes": "No")
            .put(AnalyticsEventProperty.ACTIVITY_TYPES,
                routePlannerModel.getActivityTypes()
                    .stream()
                    .map(ActivityType::getSimpleName)
                    .collect(Collectors.toList()))
            .put(AnalyticsEventProperty.ACTIVITY_TYPE_COUNT, routePlannerModel.getActivityTypes().size())
            .putOnOff(AnalyticsEventProperty.TURN_WAYPOINTS, routePlannerModel.turnByTurnEnabled)
            .put(AnalyticsEventProperty.TURN_WAYPOINT_COUNT, routePlannerModel.turnByTurnEnabled
                ? routePlannerModel.getTurnByTurnWaypointsCountForAnalytics(
                    waypointCountFromOriginalRoute)
                : 0)
            .putTrueFalse(AnalyticsEventProperty.USE_IN_WATCH, routePlannerModel.watchEnabled)
            .put(AnalyticsEventProperty.WAYPOINTS,
                routePlannerModel.getWaypointsCountForAnalytics(waypointCountFromOriginalRoute));

        RoutePlannerView view = getView();
        if (view != null) {
            properties.put(AnalyticsEventProperty.SPEED,
                Math.round(routePlannerModel.getAvgSpeed()));
            properties.put(AnalyticsEventProperty.MAP_MODE,
                view.getCurrentMapType().getName());
            properties.put(AnalyticsEventProperty.MAP_HEATMAP_TYPE,
                view.getCurrentHeatmapType() != null ? view.getCurrentHeatmapType().getAnalyticsName() :
                    AnalyticsPropertyValue.MAP_NO_HEATMAP);
            properties.put(AnalyticsEventProperty.MAP_ROAD_SURFACE_LAYER,
                getRoadSurfaceTypesPropertyValue(view.getCurrentRoadSurfaceTypes()));
            properties.putYesNo(AnalyticsEventProperty.MAP_HIDE_CYCLING_FORBIDDEN_ROADS,
                view.getHideCyclingForbiddenRoads());
        }

        return properties;
    }

    private String getRoadSurfaceTypesPropertyValue(List<RoadSurfaceType> roadSurfaceTypes) {
        if (roadSurfaceTypes == null || roadSurfaceTypes.isEmpty()) {
            return AnalyticsPropertyValue.MAP_NO_ROAD_SURFACE;
        } else {
            List<String> roadSurfaceNames = new ArrayList<>();
            for (RoadSurfaceType roadSurfaceType : roadSurfaceTypes) {
                roadSurfaceNames.add(roadSurfaceType.getAnalyticsName());
            }
            return TextUtils.join("+", roadSurfaceNames);
        }
    }

    @NonNull
    private String getRoutingModeNameForAnalytics() {
        return routingMode.getAnalyticsName();
    }

    public void onAscentInfoVisibilityRequested() {
        updateRouteAscentAndDescentTextOnView(routePlannerModel.getVerticalDelta());
    }

    public void setRestoredCameraPosition(SuuntoCameraPosition restoredCameraPosition) {
        if (restoredCameraPosition != null) {
            this.restoredCameraPosition = SuuntoCameraOptions
                .fromCameraPosition(restoredCameraPosition);
        }
    }

    public boolean isTrackUserLocation() {
        return trackUserLocation;
    }

    public void setTrackUserLocation(boolean enabled) {
        trackUserLocation = enabled;

        MapFloatingActionButtonsState currentState = _mapFloatingActionButtonState.getValue();
        if (currentState != null) {
            _mapFloatingActionButtonState.setValue(new MapFloatingActionButtonsState(
                currentState.getShowInfo(),
                currentState.getShowSearch(),
                currentState.getShow3D(),
                currentState.getEnable3D(),
                true,
                currentState.getShowMapLayers(),
                currentState.getShowLocation(),
                enabled // Update location arrow fill/normal state
            ));
        }
    }

    public void onCameraMove(boolean hasMoved) {
        cameraHasMoved = hasMoved;
    }

    public List<ActivityType> getDefaultActivities() {
        return defaultActivitiesUseCase.getDefaultActivities().stream()
            .map(activity -> ActivityType.valueOf(activity.getId()))
            .collect(Collectors.toList());
    }

    public void saveDefaultActivities(List<ActivityType> activities) {
        defaultActivitiesUseCase.saveDefaultActivities(activities.stream()
            .map(activity -> CoreActivityType.valueOf(activity.getId()))
            .collect(Collectors.toList()));
    }
    public void closeRoute() {
        LatLng startPoint = routePlannerModel.getStartPoint();
        if (startPoint == null || isRouteClosed()) return;

        addRoutePoint(startPoint);
    }

    public boolean isRouteClosed() {
        return routePlannerModel.isRouteClosed();
    }

    public void reverseRoute() {
        getDisposables().clear();
        getDisposables().add(
            Single.fromCallable(routePlannerModel::reverseSegments)
                .subscribeOn(getIoThread())
                .observeOn(getMainThread())
                .subscribe((result) -> {
                    try {
                        updateSegmentsAfterReversed(result);
                        saveChangesInProgress();
                    } catch (Exception e) {
                        // Don't inform UI about error as route model was already updated
                        Timber.w(e, "Failed to save route after appending point");
                    }
                })
        );
    }

    public void backTraceToStart() {
        getDisposables().clear();
        getDisposables().add(
            Single.fromCallable(routePlannerModel::getBackTraceSegments)
                .subscribeOn(getIoThread())
                .observeOn(getMainThread())
                .subscribe((segments) -> {
                    showNewSegments(segments, true);
                    updateRouteDistanceAndVerticalDeltaTextsOnView();
                    updateRouteDurationAndSpeed(routePlannerModel.getDuration());
                    informFetchingRouteCompleted();
                    if (routePlannerModel.getTurnByTurnWaypointCount()
                        >= MAX_TURN_BY_TURN_WAYPOINT_COUNT) {
                        informTurnByTurnWaypointMaxCountReached();
                    }
                    if (routePlannerModel.getWaypointCount(true) >= MAX_WAYPOINT_COUNT) {
                        informWaypointMaxCountReached();
                    }

                    try {
                        saveChangesInProgress();
                    } catch (Exception e) {
                        // Don't inform UI about error as route model was already updated
                        Timber.w(e, "Failed to save route after appending point");
                    }
                })
        );
    }

    private void changeStartPointOnReversed() {
        RoutePlannerView view = getView();
        if (view != null) {
            List<RouteSegment> segments = routePlannerModel.getSegments();
            if (segments.size() > 0) {
                Point point = segments.get(0).getStartPoint();
                LatLng startPoint = PointExtKt.toLatLng(point);
                routePlannerModel.setStartPoint(startPoint);
                view.moveStartPoint(startPoint);
            }
        }
    }
}
