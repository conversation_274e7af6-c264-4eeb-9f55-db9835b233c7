package com.stt.android.home.explore.toproutes.filter

import androidx.annotation.StringRes
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.R
import com.stt.android.home.explore.routes.list.DistanceFilter

data class TopRouteFilter(
    val sortFilter: SortFilter = SortFilter.MostPopular,
    val activityTypes: List<ActivityType>? = null,
    val distanceFilter: DistanceFilter = DistanceFilter.default()
)

enum class SortFilter(@StringRes val resId: Int) {
    Default(R.string.top_route_filter_default),
    MostPopular(R.string.top_route_filter_sort_most_popular),
    Nearest(R.string.top_route_filter_sort_nearest),
    Latest(com.stt.android.R.string.latest),
    UseInWatch(R.string.top_route_filter_sort_use_in_watch)
}
