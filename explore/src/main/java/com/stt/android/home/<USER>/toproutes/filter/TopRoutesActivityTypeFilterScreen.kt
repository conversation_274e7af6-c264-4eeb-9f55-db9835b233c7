package com.stt.android.home.explore.toproutes.filter

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.R

@Composable
internal fun TopRoutesActivityTypeFilterScreen(
    selectedTypes: List<ActivityType>,
    allTypes: List<ActivityType>,
    onAllSelected: () -> Unit,
    onSelectChanged: (List<ActivityType>) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            modifier = Modifier.padding(
                horizontal = MaterialTheme.spacing.medium,
                vertical = MaterialTheme.spacing.small
            ),
            text = stringResource(id = R.string.top_route_filter_sports_type),
            style = MaterialTheme.typography.bodyLargeBold,
            color = MaterialTheme.colorScheme.onSurface
        )

        TopRouteActivityTypeFilter(
            selectedTypes = selectedTypes,
            allTypes = allTypes,
            onAllSelected = onAllSelected,
            onSelectChanged = onSelectChanged,
            modifier = Modifier.padding(vertical = MaterialTheme.spacing.small)
        )
    }
}

@Composable
internal fun ActivityTypeFilterItem(
    isSelected: Boolean,
    itemDescription: String,
    showBorder: Boolean,
    onSelected: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
) {
    TopRouteFilterChip(
        selected = isSelected,
        chipText = itemDescription,
        enabled = enabled,
        showBorder = showBorder,
        onChecked = onSelected,
        modifier = modifier,
    )
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun TopRoutesActivityTypeFilterScreenPreview() {
    M3AppTheme {
        TopRoutesActivityTypeFilterScreen(
            selectedTypes = listOf(ActivityType.RUNNING),
            allTypes = listOf(
                ActivityType.RUNNING,
                ActivityType.HIKING,
                ActivityType.TREKKING,
                ActivityType.WALKING,
                ActivityType.TRAIL_RUNNING,
                ActivityType.CYCLING,
                ActivityType.MOUNTAIN_BIKING,
                ActivityType.CROSS_COUNTRY_SKIING,
            ),
            onAllSelected = {},
            onSelectChanged = {}
        )
    }
}
