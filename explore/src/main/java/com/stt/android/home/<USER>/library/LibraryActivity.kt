package com.stt.android.home.explore.library

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.IntentCompat
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.lifecycleScope
import androidx.viewpager.widget.ViewPager
import com.google.android.material.tabs.TabLayout
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.controllers.CurrentUserController
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.home.explore.ExploreAnalytics
import com.stt.android.home.explore.R
import com.stt.android.home.explore.databinding.ActivityLibraryBinding
import com.stt.android.home.explore.offlinemaps.OfflineMapsTabVisibility
import com.stt.android.home.explore.offlinemaps.analytics.OfflineMapsAnalytics
import com.stt.android.home.explore.routes.ImportGPXActionHandler
import com.stt.android.home.explore.routes.planner.BaseRoutePlannerActivity
import com.stt.android.intentresolver.LibraryTab
import com.stt.android.intentresolver.TopRouteAction
import com.stt.android.session.SignInFlowHook
import com.stt.android.utils.STTConstants
import com.stt.android.utils.getEnumExtra
import com.stt.android.utils.putEnumExtra
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
internal class LibraryActivity :
    AppCompatActivity(),
    ImportGPXActionHandler,
    ViewPager.OnPageChangeListener {
    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var signInFlowHook: SignInFlowHook

    @Inject
    lateinit var exploreAnalytics: ExploreAnalytics

    @Inject
    lateinit var offlineMapsTabVisibility: OfflineMapsTabVisibility

    @Inject
    lateinit var offlineMapsAnalytics: OfflineMapsAnalytics

    @Inject
    @FeatureTogglePreferences
    lateinit var featureTogglePreferences: SharedPreferences

    private val pickGpxLauncher: ActivityResultLauncher<String> = registerForActivityResult(
        contract = ActivityResultContracts.GetContent(),
    ) { uri ->
        uri?.let {
            BaseRoutePlannerActivity.startActivityForImportingOrRedirectToLogin(
                currentUserController,
                signInFlowHook,
                this,
                uri,
            )
        }
    }
    private lateinit var adapter: LibraryPagerAdapter

    private val binding: ActivityLibraryBinding by lazy {
        ActivityLibraryBinding.inflate(layoutInflater)
    }

    private val topRouteFeaturesEnabled: Boolean by lazy {
        featureTogglePreferences.getBoolean(
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES,
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES_DEFAULT,
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        setSupportActionBar(binding.toolbar)
        supportActionBar?.setTitle(R.string.library)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowTitleEnabled(true)

        lifecycleScope.launchWhenCreated {
            val showOfflineMaps = offlineMapsTabVisibility.isVisible()
            val topRouteAction =
                IntentCompat.getSerializableExtra(intent, LIBRARY_ROUTE_ACTION, TopRouteAction::class.java) ?: TopRouteAction.NONE
            adapter = LibraryPagerAdapter(
                supportFragmentManager,
                resources,
                showOfflineMaps,
                topRouteFeaturesEnabled,
                topRouteAction
            )
            setupBindings(showOfflineMaps)
            setupInitialTab(showOfflineMaps)
        }
    }

    private fun setupBindings(showOfflineMaps: Boolean) {
        binding.tabs.tabMode = if (showOfflineMaps) {
            TabLayout.MODE_SCROLLABLE
        } else {
            TabLayout.MODE_FIXED
        }
        binding.viewPager.adapter = adapter
        binding.viewPager.addOnPageChangeListener(this)
        binding.tabs.setupWithViewPager(binding.viewPager)
    }

    private fun setupInitialTab(supportsOfflineMaps: Boolean) {
        val initialTab = intent.getEnumExtra(
            name = INITIAL_TAB,
            eClass = LibraryTab::class.java,
            defaultEnum = LibraryTab.OFFLINE_MAPS,
        )
        intent.removeExtra(INITIAL_TAB)
        binding.viewPager.currentItem = if (supportsOfflineMaps) {
            initialTab.ordinal
        } else {
            initialTab.ordinal.minus(1).coerceAtLeast(0)
        }
    }

    override fun onDestroy() {
        binding.viewPager.adapter = null
        super.onDestroy()
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }

    override fun importGPXFile() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
        intent.addCategory(Intent.CATEGORY_OPENABLE)
        intent.type = "*/*"
        try {
            pickGpxLauncher.launch("*/*")
        } catch (e: ActivityNotFoundException) {
            Timber.w(e, "Cannot open storage access framework")
            showErrorDialog()
        }
    }

    private fun showErrorDialog() {
        val fragmentManager = supportFragmentManager
        if (!isFinishing && fragmentManager.findFragmentByTag(ERROR_DIALOG_TAG) == null) {
            val dialog: DialogFragment = SimpleDialogFragment.newInstance(
                getString(BaseR.string.error_0),
                null,
                getString(BaseR.string.ok)
            )
            dialog.show(supportFragmentManager, ERROR_DIALOG_TAG)
        }
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
        // do nothing
    }

    override fun onPageSelected(position: Int) {
        when (adapter.pageAtPosition(position)) {
            LibraryPagerAdapter.Page.OFFLINE_MAPS -> lifecycleScope.launch {
                offlineMapsAnalytics.trackMapLibraryScreenFromMap()
            }
            LibraryPagerAdapter.Page.ROUTES -> exploreAnalytics.trackRoutesLibraryScreen(lifecycleScope)
            LibraryPagerAdapter.Page.POIS -> exploreAnalytics.trackPOILibraryScreen(lifecycleScope)
            null -> Timber.w("onPageSelected: invalid position=$position")
        }
    }

    override fun onPageScrollStateChanged(state: Int) {
        // do nothing
    }

    companion object {
        private const val ERROR_DIALOG_TAG = "LibraryActivityErrorDialog"

        private const val INITIAL_TAB = "initial_tab"
        private const val LIBRARY_ROUTE_ACTION = "library_route_action"

        @JvmStatic
        fun newStartIntent(
            context: Context,
            initialTab: LibraryTab,
            topRouteAction: TopRouteAction? = null
        ): Intent = Intent(context, LibraryActivity::class.java).apply {
            putEnumExtra(INITIAL_TAB, initialTab)
            putExtra(LIBRARY_ROUTE_ACTION, topRouteAction)
        }
    }
}
