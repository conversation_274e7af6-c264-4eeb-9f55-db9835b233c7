package com.stt.android.home.explore.toproutes.map

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.home.explore.R
import com.stt.android.utils.getPremiumNoteText
import com.stt.android.R as BaseR

sealed class FabMenuItem(
    open val menuId: Int,
    @StringRes open val menuLabelResId: Int,
) {

    data object ImportRoute : FabMenuItem(
        menuId = 0,
        menuLabelResId = BaseR.string.import_route,
    )

    data object CreateRoute : FabMenuItem(
        menuId = 1,
        menuLabelResId = BaseR.string.create_route,
    )

    data object DownloadOfflineMaps : FabMenuItem(
        menuId = 2,
        menuLabelResId = BaseR.string.download_offline_maps,
    )

    companion object {
        fun getFabMenuItems(supportsOfflineMaps: Boolean): List<FabMenuItem> {
            return if (supportsOfflineMaps) {
                listOf(CreateRoute, ImportRoute, DownloadOfflineMaps)
            } else {
                listOf(CreateRoute, ImportRoute)
            }
        }
    }
}

@Composable
fun ExploreMapBottomButtons(
    isFabMenuShown: Boolean,
    fabMenuItems: List<FabMenuItem>,
    onFabMenuItemClick: (FabMenuItem) -> Unit,
    onLibraryItemClick: () -> Unit,
    modifier: Modifier = Modifier,
    popularRouteCount: Int? = null,
    showTopRoutesButton: Boolean = false,
    routeFabMenuEnabled: Boolean = true,
    showPremiumRequiredNotes: Boolean = false,
    onTopRouteButtonClick: () -> Unit = {},
) {
    val context = LocalContext.current
    var fabMenuExpanded by rememberSaveable(isFabMenuShown) { mutableStateOf(isFabMenuShown) }

    val iconRotation by animateFloatAsState(
        targetValue = if (fabMenuExpanded) 135f else 0f,
        label = "fabRotation"
    )

    Box(modifier = modifier.fillMaxSize()) {
        if (fabMenuExpanded) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(colorResource(R.color.fab_menu_cover_color))
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ) {
                        fabMenuExpanded = false
                    }
            )
        }

        Column(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(
                    end = MaterialTheme.spacing.medium,
                    bottom = MaterialTheme.spacing.medium
                ),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
            horizontalAlignment = Alignment.End
        ) {
            if (fabMenuExpanded) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
                    horizontalAlignment = Alignment.End
                ) {
                    fabMenuItems.forEach { menuItem ->
                        Surface(
                            enabled = routeFabMenuEnabled,
                            shape = RoundedCornerShape(4.dp),
                            color = MaterialTheme.colorScheme.surface,
                            shadowElevation = 8.dp,
                            onClick = {
                                onFabMenuItemClick(menuItem)
                                fabMenuExpanded = false
                            }
                        ) {
                            Text(
                                text = buildAnnotatedString {
                                    append(stringResource(menuItem.menuLabelResId))
                                    if (showPremiumRequiredNotes) {
                                        withStyle(
                                            SpanStyle(
                                                color = colorResource(com.stt.android.R.color.secondary_accent),
                                                fontSize = MaterialTheme.typography.bodyMedium.fontSize * 0.8
                                            )
                                        ) {
                                            append(getPremiumNoteText(context, false))
                                        }
                                    }
                                },
                                modifier = Modifier
                                    .padding(
                                        horizontal = MaterialTheme.spacing.medium,
                                        vertical = MaterialTheme.spacing.xsmaller
                                    ),
                                color = MaterialTheme.colorScheme.onSurface,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            }

            WhiteFloatingActionButton(
                iconResId = com.stt.android.R.drawable.ic_plus_route,
                iconRotation = iconRotation,
                onClick = { fabMenuExpanded = !fabMenuExpanded }
            )

            WhiteFloatingActionButton(
                iconResId = com.stt.android.R.drawable.save_outline,
                onClick = onLibraryItemClick
            )

            if (showTopRoutesButton) {
                MapPopularRoutesButton(
                    popularRoutesCount = popularRouteCount,
                    onClick = onTopRouteButtonClick
                )
            }
        }
    }
}

@Composable
private fun WhiteFloatingActionButton(
    @DrawableRes iconResId: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    iconRotation: Float = 0f,
    iconSize: Dp = MaterialTheme.iconSizes.small
) {
    Surface(
        shape = CircleShape,
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 6.dp,
        onClick = onClick,
        modifier = modifier,
    ) {
        Box(modifier = Modifier.padding(MaterialTheme.spacing.small)) {
            Icon(
                painter = painterResource(iconResId),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.nearBlack,
                modifier = Modifier
                    .size(iconSize)
                    .rotate(iconRotation)
            )
        }
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun ExploreMapBottomButtonsPreview() {
    M3AppTheme {
        ExploreMapBottomButtons(
            popularRouteCount = 10,
            isFabMenuShown = true,
            showTopRoutesButton = true,
            fabMenuItems = FabMenuItem.getFabMenuItems(true),
            onFabMenuItemClick = {},
            onLibraryItemClick = {},
            onTopRouteButtonClick = {}
        )
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true, locale = "zh")
@Composable
private fun ExploreMapBottomButtonsPreviewZH() {
    M3AppTheme {
        ExploreMapBottomButtons(
            popularRouteCount = 10,
            isFabMenuShown = true,
            showTopRoutesButton = true,
            fabMenuItems = FabMenuItem.getFabMenuItems(true),
            onFabMenuItemClick = {},
            onLibraryItemClick = {},
            onTopRouteButtonClick = {}
        )
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true, locale = "zh")
@Composable
private fun ExploreMapBottomButtonsPremiumPreview() {
    M3AppTheme {
        ExploreMapBottomButtons(
            popularRouteCount = 10,
            isFabMenuShown = true,
            showTopRoutesButton = true,
            showPremiumRequiredNotes = true,
            fabMenuItems = FabMenuItem.getFabMenuItems(true),
            onFabMenuItemClick = {},
            onLibraryItemClick = {},
            onTopRouteButtonClick = {}
        )
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true, locale = "zh")
@Composable
private fun ExploreMapBottomButtonsNoTopRoutesPreview() {
    M3AppTheme {
        ExploreMapBottomButtons(
            popularRouteCount = 10,
            isFabMenuShown = true,
            showTopRoutesButton = false,
            showPremiumRequiredNotes = true,
            fabMenuItems = FabMenuItem.getFabMenuItems(true),
            onFabMenuItemClick = {},
            onLibraryItemClick = {},
            onTopRouteButtonClick = {}
        )
    }
}
