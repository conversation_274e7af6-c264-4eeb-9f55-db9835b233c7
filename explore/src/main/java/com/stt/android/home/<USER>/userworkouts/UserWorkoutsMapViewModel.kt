package com.stt.android.home.explore.userworkouts

import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.loadWorkouts
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.explore.ExploreAnalyticsUtils
import com.stt.android.home.people.PeopleController
import com.stt.android.maps.MapFloatingActionButtonsState
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.map.Map3dEnabledLiveData
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelChildren
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class UserWorkoutsMapViewModel @Inject constructor(
    private val workoutHeaderController: WorkoutHeaderController,
    private val peopleController: PeopleController,
    private val currentUserController: CurrentUserController,
    private val mapSelectionModel: MapSelectionModel,
    private val map3dEnabledLiveData: Map3dEnabledLiveData,
    private val exploreAnalyticsUtils: ExploreAnalyticsUtils,
    private val datahubAnalyticsTracker: DatahubAnalyticsTracker,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
) : LoadingStateViewModel<UserWorkoutsMapViewState>(ioThread, mainThread) {

    private val getUserWorkoutsJob = Job()
    private val getUserWorkoutsScope = viewModelScope + getUserWorkoutsJob
    private val _mapFloatingActionButtonState = MutableStateFlow(
        MapFloatingActionButtonsState(
            showLocation = false,
            show3D = mapSelectionModel.show3dOption
        )
    )
    val mapFloatingActionButtonState = _mapFloatingActionButtonState.asStateFlow()
    var clickedWorkout: WorkoutHeader? = null
        private set

    init {
        viewModelScope.launch {
            map3dEnabledLiveData.asFlow()
                .catch { Timber.w(it, "Observing 3D state failed.") }
                .collectLatest { enabled ->
                    set3dOptionState(enabled)
                }
        }
    }

    fun loadWorkouts(user: User) {
        Timber.d("loadWorkouts user=$user")
        getUserWorkoutsJob.cancelChildren()
        getUserWorkoutsScope.launch {
            notifyLoading()
            runSuspendCatching {
                val workouts = doLoadWorkouts(user)
                notifyDataLoaded(UserWorkoutsMapViewState(workouts = workouts))
            }.onFailure { e ->
                Timber.w(e, "Failed to load workouts")
                notifyError(e)
            }
        }
    }

    private suspend fun doLoadWorkouts(user: User): List<WorkoutHeader> = withContext(io) {
        workoutHeaderController.loadWorkouts(user.username)
            .last()
            .filter { workoutHeader -> workoutHeader.startPosition?.isOrigin == false }
    }

    suspend fun trackWorkoutClicked(workoutHeader: WorkoutHeader) =
        exploreAnalyticsUtils.trackWorkoutClicked(workoutHeader)

    fun trackScreenEvent(user: User, source: String) {
        launch {
            val mapTypeString = mapSelectionModel.selectedMapType.name
            val heatmapTypeString = mapSelectionModel.selectedHeatmap?.name

            try {
                if (currentUserController.username == user.username) {
                    // Current user
                    trackScreenEvent(
                        source,
                        mapTypeString,
                        heatmapTypeString,
                        AnalyticsEventProperty.SELF
                    )
                }

                // Some other user, figure out following relationship
                val targetRelationship = withContext(io) {
                    peopleController.getFollowRelationshipValueForAnalytics(user)
                }

                trackScreenEvent(
                    source,
                    mapTypeString,
                    heatmapTypeString,
                    targetRelationship
                )
            } catch (e: Exception) {
                Timber.w(e, "Couldn't get following status, sending event anyway")
                trackScreenEvent(source, mapTypeString, heatmapTypeString, null)
            }
        }
    }

    fun onWorkoutDeleted(workoutId: Int) {
        val viewStateData = viewState.value?.data
        val workouts = viewStateData?.workouts ?: return
        val filteredList = workouts.filter { it.id != workoutId }
        notifyDataLoaded(UserWorkoutsMapViewState(workouts = filteredList))
    }

    fun clearSelection() {
        clickedWorkout = null
    }

    fun setSelectedWorkout(workoutHeader: WorkoutHeader) {
        clickedWorkout = workoutHeader
    }

    private fun trackScreenEvent(
        source: String,
        mapType: String?,
        heatmapType: String?,
        targetRelationship: String?
    ) {
        datahubAnalyticsTracker.trackEvent(
            AnalyticsEvent.MAP_WORKOUT_MAP_SCREEN,
            AnalyticsProperties()
                .put(AnalyticsEventProperty.SOURCE, source)
                .put(AnalyticsEventProperty.MAP_TYPE, mapType)
                .put(AnalyticsEventProperty.TARGET_RELATIONSHIP, targetRelationship)
                .put(
                    AnalyticsEventProperty.MAP_HEATMAP_TYPE,
                    heatmapType ?: AnalyticsPropertyValue.MAP_NO_HEATMAP
                )
        )
    }

    fun setInfoFloatingActionButton(showInfo: Boolean) {
        _mapFloatingActionButtonState.update {
            it.copy(showInfo = showInfo)
        }
    }

    fun handle3dOptionToggled() {
        mapSelectionModel.map3dEnabled = !mapSelectionModel.map3dEnabled
    }

    private fun set3dOptionState(enable3D: Boolean) {
        _mapFloatingActionButtonState.update {
            it.copy(enable3D = enable3D)
        }
    }

    override fun retryLoading() = Unit
}
