package com.stt.android.home.explore.toproutes

import androidx.lifecycle.LiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.stt.android.common.ui.SavedStateHandleViewModelFactory
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.routes.Route
import com.stt.android.domain.routes.SaveAndSyncRouteUseCase
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.maps.MapFloatingActionButtonsState
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.map.Map3dEnabledLiveData
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class TopRoutesViewModel @Inject constructor(
    private val saveAndSyncRouteUseCase: SaveAndSyncRouteUseCase,
    private val mapSelectionModel: MapSelectionModel,
    private val map3dEnabledLiveData: Map3dEnabledLiveData,
    isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase
) : ViewModel() {

    val onRouteSaved = SingleLiveEvent<Boolean>()
    val onRouteSaveFailed = SingleLiveEvent<Throwable>()
    val updateLocateMeIcon: LiveData<Boolean>
        get() = _updateLocateMeIcon
    private val _updateLocateMeIcon = SingleLiveEvent<Boolean>()

    val isSubscribedToPremium: Flow<Boolean> =
        isSubscribedToPremiumUseCase().shareIn(viewModelScope, SharingStarted.WhileSubscribed())
    private val _mapFloatingActionButtonState = MutableStateFlow(
        MapFloatingActionButtonsState(
            showInfo = true,
            show3D = mapSelectionModel.show3dOption
        )
    )
    val mapFloatingActionButtonsState = _mapFloatingActionButtonState.asStateFlow()

    init {
        viewModelScope.launch {
            map3dEnabledLiveData.asFlow()
                .catch { Timber.w(it, "Observing 3D state failed.") }
                .collectLatest { enabled ->
                    set3dOptionState(enabled)
                }
        }
    }

    fun saveRoute(route: Route) {
        viewModelScope.launch {
            runSuspendCatching {
                delay(500) // give room for the keyboard to go down. It causes issues on some devices.
                saveAndSyncRouteUseCase.saveAndSyncRoute(route)
                onRouteSaved.postValue(route.watchEnabled)
            }.onFailure { cause ->
                Timber.w(cause, "Saving and scheduling a sync for a top route failed.")
                onRouteSaveFailed.postValue(cause)
            }
        }
    }

    fun toggleLocateMeIcon(isCentered: Boolean) {
        if (isCentered != _updateLocateMeIcon.value) {
            _updateLocateMeIcon.value = isCentered
        }
    }

    fun setLocationEnabled(locationEnabled: Boolean) {
        _mapFloatingActionButtonState.update {
            it.copy(locationEnabled = locationEnabled)
        }
    }

    fun setInfoFloatingActionButton(showInfo: Boolean) {
        _mapFloatingActionButtonState.update {
            it.copy(showInfo = showInfo)
        }
    }

    fun handle3dOptionToggled() {
        mapSelectionModel.map3dEnabled = !mapSelectionModel.map3dEnabled
    }

    private fun set3dOptionState(enable3D: Boolean) {
        _mapFloatingActionButtonState.update {
            it.copy(enable3D = enable3D)
        }
    }

    fun enableLocation(enable: Boolean) {
        _mapFloatingActionButtonState.update {
            it.copy(locationEnabled = enable)
        }
    }
}

class TopRoutesViewModelFactory
@Inject constructor(
    private val saveAndSyncRouteUseCase: SaveAndSyncRouteUseCase,
    private val isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    private val mapSelectionModel: MapSelectionModel,
    private val map3dEnabledLiveData: Map3dEnabledLiveData,
) : SavedStateHandleViewModelFactory<TopRoutesViewModel> {
    override fun create(handle: SavedStateHandle): TopRoutesViewModel =
        TopRoutesViewModel(
            saveAndSyncRouteUseCase,
            mapSelectionModel,
            map3dEnabledLiveData,
            isSubscribedToPremiumUseCase,
        )
}
