package com.stt.android.home.explore.routes.ui

import androidx.annotation.VisibleForTesting
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance
import com.soy.algorithms.climbanalysis.entities.ClimbSegment
import com.soy.algorithms.climbanalysis.entities.ClimbSegmentType
import com.soy.algorithms.climbanalysis.entities.StravaClimbCategory
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.home.explore.R
import com.stt.android.home.explore.routes.getGuidanceColorRes
import com.stt.android.home.explore.routes.getIconRes

@Composable
internal fun ClimbGuidanceSegmentOverview(
    climbGuidance: ClimbGuidance,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .padding(
                horizontal = MaterialTheme.spacing.medium,
                vertical = MaterialTheme.spacing.xsmall,
            )
            .height(24.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(id = R.string.route_sections),
            style = MaterialTheme.typography.bodyLargeBold
        )

        climbGuidance.segments.sortedSegmentCount()
            .forEach { (type, count) ->
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))

                Icon(
                    painter = painterResource(type.getIconRes()),
                    contentDescription = stringResource(R.string.route_sections),
                    tint = colorResource(type.getGuidanceColorRes()),
                    modifier = Modifier.size(14.dp)
                )

                Text(
                    text = count.toString(),
                    style = MaterialTheme.typography.body,
                    modifier = Modifier.padding(start = MaterialTheme.spacing.xxsmall)
                )
            }
    }
}

private val SORTED_SEGMENT_TYPE = listOf(
    ClimbSegmentType.FLAT,
    ClimbSegmentType.CLIMB,
    ClimbSegmentType.UPHILL,
    ClimbSegmentType.DOWNHILL,
    ClimbSegmentType.DESCENT
)

@VisibleForTesting
internal fun List<ClimbGuidance.SegmentWithPoints>.sortedSegmentCount(): List<Pair<ClimbSegmentType, Int>> {
    val countBySegmentMap = groupBy { it.climbSegment.climbSegmentType }
        .mapValues { entry -> entry.value.size }
    return SORTED_SEGMENT_TYPE.map { segmentType ->
        segmentType to countBySegmentMap.getOrDefault(segmentType, 0)
    }
}

@Preview(showBackground = true)
@Composable
private fun ClimbGuidanceSegmentOverviewPreview() {
    val segment = ClimbSegment(
        climbSegmentType = ClimbSegmentType.CLIMB,
        stravaClimbCategory = StravaClimbCategory.CATEGORY_1,
        indexAtStart = 0,
        indexAtEnd = 0,
        distanceAtStart = 0.0,
        distanceAtEnd = 0.0,
        ascentAtStart = 0.0,
        ascentAtEnd = 0.0,
        descentAtStart = 0.0,
        descentAtEnd = 0.0,
        elevationAtStart = 0.0,
        elevationAtEnd = 0.0
    )
    val segmentWithPointsList = listOf(
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.CLIMB),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.DESCENT),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.DESCENT),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.DESCENT),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.UPHILL),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.UPHILL),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.UPHILL),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.UPHILL),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.UPHILL),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.DOWNHILL),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.FLAT),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.FLAT),
            emptyList()
        ),
        ClimbGuidance.SegmentWithPoints(
            segment.copy(climbSegmentType = ClimbSegmentType.FLAT),
            emptyList()
        ),
    )

    ClimbGuidanceSegmentOverview(ClimbGuidance(segmentWithPointsList))
}
