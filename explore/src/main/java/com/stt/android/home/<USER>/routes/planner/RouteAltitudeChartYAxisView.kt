package com.stt.android.home.explore.routes.planner

import android.content.Context
import android.util.AttributeSet
import com.stt.android.ui.fragments.workout.WorkoutChartShareYAxisView

/**
 * Custom view that draws y-axis of graph on shared photo
 */
class RouteAltitudeChartYAxisView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : WorkoutChartShareYAxisView(context, attrs, defStyle) {

    override fun calculateMarkEndByMarkerLine(i: Int): Float {
        return if (i == 0 || i == markerLineCount - 1) {
            xStart + axisMarkerLineWidth
        } else {
            xStart + axisMarkerLineWidth / 2
        }
    }

    override fun calculateAxisStart(w: Int, h: Int) {
        xStart = 0f
    }
}
