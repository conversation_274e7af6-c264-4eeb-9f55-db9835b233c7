<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <merge
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

        <View
            android:id="@+id/workoutSnapshotView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/rounded_box"
            android:elevation="@dimen/size_spacing_xsmall"
            app:layout_constraintBottom_toBottomOf="@+id/workoutDescription"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/profileImage"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/size_spacing_small"
            android:layout_marginEnd="@dimen/size_spacing_small"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:contentDescription="@string/profile_image"
            android:elevation="@dimen/size_spacing_xsmall"
            android:src="@drawable/ic_default_profile_image_light"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="@id/workoutSnapshotView"
            app:layout_constraintDimensionRatio="H,1:1"
            app:layout_constraintStart_toStartOf="@id/workoutSnapshotView"
            app:layout_constraintTop_toTopOf="@id/workoutSnapshotView" />

        <TextView
            android:id="@+id/realName"
            style="@style/Body.Larger.Bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="6dp"
            android:elevation="@dimen/size_spacing_xsmall"
            android:maxWidth="@dimen/map_workout_preview_name_length"
            android:maxLines="1"
            app:layout_constraintEnd_toStartOf="@id/workoutCounterView"
            app:layout_constraintStart_toEndOf="@id/profileImage"
            app:layout_constraintTop_toTopOf="@id/workoutSnapshotView"
            tools:text="Max Sporter" />

        <TextView
            android:id="@+id/workoutSummary"
            style="@style/Body.Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:elevation="@dimen/size_spacing_xsmall"
            android:paddingTop="2dp"
            app:layout_constraintEnd_toStartOf="@id/workoutCounterView"
            app:layout_constraintStart_toStartOf="@id/realName"
            app:layout_constraintTop_toBottomOf="@id/realName"
            tools:text="Cycling 12.23 km (An hour ago)" />

        <TextView
            android:id="@+id/workoutDescription"
            style="@style/Body.Small"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:elevation="@dimen/size_spacing_xsmall"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingTop="3dp"
            android:paddingBottom="12dp"
            android:scrollHorizontally="false"
            app:layout_constraintEnd_toStartOf="@id/workoutCounterView"
            app:layout_constraintStart_toStartOf="@id/realName"
            app:layout_constraintTop_toBottomOf="@id/workoutSummary"
            tools:text="No description" />

        <com.stt.android.ui.components.WorkoutCounterView
            android:id="@+id/workoutCounterView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="6dp"
            android:elevation="@dimen/size_spacing_xsmall"
            app:layout_constraintEnd_toEndOf="@id/workoutSnapshotView"
            app:layout_constraintTop_toTopOf="@id/realName" />

    </merge>
</layout>
