<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?suuntoBackground"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:showIn="@layout/activity_route_detail">

    <com.stt.android.home.explore.routes.RouteHeaderView
        android:id="@+id/routeHeader"
        style="@style/FeedCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/size_spacing_medium"
        android:paddingStart="@dimen/size_spacing_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_max="@dimen/content_max_width"
        tools:text="Lakeside ride\n1.2 km away" />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/route_detail_main_content_map_container"
        style="@style/FeedCard"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/routeHeader"
        app:layout_constraintWidth_max="@dimen/content_max_width"
        tools:background="@color/red" />

    <!-- Need this silly FrameLayout wrapping the avalanche info
    because setting elevation does not work for include tags -->
    <FrameLayout
        style="@style/FeedCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_small"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:background="@color/transparent"
        app:layout_constraintEnd_toEndOf="@id/route_detail_main_content_map_container"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/route_detail_main_content_map_container"
        app:layout_constraintTop_toBottomOf="@+id/routeHeader"
        app:layout_constraintWidth_max="@dimen/avalanche_info_max_width">

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/avalanche_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

