<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="androidx.core.content.ContextCompat" />

        <import type="android.view.View" />

        <variable
            name="icon"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="onSearchClick"
            type="android.view.View.OnClickListener" />

        <variable
            name="onFilterClick"
            type="android.view.View.OnClickListener" />

        <variable
            name="isEnabled"
            type="Boolean" />

        <variable
            name="isVisible"
            type="Boolean" />

        <variable
            name="isFilterVisible"
            type="Boolean" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.chip.Chip
            android:id="@+id/search_here"
            style="@style/Widget.MaterialComponents.Chip.Action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginBottom="@dimen/size_spacing_small"
            android:enabled="@{isEnabled}"
            android:onClick="@{onSearchClick}"
            android:text="@string/top_routes_search_here"
            android:textAppearance="@style/ChipTextAppearance"
            app:chipBackgroundColor="?suuntoItemBackgroundColor"
            app:chipIcon="@{icon}"
            app:chipIconEnabled="true"
            app:chipIconTint="@color/black"
            app:fadeVisible="@{isVisible}"
            app:layout_constraintBottom_toTopOf="@id/filter_top_route"
            app:layout_constraintStart_toStartOf="parent"
            tools:chipBackgroundColor="?suuntoBackground"
            tools:chipIcon="@drawable/sync" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/filter_top_route"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="@{isFilterVisible ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toTopOf="@id/list"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <com.stt.android.utils.EpoxyNonSharingRecyclerView
            android:id="@+id/list"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
