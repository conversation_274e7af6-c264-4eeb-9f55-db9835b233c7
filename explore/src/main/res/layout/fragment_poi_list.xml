<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.stt.android.home.explore.pois.list.BasePOIListViewModel.PoiEmptyState" />

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.stt.android.home.explore.pois.list.BasePOIListViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/suunto_light_gray"
                    app:layout_scrollFlags="scroll|enterAlways|snap">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/et_search"
                        style="@style/Body.Medium"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:background="@color/transparent"
                        android:drawableStart="@drawable/ic_library_search"
                        android:drawablePadding="@dimen/size_spacing_xsmaller"
                        android:hint="@string/enter_poi_name_or_activity_type"
                        android:imeOptions="actionSearch"
                        android:inputType="text"
                        android:lines="1"
                        android:paddingHorizontal="@dimen/size_spacing_smaller"
                        android:paddingVertical="@dimen/size_spacing_smaller"
                        android:text="@={viewModel.searchContent}"
                        android:textColorHint="@color/suunto_dark_gray"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@id/btn_clear_search"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/btn_clear_search"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:layout_marginEnd="@dimen/size_spacing_smaller"
                        android:src="@drawable/ic_close"
                        android:visibility="@{viewModel.searchContent.length() == 0 ? View.INVISIBLE : View.VISIBLE}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/near_black" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tv_filter_rule"
                    style="@style/Body.Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/route_library_filter_height"
                    android:layout_marginHorizontal="@dimen/size_spacing_medium"
                    android:drawablePadding="@dimen/size_spacing_small"
                    android:gravity="center_vertical"
                    android:text="@={viewModel.sortingRuleLabel}"
                    app:drawableEndCompat="@drawable/ic_dropdown_arrow_down_fill" />
            </com.google.android.material.appbar.AppBarLayout>

            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <com.stt.android.utils.EpoxyNonSharingRecyclerView
                    android:id="@+id/list"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:paddingBottom="@dimen/size_spacing_xxlarge"
                    tools:listitem="@layout/viewholder_poi_item" />

                <ProgressBar
                    android:id="@+id/progressBar"
                    style="@style/Widget.AppCompat.ProgressBar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center" />

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    app:fadeVisible="@{viewModel.emptyState.isEmpty()}">

                    <include
                        android:id="@+id/default_empty_view"
                        layout="@layout/no_pois_card"
                        android:visibility="@{viewModel.emptyState.isDefaultEmpty() ? View.VISIBLE : View.GONE}" />

                    <include
                        layout="@layout/viewholder_list_no_search_results"
                        android:visibility="@{viewModel.emptyState.isNoSearchResults() ? View.VISIBLE : View.GONE}" />

                </FrameLayout>
            </androidx.coordinatorlayout.widget.CoordinatorLayout>

        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </LinearLayout>
</layout>
