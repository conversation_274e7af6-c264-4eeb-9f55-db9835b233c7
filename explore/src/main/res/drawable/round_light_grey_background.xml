<?xml version="1.0" encoding="utf-8"?>
<inset xmlns:android="http://schemas.android.com/apk/res/android" android:inset="@dimen/size_spacing_small">
    <ripple android:color="@color/medium_grey">
        <item>
            <selector>
                <item android:state_enabled="false">
                    <shape>
                        <solid android:color="@color/suunto_light_gray"/>
                        <corners android:radius="@dimen/size_icon_medium"/>
                    </shape>
                </item>
                <item>
                    <shape>
                        <solid android:color="@color/suunto_light_gray"/>
                        <corners android:radius="@dimen/size_icon_medium"/>
                    </shape>
                </item>
            </selector>
        </item>
    </ripple>
</inset>
