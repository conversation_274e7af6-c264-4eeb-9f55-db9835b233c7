package com.stt.android.datasource.explore.pois

import com.stt.android.data.source.local.pois.LocalPOI
import com.stt.android.data.source.local.pois.LocalPOISyncState
import com.stt.android.domain.Point
import com.stt.android.domain.explore.pois.POI
import com.stt.android.remote.explore.RemotePOI
import com.stt.android.remote.explore.RemotePOIResponse
import com.stt.android.remote.routes.RemotePoint
import com.suunto.connectivity.location.fromUIntE7Coordinates
import com.suunto.connectivity.location.toE7Coordinates
import com.suunto.connectivity.poi.MdsPOI
import java.time.Duration

data class POIResponse(
    val poi: POI?,
    val errorCode: Int?
) {
    val isSuccess: Boolean
        get() = errorCode == null || errorCode == 0

    companion object {
        val EMPTY = POIResponse(null, null)
    }
}

fun LocalPOI.toDomain(): POI = POI(
    creation = creation,
    modified = modified,
    point = Point(
        longitude = longitude,
        latitude = latitude,
        altitude = altitude,
        name = name,
        type = type
    ),
    activityId = activityId,
    country = country,
    locality = locality,
    watchEnabled = watchEnabled,
    key = key,
    remoteSyncErrorCode = remoteSyncErrorCode,
    watchSyncErrorCode = watchSyncErrorCode
)

fun POI.toData(
    syncState: LocalPOISyncState,
    deleted: Boolean
): LocalPOI = LocalPOI(
    creation = creation,
    modified = modified,
    longitude = point.longitude,
    latitude = point.latitude,
    altitude = point.altitude,
    name = point.name,
    type = point.type,
    activityId = activityId,
    country = country,
    locality = locality,
    watchEnabled = watchEnabled,
    key = key,
    syncState = syncState,
    deleted = deleted,
    remoteSyncErrorCode = remoteSyncErrorCode,
    watchSyncErrorCode = watchSyncErrorCode
)

fun RemotePOIResponse.toDomain() = POIResponse(
    poi = poi?.toDomain(),
    errorCode = errorCode
)

fun RemotePOI.toDomain() = POI(
    creation = Duration.ofMillis(creationMillis).seconds,
    modified = Duration.ofMillis(modifiedMillis).seconds,
    point = point.toDomain(),
    activityId = activityId,
    country = country,
    locality = locality,
    watchEnabled = watchEnabled,
    key = key,
    remoteSyncErrorCode = null,
    watchSyncErrorCode = null
)

fun POI.toRemote() = RemotePOI(
    creationMillis = Duration.ofSeconds(creation).toMillis(),
    modifiedMillis = Duration.ofSeconds(modified).toMillis(),
    point = point.toRemote(),
    activityId = activityId,
    country = country,
    locality = locality,
    watchEnabled = watchEnabled,
    key = key
)

private fun RemotePoint.toDomain() = Point(
    longitude = longitude,
    latitude = latitude,
    altitude = altitude,
    name = name,
    type = type
)

private fun Point.toRemote() = RemotePoint(
    longitude = longitude,
    latitude = latitude,
    altitude = altitude,
    name = name,
    type = type
)

fun MdsPOI.toDomain() = POI(
    creation = creation,
    modified = modified,
    point = Point(
        longitude = longitude.fromUIntE7Coordinates(),
        latitude = latitude.fromUIntE7Coordinates(),
        altitude = altitude.toDouble(),
        name = name,
        type = type
    ),
    activityId = activityId,
    country = country,
    locality = locality,
    watchEnabled = true,
    key = null,
    remoteSyncErrorCode = null,
    watchSyncErrorCode = null
)

fun POI.toWatchData() = MdsPOI(
    type = point.type ?: 0,
    latitude = point.latitude.toE7Coordinates().toLong(),
    longitude = point.longitude.toE7Coordinates().toLong(),
    name = point.name ?: "",
    activityId = activityId,
    altitude = point.altitude?.toFloat() ?: 0f, // fixme 0m as default altitude is terrible. plan something with ng1
    country = country,
    creation = creation,
    modified = modified,
    locality = locality
)
