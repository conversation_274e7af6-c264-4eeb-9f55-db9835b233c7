package com.stt.android.datasource.explore.pois

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.datasource.explore.pois.POIWatchDataSource.Companion.RESULT_CODE_INTERNAL_SERVER_ERROR
import com.stt.android.datasource.explore.pois.POIWatchDataSource.Companion.RESULT_CODE_NOT_FOUND
import com.stt.android.datasource.explore.pois.POIWatchDataSource.Companion.RESULT_CODE_WATCH_FULL
import com.suunto.connectivity.poi.MdsPOI
import com.suunto.connectivity.poi.POIMdsException
import com.suunto.connectivity.poi.POIWatchAPI

/**
 * This class intent is to look like the MDS wrapper, caching the index list and invalidating it
 * when necessary.
 * POIs are saved in a runtime list.
 */
class TestPOIWatchAPI : POIWatchAPI {

    var maxSize = 250

    private val _pois: MutableList<MdsPOI> = mutableListOf()

    override suspend fun getCreationIndexedList(serial: String): List<Long> = _pois.map { it.creation }

    override suspend fun getPOIAt(serial: String, index: Int): MdsPOI = _pois.getOrElse(index) {
        failWith(RESULT_CODE_NOT_FOUND)
    }

    override suspend fun addPOI(serial: String, mdsPOI: MdsPOI) {
        if (_pois.size >= maxSize) failWith(RESULT_CODE_WATCH_FULL)
        if (!_pois.add(mdsPOI)) {
            failWith(RESULT_CODE_INTERNAL_SERVER_ERROR)
        }
    }

    override suspend fun removePOIAt(serial: String, index: Int): Boolean {
        return runSuspendCatching {
            _pois.removeAt(index)
        }.fold(
            onSuccess = { true },
            onFailure = { failWith(RESULT_CODE_NOT_FOUND) }
        )
    }

    override suspend fun editPOIAt(serial: String, index: Int, mdsPOI: MdsPOI) {
        runSuspendCatching { _pois.set(index, mdsPOI) }
            .onFailure { failWith(RESULT_CODE_INTERNAL_SERVER_ERROR) }
    }

    private fun failWith(code: Int): Nothing =
        when (code) {
            RESULT_CODE_NOT_FOUND -> throw POIMdsException.MdsPOINotFoundException(-1, code)
            RESULT_CODE_INTERNAL_SERVER_ERROR -> throw POIMdsException.GenericException(RuntimeException())
            else -> throw RuntimeException()
        }
}
