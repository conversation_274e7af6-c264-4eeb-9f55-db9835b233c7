package com.stt.android.wear.ui.utils;

import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.common.api.ResultCallback;
import com.google.android.gms.wearable.DataApi;
import com.google.android.gms.wearable.DataMap;
import com.google.android.gms.wearable.PutDataMapRequest;
import com.google.android.gms.wearable.Wearable;
import com.stt.android.core.bridge.ThrowableSerializer;
import com.stt.android.core.bridge.WearConstants;
import com.stt.android.core.bridge.WearHelper;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

public class CrashCollector implements GoogleApiClient.ConnectionCallbacks {
    private static final String FILE_CRASH_REPORT = "crash_report";
    private static CrashCollector instance;
    private final Context context;
    private final GoogleApiClient googleApiClient;
    private Thread.UncaughtExceptionHandler defaultExceptionHandler;
    private final Thread.UncaughtExceptionHandler handler = new Thread.UncaughtExceptionHandler() {
        @Override
        public void uncaughtException(Thread thread, Throwable ex) {
            onExceptionReported(thread, ex);

            if (defaultExceptionHandler != null) {
                defaultExceptionHandler.uncaughtException(thread, ex);
            }
        }
    };

    private CrashCollector(Context context) {
        this.context = context;

        googleApiClient = new GoogleApiClient.Builder(context).addConnectionCallbacks(this)
            .addApi(Wearable.API)
            .build();
        googleApiClient.connect();
    }

    public static void start(Context context) {
        if (instance == null) {
            synchronized (CrashCollector.class) {
                if (instance == null) {
                    instance = new CrashCollector(context.getApplicationContext());

                    instance.defaultExceptionHandler = Thread.getDefaultUncaughtExceptionHandler();
                    Thread.setDefaultUncaughtExceptionHandler(instance.handler);
                }
            }
        }
    }

    @Override
    public void onConnected(Bundle bundle) {
        sendException();
    }

    @Override
    public void onConnectionSuspended(int cause) {
        // do nothing
    }

    private void onExceptionReported(Thread thread, Throwable ex) {
        String threadName = thread.getName();
        byte[] serializedThrowable = ThrowableSerializer.serializeThrowable(ex);
        storeException(threadName, serializedThrowable);

        if (!googleApiClient.isConnected()) {
            return;
        }

        sendException();
    }

    private void storeException(String threadName, byte[] serializedThrowable) {
        DataOutputStream os = null;
        try {
            os = new DataOutputStream(
                new BufferedOutputStream(new FileOutputStream(getCrashReportFile())));
            os.writeUTF(threadName);
            os.writeInt(serializedThrowable.length);
            os.write(serializedThrowable, 0, serializedThrowable.length);
            os.flush();
        } catch (IOException e) {
            // do nothing
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    // not much we can do here :(
                }
            }
        }
    }

    private File getCrashReportFile() {
        return context.getFileStreamPath(FILE_CRASH_REPORT);
    }

    private void sendException() {
        String threadName;
        byte[] serializedThrowable;
        DataInputStream is = null;
        try {
            is = new DataInputStream(
                new BufferedInputStream(new FileInputStream(getCrashReportFile())));
            threadName = is.readUTF();
            serializedThrowable = new byte[is.readInt()];
            is.read(serializedThrowable);
        } catch (IOException e) {
            getCrashReportFile().delete();
            return;
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    // not much we can do here :(
                }
            }
        }

        PutDataMapRequest putDataMapRequest =
            PutDataMapRequest.create(WearConstants.PATH_EXCEPTION);
        DataMap dataMap = putDataMapRequest.getDataMap();
        dataMap.putString(WearConstants.KEY_DEVICE_MODEL, Build.MODEL);
        dataMap.putString(WearConstants.KEY_DEVICE_MANUFACTURER, Build.MANUFACTURER);
        dataMap.putString(WearConstants.KEY_THREAD_NAME, threadName);
        dataMap.putByteArray(WearConstants.KEY_SERIALIZED_THROWABLE, serializedThrowable);

        // the data item shall be deleted by phone
        WearHelper.sendDataItem(googleApiClient, putDataMapRequest)
            .setResultCallback(new ResultCallback<DataApi.DataItemResult>() {
                @Override
                public void onResult(DataApi.DataItemResult dataItemResult) {
                    if (dataItemResult.getStatus().isSuccess()) {
                        getCrashReportFile().delete();
                    }
                }
            });
    }
}
