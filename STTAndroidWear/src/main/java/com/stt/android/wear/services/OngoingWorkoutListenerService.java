package com.stt.android.wear.services;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.net.Uri;
import androidx.annotation.StringRes;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import com.google.android.gms.common.data.FreezableUtils;
import com.google.android.gms.wearable.DataEvent;
import com.google.android.gms.wearable.DataEventBuffer;
import com.google.android.gms.wearable.DataItem;
import com.google.android.gms.wearable.DataMap;
import com.google.android.gms.wearable.DataMapItem;
import com.google.android.gms.wearable.WearableListenerService;
import com.stt.android.core.R;
import com.stt.android.core.bridge.WearConstants;
import com.stt.android.wear.ui.activities.MainActivity;
import java.util.ArrayList;

public class OngoingWorkoutListenerService extends WearableListenerService {
    private static final String CHANNEL_ID = "activity_recording";

    private static final int NOTIFICATION_ID = 1;

    private static PendingIntent getBasePendingIntent(Context context) {
        return PendingIntent.getActivity(context, 0, MainActivity.newStartIntent(context),
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
    }

    private static NotificationCompat.Builder createBaseNotificationBuilder(Context context,
        PendingIntent pendingIntent, @StringRes int contentText) {

        createNotificationChannel(context);

        Resources resources = context.getResources();
        return new NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentIntent(pendingIntent)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(resources.getString(R.string.sports_tracker))
            .setContentText(resources.getString(contentText))
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH);
    }

    @Override
    public void onDataChanged(DataEventBuffer dataEvents) {
        ArrayList<DataEvent> events = FreezableUtils.freezeIterable(dataEvents);
        dataEvents.release();
        for (DataEvent dataEvent : events) {
            DataItem dataItem = dataEvent.getDataItem();
            Uri dataItemUri = dataItem.getUri();
            String path = dataItemUri.getPath();
            switch (dataEvent.getType()) {
                case DataEvent.TYPE_CHANGED:
                    if (WearConstants.ONGOING_WORKOUT_RECORDING_STATE.equals(path)) {
                        DataMap dataMap = DataMapItem.fromDataItem(dataItem).getDataMap();
                        byte action = dataMap.getByte(WearConstants.RECORDING_STATE, (byte) -1);
                        switch (action) {
                            case WearConstants.WARMED_UP:
                                startActivity(MainActivity.newStartIntent(this)
                                    .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK));
                                break;
                            case WearConstants.WARM_UP_STOPPED:
                                // do nothing
                                break;
                            case WearConstants.STARTED_OR_RESUMED:
                                showRecordingStartedOrResumedNotification();
                                break;
                            case WearConstants.PAUSED:
                                showRecordingPausedNotification();
                                break;
                            case WearConstants.AUTO_PAUSED:
                                showRecordingAutoPausedNotification();
                                break;
                            case WearConstants.STOPPED:
                                cancelRecordingNotification();
                                break;
                        }
                    }
                    break;
                case DataEvent.TYPE_DELETED:
                    if (WearConstants.ONGOING_WORKOUT_RECORDING_STATE.equals(path)) {
                        cancelRecordingNotification();
                    }
                    break;
            }
        }
    }

    private void showRecordingStartedOrResumedNotification() {
        NotificationManagerCompat nm = NotificationManagerCompat.from(this);
        nm.notify(NOTIFICATION_ID,
            createBaseNotificationBuilder(this, getBasePendingIntent(this),
                R.string.recording).build());
    }

    private void showRecordingPausedNotification() {
        NotificationManagerCompat nm = NotificationManagerCompat.from(this);

        nm.notify(NOTIFICATION_ID,
            createBaseNotificationBuilder(this, getBasePendingIntent(this),
                R.string.paused).build());
    }

    private void showRecordingAutoPausedNotification() {
        NotificationManagerCompat nm = NotificationManagerCompat.from(this);
        nm.notify(NOTIFICATION_ID,
            createBaseNotificationBuilder(this, getBasePendingIntent(this),
                R.string.auto_paused).build());
    }

    private void cancelRecordingNotification() {
        NotificationManagerCompat nm = NotificationManagerCompat.from(this);
        nm.cancel(NOTIFICATION_ID);
    }

    private static void createNotificationChannel(Context context) {
        NotificationManagerCompat nm = NotificationManagerCompat.from(context);
        nm.createNotificationChannel(
            new NotificationChannel(
                CHANNEL_ID,
                context.getString(R.string.notification_channel_activity_recording),
                NotificationManager.IMPORTANCE_DEFAULT
            )
        );
    }
}
