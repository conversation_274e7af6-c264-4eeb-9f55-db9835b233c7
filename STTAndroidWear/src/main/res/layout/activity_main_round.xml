<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/time"
            style="@style/Text.Label"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="2dp"
            android:textSize="@dimen/text_size_16dp"
            tools:text="12:34" />

        <android.support.wearable.view.GridViewPager
            android:id="@+id/pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/battery"
            style="@style/Text.Label"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="2dp"
            android:textSize="@dimen/text_size_16dp"
            tools:drawableStart="@drawable/battery_indicator_2"
            tools:text="56%" />

        <android.support.wearable.view.DotsPageIndicator
            android:id="@+id/page_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|bottom" />
    </LinearLayout>

    <com.stt.android.wear.ui.components.LapInfoView
        android:id="@+id/lap_info_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        android:visibility="invisible" />
</FrameLayout>
