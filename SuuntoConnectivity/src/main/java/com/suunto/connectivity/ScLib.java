package com.suunto.connectivity;

import android.net.Uri;
import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import com.suunto.connectivity.notifications.NotificationsSettings;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import java.io.File;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Collection;
import java.util.List;
import rx.Completable;
import rx.Observable;
import rx.Single;

/**
 * Suunto Connectivity Library interface
 */
public interface ScLib {

    String SPARTAN_SYNC_RESULT_AVAILABLE_ACTION =
        "com.suunto.connectivity.SpartanSyncResultAvailable";
    String SPARTAN_SYNC_RESULT_EXTRA_KEY = "com.suunto.connectivity.SyncResult";

    int LOG_MDS = 0;
    int LOG_WATCH_SYSTEM_EVENTS = 1;
    int LOG_SUUNTO_CONNECTIVITY = 2;
    int LOG_REPOSITORY_SERVICE = 3;
    int LOG_AMBIT3_MOVE_BINS = 4;
    int LOG_MDS_DESCRIPTORS = 5;

    @IntDef({LOG_MDS, LOG_WATCH_SYSTEM_EVENTS, LOG_SUUNTO_CONNECTIVITY, LOG_REPOSITORY_SERVICE, LOG_AMBIT3_MOVE_BINS})
    @Retention(RetentionPolicy.SOURCE)
    @interface LogType {}

    /**
     * Converts SuuntoBtDevice to a Spartan device or throws IllegalArgumentException
     * if given device is not proper Spartan device
     *
     * @param suuntoBtDevice SuuntoBtDevice to convert
     * @return Spartan device
     */
    Spartan toSpartan(SuuntoBtDevice suuntoBtDevice);

    /**
     * Gets a list of SuuntoBtDevices available in Android os. For a BT device to
     * be in this list it must be in BluetoothAdapter bonded devices list.
     * In case BT is turned off, empty list is returned.
     *
     * @return List of currently available SuuntoBtDevices
     */
    List<SuuntoBtDevice> getAvailableBondedDevices();

    /**
     * Gets a list of SuuntoBtDevices available in the repository.
     * Even if the bluetooth is off or device is currently not connected it might still make it
     * to this list if it was ever paired before.
     *
     * This should enable you to work with devices in offline mode
     *
     * @return List of currently available SuuntoBtDevices in the repository
     */
    Single<Collection<Spartan>> getAvailableDevices();

    /**
     * Get notififications settings object.
     *
     * @return Notifications settings.
     */
    NotificationsSettings getNotificationSettings();

    /**
     * @return a {@link Single} that emits {@link SuuntoBtDevice} if we know about it otherwise
     * an error event ({@link DeviceNotFoundException}) is emitted
     */
    Single<SuuntoBtDevice> getDevice(String macAddress);

    /**
     * Starts logging to files which will be in directory given as a parameter
     *
     * @param directory Directory in which the files should be created
     * @return Completable which completes if logging was started successfully
     */
    Completable startLoggingToFile(@NonNull File directory);

    /**
     * Stops logging to files
     *
     * @return Observable which emits all files that were created during this logging
     */
    Observable<File> stopLoggingToFile();

    /**
     * Gets a snapshot of logs of the given type as file.
     *
     * @param logType Type of log to get
     * @return Observable which emits the log file or an error
     */
    Observable<File> getLogs(@LogType int logType);

    /**
     * Activate/Disable OTA update on connectivity based on FT status
     *
     */
    void setOTAUpdate();

    /**
     * Import workout from file.
     *
     * @param workoutFileUri
     * @return
     */
    public Completable importWorkoutFromFile(Uri workoutFileUri);

    /**
     * @return File where asko user settings can be saved to access from connectivity process
     * fixme refactor to use room db instead
     */
    File userSettingsToWatchFile();
}
