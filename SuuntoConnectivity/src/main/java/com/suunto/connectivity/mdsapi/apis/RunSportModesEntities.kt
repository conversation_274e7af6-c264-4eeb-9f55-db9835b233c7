package com.suunto.connectivity.mdsapi.apis

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import com.suunto.connectivity.mdsapi.annotations.MdsQuery
import com.suunto.connectivity.mdsapi.annotations.MdsResponse
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.runsportmodes.entities.SportHeaderEntity
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

// ==================== Query Classes ====================

@MdsQuery(messageType = SuuntoRepositoryService.MSG_GET_ALL_SPORTS_V2)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetAllSportsQueryV2(
    override val macAddress: String,
    val withSportTags: Boolean,
) : MdsQuery

// ==================== Response Classes ====================

@MdsResponse
@JsonClass(generateAdapter = true)
@Parcelize
data class GetAllSportsResponseV2(
    override val success: Boolean,
    override val data: GetAllSportsResponse?,
    override val error: String? = null,
    override val statusCode: Int? = null
) : MdsResponse<GetAllSportsResponse>

@Parcelize
data class GetAllSportsResponse(
    val arrayData: List<SportHeaderEntity>
) : Parcelable
