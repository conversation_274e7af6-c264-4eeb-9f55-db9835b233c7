package com.suunto.connectivity.mdsapi.apis

import android.os.Parcelable
import com.squareup.moshi.JsonClass
import com.suunto.connectivity.mdsapi.annotations.MdsQuery
import com.suunto.connectivity.mdsapi.annotations.MdsResponse
import kotlinx.parcelize.Parcelize

// ==================== Example New Query/Response Classes ====================
// This demonstrates how to use the new annotation system

/**
 * Example Query class using the new @MdsQuery annotation
 * MessageType will be auto-generated since not specified
 */
@MdsQuery  // No messageType specified - will be auto-generated
@JsonClass(generateAdapter = true)
@Parcelize
data class GetDeviceInfoQueryV2(
    override val macAddress: String,
    val includeDetails: Boolean = false
) : MdsQuery

/**
 * Example Response class using the new @MdsResponse annotation
 */
@MdsResponse
@JsonClass(generateAdapter = true)
@Parcelize
data class GetDeviceInfoResponseV2(
    override val success: Boolean,
    override val data: DeviceInfo?,
    override val error: String? = null,
    override val statusCode: Int? = null
) : MdsResponse<DeviceInfo>

/**
 * Data class for device information
 */
@Parcelize
data class DeviceInfo(
    val deviceName: String,
    val firmwareVersion: String,
    val batteryLevel: Int,
    val serialNumber: String
) : Parcelable

/**
 * Another example with custom messageType
 */
@MdsQuery(messageType = 15001)  // Custom messageType specified
@JsonClass(generateAdapter = true)
@Parcelize
data class GetBatteryStatusQueryV2(
    override val macAddress: String
) : MdsQuery

@MdsResponse
@JsonClass(generateAdapter = true)
@Parcelize
data class GetBatteryStatusResponseV2(
    override val success: Boolean,
    override val data: BatteryStatus?,
    override val error: String? = null,
    override val statusCode: Int? = null
) : MdsResponse<BatteryStatus>

@Parcelize
data class BatteryStatus(
    val level: Int,
    val isCharging: Boolean,
    val estimatedTimeRemaining: Long?
) : Parcelable
