package com.suunto.connectivity.mdsapi

import com.movesense.mds.MdsException

/**
 * Generic response wrapper for MDS API calls
 */
sealed class MdsResponse<out T> {
    data class Success<T>(val data: T, val statusCode: Int = 200) : MdsResponse<T>()
    data class Error(val exception: Throwable, val statusCode: Int? = null) : MdsResponse<Nothing>()
    
    val isSuccess: Boolean get() = this is Success
    val isError: <PERSON>olean get() = this is Error
    
    fun getOrNull(): T? = when (this) {
        is Success -> data
        is Error -> null
    }
    
    fun getOrThrow(): T = when (this) {
        is Success -> data
        is Error -> throw exception
    }
    
    inline fun <R> map(transform: (T) -> R): MdsResponse<R> = when (this) {
        is Success -> Success(transform(data), statusCode)
        is Error -> this
    }
    
    inline fun onSuccess(action: (T) -> Unit): MdsResponse<T> {
        if (this is Success) action(data)
        return this
    }
    
    inline fun onError(action: (Throwable) -> Unit): MdsResponse<T> {
        if (this is Error) action(exception)
        return this
    }
    
    companion object {
        fun <T> success(data: T, statusCode: Int = 200): MdsResponse<T> = Success(data, statusCode)
        
        fun error(exception: Throwable, statusCode: Int? = null): MdsResponse<Nothing> = 
            Error(exception, statusCode)
        
        fun error(message: String, statusCode: Int? = null): MdsResponse<Nothing> = 
            Error(RuntimeException(message), statusCode)
        
        fun fromMdsException(exception: MdsException): MdsResponse<Nothing> = 
            Error(exception, exception.statusCode)
    }
}

/**
 * MDS specific error codes
 */
object MdsErrorCodes {
    const val OK = 200
    const val CONTINUE = 100
    const val BAD_REQUEST = 400
    const val NOT_FOUND = 404
    const val CONFLICT = 409
    const val INTERNAL_SERVER_ERROR = 500
}
