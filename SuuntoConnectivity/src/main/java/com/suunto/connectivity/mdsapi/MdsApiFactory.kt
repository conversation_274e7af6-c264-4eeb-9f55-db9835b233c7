package com.suunto.connectivity.mdsapi

import com.squareup.moshi.Moshi
import com.suunto.connectivity.sdsmanager.MdsRx
import java.lang.reflect.Proxy
import java.util.concurrent.ConcurrentHashMap

/**
 * Factory for creating MDS API implementations using dynamic proxy
 * Similar to Retrofit's design pattern
 */
class MdsApiFactory private constructor(
    private val mdsRx: MdsRx,
    private val moshi: Moshi,
    private val baseUrl: String = "",
    private val defaultTimeout: Long = 30000L
) {
    
    private val serviceCache = ConcurrentHashMap<Class<*>, Any>()
    
    /**
     * Create an implementation of the API endpoints defined by the service interface
     */
    @Suppress("UNCHECKED_CAST")
    fun <T> create(service: Class<T>): T {
        validateServiceInterface(service)
        
        return serviceCache.getOrPut(service) {
            val invocationHandler = MdsApiInvocationHandler(
                service = service,
                mdsRx = mdsRx,
                moshi = moshi,
                baseUrl = baseUrl,
                defaultTimeout = defaultTimeout
            )
            
            Proxy.newProxyInstance(
                service.classLoader,
                arrayOf(service),
                invocationHandler
            )
        } as T
    }
    
    /**
     * Create an implementation of the API endpoints defined by the service interface (inline version)
     */
    inline fun <reified T> create(): T = create(T::class.java)
    
    private fun validateServiceInterface(service: Class<*>) {
        require(service.isInterface) {
            "API declarations must be interfaces."
        }
        
        require(service.interfaces.isEmpty()) {
            "API interfaces must not extend other interfaces."
        }
    }
    
    /**
     * Builder for MdsApiFactory
     */
    class Builder {
        private var mdsRx: MdsRx? = null
        private var moshi: Moshi? = null
        private var baseUrl: String = ""
        private var defaultTimeout: Long = 30000L
        
        fun mdsRx(mdsRx: MdsRx) = apply {
            this.mdsRx = mdsRx
        }
        
        fun moshi(moshi: Moshi) = apply {
            this.moshi = moshi
        }
        
        fun baseUrl(baseUrl: String) = apply {
            this.baseUrl = baseUrl
        }
        
        fun defaultTimeout(timeout: Long) = apply {
            this.defaultTimeout = timeout
        }
        
        fun build(): MdsApiFactory {
            val mdsRx = checkNotNull(this.mdsRx) { "MdsRx is required" }
            val moshi = checkNotNull(this.moshi) { "Moshi is required" }
            
            return MdsApiFactory(
                mdsRx = mdsRx,
                moshi = moshi,
                baseUrl = baseUrl,
                defaultTimeout = defaultTimeout
            )
        }
    }
    
    companion object {
        /**
         * Create a new builder for MdsApiFactory
         */
        fun builder(): Builder = Builder()
    }
}
