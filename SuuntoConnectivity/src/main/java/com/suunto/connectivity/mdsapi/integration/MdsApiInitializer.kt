package com.suunto.connectivity.mdsapi.integration

import android.content.Context
import androidx.startup.Initializer
import com.squareup.moshi.Moshi
import com.suunto.connectivity.mdsapi.MdsApiFactory
import com.suunto.connectivity.sdsmanager.MdsRx
import timber.log.Timber

/**
 * Initializer for MDS API framework using AndroidX Startup
 */
class MdsApiInitializer : Initializer<MdsApiIntegration> {
    
    override fun create(context: Context): MdsApiIntegration {
        Timber.d("Initializing MDS API framework")
        
        // Initialize auto-registration
        MdsApiAutoRegistration.initializeAutoRegistration()
        
        // Return the singleton instance
        return MdsApiIntegration.getInstance()
    }
    
    override fun dependencies(): List<Class<out Initializer<*>>> {
        // No dependencies for now
        return emptyList()
    }
}

/**
 * Manual initialization for cases where AndroidX Startup is not available
 */
object MdsApiManualInitializer {
    
    private var isInitialized = false
    
    /**
     * Initialize the MDS API framework manually
     */
    fun initialize(context: Context, mdsRx: MdsRx? = null, moshi: Moshi? = null) {
        if (isInitialized) {
            Timber.d("MDS API framework already initialized")
            return
        }
        
        Timber.d("Manually initializing MDS API framework")
        
        // Initialize auto-registration
        MdsApiAutoRegistration.initializeAutoRegistration()
        
        // Initialize the integration if MdsRx and Moshi are provided
        if (mdsRx != null && moshi != null) {
            val integration = MdsApiIntegration.getInstance()
            integration.initialize(mdsRx, moshi)
        }
        
        isInitialized = true
        Timber.d("MDS API framework initialization completed")
    }
    
    /**
     * Check if the framework is initialized
     */
    fun isInitialized(): Boolean = isInitialized
    
    /**
     * Reset initialization state (mainly for testing)
     */
    fun reset() {
        isInitialized = false
        MdsApiAutoRegistration.clearRegistrations()
    }
}
