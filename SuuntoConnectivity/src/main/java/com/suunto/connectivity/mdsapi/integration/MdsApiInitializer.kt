package com.suunto.connectivity.mdsapi.integration

import android.content.Context
import androidx.startup.Initializer
import com.squareup.moshi.Moshi
import com.suunto.connectivity.mdsapi.MdsApiFactory
import com.suunto.connectivity.sdsmanager.MdsRx
import timber.log.Timber

/**
 * Initializer for MDS API framework using AndroidX Startup
 */
class MdsApiInitializer : Initializer<MdsApiIntegration> {
    
    override fun create(context: Context): MdsApiIntegration {
        Timber.d("Initializing MDS API framework")
        
        // Initialize auto-registration
        MdsApiAutoRegistration.initializeAutoRegistration()
        
        // Return the singleton instance
        return MdsApiIntegration.getInstance()
    }
    
    override fun dependencies(): List<Class<out Initializer<*>>> {
        // No dependencies for now
        return emptyList()
    }
}

/**
 * Manual initialization for cases where AndroidX Startup is not available
 */
object MdsApiManualInitializer {
    
    private var isInitialized = false
    
    /**
     * Initialize the MDS API framework manually
     */
    fun initialize(context: Context, mdsRx: MdsRx? = null, moshi: Moshi? = null) {
        if (isInitialized) {
            Timber.d("MDS API framework already initialized")
            return
        }
        
        Timber.d("Manually initializing MDS API framework")

        // Initialize auto-registration
        MdsApiAutoRegistration.initializeAutoRegistration()

        // Force load generated classes to trigger auto-registration
        forceLoadGeneratedClasses()

        // Initialize the integration if MdsRx and Moshi are provided
        if (mdsRx != null && moshi != null) {
            val integration = MdsApiIntegration.getInstance()
            integration.initialize(mdsRx, moshi)
        }

        isInitialized = true
        Timber.d("MDS API framework initialization completed")
        Timber.d("Registered ${MdsApiAutoRegistration.getRegisteredConsumerClasses().size} consumers")
        Timber.d("Registered ${MdsApiAutoRegistration.getRegisteredProducerClasses().size} producers")
    }
    
    /**
     * Check if the framework is initialized
     */
    fun isInitialized(): Boolean = isInitialized
    
    /**
     * Force load generated classes to trigger their auto-registration
     */
    private fun forceLoadGeneratedClasses() {
        try {
            // Try to load known generated packages to trigger static initialization
            val generatedPackages = listOf(
                "com.suunto.connectivity.mdsapi.apis",
                "com.suunto.connectivity.mdsapi.generated"
            )

            generatedPackages.forEach { packageName ->
                try {
                    loadGeneratedClassesFromPackage(packageName)
                } catch (e: Exception) {
                    Timber.d("No generated classes found in package: $packageName")
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "Failed to force load some generated classes")
        }
    }

    /**
     * Load generated classes from a specific package
     */
    private fun loadGeneratedClassesFromPackage(packageName: String) {
        // This is a simplified approach. In a real implementation, you might use
        // classpath scanning or maintain a registry of generated classes

        // For now, we rely on the generated auto-registration objects being loaded
        // when their containing classes are first accessed
        Timber.d("Attempting to load generated classes from package: $packageName")
    }

    /**
     * Reset initialization state (mainly for testing)
     */
    fun reset() {
        isInitialized = false
        MdsApiAutoRegistration.clearRegistrations()
    }
}
