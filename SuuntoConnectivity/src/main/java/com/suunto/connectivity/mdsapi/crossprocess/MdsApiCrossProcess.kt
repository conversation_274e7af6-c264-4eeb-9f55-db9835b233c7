package com.suunto.connectivity.mdsapi.crossprocess

import android.os.Parcelable
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.Response
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

/**
 * Base interface for all MDS API queries that need to be sent across processes
 */
interface MdsApiQuery : DeviceSpecificQuery {
    val apiMethod: String
    val parameters: @RawValue Map<String, Any?>
    val returnType: String
}

/**
 * Base interface for all MDS API responses that need to be sent across processes
 */
interface MdsApiResponse : Response {
    val success: Boolean
    val data: @RawValue Any?
    val error: String?
    val statusCode: Int?
}

/**
 * Success response for MDS API calls
 */
@Parcelize
data class MdsApiSuccessResponse<T : Parcelable>(
    val result: T,
    override val statusCode: Int? = 200
) : MdsApiResponse {
    override val success: Boolean = true
    override val data: Any? = result
    override val error: String? = null
}

/**
 * Error response for MDS API calls
 */
@Parcelize
data class MdsApiErrorResponse(
    override val error: String,
    override val statusCode: Int? = null,
    val cause: String? = null
) : MdsApiResponse {
    override val success: Boolean = false
    override val data: Any? = null
}

/**
 * Registry for MDS API message types
 * This will be populated by the code generator
 */
object MdsApiMessageRegistry {
    private val messageTypeMap = mutableMapOf<String, Int>()
    private val reverseMessageTypeMap = mutableMapOf<Int, String>()
    
    fun registerMessageType(apiMethod: String, messageType: Int) {
        messageTypeMap[apiMethod] = messageType
        reverseMessageTypeMap[messageType] = apiMethod
    }
    
    fun getMessageType(apiMethod: String): Int? {
        return messageTypeMap[apiMethod]
    }
    
    fun getApiMethod(messageType: Int): String? {
        return reverseMessageTypeMap[messageType]
    }
    
    fun getAllMessageTypes(): Set<Int> {
        return reverseMessageTypeMap.keys
    }
}

/**
 * Interface for MDS API Consumer (client side)
 */
interface MdsApiConsumer {
    suspend fun <T> callApi(
        macAddress: String,
        apiMethod: String,
        parameters: Map<String, Any?>,
        returnType: Class<T>
    ): T
}

/**
 * Interface for MDS API Producer (server side)
 */
interface MdsApiProducer {
    suspend fun handleApiCall(
        apiMethod: String,
        parameters: Map<String, Any?>,
        macAddress: String
    ): Any?
}
