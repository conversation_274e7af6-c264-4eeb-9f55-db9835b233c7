package com.suunto.connectivity.mdsapi.integration

import com.squareup.moshi.Moshi
import com.suunto.connectivity.SuuntoQueryConsumer
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.mdsapi.MdsApiFactory
import com.suunto.connectivity.mdsapi.apis.RunSportModesMdsApiV2
import com.suunto.connectivity.mdsapi.crossprocess.MdsApiMessageRegistry
import com.suunto.connectivity.repository.SuuntoRepositoryClient
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.sdsmanager.MdsRx

/**
 * Integration manager for MDS API framework
 * Handles registration of consumers and producers
 */
class MdsApiIntegration {
    
    private lateinit var mdsApiFactory: MdsApiFactory
    private val registeredConsumers = mutableListOf<SuuntoQueryConsumer>()
    private val registeredProducers = mutableListOf<SuuntoResponseProducer<Response>>()
    
    /**
     * Initialize the MDS API integration
     */
    fun initialize(mdsRx: MdsRx, moshi: <PERSON><PERSON>) {
        mdsApiFactory = MdsApiFactory.builder()
            .mdsRx(mdsRx)
            .moshi(moshi)
            .build()
        
        // Register message types for all APIs
        registerMessageTypes()
    }
    
    /**
     * Register all message types with the registry
     */
    private fun registerMessageTypes() {
        // This will be called by generated code
        // For now, we'll register RunSportModesMdsApiV2 manually
        // In the future, this will be automated by KSP
        
        // Example registration (this would be generated):
        // RunSportModesMdsApiV2MessageConstants.registerMessageTypes()
    }
    
    /**
     * Register consumers with SuuntoRepositoryClient
     */
    fun registerConsumers(client: SuuntoRepositoryClient): List<SuuntoQueryConsumer> {
        val consumers = MdsApiAutoRegistration.createConsumers(client)
        registeredConsumers.addAll(consumers)
        return consumers
    }
    
    /**
     * Register producers with SuuntoRepositoryService
     */
    fun registerProducers(service: SuuntoRepositoryService): List<SuuntoResponseProducer<Response>> {
        val producers = MdsApiAutoRegistration.createProducers(service, mdsApiFactory)
        registeredProducers.addAll(producers)
        return producers
    }
    
    /**
     * Create an MDS API implementation
     */
    fun <T> createApi(apiClass: Class<T>): T {
        return mdsApiFactory.create(apiClass)
    }
    
    /**
     * Create an MDS API implementation (inline version)
     */
    inline fun <reified T> createApi(): T = createApi(T::class.java)
    
    /**
     * Get the MDS API factory
     */
    fun getMdsApiFactory(): MdsApiFactory = mdsApiFactory
    
    companion object {
        @Volatile
        private var INSTANCE: MdsApiIntegration? = null
        
        /**
         * Get the singleton instance
         */
        fun getInstance(): MdsApiIntegration {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MdsApiIntegration().also { INSTANCE = it }
            }
        }
    }
}

/**
 * Extension function to easily get MDS API implementations
 */
inline fun <reified T> SuuntoRepositoryClient.getMdsApi(): T {
    return MdsApiIntegration.getInstance().createApi()
}

/**
 * Extension function to easily get MDS API implementations from service
 */
inline fun <reified T> SuuntoRepositoryService.getMdsApi(): T {
    return MdsApiIntegration.getInstance().createApi()
}
