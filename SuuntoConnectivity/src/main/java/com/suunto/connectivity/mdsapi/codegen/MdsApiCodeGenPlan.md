# MDS API 代码生成方案

## 🎯 **目标**
使用KSP自动生成Consumer/Producer代码，实现MDS API接口的跨进程调用。

## 🏗️ **架构设计**

### **1. 通用Query/Response基类**
```kotlin
// 通用的MDS API查询基类
abstract class MdsApiQuery : Parcelable {
    abstract val apiMethod: String
    abstract val parameters: Map<String, Any?>
}

// 通用的MDS API响应基类  
abstract class MdsApiResponse : Response, Parcelable {
    abstract val success: Boolean
    abstract val data: Any?
    abstract val error: String?
}
```

### **2. KSP注解处理器**
```kotlin
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.SOURCE)
annotation class GenerateMdsConsumerProducer

// 使用示例
@GenerateMdsConsumerProducer
interface RunSportModesMdsApiV2 {
    @GET("%s/SportMode2/Sports")
    suspend fun getAllSports(
        @Serial serial: String,
        @Query("withSportTag") withSportTags: Boolean
    ): MdsResponse<GetAllSportsResponse>
}
```

### **3. 生成的Consumer（客户端）**
```kotlin
// 自动生成：RunSportModesMdsApiV2Consumer.kt
class RunSportModesMdsApiV2Consumer(
    private val suuntoRepositoryClient: SuuntoRepositoryClient
) : RunSportModesMdsApiV2 {
    
    override suspend fun getAllSports(
        serial: String, 
        withSportTags: Boolean
    ): MdsResponse<GetAllSportsResponse> {
        val query = GetAllSportsQuery(serial, withSportTags)
        val response = suuntoRepositoryClient.sendQuery(query).await()
        return response as GetAllSportsResponse
    }
    
    // 其他方法...
}

// 自动生成的Query类
data class GetAllSportsQuery(
    val serial: String,
    val withSportTags: Boolean
) : MdsApiQuery() {
    override val apiMethod = "getAllSports"
    override val parameters = mapOf(
        "serial" to serial,
        "withSportTags" to withSportTags
    )
}
```

### **4. 生成的Producer（服务端）**
```kotlin
// 自动生成：RunSportModesMdsApiV2Producer.kt
class RunSportModesMdsApiV2Producer(
    private val mdsRx: MdsRx,
    private val moshi: Moshi
) : SuuntoResponseProducer<MdsApiResponse> {
    
    private val actualApi: RunSportModesMdsApiV2 by lazy {
        // 在服务端创建真正的API实例
        MdsApiRegistryBuilder()
            .mdsRx(mdsRx)
            .moshi(moshi)
            .addApi(RunSportModesMdsApiV2::class.java)
            .build()
            .getApi(RunSportModesMdsApiV2::class.java)
    }
    
    override fun isRelated(messageType: Int): Boolean {
        return messageType in MSG_GET_ALL_SPORTS..MSG_CREATE_COMPETITION_INFO_TARGET
    }
    
    override fun provideResponseObservable(
        messageType: Int, 
        requestBundle: Bundle
    ): Observable<MdsApiResponse> {
        return when (messageType) {
            MSG_GET_ALL_SPORTS -> {
                val query = requestBundle.getParcelable<GetAllSportsQuery>(ArgumentKeys.ARG_DATA)
                handleGetAllSports(query)
            }
            // 其他方法...
        }
    }
    
    private fun handleGetAllSports(query: GetAllSportsQuery): Observable<MdsApiResponse> {
        return Single.fromCallable {
            runBlocking {
                actualApi.getAllSports(query.serial, query.withSportTags)
            }
        }.map { result ->
            GetAllSportsResponse(result.isSuccess, result.data, result.error?.message)
        }.toObservable()
    }
}
```

## 🔧 **KSP处理器实现**

### **1. 符号处理器**
```kotlin
class MdsApiSymbolProcessor(
    private val codeGenerator: CodeGenerator,
    private val logger: KSPLogger
) : SymbolProcessor {
    
    override fun process(resolver: Resolver): List<KSAnnotated> {
        val symbols = resolver.getSymbolsWithAnnotation(
            GenerateMdsConsumerProducer::class.qualifiedName!!
        )
        
        symbols.filterIsInstance<KSClassDeclaration>()
            .forEach { classDeclaration ->
                generateConsumer(classDeclaration)
                generateProducer(classDeclaration)
                generateQueries(classDeclaration)
                generateResponses(classDeclaration)
                generateMessageConstants(classDeclaration)
            }
        
        return emptyList()
    }
    
    private fun generateConsumer(classDeclaration: KSClassDeclaration) {
        // 生成Consumer代码
    }
    
    private fun generateProducer(classDeclaration: KSClassDeclaration) {
        // 生成Producer代码
    }
}
```

### **2. 方法解析**
```kotlin
data class MdsApiMethod(
    val name: String,
    val httpMethod: HttpMethod,
    val urlTemplate: String,
    val parameters: List<MdsApiParameter>,
    val returnType: KSType,
    val isSuspend: Boolean
)

data class MdsApiParameter(
    val name: String,
    val type: KSType,
    val annotation: ParameterAnnotation // @Serial, @Query, @Body等
)
```

## 📋 **实现步骤**

### **Phase 1: 基础设施**
1. ✅ 创建通用Query/Response基类
2. ✅ 定义KSP注解
3. ✅ 实现基础的符号处理器

### **Phase 2: 代码生成**
1. ✅ 实现Consumer生成器
2. ✅ 实现Producer生成器  
3. ✅ 实现Query/Response类生成器
4. ✅ 实现消息常量生成器

### **Phase 3: 集成**
1. ✅ 在SuuntoRepositoryClient中注册Consumer
2. ✅ 在SuuntoRepositoryService中注册Producer
3. ✅ 添加消息类型到MessageTypes注解
4. ✅ 更新消息处理逻辑

### **Phase 4: 测试**
1. ✅ 为RunSportModesMdsApiV2添加注解
2. ✅ 验证生成的代码
3. ✅ 测试跨进程调用
4. ✅ 性能测试

## 🎯 **优势**

1. **🔄 保持现有架构** - 完全符合Message机制
2. **🤖 自动化生成** - 减少手动编写样板代码
3. **🛡️ 类型安全** - 编译时检查
4. **📈 可扩展** - 支持任意MDS API接口
5. **🧪 易于测试** - Consumer/Producer可以独立mock

## 📝 **注意事项**

1. **序列化** - 确保所有参数和返回值都可序列化
2. **错误处理** - 保持与现有错误处理机制一致
3. **性能** - 避免过度的对象创建
4. **向后兼容** - 与现有Consumer/Producer共存

这个方案既保持了现有架构的一致性，又实现了MDS API的自动化，是最佳的解决方案。
