package com.suunto.connectivity.mdsapi.apis

import com.suunto.connectivity.mdsapi.MdsResponse
import com.suunto.connectivity.mdsapi.annotations.*
import com.suunto.connectivity.runsportmodes.*

/**
 * New MDS API interface for RunSportModes using the MDS API framework
 * This replaces the manual RunSportModesMdsApi implementation
 */
@GenerateMdsConsumerProducer
interface RunSportModesMdsApiV2 {

    /**
     * Get all sports with optional sport tags
     */
    @GET("%s/SportMode2/Sports")
    suspend fun getAllSports(
        @Serial serial: String,
        @Query("withSportTag") withSportTags: Boolean
    ): GetAllSportsResponseV2
}
