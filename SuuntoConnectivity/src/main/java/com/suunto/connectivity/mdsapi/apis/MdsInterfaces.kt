package com.suunto.connectivity.mdsapi.apis

import com.suunto.connectivity.repository.commands.Query
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.mdsapi.annotations.MdsQuery as MdsQueryAnnotation

/**
 * Base interface for MDS API Query objects
 * MessageType can be specified via @MdsQuery annotation or overridden manually
 */
interface MdsQuery : Query {
    val macAddress: String

    /**
     * Message type for this query.
     * Default implementation uses reflection to check for @MdsQuery annotation.
     * Can be overridden for custom behavior.
     */
    override val messageType: Int
        get() = getMessageTypeFromAnnotation() ?: error("MessageType not specified for ${this::class.simpleName}")

    /**
     * Get message type from @MdsQuery annotation if present
     */
    private fun getMessageTypeFromAnnotation(): Int? {
        val annotation = this::class.java.getAnnotation(MdsQueryAnnotation::class.java)
        return if (annotation != null && annotation.messageType != -1) {
            annotation.messageType
        } else {
            // Try to get from generated message types registry
            getGeneratedMessageType()
        }
    }

    /**
     * Get message type from generated registry (will be implemented by code generator)
     */
    private fun getGeneratedMessageType(): Int? {
        return try {
            val registryClass = Class.forName("com.suunto.connectivity.mdsapi.generated.MdsApiMessageTypesHelper")
            val method = registryClass.getMethod("getMessageTypeFor", String::class.java)
            method.invoke(null, this::class.simpleName) as? Int
        } catch (e: Exception) {
            null
        }
    }
}

/**
 * Base interface for MDS API Response objects
 */
interface MdsResponse<T> : Response {
    val success: Boolean
    val data: T?
    val error: String?
    val statusCode: Int?
}
