package com.suunto.connectivity.mdsapi.apis

import com.suunto.connectivity.repository.commands.Query
import com.suunto.connectivity.repository.commands.Response

/**
 * Base interface for MDS API Query objects
 */
interface MdsQuery : Query {
    val macAddress: String
}

/**
 * Base interface for MDS API Response objects
 */
interface MdsResponse<T> : Response {
    val success: Boolean
    val data: T?
    val error: String?
    val statusCode: Int?
}
