# MDS API 自动化框架

这是一套类似Retrofit的MDS接口自动化框架，用于简化MDS API的开发和维护。

## 🎯 特性

- **类似Retrofit的API设计** - 使用注解定义API接口
- **自动代码生成** - 使用KSP自动生成Consumer/Producer代码
- **类型安全** - 编译时检查，减少运行时错误
- **跨进程支持** - 自动处理Client/Server间的通信
- **错误处理** - 统一的错误处理机制
- **Flow支持** - 支持suspend函数和Flow返回类型

## 📋 快速开始

### 1. 定义API接口

```kotlin
@GenerateMdsConsumerProducer
interface MyMdsApi {
    
    @GET("%s/my/endpoint")
    suspend fun getData(
        @Serial serial: String,
        @Query("param") param: String
    ): MdsResponse<MyDataResponse>
    
    @POST("%s/my/endpoint")
    suspend fun createData(
        @Serial serial: String,
        @Body data: MyData
    ): MdsResponse<Unit>
    
    @PUT("%s/my/endpoint/{id}")
    suspend fun updateData(
        @Serial serial: String,
        @Path("id") id: Int,
        @Body data: MyData
    ): MdsResponse<Unit>
    
    @DELETE("%s/my/endpoint/{id}")
    suspend fun deleteData(
        @Serial serial: String,
        @Path("id") id: Int
    ): MdsResponse<Unit>
    
    @SUBSCRIBE("%s/my/stream")
    fun subscribeToUpdates(
        @Serial serial: String
    ): Flow<MdsResponse<MyUpdateEvent>>
}
```

### 2. 使用API

```kotlin
// 创建API实例
val factory = MdsApiFactory.builder()
    .mdsRx(mdsRx)
    .moshi(moshi)
    .build()

val api = factory.create<MyMdsApi>()

// 使用API
val response = api.getData("SERIAL123", "paramValue")
if (response.isSuccess) {
    val data = response.getOrNull()
    // 处理数据
} else {
    // 处理错误
    response.onError { error ->
        println("Error: ${error.message}")
    }
}
```

## 🔧 注解说明

### HTTP方法注解

- `@GET(url)` - GET请求
- `@POST(url)` - POST请求  
- `@PUT(url)` - PUT请求
- `@DELETE(url)` - DELETE请求
- `@SUBSCRIBE(url)` - 订阅请求

### 参数注解

- `@Serial` - 设备序列号参数（会替换URL中的%s占位符）
- `@Path("name")` - 路径参数（替换URL中的{name}占位符）
- `@Query("name")` - 查询参数（添加到URL查询字符串）
- `@Body` - 请求体参数

### 其他注解

- `@GenerateMdsConsumerProducer` - 标记接口需要生成Consumer/Producer
- `@Timeout(millis)` - 设置请求超时时间

## 📦 MdsResponse

所有API调用都返回`MdsResponse<T>`包装器：

```kotlin
sealed class MdsResponse<out T> {
    data class Success<T>(val data: T, val statusCode: Int = 200) : MdsResponse<T>()
    data class Error(val exception: Throwable, val statusCode: Int? = null) : MdsResponse<Nothing>()
    
    // 便利方法
    val isSuccess: Boolean
    val isError: Boolean
    fun getOrNull(): T?
    fun getOrThrow(): T
    fun <R> map(transform: (T) -> R): MdsResponse<R>
    fun onSuccess(action: (T) -> Unit): MdsResponse<T>
    fun onError(action: (Throwable) -> Unit): MdsResponse<T>
}
```

### 使用模式

```kotlin
// 模式1: 使用getOrNull()
val data = response.getOrNull()
if (data != null) {
    // 处理成功情况
}

// 模式2: 使用onSuccess/onError
response
    .onSuccess { data -> 
        // 处理成功
    }
    .onError { error -> 
        // 处理错误
    }

// 模式3: 使用getOrThrow()
try {
    val data = response.getOrThrow()
    // 处理数据
} catch (e: Exception) {
    // 处理异常
}

// 模式4: 使用map转换
val count = response.map { it.items.size }.getOrNull() ?: 0
```

## 🏗️ 架构设计

### 客户端 (Client)
```
RunSportModeWatchApi -> MdsApiFactory -> RunSportModesMdsApiV2 -> Dynamic Proxy -> MdsRx
```

### 服务端 (Server)  
```
SuuntoRepositoryService -> Generated Producer -> Generated Consumer -> MdsApiFactory -> MdsRx
```

### 跨进程通信
```
Client: API Call -> Query -> Message -> IPC
Server: Message -> Query -> API Call -> Response -> Message -> IPC  
Client: Message -> Response -> API Result
```

## 🔄 代码生成

KSP会自动生成以下代码：

1. **Consumer类** - 客户端使用，实现跨进程调用
2. **Producer类** - 服务端使用，处理跨进程请求
3. **Query类** - 跨进程传输的请求数据
4. **Response类** - 跨进程传输的响应数据
5. **消息常量** - 用于IPC的消息类型定义

## 📝 最佳实践

### 1. 接口设计
- 使用清晰的方法名
- 合理使用参数注解
- 返回类型使用MdsResponse包装
- 对于可能失败的操作，考虑使用适当的错误处理

### 2. 错误处理
- 总是检查MdsResponse的成功状态
- 使用适当的错误处理模式
- 记录错误信息用于调试

### 3. 性能考虑
- 避免频繁的API调用
- 使用适当的超时设置
- 考虑使用Flow进行流式数据处理

## 🧪 测试

框架提供了测试工具类：

```kotlin
val test = MdsApiFrameworkTest()
test.runAllTests(mdsRx, moshi)
```

## 📚 示例

查看以下文件获取更多示例：
- `MdsApiUsageExample.kt` - 基本使用示例
- `MdsApiFrameworkTest.kt` - 测试示例
- `RunSportModesMdsApiV2.kt` - 完整API定义示例

## 🔧 集成到现有项目

1. 在API接口上添加`@GenerateMdsConsumerProducer`注解
2. 运行KSP生成代码
3. 在SuuntoRepositoryClient/Service中注册生成的Consumer/Producer
4. 使用MdsApiFactory创建API实例

## ⚠️ 注意事项

1. 所有参数和返回值必须可序列化
2. 保持与现有错误处理机制一致
3. 避免过度的对象创建
4. 确保向后兼容性
