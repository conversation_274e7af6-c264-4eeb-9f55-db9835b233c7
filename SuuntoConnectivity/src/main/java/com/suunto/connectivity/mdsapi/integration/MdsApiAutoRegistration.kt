package com.suunto.connectivity.mdsapi.integration

import com.suunto.connectivity.SuuntoQueryConsumer
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.mdsapi.MdsApiFactory
import com.suunto.connectivity.repository.SuuntoRepositoryClient
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.commands.Response
import timber.log.Timber

/**
 * Auto-registration system for MDS API generated Consumer/Producer classes
 */
object MdsApiAutoRegistration {
    
    private val registeredConsumerClasses = mutableSetOf<Class<out SuuntoQueryConsumer>>()
    private val registeredProducerClasses = mutableSetOf<Class<out SuuntoResponseProducer<Response>>>()
    
    /**
     * Register a Consumer class for auto-instantiation
     */
    fun registerConsumerClass(consumerClass: Class<out SuuntoQueryConsumer>) {
        registeredConsumerClasses.add(consumerClass)
        Timber.d("Registered MDS API Consumer: ${consumerClass.simpleName}")
    }
    
    /**
     * Register a Producer class for auto-instantiation
     */
    fun registerProducerClass(producerClass: Class<out SuuntoResponseProducer<Response>>) {
        registeredProducerClasses.add(producerClass)
        Timber.d("Registered MDS API Producer: ${producerClass.simpleName}")
    }
    
    /**
     * Create and return all registered Consumer instances for the given client
     */
    fun createConsumers(client: SuuntoRepositoryClient): List<SuuntoQueryConsumer> {
        val consumers = mutableListOf<SuuntoQueryConsumer>()
        
        registeredConsumerClasses.forEach { consumerClass ->
            try {
                // Try to find constructor that takes SuuntoRepositoryClient
                val constructor = consumerClass.getConstructor(SuuntoRepositoryClient::class.java)
                val consumer = constructor.newInstance(client)
                consumers.add(consumer)
                Timber.d("Created MDS API Consumer: ${consumerClass.simpleName}")
            } catch (e: Exception) {
                Timber.e(e, "Failed to create MDS API Consumer: ${consumerClass.simpleName}")
            }
        }
        
        return consumers
    }
    
    /**
     * Create and return all registered Producer instances for the given service
     */
    fun createProducers(
        service: SuuntoRepositoryService,
        mdsApiFactory: MdsApiFactory
    ): List<SuuntoResponseProducer<Response>> {
        val producers = mutableListOf<SuuntoResponseProducer<Response>>()
        
        registeredProducerClasses.forEach { producerClass ->
            try {
                // Try to find constructor that takes SuuntoRepositoryService and MdsApiFactory
                val constructor = producerClass.getConstructor(
                    SuuntoRepositoryService::class.java,
                    MdsApiFactory::class.java
                )
                val producer = constructor.newInstance(service, mdsApiFactory)
                producers.add(producer)
                Timber.d("Created MDS API Producer: ${producerClass.simpleName}")
            } catch (e: Exception) {
                Timber.e(e, "Failed to create MDS API Producer: ${producerClass.simpleName}")
            }
        }
        
        return producers
    }
    
    /**
     * Get all registered Consumer classes
     */
    fun getRegisteredConsumerClasses(): Set<Class<out SuuntoQueryConsumer>> {
        return registeredConsumerClasses.toSet()
    }
    
    /**
     * Get all registered Producer classes
     */
    fun getRegisteredProducerClasses(): Set<Class<out SuuntoResponseProducer<Response>>> {
        return registeredProducerClasses.toSet()
    }
    
    /**
     * Clear all registrations (mainly for testing)
     */
    fun clearRegistrations() {
        registeredConsumerClasses.clear()
        registeredProducerClasses.clear()
        Timber.d("Cleared all MDS API registrations")
    }
    
    /**
     * Initialize auto-registration by scanning for generated classes
     * This method should be called during application startup
     */
    fun initializeAutoRegistration() {
        try {
            // This would be called by generated code during static initialization
            // For now, we'll register known classes manually
            
            // Example of how generated code would register itself:
            // registerConsumerClass(RunSportModesMdsApiV2Consumer::class.java)
            // registerProducerClass(RunSportModesMdsApiV2Producer::class.java)
            
            Timber.d("MDS API auto-registration initialized")
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize MDS API auto-registration")
        }
    }
}

/**
 * Extension function to easily register MDS API consumers with SuuntoRepositoryClient
 */
fun SuuntoRepositoryClient.registerMdsApiConsumers(): List<SuuntoQueryConsumer> {
    return MdsApiAutoRegistration.createConsumers(this)
}

/**
 * Extension function to easily register MDS API producers with SuuntoRepositoryService
 */
fun SuuntoRepositoryService.registerMdsApiProducers(mdsApiFactory: MdsApiFactory): List<SuuntoResponseProducer<Response>> {
    return MdsApiAutoRegistration.createProducers(this, mdsApiFactory)
}
