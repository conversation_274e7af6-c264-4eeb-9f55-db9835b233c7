package com.suunto.connectivity.mdsapi.integration

import com.suunto.connectivity.SuuntoQueryConsumer
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.mdsapi.MdsApiFactory
import com.suunto.connectivity.repository.SuuntoRepositoryClient
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.commands.Response
import timber.log.Timber

/**
 * Auto-registration system for MDS API generated Consumer/Producer classes
 */
object MdsApiAutoRegistration {
    
    private val registeredConsumerClasses = mutableSetOf<Class<out SuuntoQueryConsumer>>()
    private val registeredProducerClasses = mutableSetOf<Class<out SuuntoResponseProducer<Response>>>()
    
    /**
     * Register a Consumer class for auto-instantiation
     */
    fun registerConsumerClass(consumerClass: Class<out SuuntoQueryConsumer>) {
        registeredConsumerClasses.add(consumerClass)
        Timber.d("Registered MDS API Consumer: ${consumerClass.simpleName}")
    }
    
    /**
     * Register a Producer class for auto-instantiation
     */
    fun registerProducerClass(producerClass: Class<out SuuntoResponseProducer<Response>>) {
        registeredProducerClasses.add(producerClass)
        Timber.d("Registered MDS API Producer: ${producerClass.simpleName}")
    }
    
    /**
     * Create and return all registered Consumer instances for the given client
     */
    fun createConsumers(client: SuuntoRepositoryClient): List<SuuntoQueryConsumer> {
        val consumers = mutableListOf<SuuntoQueryConsumer>()
        
        registeredConsumerClasses.forEach { consumerClass ->
            try {
                // Try to find constructor that takes SuuntoRepositoryClient
                val constructor = consumerClass.getConstructor(SuuntoRepositoryClient::class.java)
                val consumer = constructor.newInstance(client)
                consumers.add(consumer)
                Timber.d("Created MDS API Consumer: ${consumerClass.simpleName}")
            } catch (e: Exception) {
                Timber.e(e, "Failed to create MDS API Consumer: ${consumerClass.simpleName}")
            }
        }
        
        return consumers
    }
    
    /**
     * Create and return all registered Producer instances for the given service
     */
    fun createProducers(
        service: SuuntoRepositoryService,
        mdsApiFactory: MdsApiFactory
    ): List<SuuntoResponseProducer<Response>> {
        val producers = mutableListOf<SuuntoResponseProducer<Response>>()
        
        registeredProducerClasses.forEach { producerClass ->
            try {
                // Try to find constructor that takes SuuntoRepositoryService and MdsApiFactory
                val constructor = producerClass.getConstructor(
                    SuuntoRepositoryService::class.java,
                    MdsApiFactory::class.java
                )
                val producer = constructor.newInstance(service, mdsApiFactory)
                producers.add(producer)
                Timber.d("Created MDS API Producer: ${producerClass.simpleName}")
            } catch (e: Exception) {
                Timber.e(e, "Failed to create MDS API Producer: ${producerClass.simpleName}")
            }
        }
        
        return producers
    }
    
    /**
     * Get all registered Consumer classes
     */
    fun getRegisteredConsumerClasses(): Set<Class<out SuuntoQueryConsumer>> {
        return registeredConsumerClasses.toSet()
    }
    
    /**
     * Get all registered Producer classes
     */
    fun getRegisteredProducerClasses(): Set<Class<out SuuntoResponseProducer<Response>>> {
        return registeredProducerClasses.toSet()
    }
    
    /**
     * Clear all registrations (mainly for testing)
     */
    fun clearRegistrations() {
        registeredConsumerClasses.clear()
        registeredProducerClasses.clear()
        Timber.d("Cleared all MDS API registrations")
    }
    
    /**
     * Initialize auto-registration by scanning for generated classes
     * This method should be called during application startup
     */
    fun initializeAutoRegistration() {
        try {
            // Initialize generated message types
            initializeGeneratedMessageTypes()

            // Auto-discover and register generated Consumer/Producer classes
            autoDiscoverAndRegisterClasses()

            Timber.d("MDS API auto-registration initialized with ${registeredConsumerClasses.size} consumers and ${registeredProducerClasses.size} producers")
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize MDS API auto-registration")
        }
    }

    /**
     * Initialize generated message types registry
     */
    private fun initializeGeneratedMessageTypes() {
        try {
            // Try to load and initialize the generated message type registration
            val registrationClass = Class.forName("com.suunto.connectivity.mdsapi.generated.MdsApiMessageTypeRegistration")
            // The class initialization will automatically register message types
            Timber.d("Initialized generated message types")
        } catch (e: ClassNotFoundException) {
            Timber.w("No generated message types found - this is normal if no @MdsQuery classes exist")
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize generated message types")
        }
    }

    /**
     * Auto-discover and register generated Consumer/Producer classes
     */
    private fun autoDiscoverAndRegisterClasses() {
        try {
            // Scan for generated auto-registration objects
            val packageName = "com.suunto.connectivity.mdsapi"
            scanPackageForAutoRegistrationObjects(packageName)
        } catch (e: Exception) {
            Timber.e(e, "Failed to auto-discover generated classes")
        }
    }

    /**
     * Scan package for auto-registration objects
     */
    private fun scanPackageForAutoRegistrationObjects(packageName: String) {
        try {
            // This is a simplified approach - in a real implementation, you might use
            // reflection or annotation processing to discover classes

            // For now, we rely on the generated auto-registration objects to register themselves
            // during class loading. The generated code includes static initialization blocks
            // that call registerConsumerClass() and registerProducerClass()

            Timber.d("Auto-registration objects will register themselves during class loading")
        } catch (e: Exception) {
            Timber.e(e, "Failed to scan package for auto-registration objects")
        }
    }
}

/**
 * Extension function to easily register MDS API consumers with SuuntoRepositoryClient
 */
fun SuuntoRepositoryClient.registerMdsApiConsumers(): List<SuuntoQueryConsumer> {
    return MdsApiAutoRegistration.createConsumers(this)
}

/**
 * Extension function to easily register MDS API producers with SuuntoRepositoryService
 */
fun SuuntoRepositoryService.registerMdsApiProducers(mdsApiFactory: MdsApiFactory): List<SuuntoResponseProducer<Response>> {
    return MdsApiAutoRegistration.createProducers(this, mdsApiFactory)
}
