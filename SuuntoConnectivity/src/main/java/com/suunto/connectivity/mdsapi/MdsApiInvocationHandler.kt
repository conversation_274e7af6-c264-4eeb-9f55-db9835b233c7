package com.suunto.connectivity.mdsapi

import androidx.core.net.toUri
import com.movesense.mds.MdsException
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.coroutines.await
import com.suunto.connectivity.STATUS_CONTINUE
import com.suunto.connectivity.STATUS_OK
import com.suunto.connectivity.mdsapi.annotations.*
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.sdsmanager.model.MdsContent
import com.suunto.connectivity.sdsmanager.model.MdsValue
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_CONTRACT_EMPTY
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_SCHEME_PREFIX
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withTimeout
import java.lang.reflect.InvocationHandler
import java.lang.reflect.Method
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type
import java.util.Base64

/**
 * InvocationHandler for MDS API dynamic proxy
 */
class MdsApiInvocationHandler(
    private val service: Class<*>,
    private val mdsRx: MdsRx,
    private val moshi: Moshi,
    private val baseUrl: String,
    private val defaultTimeout: Long
) : InvocationHandler {
    
    private val serviceMethodCache = mutableMapOf<Method, ServiceMethod>()
    
    override fun invoke(proxy: Any?, method: Method, args: Array<out Any>?): Any? {
        // Handle Object methods
        if (method.declaringClass == Object::class.java) {
            return method.invoke(this, *(args ?: emptyArray()))
        }
        
        val serviceMethod = serviceMethodCache.getOrPut(method) {
            ServiceMethod.parse(method)
        }
        
        val call = MdsApiCall(
            serviceMethod = serviceMethod,
            args = args ?: emptyArray(),
            mdsRx = mdsRx,
            moshi = moshi,
            baseUrl = baseUrl,
            timeout = getTimeout(method)
        )

        // For suspend functions, we need to handle the continuation properly
        // This is a simplified approach - in a real implementation, we'd need to handle
        // Kotlin's continuation mechanism properly
        return when {
            serviceMethod.isSuspend -> {
                // Return a suspend lambda that can be called by the coroutine machinery
                suspend { call.execute() }
            }
            serviceMethod.isFlow -> {
                call.executeAsFlow()
            }
            else -> {
                throw IllegalArgumentException("Only suspend functions and Flow return types are supported")
            }
        }
    }
    
    private fun getTimeout(method: Method): Long {
        return method.getAnnotation(Timeout::class.java)?.value ?: defaultTimeout
    }
}

/**
 * Represents a single MDS API call
 */
private class MdsApiCall(
    private val serviceMethod: ServiceMethod,
    private val args: Array<out Any>,
    private val mdsRx: MdsRx,
    private val moshi: Moshi,
    private val baseUrl: String,
    private val timeout: Long
) {
    
    suspend fun execute(): Any? {
        return withTimeout(timeout) {
            when {
                serviceMethod.isFlow -> executeAsFlow()
                serviceMethod.isSuspend -> executeSuspend()
                else -> throw IllegalArgumentException("Only suspend functions and Flow return types are supported")
            }
        }
    }
    
    fun executeAsFlow(): Flow<Any?> = flow {
        val result = executeMdsCall()
        emit(result)
    }
    
    private suspend fun executeSuspend(): Any? {
        return executeMdsCall()
    }
    
    private suspend fun executeMdsCall(): Any? {
        val url = buildUrl()
        val contract = buildContract()
        
        return when (serviceMethod.httpMethod) {
            HttpMethod.GET -> executeGet(url, contract)
            HttpMethod.POST -> executePost(url, contract)
            HttpMethod.PUT -> executePut(url, contract)
            HttpMethod.DELETE -> executeDelete(url, contract)
            HttpMethod.SUBSCRIBE -> executeSubscribe(url, contract)
        }
    }
    
    private fun buildUrl(): String {
        var url = serviceMethod.urlTemplate
        
        // Replace path parameters
        serviceMethod.parameters.filterIsInstance<ParameterInfo.Path>().forEach { pathParam ->
            val value = args[pathParam.index].toString()
            url = url.replace("{${pathParam.name}}", value)
        }
        
        // Handle serial parameter (special case for %s placeholder)
        serviceMethod.parameters.filterIsInstance<ParameterInfo.Serial>().forEach { serialParam ->
            val serial = args[serialParam.index].toString()
            url = String.format(url, serial)
        }
        
        // Add query parameters
        val queryParams = serviceMethod.parameters.filterIsInstance<ParameterInfo.Query>()
        if (queryParams.isNotEmpty()) {
            val uri = url.toUri().buildUpon()
            queryParams.forEach { queryParam ->
                val value = args[queryParam.index]
                uri.appendQueryParameter(queryParam.name, value.toString())
            }
            url = uri.build().toString()
        }
        
        return if (url.startsWith(MDS_SCHEME_PREFIX)) url else "$MDS_SCHEME_PREFIX$url"
    }
    
    private fun buildContract(): String {
        val bodyParam = serviceMethod.parameters.filterIsInstance<ParameterInfo.Body>().firstOrNull()
        return if (bodyParam != null) {
            val bodyValue = args[bodyParam.index]
            encodeMdsValue(bodyValue)
        } else {
            MDS_CONTRACT_EMPTY
        }
    }
    
    private suspend fun executeGet(url: String, contract: String): Any? {
        val response = mdsRx[url, contract].await()
        return processResponse(response)
    }
    
    private suspend fun executePost(url: String, contract: String): Any? {
        val response = mdsRx.postWithHeader(url, contract).await()
        if (response.statusCode != STATUS_OK) {
            throw MdsException("POST request failed", response.statusCode)
        }
        return processResponse(response.body)
    }
    
    private suspend fun executePut(url: String, contract: String): Any? {
        val response = mdsRx.putWithHeader(url, contract).await()
        if (response.statusCode != STATUS_OK) {
            throw MdsException("PUT request failed", response.statusCode)
        }
        return processResponse(response.body)
    }
    
    private suspend fun executeDelete(url: String, contract: String): Any? {
        val response = mdsRx.deleteWithHeader(url, contract).await()
        if (response.statusCode != STATUS_OK) {
            throw MdsException("DELETE request failed", response.statusCode)
        }
        return processResponse(response.body)
    }
    
    private suspend fun executeSubscribe(url: String, contract: String): Any? {
        // For subscribe operations, we might need special handling
        // This is a placeholder implementation
        val response = mdsRx[url, contract].await()
        return processResponse(response)
    }
    
    private fun processResponse(responseBody: String): Any? {
        // Handle Unit return type
        if (serviceMethod.returnType == Unit::class.java || serviceMethod.returnType == Void.TYPE) {
            return Unit
        }
        
        // Handle MdsResponse wrapper
        if (isWrappedInMdsResponse()) {
            return try {
                val actualType = getActualResponseType()
                val data = if (actualType == Unit::class.java) {
                    Unit
                } else {
                    responseBody.decodeMdsResponseWrappedInContent(actualType)
                }
                MdsResponse.success(data)
            } catch (e: Exception) {
                MdsResponse.error(e)
            }
        }
        
        // Handle direct response type
        return responseBody.decodeMdsResponseWrappedInContent(serviceMethod.returnType)
    }
    
    private fun isWrappedInMdsResponse(): Boolean {
        val returnType = serviceMethod.returnType
        return when (returnType) {
            is ParameterizedType -> returnType.rawType == MdsResponse::class.java
            is Class<*> -> MdsResponse::class.java.isAssignableFrom(returnType)
            else -> false
        }
    }
    
    private fun getActualResponseType(): Type {
        val returnType = serviceMethod.returnType
        return if (returnType is ParameterizedType && returnType.rawType == MdsResponse::class.java) {
            returnType.actualTypeArguments.firstOrNull() ?: Unit::class.java
        } else {
            returnType
        }
    }
    
    private fun <T> encodeMdsValue(value: T, type: Type): String {
        // Handle special case for List<Int> (competition samples)
        if (value is List<*> && value.firstOrNull() is Int) {
            @Suppress("UNCHECKED_CAST")
            val intList = value as List<Int>
            val byteArray = intList.toLittleEndianByteArray()
            val byteStream = Base64.getEncoder().withoutPadding().encodeToString(byteArray)
            return encodeMdsValue(byteStream, String::class.java)
        }

        val mdsValueType = Types.newParameterizedType(MdsValue::class.java, type)
        val adapter = moshi.adapter<MdsValue<T>>(mdsValueType)
        return adapter.toJson(MdsValue(value))
    }

    private inline fun <reified T> encodeMdsValue(value: T): String {
        return encodeMdsValue(value, T::class.java)
    }
    
    private inline fun <reified T> String.decodeMdsResponseWrappedInContent(): T {
        return decodeMdsResponseWrappedInContent(T::class.java)
    }
    
    @Suppress("UNCHECKED_CAST")
    private fun <T> String.decodeMdsResponseWrappedInContent(type: Type): T {
        val contentType = Types.newParameterizedType(MdsContent::class.java, type)
        val adapter = moshi.adapter<MdsContent<T>>(contentType)
        return adapter.fromJson(this)?.content
            ?: throw UnsupportedOperationException("Invalid json response: $this")
    }
}

/**
 * Extension function to convert List<Int> to little-endian byte array
 */
private fun List<Int>.toLittleEndianByteArray(): ByteArray {
    val byteArray = ByteArray(this.size * 4)
    forEachIndexed { index, value ->
        val offset = index * 4
        byteArray[offset] = (value and 0xFF).toByte()
        byteArray[offset + 1] = ((value shr 8) and 0xFF).toByte()
        byteArray[offset + 2] = ((value shr 16) and 0xFF).toByte()
        byteArray[offset + 3] = ((value shr 24) and 0xFF).toByte()
    }
    return byteArray
}
