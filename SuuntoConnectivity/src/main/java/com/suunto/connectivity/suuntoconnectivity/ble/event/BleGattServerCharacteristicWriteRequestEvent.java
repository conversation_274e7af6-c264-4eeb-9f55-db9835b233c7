package com.suunto.connectivity.suuntoconnectivity.ble.event;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGattCharacteristic;

public class BleGattServerCharacteristicWriteRequestEvent extends BleGattServerEvent {

    private final int requestId;
    private final BluetoothGattCharacteristic characteristic;
    private final boolean preparedWrite;
    private final boolean responseNeeded;
    private final int offset;
    private final byte[] value;

    public BleGattServerCharacteristicWriteRequestEvent(BluetoothDevice bluetoothDevice, int requestId,
                                                        BluetoothGattCharacteristic characteristic,
                                                        boolean preparedWrite, boolean responseNeeded,
                                                        int offset, byte[] value) {
        super(bluetoothDevice);

        this.requestId = requestId;
        this.characteristic = characteristic;
        this.preparedWrite = preparedWrite;
        this.responseNeeded = responseNeeded;
        this.offset = offset;
        this.value = value;
    }

    public int getRequestId() {
        return requestId;
    }

    public BluetoothGattCharacteristic getCharacteristic() {
        return characteristic;
    }

    public boolean isPreparedWrite() {
        return preparedWrite;
    }

    public boolean isResponseNeeded() {
        return responseNeeded;
    }

    public int getOffset() {
        return offset;
    }

    public byte[] getValue() {
        return value;
    }
}
