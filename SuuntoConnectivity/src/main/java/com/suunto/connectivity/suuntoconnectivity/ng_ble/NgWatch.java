package com.suunto.connectivity.suuntoconnectivity.ng_ble;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothGattService;
import android.content.Context;
import android.os.Handler;
import androidx.annotation.NonNull;

import com.suunto.connectivity.suuntoconnectivity.DeviceHandle;
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityListener;
import com.suunto.connectivity.suuntoconnectivity.ancs.AncsNotificationProvider;
import com.suunto.connectivity.suuntoconnectivity.ble.BleCore;
import com.suunto.connectivity.suuntoconnectivity.utils.AndroidBtEnvironment;
import com.suunto.connectivity.suuntoconnectivity.utils.BtStateMonitor;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectionStateMonitor;
import com.suunto.connectivity.util.workqueue.WorkQueue;

import java.util.UUID;
import org.greenrobot.eventbus.EventBus;

import java.util.List;

import timber.log.Timber;

public class NgWatch extends GattServerDevice implements BtStateMonitor.Listener {

    public NgWatch(@NonNull Context context, @NonNull WorkQueue workQueue,
                   @NonNull BluetoothAdapter bluetoothAdapter,
                   @NonNull BtStateMonitor btStateMonitor,
                   @NonNull AndroidBtEnvironment androidBtEnvironment,
                   @NonNull EventBus eventBus,
                   @NonNull String macAddress, @NonNull DeviceHandle handle,
                   @NonNull SuuntoConnectivityListener callback,
                   @NonNull AncsNotificationProvider ancsNotificationProvider,
                   @NonNull ConnectionStateMonitor connectionStateMonitor,
                   @NonNull Handler connectivityHandler) {
        super(context, workQueue, bluetoothAdapter, btStateMonitor,
            androidBtEnvironment, eventBus, macAddress,
            handle, callback, ancsNotificationProvider, connectionStateMonitor, connectivityHandler,
            true);
    }

    @Override
    public boolean supportsANCS() {
        return false;
    }

    protected void findCharacteristics(List<BluetoothGattService> services) {
        BluetoothGattService ngService = null;
        for (BluetoothGattService service : services) {
            UUID serviceUuid = service.getUuid();

            // Compare service UUID. See definition of byteReversedUuid, why
            // we are accepting also reversed UUIDs.
            if (BleCore.NG_SERVICE_UUID.equals(serviceUuid) ||
                BleCore.NG_SERVICE_UUID.equals(byteReversedUuid(serviceUuid))) {
                ngService = service;
                break;
            }
        }

        if (ngService == null) {
            Timber.e("NG service not found");
            return;
        }

        notifyCharacteristic = ngService.getCharacteristic(BleCore.NG_NOTIFY_CHARACTERISTIC_UUID);
        writeCharacteristic = ngService.getCharacteristic(BleCore.NG_WRITE_CHARACTERISTIC_UUID);
    }
}
