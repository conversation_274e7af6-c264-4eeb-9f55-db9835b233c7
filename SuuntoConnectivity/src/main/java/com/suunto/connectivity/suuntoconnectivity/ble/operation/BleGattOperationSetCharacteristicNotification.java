package com.suunto.connectivity.suuntoconnectivity.ble.operation;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import androidx.annotation.NonNull;

import androidx.annotation.Nullable;
import com.suunto.connectivity.suuntoconnectivity.ble.BleCore;
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattDescriptorWriteEvent;
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattEvent;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.GattSetCharacteristicNotificationException;
import timber.log.Timber;

import static android.bluetooth.BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT;

public class BleGattOperationSetCharacteristicNotification extends BleGattOperation<Integer> {

    private final BluetoothGattCharacteristic characteristic;
    private final BluetoothGattDescriptor notifyDescriptor;
    private final boolean enable;
    private String timeoutLog;

    public BleGattOperationSetCharacteristicNotification(@NonNull BluetoothGatt bluetoothGatt,
        @NonNull BluetoothGattCharacteristic characteristic,
        boolean enable) {
        super(bluetoothGatt);

        this.characteristic = characteristic;
        this.notifyDescriptor = characteristic.getDescriptor(
            BleCore.CHARACTERISTIC_UPDATE_NOTIFICATION_DESCRIPTOR_UUID);
        this.enable = enable;
    }

    @SuppressLint("MissingPermission")
    @Override
    protected void protectedRun() throws Throwable {
        super.protectedRun();

        if (notifyDescriptor == null) {
            onError(new GattSetCharacteristicNotificationException("Notify descriptor null"));
            return;
        }

        if (!bluetoothGatt.get().setCharacteristicNotification(characteristic, enable)) {
            onError(new GattSetCharacteristicNotificationException("Setting characteristic notification failed"));
            return;
        }

        if (!notifyDescriptor.setValue(enable ?
            BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE :
            BluetoothGattDescriptor.DISABLE_NOTIFICATION_VALUE)) {
            onError(new GattSetCharacteristicNotificationException("Setting descriptor value failed"));
            return;
        }

        /*
         * Following workaround and comment below has been copied from RxAndroidBle library's
         * DescriptorWriteOperation.java file. See https://github.com/Polidea/RxAndroidBle
         *
         * According to the source code below Android 7.0.0 the BluetoothGatt.writeDescriptor() function used
         * writeType of the parent BluetoothCharacteristic which caused operation failure (for instance when
         * setting Client Characteristic Config). With WRITE_TYPE_DEFAULT problem did not occurred.
         * Compare:
         * https://android.googlesource.com/platform/frameworks/base/+/android-6.0.1_r74/core/java/android/bluetooth/BluetoothGatt.java#1039
         * https://android.googlesource.com/platform/frameworks/base/+/android-7.0.0_r1/core/java/android/bluetooth/BluetoothGatt.java#947
         *
         * Workaround potential writeDescriptor issues by temporarily setting write type to
         * WRITE_TYPE_DEFAULT.
         */
        final BluetoothGattCharacteristic descriptorCharacteristic = notifyDescriptor
            .getCharacteristic();
        int originalWriteType = WRITE_TYPE_DEFAULT;
        if (descriptorCharacteristic != null) {
            originalWriteType = descriptorCharacteristic.getWriteType();
            Timber.d("Characterisrics write type: %d", originalWriteType);
            descriptorCharacteristic.setWriteType(WRITE_TYPE_DEFAULT);
        }
        boolean descriptorWritten = bluetoothGatt.get().writeDescriptor(notifyDescriptor);
        if (descriptorCharacteristic != null) {
            descriptorCharacteristic.setWriteType(originalWriteType);
        }
        if (!descriptorWritten) {
            onError(new GattSetCharacteristicNotificationException("Writing descriptor failed"));
        }
    }

    @Override
    protected void handleBleGattEvent(BleGattEvent event) {
        super.handleBleGattEvent(event);

        if (event instanceof BleGattDescriptorWriteEvent) {
            BleGattDescriptorWriteEvent writeEvent = (BleGattDescriptorWriteEvent) event;
            if (notifyDescriptor.getUuid() != null) {
                timeoutLog = " uid ";
            } else {
                timeoutLog = " nouid ";
            }
            if (writeEvent.getDescriptor() != null) {
                timeoutLog = timeoutLog + "desc ";
                if (writeEvent.getDescriptor().getUuid() != null) {
                    timeoutLog = timeoutLog + "uid ";
                } else {
                    timeoutLog = timeoutLog + "nouid ";
                }
            } else {
                timeoutLog = timeoutLog + "nodesc ";
            }

            if (writeEvent.getDescriptor().getUuid().equals(notifyDescriptor.getUuid())) {
                if (writeEvent.getStatus() == BluetoothGatt.GATT_SUCCESS) {
                    onCompleted(writeEvent.getStatus());
                } else {
                    onError(new GattSetCharacteristicNotificationException(writeEvent.getStatus()));
                }
            }
        }
    }

    @Override
    protected void onError(@Nullable Throwable throwable) {
        if (timeoutLog != null &&
            throwable instanceof GattSetCharacteristicNotificationException &&
            OPERATION_TIMEOUT.equals(throwable.getMessage())) {
            Timber.d("Timeout log: %s", timeoutLog);
            super.onError(
                new GattSetCharacteristicNotificationException("SetCharacteristicNotification timed out" + timeoutLog).initCause(throwable));
            return;
        }
        super.onError(throwable);
    }

    @Nullable
    @Override
    protected Exception customTimeoutException() {
        return new GattSetCharacteristicNotificationException(OPERATION_TIMEOUT);
    }
}
