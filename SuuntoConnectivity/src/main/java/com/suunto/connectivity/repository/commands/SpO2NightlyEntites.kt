package com.suunto.connectivity.repository.commands

import android.annotation.SuppressLint
import androidx.annotation.RestrictTo
import com.google.gson.annotations.SerializedName
import com.suunto.connectivity.repository.SuuntoRepositoryService
import kotlinx.parcelize.Parcelize

/**
 * Query and response for getting SpO2 nightly enabled state
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class GetSpO2NightlyEnabledQuery(
    val macAddress: String
) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_SPO2_NIGHTLY_ENABLED
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class GetSpO2NightlyEnabledResponse(
    @SerializedName("Content")
    val enabled: Boolean,
) : Response

/**
 * Query and response for setting SpO2 nightly enabled state
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class SetSpO2NightlyEnabledQuery(
    val macAddress: String,
    val enabled: Boolean,
) : Query {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_SET_SPO2_NIGHTLY_ENABLED
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class SetSpO2NightlyEnabledResponse(
    val isSuccessful: Boolean,
) : Response
