package com.suunto.connectivity.repository;

import android.bluetooth.BluetoothAdapter;
import com.suunto.connectivity.watch.WatchBt;

public class ConnectionAnalytics {

    final BluetoothAdapter bluetoothAdapter;
    final ConnectSuccessRateCounter connectSuccessRateCounter;

    public ConnectionAnalytics(BluetoothAdapter bluetoothAdapter,
        ConnectSuccessRateCounter connectSuccessRateCounter) {
        this.bluetoothAdapter = bluetoothAdapter;
        this.connectSuccessRateCounter = connectSuccessRateCounter;
    }

    public ConnectionAnalyticsSequence createNewSequence(WatchBt watchBt) {
        return new ConnectionAnalyticsSequence(watchBt, bluetoothAdapter, connectSuccessRateCounter);
    }

    public ConnectSuccessRateCounter getConnectSuccessRateCounter() {
        return connectSuccessRateCounter;
    }
}
