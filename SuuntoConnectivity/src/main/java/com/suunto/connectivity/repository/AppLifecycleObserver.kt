package com.suunto.connectivity.repository

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import timber.log.Timber

class AppLifecycleObserver(
    val suuntoRepositoryClient: SuuntoRepositoryClient
) : DefaultLifecycleObserver {

    var isForeground: Boolean = false

    override fun onStart(owner: LifecycleOwner) {
        Timber.d("App is in foreground")
        isForeground = true
        refreshClient()
    }

    override fun onStop(owner: LifecycleOwner) {
        Timber.d("App is in background")
        isForeground = false
        refreshClient()
    }

    private fun refreshClient() {
        suuntoRepositoryClient.reportAppProcessForeground(isForeground)
    }
}
