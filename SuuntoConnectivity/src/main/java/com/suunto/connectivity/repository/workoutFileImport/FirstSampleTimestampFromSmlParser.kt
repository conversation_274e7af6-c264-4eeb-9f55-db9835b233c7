package com.suunto.connectivity.repository.workoutFileImport

import com.squareup.moshi.JsonReader
import com.stt.android.TestOpen
import com.stt.android.moshi.jsonArray
import com.stt.android.moshi.readObject

@TestOpen
class FirstSampleTimestampFromSmlParser {
    fun parse(reader: J<PERSON><PERSON>eader): String? {
        reader.beginObject()
        while (reader.hasNext()) {
            when (reader.nextName()) {
                "DeviceLog" -> return null // UltimateManager format
                "Data" -> reader.skipValue()
                "Summary" -> {
                    parseData(reader)?.let { return it }
                }
                else -> reader.skipValue()
            }
        }

        return null
    }

    private fun parseData(reader: JsonReader): String? {
        reader.readObject {
            if (reader.nextName() == "Samples") {
                parseSamples(reader)?.let { return it }
            } else {
                reader.skipValue()
            }
        }

        return null // No TimeISO8601 found
    }

    private fun parseSamples(reader: <PERSON><PERSON><PERSON><PERSON><PERSON>): String? {
        reader.jsonArray {
            reader.readObject {
                if (reader.nextName() == "TimeISO8601") {
                    return reader.nextString()
                } else {
                    reader.skipValue()
                }
            }
        }

        return null // No TimeISO8601 found
    }
}
