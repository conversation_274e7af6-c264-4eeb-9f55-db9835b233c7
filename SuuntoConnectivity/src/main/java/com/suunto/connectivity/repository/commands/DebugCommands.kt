package com.suunto.connectivity.repository.commands

import android.annotation.SuppressLint
import androidx.annotation.RestrictTo
import com.suunto.connectivity.repository.SuuntoRepositoryService
import kotlinx.parcelize.Parcelize

class DebugCommands {
    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    @SuppressLint("ParcelCreator")
    @Parcelize
    data class SetLocationCoordinatesQuery(
        override val macAddress: String,
        val latitude: Int,
        val longitude: Int,
    ) : DeviceSpecificQuery {
        override val messageType: Int
            get() = SuuntoRepositoryService.MSG_DEBUG_SET_LOCATION_COORDINATES

        override fun toString(): String {
            return "SetLocationCoordinates(macAddress=$macAddress, latitude=$latitude, longitude=$longitude"
        }
    }
}
