package com.suunto.connectivity.repository.stateMachines.connectionStateMachine;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.os.OperationCanceledException;
import androidx.annotation.Nullable;
import com.stt.android.utils.NearbyDevicesUtilsKt;
import com.suunto.connectivity.ngBleManager.NgBleManager;
import com.suunto.connectivity.repository.ConnectionAnalytics;
import com.suunto.connectivity.repository.ConnectionAnalyticsSequence;
import com.suunto.connectivity.repository.PairingState;
import com.suunto.connectivity.repository.stateMachines.base.Transition;
import com.suunto.connectivity.repository.stateMachines.base.Trigger;
import static com.suunto.connectivity.repository.stateMachines.connectionStateMachine.ReconnectState.ExitTriggers.ReconnectFailed;
import static com.suunto.connectivity.repository.stateMachines.connectionStateMachine.States.Reconnecting;
import static com.suunto.connectivity.repository.stateMachines.connectionStateMachine.Triggers.ConnectionInstabilityDetected;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.BluetoothOffException;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectMetadata;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectReason;
import com.suunto.connectivity.watch.WatchBt;
import com.suunto.connectivity.watch.WatchConnector;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import rx.Completable;
import rx.Subscription;
import timber.log.Timber;

/**
 * Reconnect state is responsible of reconnecting device after
 * - Service is started
 * - MDS disconnects
 * - Bt is turned on
 * - Connection instability state is cleared
 *
 * Reconnect state is also responsible of
 * - Handling connection retries in case connect fails.
 * - Recognising connection instability
 * - Detecting pairing loss and stop connecting in that case.
 */
public class ReconnectState extends ConnectionStateBase {

    /**
     * Max connection retry count for the connection errors which are reported as "counted"
     */
    public static final int MAX_COUNTED_CONNECT_ATTEMPTS = 20;

    /**
     * Max connection retry count in total.
     */
    public static final int MAX_TOTAL_CONNECT_ATTEMPTS = 96;
    static private final int RECONNECT_DELAY_MS = 4000;
    private static final int LEGACY_REFRESH_RECONNECT_DELAY_MS = 7000;

    public enum ExitTriggers implements Trigger {
        ReconnectFailed
    }

    private final WatchBt watchBt;
    private Subscription connectSubscription;
    @Nullable
    private Boolean pairingCheckedOnEntry;
    private final ConnectionLoopDetector connectionLoopDetector;
    private final ConnectionAnalytics analytics;
    private final Context context;
    private final BluetoothAdapter bluetoothAdapter;

    /*
     * Legacy device refresher, which will refresh reconnection attempt periodically for
     * legacy devices.
     */
    @Nullable
    private LegacyDeviceConnectRefresher legacyDeviceConnectRefresher;

    ReconnectState(WatchBt watchBt,
        ConnectionLoopDetector connectionLoopDetector,
        ConnectionAnalytics analytics, Context context,
        BluetoothAdapter bluetoothAdapter) {
        super(Reconnecting.name());
        this.watchBt = watchBt;
        this.connectionLoopDetector = connectionLoopDetector;
        this.analytics = analytics;
        this.context = context;
        this.bluetoothAdapter = bluetoothAdapter;
    }

    public void setLegacyDeviceConnectRefresher(
        @Nullable LegacyDeviceConnectRefresher legacyDeviceConnectRefresher) {
        this.legacyDeviceConnectRefresher = legacyDeviceConnectRefresher;
    }

    /**
     * Reconnect after a delay.
     *
     * @param connectMetadata Connect metadata.
     * @param reconnectDelayMs Timeout in milliseconds.
     */
    private void reconnect(final ConnectMetadata connectMetadata, int reconnectDelayMs) {
        Timber.d("Reconnect device %s. ConnectReason %s. Attempt %s", watchBt.getSerial(),
            connectMetadata.getConnectReason().toString(),
            connectMetadata.getTotalConnectAttempts());
        ConnectionAnalyticsSequence analyticsSequence =
            analytics.createNewSequence(watchBt);

        if (watchBt.getSuuntoBtDevice().getDeviceType().isDataLayerDevice()) {

            // Datalayer device is assumed to be paired.
            analyticsSequence.connectingStarted(PairingState.Paired);
        } else {

            // Devices advertised pairing state is unknown at this point because there has not
            // been any scan performed on reconnect.
            analyticsSequence.connectingStarted(PairingState.Unknown);
        }
        boolean forAnalyticsAlreadyConnected = watchBt.isAlreadyConnected();
        connectSubscription = Completable.timer(reconnectDelayMs, TimeUnit.MILLISECONDS)
            .andThen(watchBt.connect(connectMetadata))
            .doOnSubscribe(analyticsSequence::connectionAttemptStarted)
            .subscribe(s -> {
                    analytics.getConnectSuccessRateCounter()
                        .countSuccessOrFail(watchBt.getMacAddress(), true);
                    Timber.d("Connect succeeded %s", watchBt.getMacAddress());
                    stateMachine().fire(Triggers.Connected);
                },
                throwable -> {
                    if (throwable instanceof OperationCanceledException) {
                        // Reconnect is cancelled only after unexpected transition to another
                        // state. Do nothing.
                        return;
                    }
                    Timber.d("Connection error %s", throwable.toString());
                    analyticsSequence.connectionAttemptFailed(context, throwable, connectMetadata,
                        forAnalyticsAlreadyConnected,
                        watchBt.isInvalidPacketDetectedAfterLastWatchConnect());
                    analytics.getConnectSuccessRateCounter()
                        .countSuccessOrFail(watchBt.getMacAddress(), false);
                    if (throwable instanceof BluetoothOffException) {
                        /*
                         * Bluetooth off. Can not reconnect. No need to fire Triggers.BToff
                         * because BtStateMonitor in ConnectionStateMachine fires it.
                         */
                        Timber.d("Bluetooth is off. No reconnect but wait for BT on");
                        return;
                    }

                    if (connectMetadata.getCountedConnectAttempts()
                        < MAX_COUNTED_CONNECT_ATTEMPTS
                        && connectMetadata.getTotalConnectAttempts()
                        < MAX_TOTAL_CONNECT_ATTEMPTS) {
                        // Reconnect failed. Request new connect with update connect metadata.
                        final boolean countedConnectionAttempt;
                        countedConnectionAttempt =
                            !(throwable instanceof NgBleManager.BleConnectionLostError);
                        final ConnectMetadata newConnection =
                            new ConnectMetadata(connectMetadata, countedConnectionAttempt, true);
                        stateMachine().fire(ReconnectFailed, newConnection,
                            ConnectMetadata.class);
                    } else {
                        /*
                         * To many failed connect attempts. Go to connections instability
                         * state.
                         */
                        Timber.d("Unable to connect BLE. Too many failed connection attempts.");
                        stateMachine().fire(ConnectionInstabilityDetected);
                    }
                });
    }

    @Override
    public void onEntry(Transition transition) {
        super.onEntry(transition);
        pairingCheckedOnEntry = null;
        if (legacyDeviceConnectRefresher != null) {
            legacyDeviceConnectRefresher.setupRefresh();
        }
        if (!bluetoothAdapter.isEnabled()) {
            /*
             * Bluetooth off. Can not reconnect.
             */
            Timber.d("Bluetooth is off. No reconnect but wait for BT on");
            stateMachine().fire(Triggers.BToff);
            return;
        }
        if (isUnpairedOnEntry()) {
            stateMachine().fireNext(Triggers.Unpaired);
            return;
        }

        /*
         * Resolve connection metadata based on on entry trigger.
         */
        ConnectMetadata connectMetadata = null;
        int reconnectDelay = RECONNECT_DELAY_MS;
        if (transition.getTrigger() == Triggers.Disconnected) {
            connectMetadata =
                new ConnectMetadata(ConnectReason.WatchDisconnected, true);
        } else if (transition.getTrigger() == Triggers.BTon) {
            connectMetadata = new ConnectMetadata(ConnectReason.BtTurnedOn, true);
        } else if (transition.getTrigger() == Triggers.ConnectionInstabilityDelayPassed) {
            connectMetadata = new ConnectMetadata(ConnectReason.ConnectionInstabilityDelayPassed, true);
        } else if (transition.getTrigger() == Triggers.ConnectionInstabilityCleared) {
            connectMetadata = new ConnectMetadata(ConnectReason.ConnectionInstabilityCleared, true);
        } else if (transition.getTrigger() == Triggers.ConnectionResetDelayPassed) {
            connectMetadata = new ConnectMetadata(ConnectReason.WatchDisconnected, true);
            connectionLoopDetector.reset();
        } else if (transition.getTrigger() == Triggers.LegacyDeviceRefreshAlarm) {
            reconnectDelay = LEGACY_REFRESH_RECONNECT_DELAY_MS;
            connectMetadata = new ConnectMetadata(ConnectReason.WatchDisconnected, true);
        } else if (transition.getTrigger() == Triggers.AncsStateChanged) {
            connectMetadata = new ConnectMetadata(ConnectReason.ANCSServiceStatusChange, true);
        }
        if (connectMetadata != null) {
            if (connectionLoopDetector.isConnectionLoop(connectMetadata)) {
                /*
                 * Connection loop detected by by connection loop detector.
                 */
                Timber.d("Unable to connect BLE. Connection loop recognized.");
                stateMachine().fireNext(ConnectionInstabilityDetected);
            } else {
                reconnect(connectMetadata, reconnectDelay);
            }
        }
    }

    @Override
    protected <TArg> void onEntry(TArg arg, Transition transition) {
        if (!bluetoothAdapter.isEnabled()) {
            /*
             * Bluetooth off. Can not reconnect. Triggers.BToff fired already in
             * onEntry(Transition transition) method.
             */
            return;
        }
        if (isUnpairedOnEntry() || connectionLoopDetector.isConnectionLoop()) {
            return;
        }
        final Trigger trigger = transition.getTrigger();
        if (trigger == ReconnectFailed ||
            trigger == Triggers.ServiceStartConnect) {
            // Previous reconnect attempt failed. Reconnect state re-entry.
            if (arg instanceof ConnectMetadata) {
                reconnect((ConnectMetadata) arg, RECONNECT_DELAY_MS);
            } else {
                Timber.e("Wrong entry parameter class %s", arg.getClass().getSimpleName());
            }
        }
    }

    @Override
    public void onExit(Transition transition) {
        if (legacyDeviceConnectRefresher != null) {
            legacyDeviceConnectRefresher.cancelRefresh();
        }
        Trigger trigger = transition.getTrigger();
        if (connectSubscription != null) {
            connectSubscription.unsubscribe();
        }

        if (trigger != Triggers.Connected
            && trigger != ReconnectFailed &&
            trigger != ConnectionInstabilityDetected) {
            /*
             * Unexpected exit trigger - Clean up connect attempt.
             */
            Timber.d("Exited before connect result");
            // Ensure device is disconnected
            watchBt.disconnect()
                .onErrorComplete()
                .subscribe();
        }
    }

    private boolean isUnpairedOnEntry() {
        if (pairingCheckedOnEntry == null) {
            pairingCheckedOnEntry = checkPairing();
        }
        return !pairingCheckedOnEntry;
    }

    /**
     * Check device pairing.
     *
     * @return False if not paired for sure. True if paired or pairing state can not be resolved.
     */
    @SuppressLint("MissingPermission")
    private boolean checkPairing() {
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (bluetoothAdapter != null && bluetoothAdapter.isEnabled() &&
            NearbyDevicesUtilsKt.isNearbyDevicesPermissionGranted(context)) {
            if (watchBt.getSuuntoBtDevice().getDeviceType().isDataLayerDevice()) {
                // For data layer devices we can check with device name
                final String deviceName = watchBt.getSuuntoBtDevice().getName();
                Set<BluetoothDevice> bondedDevices = bluetoothAdapter.getBondedDevices();
                if (bondedDevices != null && !bondedDevices.isEmpty()) {
                    for (BluetoothDevice bluetoothDevice : bluetoothAdapter.getBondedDevices()) {
                        if (bluetoothDevice.getName().equals(deviceName)) {
                            // Device is paired.
                            watchBt.setPaired(true);
                            return true;
                        }
                    }
                }
                Timber.d("Device %s not paired anymore.", deviceName);
            } else {
                final String macAddress = watchBt.getSuuntoBtDevice().getMacAddress();
                Set<BluetoothDevice> bondedDevices = bluetoothAdapter.getBondedDevices();
                if (bondedDevices != null && !bondedDevices.isEmpty()) {
                    for (BluetoothDevice bluetoothDevice : bluetoothAdapter.getBondedDevices()) {
                        if (bluetoothDevice.getAddress().equals(macAddress)) {
                            // Device is paired.
                            watchBt.setPaired(true);
                            return true;
                        }
                    }
                }
                Timber.d("Device %s not paired anymore.", macAddress);
            }

            // Device is not paired.
            watchBt.setPaired(false);
            return false;
        }
        return true;
    }
}
