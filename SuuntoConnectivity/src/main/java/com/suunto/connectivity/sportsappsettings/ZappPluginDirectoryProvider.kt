package com.suunto.connectivity.sportsappsettings

import android.os.Bundle
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.ErrorResponse
import com.suunto.connectivity.repository.commands.GetZappPluginDirectoryQuery
import com.suunto.connectivity.repository.commands.GetZappPluginDirectoryResponse
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.util.handleDeviceSpecificQuery
import com.suunto.connectivity.watch.WatchBt
import rx.Observable
import rx.Single

class ZappPluginDirectoryProvider(
    private val suuntoRepositoryService: SuuntoRepositoryService
) : SuuntoResponseProducer<Response> {

    override fun isRelated(messageType: Int): Boolean =
        messageType == SuuntoRepositoryService.MSG_GET_ZAPP_PLUG_IN_DIRECTORY

    override fun provideResponseObservable(
        messageType: Int,
        bundle: Bundle
    ): Observable<out Response> =
        when (messageType) {
            SuuntoRepositoryService.MSG_GET_ZAPP_PLUG_IN_DIRECTORY ->
                handleQuery<GetZappPluginDirectoryQuery>(bundle) { watchBt, query ->
                    watchBt.zappPluginDirectory.map { GetZappPluginDirectoryResponse(it) }
                }
            else -> Observable.just(ErrorResponse("Unknown query"))
        }

    private fun <T : DeviceSpecificQuery> handleQuery(
        bundle: Bundle,
        handler: (WatchBt, T) -> Single<Response>
    ): Observable<Response> =
        handleDeviceSpecificQuery(suuntoRepositoryService.activeDevices, bundle, handler)
}
