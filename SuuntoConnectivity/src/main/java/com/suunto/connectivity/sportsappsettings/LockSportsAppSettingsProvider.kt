package com.suunto.connectivity.sportsappsettings

import android.os.Bundle
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.EmptyResponse
import com.suunto.connectivity.repository.commands.ErrorResponse
import com.suunto.connectivity.repository.commands.LockOrUnlockSportsAppQuery
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.util.handleDeviceSpecificQuery
import com.suunto.connectivity.watch.WatchBt
import rx.Observable
import rx.Single

class LockSportsAppSettingsProvider(
    private val suuntoRepositoryService: SuuntoRepositoryService
) : SuuntoResponseProducer<Response> {

    override fun isRelated(messageType: Int): Boolean =
        messageType == SuuntoRepositoryService.MSG_LOCK_UNLOCK_SPORTS_APP

    override fun provideResponseObservable(
        messageType: Int,
        bundle: Bundle
    ): Observable<out Response> =
        when (messageType) {
            SuuntoRepositoryService.MSG_LOCK_UNLOCK_SPORTS_APP ->
                handleQuery<LockOrUnlockSportsAppQuery>(bundle) { watchBt, query ->
                    if (query.lock) {
                        watchBt.lockSportsApp(query.pluginId)
                    } else {
                        watchBt.unlockSportsApp(query.pluginId)
                    }
                        .andThen(Single.just(EmptyResponse()))
                }
            else -> Observable.just(ErrorResponse("Unknown query"))
        }

    private fun <T : DeviceSpecificQuery> handleQuery(
        bundle: Bundle,
        handler: (WatchBt, T) -> Single<Response>
    ): Observable<Response> =
        handleDeviceSpecificQuery(suuntoRepositoryService.activeDevices, bundle, handler)
}
