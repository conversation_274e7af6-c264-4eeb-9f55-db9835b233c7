package com.suunto.connectivity.offlinemusic

import android.util.Base64
import com.google.gson.Gson
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.sdsmanager.model.MdsContent
import com.suunto.connectivity.settings.Setting
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants
import org.json.JSONException
import org.json.JSONObject
import rx.Completable
import rx.Single
import rx.exceptions.Exceptions
import java.io.IOException
import java.util.Locale

class OfflineMusicPlaylistSetting<T> : Setting<T> {

    private var uri: String
    private var name: String
    private var contract: String
    private var typeOfT: Class<T>
    private var mdsRx: MdsRx
    private var gson: Gson

    private var cached: T? = null

    constructor(
        key: String,
        name: String,
        contract: String,
        typeOfT: Class<T>,
        serialNumber: String,
        mdsRx: MdsRx,
        gson: Gson
    ) : this(WATCH_OFFLINE_MUSIC_URI, key, name, contract, typeOfT, serialNumber, mdsRx, gson)

    constructor(
        baseUri: String,
        key: String,
        name: String,
        contract: String,
        typeOfT: Class<T>,
        serialNumber: String,
        mdsRx: MdsRx,
        gson: Gson
    ) {
        this.uri = String.format(Locale.US, baseUri, serialNumber, key)
        this.name = name
        this.contract = contract
        this.typeOfT = typeOfT
        this.mdsRx = mdsRx
        this.gson = gson
    }

    override fun getValue(): Single<T> {
        return mdsRx.get(uri, contract)
            .map { body ->
                if (body.isNullOrEmpty()) {
                    throw IllegalArgumentException("Response contained no body")
                }
                try {
                    val mdsContent = gson.fromJson(body, MdsContent::class.java)
                    // Do not allow nulls to be passed on users
                    if (mdsContent?.content == null) {
                        throw IllegalArgumentException("Response body contained no content")
                    }
                    val decodedBytes = Base64.decode(mdsContent.content.toString(), Base64.DEFAULT)
                    val jsonString = String(decodedBytes, Charsets.UTF_8)
                    val data = gson.fromJson(jsonString, typeOfT)
                    cached = data
                    return@map data
                } catch (e: IOException) {
                    throw Exceptions.propagate(e)
                }
            }
    }

    override fun setValue(value: T & Any): Completable {
        // Create the JSONObject to send
        val jsonObject = JSONObject()
        try {
            jsonObject.put(name, value)
        } catch (e: JSONException) {
            return Completable.error(e)
        }

        return mdsRx.put(uri, jsonObject.toString())
            .doOnSuccess { cached = value }
            .toCompletable()
    }

    override fun getCachedValue(): T? {
        return cached
    }

    override fun clearCachedValue() {
        cached = null
    }

    companion object {
        private const val WATCH_OFFLINE_MUSIC_URI: String =
            SuuntoConnectivityConstants.MDS_SCHEME_PREFIX + "%s/offline/music/%s"
    }
}
