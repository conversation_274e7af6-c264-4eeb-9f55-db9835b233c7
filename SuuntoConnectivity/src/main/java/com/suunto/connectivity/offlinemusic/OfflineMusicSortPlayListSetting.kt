package com.suunto.connectivity.offlinemusic

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.suunto.connectivity.repository.commands.SortPlayListsContract
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_SCHEME_PREFIX
import com.suunto.connectivity.watch.SpartanMoshiSetting

class OfflineMusicSortPlayListSetting(
    moshi: <PERSON>shi,
    serialNumber: String,
    mdsRx: MdsRx
) : SpartanMoshiSetting<SortPlayListsContract>(
    moshi,
    serialNumber,
    mdsRx,
    SortPlayListsContract::class.java
) {

    override val uri: String =
        "$MDS_SCHEME_PREFIX$serialNumber/offline/music/sortList"

    private val requestAdapter: JsonAdapter<SortPlayListsContract> =
        moshi.adapter(SortPlayListsContract::class.java)

    override fun buildRequestJson(value: SortPlayListsContract): String {
        return requestAdapter.toJson(value)
    }
}
