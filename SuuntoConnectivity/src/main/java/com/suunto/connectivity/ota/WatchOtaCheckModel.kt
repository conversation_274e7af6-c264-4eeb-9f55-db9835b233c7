package com.suunto.connectivity.ota

import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.firmware.BaseFirmwareInformationInterface
import com.stt.android.domain.firmware.BaseWatchFirmwareInfo
import com.stt.android.utils.toV2
import com.suunto.connectivity.ServiceCoroutineScope
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.watch.WatchBt
import dagger.hilt.android.scopes.ServiceScoped
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.asFlow
import timber.log.Timber
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

@ServiceScoped
class WatchOtaCheckModel @Inject constructor(
    @ServiceCoroutineScope private val serviceScope: CoroutineScope,
    private val firmwareInformationInterface: BaseFirmwareInformationInterface
) {

    private var stateChangeJob: Job? = null

    /**
     * Catch coroutines exception.
     */
    private val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        Timber.e(exception, "subscribe watch ota check failed.")
    }

    /**
     * override LifecycleCoroutineScope's CoroutineContext
     */
    private val coroutineContext: CoroutineContext = Dispatchers.Default + exceptionHandler

    private var subscribeWatchJob: Job? = null

    fun startObservingDevice(watchBt: WatchBt) {
        Timber.v("startObservingDevice")

        stateChangeJob?.cancel()
        stateChangeJob = serviceScope.launch(coroutineContext) {
            watchBt.stateChangeObservable
                .toV2()
                .filter {
                    SuuntoDeviceCapabilityInfoProvider[watchBt.suuntoBtDevice.deviceType]
                        .supportsOtaUpdateCheck(it.deviceInfo?.capabilities)
                }
                .map { it.isConnected }
                .distinctUntilChanged()
                .asFlow()
                .collect { connected ->
                    if (connected) {
                        Timber.d("start to getLatestFirmwareFromBackend")
                        subscribeOtaCheck(watchBt)
                    } else {
                        unsubscribeOtaCheck()
                    }
                }
        }
    }

    private fun subscribeOtaCheck(watchBt: WatchBt) {
        subscribeWatchJob?.cancel()
        subscribeWatchJob = serviceScope.launch(coroutineContext) {
            watchBt.subscribeWatchOtaCheckObservable()
                .toV2()
                .asFlow()
                .collect {
                    updateFirmwareTag(watchBt)
                }
        }
    }

    private suspend fun updateFirmwareTag(watchBt: WatchBt) {
        val deviceInfo = watchBt.currentState.deviceInfo
        val isSupportOtaUpdateCheck =
            watchBt.suuntoBtDevice.deviceType.supportOtaUpdateCheck(deviceInfo)
        deviceInfo?.let {
            runSuspendCatching {
                val firmwareInfo = firmwareInformationInterface.getLatestFirmwareInfo(
                    variant = it.variant,
                    hwCompatibilityIdentifier = it.hwCompatibilityId,
                    currentVersion = it.swVersion,
                    productVersion = it.productVersion,
                    isSupportOtaUpdateCheck
                )
                putFirmwareFlag(watchBt, firmwareInfo)
            }.onFailure { e ->
                Timber.w(e, "Error while updating firmware tag $e")
            }
        }
    }

    private suspend fun putFirmwareFlag(watchBt: WatchBt, firmwareInfo: BaseWatchFirmwareInfo?) {
        firmwareInfo?.let {
            runSuspendCatching {
                val contract = it.forceUpdateToContract()
                watchBt.updateOtaForceTag(contract).await()
            }.onFailure { e ->
                Timber.w(e, "put firmware flag $e")
            }
        }
    }

    private fun unsubscribeOtaCheck() {
        subscribeWatchJob?.cancel()
        subscribeWatchJob = null
    }
}
