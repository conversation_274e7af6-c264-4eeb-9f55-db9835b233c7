package com.suunto.connectivity.notifications

import com.suunto.connectivity.SuuntoQueryConsumer
import com.suunto.connectivity.repository.ResponseMessage
import com.suunto.connectivity.repository.SuuntoRepositoryClient
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.repository.commands.SetPredefinedRepliesQuery
import com.suunto.connectivity.repository.commands.SetPredefinedRepliesResponse
import hu.akarnokd.rxjava.interop.RxJavaInterop
import io.reactivex.Completable

class SetPredefinedRepliesQueryConsumer(
    private val suuntoRepositoryClient: SuuntoRepositoryClient
) : SuuntoQueryConsumer {
    override fun isResponseRelated(response: Response): Boolean = response is SetPredefinedRepliesResponse

    override fun getResponseMessage(messageId: Int, response: Response): ResponseMessage<*> {
        return ResponseMessage(messageId, response)
    }

    fun setPredefinedReplies(
        macAddress: String,
        predefinedReplies: List<String>
    ): Completable {
        return RxJavaInterop.toV2Completable(
            suuntoRepositoryClient.waitForServiceReady().andThen(
                suuntoRepositoryClient.sendQuery(SetPredefinedRepliesQuery(predefinedReplies, macAddress))
                    .first()
                    .toCompletable()
            )
        )
    }
}
