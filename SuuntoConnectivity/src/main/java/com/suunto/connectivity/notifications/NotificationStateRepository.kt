package com.suunto.connectivity.notifications

import android.content.SharedPreferences
import androidx.core.content.edit
import com.suunto.connectivity.SuuntoServicePreferences
import javax.inject.Inject

class NotificationStateRepository @Inject constructor(
    @SuuntoServicePreferences private var sharedPreferences: SharedPreferences
) {

    fun getCallEnabled() = sharedPreferences.getBoolean(CALL_NOTIFICATIONS_ENABLED_KEY, true)

    fun getSmsEnabled() = sharedPreferences.getBoolean(SMS_NOTIFICATIONS_ENABLED_KEY, true)

    fun getApplicationEnabled() = sharedPreferences.getBoolean(APP_NOTIFICATIONS_ENABLED_KEY, true)

    fun saveNotificationsCategoryEnabled(
        call: <PERSON>olean,
        sms: <PERSON>olean,
        application: Boolean
    ) = sharedPreferences.edit {
        putBoolean(APP_NOTIFICATIONS_ENABLED_KEY, application)
        putBoolean(CALL_NOTIFICATIONS_ENABLED_KEY, call)
        putBoolean(SMS_NOTIFICATIONS_ENABLED_KEY, sms)
    }

    fun saveNotificationsCategoryEnabled(
        categoryEnabled: MdsNotificationCategoryEnabled
    ) {
        this.saveNotificationsCategoryEnabled(
            categoryEnabled.call,
            categoryEnabled.messages,
            categoryEnabled.others
        )
    }

    companion object {
        private const val APP_NOTIFICATIONS_ENABLED_KEY = "app_notifications_enabled_key"
        private const val CALL_NOTIFICATIONS_ENABLED_KEY = "call_notifications_enabled_key"
        private const val SMS_NOTIFICATIONS_ENABLED_KEY = "sms_notifications_enabled_key"
    }
}
