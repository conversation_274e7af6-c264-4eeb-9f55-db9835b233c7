package com.suunto.connectivity.battery

import android.os.Bundle
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_CHARGING_STATE
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.ErrorResponse
import com.suunto.connectivity.repository.commands.GetChargingStateQuery
import com.suunto.connectivity.repository.commands.GetChargingStateResponse
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.util.handleDeviceSpecificQuery
import com.suunto.connectivity.watch.WatchBt
import rx.Observable
import rx.Single

class ChargingStateProvider(
    private val suuntoRepositoryService: SuuntoRepositoryService
) : SuuntoResponseProducer<Response> {

    override fun isRelated(messageType: Int): Boolean =
        messageType == MSG_GET_CHARGING_STATE

    override fun provideResponseObservable(
        messageType: Int,
        bundle: Bundle
    ): Observable<out Response> =
        when (messageType) {
            MSG_GET_CHARGING_STATE ->
                handleQuery<GetChargingStateQuery>(bundle) { watchBt, _ ->
                    watchBt.chargingState
                        .map { chargingState ->
                            GetChargingStateResponse(chargingState)
                        }
                }
            else -> Observable.just(ErrorResponse("Unknown query"))
        }

    private fun <T : DeviceSpecificQuery> handleQuery(
        bundle: Bundle,
        handler: (WatchBt, T) -> Single<Response>
    ): Observable<Response> =
        handleDeviceSpecificQuery(suuntoRepositoryService.activeDevices, bundle, handler)
}
