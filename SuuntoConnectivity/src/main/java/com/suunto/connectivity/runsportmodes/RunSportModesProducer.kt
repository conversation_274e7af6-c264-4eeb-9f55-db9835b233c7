package com.suunto.connectivity.runsportmodes

import android.os.Bundle
import com.movesense.mds.MdsException
import com.stt.android.utils.toV1
import com.suunto.connectivity.SuuntoResponseProducer
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_DEL_DATA_SCREEN
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_DEL_TRAINING_MODE
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_ALL_SPORTS
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_DATA_SCREEN_LIST
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_DATA_SCREEN_TEMPLATE_LIST
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_RECENT_SPORTS
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_TRAINING_MODE
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_TRAINING_MODE_LIST
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_TRAINING_MODE_TEMPLATE
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_GET_TRAINING_MODE_TEMPLATE_LIST
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_POST_COMPETITION_INFO_TARGET
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_POST_COMPETITION_SAMPLES_TARGET
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_POST_DATA_SCREEN
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_POST_TRAINING_MODE
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_PUT_DATA_SCREEN
import com.suunto.connectivity.repository.SuuntoRepositoryService.MSG_PUT_TRAINING_MODE
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.ErrorResponse
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.runsportmodes.exceptions.DuplicatedSportModeNameException
import com.suunto.connectivity.watch.WatchBt
import kotlinx.coroutines.rx2.rxSingle
import rx.Observable

class RunSportModesProducer(
    private val suuntoRepositoryService: SuuntoRepositoryService,
    private val runSportModesWatchApi: RunSportModesWatchApi,
) : SuuntoResponseProducer<Response> {
    override fun isRelated(messageType: Int): Boolean {
        val types = listOf(
            MSG_GET_RECENT_SPORTS,
            MSG_GET_ALL_SPORTS,
            MSG_GET_TRAINING_MODE_TEMPLATE_LIST,
            MSG_GET_TRAINING_MODE_TEMPLATE,
            MSG_GET_TRAINING_MODE_LIST,
            MSG_GET_TRAINING_MODE,
            MSG_POST_TRAINING_MODE,
            MSG_PUT_TRAINING_MODE,
            MSG_DEL_TRAINING_MODE,
            MSG_GET_DATA_SCREEN_TEMPLATE_LIST,
            MSG_GET_DATA_SCREEN_LIST,
            MSG_POST_DATA_SCREEN,
            MSG_PUT_DATA_SCREEN,
            MSG_DEL_DATA_SCREEN,
            MSG_POST_COMPETITION_INFO_TARGET,
            MSG_POST_COMPETITION_SAMPLES_TARGET
        )
        return messageType in types
    }

    override fun provideResponseObservable(
        messageType: Int,
        bundle: Bundle
    ): Observable<out Response> {
        return when (messageType) {
            MSG_GET_RECENT_SPORTS -> handleQuery<GetRecentSportsQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.getRecentSports(watchBt.serial, query.withSportTags)
            }

            MSG_GET_ALL_SPORTS -> handleQuery<GetAllSportsQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.getAllSports(watchBt.serial, query.withSportTags)
            }

            MSG_GET_TRAINING_MODE_TEMPLATE_LIST -> handleQuery<GetTrainingModeTemplateListQuery>(
                bundle
            ) { watchBt, query ->
                runSportModesWatchApi.getTrainingModeHeaderList(
                    watchBt.serial,
                    query.sportItemContract
                )
            }

            MSG_GET_TRAINING_MODE_LIST -> handleQuery<GetTrainingModeListQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.getTrainingModeList(watchBt.serial, query.sportItemContract)
            }

            MSG_GET_TRAINING_MODE -> handleQuery<GetTrainingModeQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.getTrainingMode(
                    watchBt.serial,
                    query.trainingModeItemContract
                )
            }

            MSG_GET_TRAINING_MODE_TEMPLATE -> handleQuery<GetTrainingModeTemplateQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.getDefaultTrainingMode(
                    watchBt.serial,
                    query.trainingModeItemContract
                )
            }

            MSG_POST_TRAINING_MODE -> handleSimpleQuery<PostTrainingModeQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.createTrainingMode(
                    watchBt.serial,
                    query.saveTrainingModeContract
                )
            }

            MSG_PUT_TRAINING_MODE -> handleSimpleQuery<PutTrainingModeQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.updateTrainingMode(
                    watchBt.serial,
                    query.saveTrainingModeContract
                )
            }

            MSG_DEL_TRAINING_MODE -> handleSimpleQuery<DelTrainingModeQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.deleteTrainingMode(
                    watchBt.serial,
                    query.trainingModeItemContract
                )
            }

            MSG_POST_DATA_SCREEN -> handleSimpleQuery<PostDataScreenQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.createDataScreen(watchBt.serial, query.dataScreen)
            }

            MSG_PUT_DATA_SCREEN -> handleSimpleQuery<PutDataScreenQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.updateDataScreen(watchBt.serial, query.dataScreen)
            }

            MSG_DEL_DATA_SCREEN -> handleSimpleQuery<DelDataScreenQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.deleteDataScreen(watchBt.serial, query.dataScreen)
            }

            MSG_GET_DATA_SCREEN_TEMPLATE_LIST -> handleQuery<GetDataScreenTemplateListQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.getDefaultDataScreens(
                    watchBt.serial,
                    query.trainingModeBaseContract
                )
            }

            MSG_GET_DATA_SCREEN_LIST -> handleQuery<GetDataScreenListQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.getDataScreens(watchBt.serial, query.trainingModeItemContract)
            }

            MSG_POST_COMPETITION_INFO_TARGET -> handleSimpleQuery<PutCompetitionTargetQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.putCompetitionInfoTarget(watchBt.serial, query.competitionTargetContract)
            }

            MSG_POST_COMPETITION_SAMPLES_TARGET -> handleSimpleQuery<PutCompetitionSamplesTargetQuery>(bundle) { watchBt, query ->
                runSportModesWatchApi.putCompetitionSamplesTarget(watchBt.serial, query.speedSamples, query.distanceIncluded)
            }

            else -> throw UnsupportedOperationException("Not supported msg: $messageType")
        }
    }

    private fun <T : DeviceSpecificQuery> handleQuery(
        bundle: Bundle,
        handler: suspend (WatchBt, T) -> Response
    ): Observable<Response> {
        val query: T = bundle.getParcelable(SuuntoRepositoryService.ArgumentKeys.ARG_DATA)
            ?: return Observable.just(ErrorResponse("Query data missing"))
        val watchBt = suuntoRepositoryService.activeDevices.getWatchBt(query.macAddress)
            ?: return Observable.just(
                ErrorResponse("Watch not found with MAC address: ${query.macAddress}")
            )
        return rxSingle { handler(watchBt, query) }
            .toV1()
            .toObservable()
    }

    private fun <T : DeviceSpecificQuery> handleSimpleQuery(
        bundle: Bundle,
        handler: suspend (WatchBt, T) -> Unit
    ): Observable<out Response> {
        val query: T = bundle.getParcelable(SuuntoRepositoryService.ArgumentKeys.ARG_DATA)
            ?: return Observable.just(ErrorResponse("Query data missing"))
        val watchBt = suuntoRepositoryService.activeDevices.getWatchBt(query.macAddress)
            ?: return Observable.just(
                ErrorResponse("Watch not found with MAC address: ${query.macAddress}")
            )
        return rxSingle { handler(watchBt, query) }
            .toV1()
            .toObservable()
            .flatMap { Observable.just<Response>(SportModesSimpleResponse(true)) }
            .onErrorResumeNext { error ->
                if (error is MdsException && error.statusCode == STATUS_CODE_DUPLICATED_MODE_NAME) {
                    Observable.just<Response>(ErrorResponse(error.message, DuplicatedSportModeNameException()))
                } else {
                    Observable.just<Response>(SportModesSimpleResponse(false)) // In case of other errors, return false response
                }
            }
    }

    private companion object {
        private const val STATUS_CODE_DUPLICATED_MODE_NAME = 409
    }
}
