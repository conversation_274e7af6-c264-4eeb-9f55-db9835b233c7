package com.suunto.connectivity.runsportmodes

import com.stt.android.coroutines.await
import com.suunto.connectivity.SuuntoQueryConsumer
import com.suunto.connectivity.repository.ResponseMessage
import com.suunto.connectivity.repository.SuuntoRepositoryClient
import com.suunto.connectivity.repository.SuuntoRepositoryException
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.ErrorResponse
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.runsportmodes.entities.Challenge
import com.suunto.connectivity.runsportmodes.entities.RecentSportHeaderEntity
import com.suunto.connectivity.runsportmodes.entities.SportHeaderEntity
import com.suunto.connectivity.runsportmodes.entities.TrainingModeHeaderEntity

class RunSportModesConsumer(
    private val suuntoRepositoryClient: SuuntoRepositoryClient
) : SuuntoQueryConsumer {

    private val relatedClass = listOf(
        GetRecentSportsResponse::class.java,
        GetAllSportsResponse::class.java,
        GetTrainingModeHeaderListResponse::class.java,
        GetTrainingModeListResponse::class.java,
        TrainingModeResponse::class.java,
        SportModesSimpleResponse::class.java,
        GetDataScreenResponse::class.java,
    )

    override fun isResponseRelated(response: Response): Boolean {
        return response::class.java in relatedClass
    }

    override fun getResponseMessage(messageId: Int, response: Response): ResponseMessage<*>? {
        return ResponseMessage(messageId, response)
    }

    suspend fun getRecentSportIds(
        macAddress: String,
        withSportTags: Boolean
    ): List<RecentSportHeaderEntity> =
        sendQuery<GetRecentSportsResponse>(
            GetRecentSportsQuery(
                macAddress,
                withSportTags
            )
        ).arrayData

    suspend fun getAllSportHeaders(
        macAddress: String,
        withSportTags: Boolean
    ): List<SportHeaderEntity> =
        sendQuery<GetAllSportsResponse>(GetAllSportsQuery(macAddress, withSportTags)).arrayData

    suspend fun getDefaultTrainingModeList(
        macAddress: String,
        sportId: Int,
        sportTag: Int?,
    ): List<TrainingModeHeaderEntity> = sendQuery<GetTrainingModeHeaderListResponse>(
        GetTrainingModeTemplateListQuery(
            macAddress,
            SportItemContract(sportId, sportTag = sportTag)
        )
    ).arrayData

    suspend fun getTrainingModeList(
        macAddress: String,
        sportId: Int,
        lastModeId: Int?,
        sportTag: Int?
    ): List<TrainingModeHeaderEntity> = sendQuery<GetTrainingModeListResponse>(
        GetTrainingModeListQuery(macAddress, SportItemContract(sportId, lastModeId, sportTag))
    ).arrayData

    suspend fun getDefaultTrainingMode(
        macAddress: String,
        sportId: Int,
        trainingModeId: Int,
        sportTag: Int?,
    ): TrainingModeResponse = sendQuery<TrainingModeResponse>(
        GetTrainingModeTemplateQuery(
            macAddress,
            TrainingModeItemContract(sportId, trainingModeId, sportTag)
        )
    )

    suspend fun getTrainingMode(
        macAddress: String,
        sportId: Int,
        trainingModeId: Int,
        sportTag: Int?,
    ): TrainingModeResponse = sendQuery<TrainingModeResponse>(
        GetTrainingModeQuery(
            macAddress,
            TrainingModeItemContract(sportId, trainingModeId, sportTag)
        )
    )

    suspend fun postTrainingMode(
        macAddress: String,
        sportId: Int,
        trainingMode: TrainingModeResponse,
        sportTag: Int?,
    ): Boolean = sendSimpleQuery(
        PostTrainingModeQuery(
            macAddress,
            SaveTrainingModeContract(sportId, trainingMode, sportTag)
        )
    )

    suspend fun putTrainingMode(
        macAddress: String,
        sportId: Int,
        trainingMode: TrainingModeResponse,
        sportTag: Int?,
    ): Boolean = sendSimpleQuery(
        PutTrainingModeQuery(
            macAddress,
            SaveTrainingModeContract(sportId, trainingMode, sportTag)
        )
    )

    suspend fun deleteTrainingMode(
        macAddress: String,
        sportId: Int,
        modeId: Int,
        sportTag: Int?
    ): Boolean = sendSimpleQuery(
        DelTrainingModeQuery(
            macAddress,
            TrainingModeItemContract(sportId, modeId, sportTag)
        )
    )

    suspend fun getDefaultDataScreenList(
        macAddress: String,
        sportId: Int,
        modeBaseId: Int,
        sportTag: Int?,
    ): GetDataScreenResponse = sendQuery<GetDataScreenResponse>(
        GetDataScreenTemplateListQuery(
            macAddress,
            TrainingModeBaseContract(sportId, modeBaseId, sportTag)
        )
    )

    suspend fun getDataScreenList(
        macAddress: String,
        sportId: Int,
        modeId: Int,
        sportTag: Int?,
    ): GetDataScreenResponse = sendQuery<GetDataScreenResponse>(
        GetDataScreenListQuery(macAddress, TrainingModeItemContract(sportId, modeId, sportTag))
    )

    suspend fun postDataScreen(
        macAddress: String,
        sportId: Int,
        modeId: Int,
        dataScreen: GetDataScreenResponse,
        sportTag: Int?,
    ): Boolean = sendSimpleQuery(
        PostDataScreenQuery(
            macAddress,
            DataScreenContract(sportId, modeId, dataScreen, sportTag)
        )
    )

    suspend fun putDataScreen(
        macAddress: String,
        sportId: Int,
        modeId: Int,
        dataScreen: GetDataScreenResponse,
        sportTag: Int?,
    ): Boolean = sendSimpleQuery(
        PutDataScreenQuery(
            macAddress,
            DataScreenContract(sportId, modeId, dataScreen, sportTag)
        )
    )

    suspend fun deleteDataScreen(
        macAddress: String,
        sportId: Int,
        modeId: Int,
        dataScreen: GetDataScreenResponse,
        sportTag: Int?
    ): Boolean = sendSimpleQuery(
        DelDataScreenQuery(
            macAddress,
            DataScreenContract(sportId, modeId, dataScreen, sportTag)
        )
    )

    suspend fun putCompetitionTarget(macAddress: String, challenge: Challenge): Boolean =
        sendSimpleQuery(
            PutCompetitionTargetQuery(
                macAddress,
                CompetitionTargetContract(
                    sportId = challenge.sportId,
                    modeId = challenge.modeId,
                    workoutKey = challenge.workoutKey,
                    totalDistance = challenge.totalDistance,
                    avgSpeed = challenge.avgSpeed,
                    maxSpeed = challenge.maxSpeed,
                    maxSpeedTime = challenge.maxSpeedTime,
                    minSpeed = challenge.minSpeed,
                    minSpeedTime = challenge.minSpeedTime,
                    samplesCount = challenge.samplesCount,
                    totalDuration = challenge.totalDuration,
                    userName = challenge.userName,
                    workoutOwner = challenge.workoutOwner,
                    sportTag = challenge.sportTag,
                )
            )
        )

    suspend fun putCompetitionSamplesTarget(
        macAddress: String,
        samples: List<Int>,
        distanceIncluded: Boolean
    ): Boolean = sendSimpleQuery(
        PutCompetitionSamplesTargetQuery(
            macAddress,
            samples,
            distanceIncluded
        )
    )

    private suspend inline fun <reified T : Response> sendQuery(query: DeviceSpecificQuery): T {
        return suuntoRepositoryClient.waitForServiceReady()
            .andThen(suuntoRepositoryClient.sendQuery(query).first().toSingle().map { response ->
                if (response is T) {
                    return@map response
                }
                (response as? ErrorResponse)?.cause?.let { error -> throw error }
                throw SuuntoRepositoryException("Invalid response [$response]")
            })
            .await()
    }

    private suspend fun sendSimpleQuery(query: DeviceSpecificQuery): Boolean =
        sendQuery<SportModesSimpleResponse>(query).isSuccess
}
