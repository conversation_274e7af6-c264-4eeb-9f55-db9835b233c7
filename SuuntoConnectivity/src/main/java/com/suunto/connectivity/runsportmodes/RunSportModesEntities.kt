package com.suunto.connectivity.runsportmodes

import android.os.Parcelable
import androidx.annotation.RestrictTo
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.Response
import com.suunto.connectivity.runsportmodes.entities.RecentSportHeaderEntity
import com.suunto.connectivity.runsportmodes.entities.SportHeaderEntity
import com.suunto.connectivity.runsportmodes.entities.TrainingModeData
import com.suunto.connectivity.runsportmodes.entities.TrainingModeHeaderEntity
import kotlinx.parcelize.Parcelize

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class GetRecentSportsQuery(
    override val macAddress: String,
    val withSportTags: Boolean,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_RECENT_SPORTS
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class GetAllSportsQuery(
    override val macAddress: String,
    val withSportTags: Boolean,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_ALL_SPORTS
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class GetTrainingModeTemplateListQuery(
    override val macAddress: String,
    val sportItemContract: SportItemContract,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_TRAINING_MODE_TEMPLATE_LIST
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetTrainingModeTemplateQuery(
    override val macAddress: String,
    val trainingModeItemContract: TrainingModeItemContract,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_TRAINING_MODE_TEMPLATE
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class GetTrainingModeListQuery(
    override val macAddress: String,
    val sportItemContract: SportItemContract,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_TRAINING_MODE_LIST
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetTrainingModeQuery(
    override val macAddress: String,
    val trainingModeItemContract: TrainingModeItemContract,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_TRAINING_MODE
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetDataScreenTemplateListQuery(
    override val macAddress: String,
    val trainingModeBaseContract: TrainingModeBaseContract,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_DATA_SCREEN_TEMPLATE_LIST
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetDataScreenListQuery(
    override val macAddress: String,
    val trainingModeItemContract: TrainingModeItemContract
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_DATA_SCREEN_LIST
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class PostTrainingModeQuery(
    override val macAddress: String,
    val saveTrainingModeContract: SaveTrainingModeContract,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_POST_TRAINING_MODE
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class PutTrainingModeQuery(
    override val macAddress: String,
    val saveTrainingModeContract: SaveTrainingModeContract,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_PUT_TRAINING_MODE
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class DelTrainingModeQuery(
    override val macAddress: String,
    val trainingModeItemContract: TrainingModeItemContract
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_DEL_TRAINING_MODE
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class PostDataScreenQuery(
    override val macAddress: String,
    val dataScreen: DataScreenContract
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_POST_DATA_SCREEN
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class PutDataScreenQuery(
    override val macAddress: String,
    val dataScreen: DataScreenContract
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_PUT_DATA_SCREEN
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class DelDataScreenQuery(
    override val macAddress: String,
    val dataScreen: DataScreenContract
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_DEL_DATA_SCREEN
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class PutCompetitionTargetQuery(
    override val macAddress: String,
    val competitionTargetContract: CompetitionTargetContract,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_POST_COMPETITION_INFO_TARGET
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class PutCompetitionSamplesTargetQuery(
    override val macAddress: String,
    val speedSamples: List<Int>,
    val distanceIncluded: Boolean
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_POST_COMPETITION_SAMPLES_TARGET
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetRecentSportsResponse(
    val arrayData: List<RecentSportHeaderEntity>
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetAllSportsResponse(
    val arrayData: List<SportHeaderEntity>
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetTrainingModeHeaderListResponse(
    val arrayData: List<TrainingModeHeaderEntity>
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetTrainingModeListResponse(
    val arrayData: List<TrainingModeHeaderEntity>
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class TrainingModeResponse(
    val trainingBaseMode: Int,
    val modeId: Int,
    val modeName: String,
    val trainingModeData: TrainingModeData
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class SportModesSimpleResponse(
    val isSuccess: Boolean
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetDataScreenResponse(
    val dsData: String,
) : Response {
    companion object {
        const val GROUP_SEPARATOR = ";"
        const val DATA_OPTION_SEPARATOR = ","
    }
}

@JsonClass(generateAdapter = true)
@Parcelize
data class SportItemContract(
    val sportId: Int,
    val lastModeId: Int? = null,
    val sportTag: Int? = null,
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class TrainingModeItemContract(
    @Json(name = "sportModeId") val sportId: Int,
    val trainingModeId: Int,
    val sportTag: Int? = null,
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class TrainingModeBaseContract(
    @Json(name = "sportModeId") val sportId: Int,
    val trainingBaseMode: Int,
    val sportTag: Int?,
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class SaveTrainingModeContract(
    val sportId: Int,
    val modeItem: TrainingModeResponse,
    val sportTag: Int?,
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class DataScreenContract(
    val sportId: Int,
    val modeId: Int,
    val dataScreens: GetDataScreenResponse,
    val sportTag: Int?,
) : Parcelable

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class CompetitionTargetContract(
    val sportId: Int,
    val modeId: Int,
    val workoutKey: String? = "",
    val totalDistance: Double? = 0.0,
    val avgSpeed: Double,
    val maxSpeed: Double,
    val maxSpeedTime: Int?,
    val minSpeed: Double,
    val minSpeedTime: Int?,
    val samplesCount: Int,
    val totalDuration: Double? = 0.0,
    val userName: String? = "",
    val workoutOwner: String? = "",
    val sportTag: Int? = null,
) : Parcelable
