package com.suunto.connectivity.runsportmodes.entities

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@Parcelize
@JsonClass(generateAdapter = true)
data class SportHeaderEntity(
    @<PERSON><PERSON>(name = "sportModeId") val sportId: Int,
    @<PERSON>son(name = "trainingModeNum") val trainingModeNum: Int,
    @Json(name = "sportTag") val sportTag: Int?,
) : Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class RecentSportHeaderEntity(
    @<PERSON>son(name = "sportId") val sportId: Int,
    @Json(name = "lastUseTime") val lastUseTime: Int,
    @Json(name = "sportTag") val sportTag: Int?,
) : Parcelable
