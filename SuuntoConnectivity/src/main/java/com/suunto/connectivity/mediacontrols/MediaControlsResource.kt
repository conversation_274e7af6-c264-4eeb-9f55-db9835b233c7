package com.suunto.connectivity.mediacontrols

import android.content.Context
import android.net.Uri
import com.movesense.mds.MdsException
import com.movesense.mds.MdsResource
import com.movesense.mds.MdsResponse
import com.squareup.moshi.JsonDataException
import com.squareup.moshi.Moshi
import com.stt.android.coroutines.LoggingExceptionHandler
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.suunto.connectivity.R
import com.suunto.connectivity.mediacontrols.callback.MediaControlListener
import com.suunto.connectivity.mediacontrols.domain.MediaCommandRequest
import com.suunto.connectivity.mediacontrols.domain.MediaNotificationActionRequest
import com.suunto.connectivity.mediacontrols.domain.MediaPlayerStateResponse
import com.suunto.connectivity.mediacontrols.domain.MediaTrackInfoResponse
import com.suunto.connectivity.mediacontrols.exceptions.CommandNotValidException
import com.suunto.connectivity.mediacontrols.exceptions.NotificationActionNotValidException
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.util.FileUtils
import com.suunto.connectivity.util.NotificationSettingsHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.net.HttpURLConnection.HTTP_BAD_REQUEST
import java.net.HttpURLConnection.HTTP_FORBIDDEN
import java.net.HttpURLConnection.HTTP_NOT_IMPLEMENTED
import java.net.HttpURLConnection.HTTP_NO_CONTENT
import java.net.HttpURLConnection.HTTP_OK
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject

class MediaControlsResource @Inject constructor(
    private val context: Context,
    private val mediaControlsModel: MediaControlsModel,
    private val notificationSettingsHelper: NotificationSettingsHelper,
    private val mdsRx: MdsRx,
    moshi: Moshi,
) : MdsResource {
    private val scope = CoroutineScope(Main.immediate + SupervisorJob() + LoggingExceptionHandler)

    private val isInitialized = AtomicBoolean(false)
    private val initializationMutex = Mutex()

    private val playerStateSubscriberCount = AtomicInteger(0)
    private val trackInfoSubscriberCount = AtomicInteger(0)

    private val mediaPlayerStateResponseAdapter by lazy {
        moshi.adapter(MediaPlayerStateResponse::class.java)
    }
    private val mediaTrackInfoResponseAdapter by lazy {
        moshi.adapter(MediaTrackInfoResponse::class.java)
    }
    private val mediaCommandRequestAdapter by lazy {
        moshi.adapter(MediaCommandRequest::class.java)
    }
    private val mediaNotificationActionRequestAdapter by lazy {
        moshi.adapter(MediaNotificationActionRequest::class.java)
    }

    private var mediaTrackUpdatesJob: Job? = null
    private var playerStateUpdatesJob: Job? = null

    fun initializeResource() {
        scope.launch {
            initializeResourceInternal()
        }
    }

    private suspend fun initializeResourceInternal() = withContext(IO) {
        if (isInitialized.get()) {
            return@withContext
        }

        initializationMutex.withLock {
            if (isInitialized.get()) {
                return@withContext
            }

            Timber.d("Initializing MediaControlsResource")

            runSuspendCatching {
                FileUtils.copyRawResourceToStream(
                    context,
                    R.raw.suunto_media,
                    context.openFileOutput(RESOURCE_DESCRIPTOR, Context.MODE_PRIVATE),
                )

                mdsRx.registerResource(RESOURCE_DESCRIPTOR, this@MediaControlsResource)
                    .await()

                isInitialized.set(true)

                withContext(Main) {
                    launchMediaControlsUpdates()
                }
            }.onFailure { e ->
                Timber.w(e, "Error while initializing media controls resource")
            }
        }
    }

    fun launchMediaControlsUpdates() {
        if (!isInitialized.get()) {
            initializeResource()
            return
        }
        if (!notificationSettingsHelper.notificationsEnabled(context)) {
            Timber.w("Cannot launch MediaControls updates, missing notifications access permission")
            return
        }

        Timber.d("Launching MediaControls updates")

        mediaTrackUpdatesJob?.cancel()
        mediaTrackUpdatesJob = mediaControlsModel.mediaTrackInfo
            .onEach { mediaTrackInfo ->
                if (trackInfoSubscriberCount.get() <= 0) {
                    return@onEach
                }

                val response = mediaTrackInfo?.let(::MediaTrackInfoResponse)
                    ?.let(mediaTrackInfoResponseAdapter::toJson)
                sendNotification(URI_MEDIA_TRACK_INFO, response)
            }
            .launchIn(scope)

        playerStateUpdatesJob?.cancel()
        playerStateUpdatesJob = mediaControlsModel.mediaPlayerState
            .onEach { mediaPlayerState ->
                if (playerStateSubscriberCount.get() <= 0) {
                    return@onEach
                }

                val response = mediaPlayerState?.let(::MediaPlayerStateResponse)
                    ?.let(mediaPlayerStateResponseAdapter::toJson)
                sendNotification(URI_MEDIA_PLAYER_STATE, response)
            }.launchIn(scope)
    }

    fun stopMediaControlUpdates() {
        Timber.d("Stopping MediaControls updates")
        mediaTrackUpdatesJob?.cancel()
        playerStateUpdatesJob?.cancel()
        scope.launch {
            if (playerStateSubscriberCount.get() > 0) {
                sendNotification(URI_MEDIA_PLAYER_STATE, null)
            }
            if (trackInfoSubscriberCount.get() > 0) {
                sendNotification(URI_MEDIA_TRACK_INFO, null)
            }
        }
    }

    fun setMediaControlListener(mediaControlListener: MediaControlListener) {
        mediaControlsModel.mediaControlListener = mediaControlListener
    }

    override fun onResourceReady() {
        Timber.d("MediaControlsResource registered")
    }

    override fun onError(e: MdsException) {
        Timber.w(e, "MediaControlsResource error")
    }

    override fun get(uri: Uri, body: String): MdsResponse = when (uri.toString()) {
        URI_MEDIA_PLAYER_STATE -> mediaControlsModel.getLatestMediaPlayerState()
            ?.let(::MediaPlayerStateResponse)
            ?.let(mediaPlayerStateResponseAdapter::toJson)
            ?.let { MdsResponse(HTTP_OK, it) }
            ?: mdsResponseWithCode(HTTP_NO_CONTENT)

        URI_MEDIA_TRACK_INFO -> mediaControlsModel.mediaTrackInfo
            .value
            ?.let(::MediaTrackInfoResponse)
            ?.let(mediaTrackInfoResponseAdapter::toJson)
            ?.let { MdsResponse(HTTP_OK, it) }
            ?: mdsResponseWithCode(HTTP_NO_CONTENT)

        else -> mdsResponseWithCode(HTTP_NOT_IMPLEMENTED)
    }

    override fun put(uri: Uri, s: String): MdsResponse = when (uri.toString()) {
        URI_MEDIA_PLAYER_CONTROL -> {
            val result = runCatching {
                mediaCommandRequestAdapter.fromJson(s)
                    ?.let(mediaControlsModel::executeCommand)
                    ?: throw CommandNotValidException()
            }
            val responseCode = when (result.exceptionOrNull()) {
                null -> HTTP_OK
                is JsonDataException,
                is CommandNotValidException -> HTTP_BAD_REQUEST
                else -> HTTP_FORBIDDEN
            }
            mdsResponseWithCode(responseCode)
        }

        URI_MEDIA_NOTIFICATION_ACTION -> {
            val result = runCatching {
                mediaNotificationActionRequestAdapter.fromJson(s)
                    ?.let(mediaControlsModel::executeMediaNotificationAction)
                    ?: throw CommandNotValidException()
            }
            val responseCode = when (result.exceptionOrNull()) {
                null -> HTTP_OK
                is CommandNotValidException -> HTTP_BAD_REQUEST
                is NotificationActionNotValidException -> HTTP_BAD_ACTION_REQUEST
                else -> HTTP_FORBIDDEN
            }
            mdsResponseWithCode(responseCode)
        }

        else -> mdsResponseWithCode(HTTP_NOT_IMPLEMENTED)
    }

    override fun put(uri: Uri, data: ByteArray, offset: Long, total: Long): MdsResponse =
        mdsResponseWithCode(HTTP_NOT_IMPLEMENTED)

    override fun post(uri: Uri, s: String): MdsResponse =
        mdsResponseWithCode(HTTP_NOT_IMPLEMENTED)

    override fun delete(uri: Uri, s: String?): MdsResponse =
        mdsResponseWithCode(HTTP_NOT_IMPLEMENTED)

    override fun subscribe(uri: Uri, body: String): MdsResponse {
        Timber.d("Received SUBSCRIBE: uri = $uri, body = $body")
        return when (uri.toString()) {
            URI_MEDIA_PLAYER_STATE -> {
                playerStateSubscriberCount.incrementAndGet()
                get(uri, body)
            }
            URI_MEDIA_TRACK_INFO -> {
                trackInfoSubscriberCount.incrementAndGet()
                get(uri, body)
            }
            else -> {
                mdsResponseWithCode(HTTP_NOT_IMPLEMENTED)
            }
        }
    }

    override fun unsubscribe(uri: Uri, body: String): MdsResponse {
        Timber.d("Received UNSUBSCRIBE: uri = $uri, body = $body")
        return when (uri.toString()) {
            URI_MEDIA_PLAYER_STATE -> {
                playerStateSubscriberCount.decrementAndGet()
                mdsResponseWithCode(HTTP_OK)
            }
            URI_MEDIA_TRACK_INFO -> {
                trackInfoSubscriberCount.decrementAndGet()
                mdsResponseWithCode(HTTP_OK)
            }
            else -> {
                mdsResponseWithCode(HTTP_NOT_IMPLEMENTED)
            }
        }
    }

    fun onDestroy() {
        scope.cancel()
        mediaControlsModel.onDestroy()
    }

    private suspend fun sendNotification(uri: String, body: String?) {
        Timber.d("Sending notification: uri = $uri, body = $body")
        runSuspendCatching {
            mdsRx.sendNotification(uri, body).await()
        }.onFailure { e ->
            Timber.w(e, "Failed to send notification")
        }
    }

    private companion object {
        const val RESOURCE_DESCRIPTOR = "suunto_media.wbr"
        const val URI_MEDIA_PLAYER_CONTROL = "/Media/Player/Control"
        const val URI_MEDIA_PLAYER_STATE = "/Media/Player/State"
        const val URI_MEDIA_TRACK_INFO = "/Media/Track/Info"
        const val URI_MEDIA_NOTIFICATION_ACTION = "/Media/Notification/Action"
        const val HTTP_BAD_ACTION_REQUEST = 416

        fun mdsResponseWithCode(code: Int): MdsResponse = MdsResponse(code, "{}")
    }
}
