package com.suunto.connectivity.util

import android.content.SharedPreferences
import androidx.core.content.edit
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.squareup.moshi.Moshi
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice

@JsonClass(generateAdapter = true)
data class DataLayerDeviceMac(
    @Json(name = "nodeId")
    val nodeId: String,

    @Json(name = "macId")
    val macId: String
)

fun getDataLayerDeviceMacFromPreferences(
    device: SuuntoBtDevice,
    sharedPreferences: SharedPreferences,
    key: String,
    moshi: Moshi
): String? {
    val json: String? =
        sharedPreferences.getString(key, null)

    if (json != null) {
        val adapter = moshi.adapter(DataLayerDeviceMac::class.java)
        val dataLayerDeviceMac = adapter.fromJson(json)
        dataLayerDeviceMac?.let {
            if (it.nodeId == device.macAddress) {
                return dataLayerDeviceMac.macId
            }
        }
    }
    return null
}

fun setDataLayerDeviceMacToPreferences(
    device: SuuntoBtDevice,
    sharedPreferences: SharedPreferences,
    key: String,
    moshi: Moshi,
    mac: String
) {
    val adapter = moshi.adapter(DataLayerDeviceMac::class.java)
    val dataLayerDeviceMac = DataLayerDeviceMac(device.macAddress, mac)
    sharedPreferences.edit {
        putString(key, adapter.toJson(dataLayerDeviceMac))
    }
}
