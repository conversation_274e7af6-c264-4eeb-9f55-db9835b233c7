package com.suunto.connectivity.util;

import android.content.Context;
import androidx.annotation.NonNull;
import java.io.FileNotFoundException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import timber.log.Timber;

import javax.inject.Inject;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;

public class FileUtils {
    @Inject
    public FileUtils() {
    }

    public static String readRawResourceFile(Context ctx, int resourceId) {
        InputStream in = null;
        try {
            in = ctx.getResources().openRawResource(resourceId);

            StringBuilder sb = new StringBuilder();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) != -1) {
                sb.append(new String(buffer, 0, len, "UTF-8"));
            }

            return sb.toString();
        } catch (IOException e) {
            Timber.e(e, "Reading raw resource file failed");
            return null;
        } finally {
            IOUtils.closeQuietly(in);
        }
    }

    /**
     * Writes json content to a path relative to files directory of the context
     *
     * @param context      Android context
     * @param json         Json to write to a file
     * @param relativePath Path of the output file
     * @return True if writing succeeds
     */
    public boolean writeJsonTo(Context context, String json, String relativePath) {
        return writeJsonTo(json, new File(context.getFilesDir(), relativePath));
    }

    public boolean writeJsonTo(String json, File file) {
        BufferedWriter writer = null;
        try {
            String jsonLog;
            if (json.length() > 200) {
                jsonLog = json.substring(0, 200) + "...";
            } else {
                jsonLog = json;
            }
            Timber.d("Writing json [%s] to file [%s]", jsonLog, file.getAbsolutePath());
            writer = new BufferedWriter(new FileWriter(file));
            writer.write(json);
            writer.flush();
            return true;
        } catch (IOException e) {
            Timber.w(e, "Unable to write json file [%s]", file.getPath());
            return false;
        } finally {
            IOUtils.closeQuietly(writer);
        }
    }

    public static String readJsonFile(File file) {
        StringBuilder contentBuilder = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new FileReader(file)))
        {
            String sCurrentLine;
            while ((sCurrentLine = br.readLine()) != null)
            {
                contentBuilder.append(sCurrentLine).append("\n");
            }
        } catch (Exception e) {
            Timber.e(e, "Json file %s reading failed", file.getAbsolutePath());
        }
        return contentBuilder.toString();
    }

    /**
     * Copy raw resource file to an output stream.
     *
     * @param resourceId Resource id
     * @param out Target stream
     * @throws IOException
     */
    public static void copyRawResourceToStream(Context context, int resourceId, OutputStream out)
        throws IOException {
        InputStream in = null;
        try {
            in = context.getResources().openRawResource(resourceId);
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
        } finally {
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(in);
        }
    }

    /**
     * Checks whether a file exists
     *
     * @param context Android context
     * @param relativePath Path to the file to check
     * @return True if the file exists
     */
    public boolean fileExists(Context context, String relativePath) {
        File file = new File(context.getFilesDir(), relativePath);
        return file.exists();
    }

    /**
     * Remove file. Ignore the success result.
     *
     * @param context      Android context
     * @param relativePath Path to the file.
     */
    public void removeFile(Context context, String relativePath) {
        File file = new File(context.getFilesDir(), relativePath);
        file.delete();
    }

    /**
     * Reads a file included in test/resources/ folder and returns it as a String
     *
     * @param path Path relative to test/resources/ folder
     * @return File as a String
     * @throws IOException when file does not exist
     */
    public static String getResourceAsString(String path) throws IOException {
        InputStream is = FileUtils.class.getClassLoader().getResourceAsStream(path);

        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new InputStreamReader(is));
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }

            return sb.toString();
        } finally {
            IOUtils.closeQuietly(reader);
        }
    }

    /**
     * Writes data to a file
     *
     * @param path Path to which the file should be written to
     * @param filename Filename to write
     * @param data Data to write to given file
     * @throws IOException
     */
    public void writeDataToFile(File path, String filename, String data) throws IOException {
        FileOutputStream out = null;
        try {
            File file = new File(path, filename);
            out = new FileOutputStream(file);
            out.write(data.getBytes());
        } finally {
            IOUtils.closeQuietly(out);
        }
    }

    /**
     * Copies file contents to another file
     *
     * @param source Source file to copy
     * @param path Path to which the file should be written to
     * @param filename Filename to write
     * @throws IOException
     */
    public void copyFile(File source, File path, String filename) throws IOException {
        copyFile(source, new File(path, filename));
    }

    /**
     * Copies file contents to another file
     *
     * @param source Source file to copy from
     * @param target Target file to copy to
     * @throws IOException
     */
    public void copyFile(File source, File target) throws IOException {
        FileInputStream in = null;
        FileOutputStream out = null;
        try {
            in = new FileInputStream(source);
            out = new FileOutputStream(target);

            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } finally {
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(out);
        }
    }

    /**
     * Copies input stream contents to file
     *
     * @param in     Input stream
     * @param target Target file to copy to
     * @throws IOException
     */
    public static void copyFromStreamToFile(@NonNull InputStream in, @NonNull File target) throws IOException {
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(target);

            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } finally {
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(out);
        }
    }

    /**
     * Delete directory and its contents including subdirectories.
     *
     * @param directory Directory.
     */
    static public void deleteDirectoryAndContents(File directory) {
        if (directory.isDirectory()) {
            File[] children = directory.listFiles();
            for (File child : children) {
                if (child.isDirectory()) {
                    deleteDirectoryAndContents(child);
                } else {
                    child.delete();
                }
            }
            directory.delete();
        }
    }

    static public void zipFolder(File inputFolder, File outputFile, @NonNull String pathStartString) {
        try (FileOutputStream fos = new FileOutputStream(outputFile);
             ZipOutputStream zos = new ZipOutputStream(fos)) {
            zipFolderRecursive(inputFolder, zos, pathStartString);
        } catch (IOException exception) {
            Timber.w("Error in zipping folder: %s", exception.toString());
        }
    }

    static public void zipFolderRecursive(File inputFolder, ZipOutputStream zos, @NonNull String pathStartString)
        throws IOException {
        if (inputFolder.isDirectory()) {
            File[] children = inputFolder.listFiles();
            if (children != null) {
                for (File child : children) {
                    if (child.isDirectory()) {
                        zipFolderRecursive(child, zos, pathStartString);
                    } else if (child.getPath().contains(pathStartString)) {
                        final int pathStart = child.getPath().indexOf(pathStartString);
                        final String entry =
                            child.getPath().substring(pathStart) + "/" + child.getName();
                        Timber.d("Adding file: %s", entry);
                        byte[] buffer = new byte[1024];

                        try (FileInputStream fis = new FileInputStream(child)) {
                            zos.putNextEntry(new ZipEntry(entry));
                            int length;
                            while ((length = fis.read(buffer)) > 0) {
                                zos.write(buffer, 0, length);
                            }
                            zos.closeEntry();
                        }
                    }
                }
            }
        }
    }
}
