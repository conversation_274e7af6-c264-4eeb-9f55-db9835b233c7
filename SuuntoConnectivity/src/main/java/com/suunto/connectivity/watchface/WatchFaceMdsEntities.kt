package com.suunto.connectivity.watchface

import android.os.Parcelable
import androidx.annotation.RestrictTo
import com.squareup.moshi.JsonClass
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.repository.commands.DeviceSpecificQuery
import com.suunto.connectivity.repository.commands.Response
import kotlinx.parcelize.Parcelize

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetWatchFaceCapabilitiesQuery(
    override val macAddress: String,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_WATCH_FACES_CAPABILITIES
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetCurrentWatchFaceInfoQuery(
    override val macAddress: String,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_CURRENT_WATCH_FACE_INFO
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetWatchFaceInfoQuery(
    override val macAddress: String,
    val watchFaceId: String
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_WATCH_FACE_INFO
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class SetAsCurrentWatchFaceQuery(
    override val macAddress: String,
    val watchFaceId: String,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_SET_WATCH_FACES_AS_CURRENT
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetWatchFaceInstalledListQuery(
    override val macAddress: String,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_WATCH_FACES_INSTALLED_LIST
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class PutWatchFaceInfoQuery(
    override val macAddress: String,
    val contract: InstallWatchFaceContract,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_PREPARE_WATCH_FACE_INSTALL
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetWatchFaceInstallPathQuery(
    override val macAddress: String,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_WATCH_FACE_INSTALL_PATH
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class StartWatchFaceInstallQuery(
    override val macAddress: String,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_WATCH_FACE_START_INSTALL
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class UninstallWatchFaceInfoQuery(
    override val macAddress: String,
    val watchFaceId: String,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_UNINSTALL_WATCH_FACE
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class SyncWatchFaceQuery(
    override val macAddress: String,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_SYNC_WATCH_FACE
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class WatchFaceSimpleResponse(
    val isSuccess: Boolean
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetWatchFaceCapabilitiesResponse(
    val capabilities: List<String>
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetWatchFaceInstalledListResponse(
    val lists: List<MdsWatchFace>
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetWatchFaceInfoResponse(
    val watchFace: MdsWatchFace?
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@JsonClass(generateAdapter = true)
@Parcelize
data class GetWatchFaceInstalledPathResponse(
    val installPath: String
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
class SyncWatchFaceResponse : Response

@JsonClass(generateAdapter = true)
@Parcelize
data class GetWatchFaceInfoContract(
    val id: String,
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class SetAsCurrentWatchFaceContract(
    val id: String,
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class WatchFaceDeleteContract(
    val id: String
) : Parcelable

@JsonClass(generateAdapter = true)
@Parcelize
data class InstallWatchFaceContract(
    val id: String,
    val name: String,
    val version: String,
    val size: Long,
    val capabilities: String,
    val md5sum: String,
    val preImgName: String,
) : Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class MdsWatchFace(
    val id: String,
    val name: String,
    val version: String,
    val sortIndex: Int,
    val current: Boolean,
) : Parcelable

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class SubscribeCurrentWatchFaceIdQuery(
    override val macAddress: String,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_SUBSCRIBE_CURRENT_WATCH_FACE_ID
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class SubscribeCurrentWatchFaceIdResponse(
    val currentWatchFaceId: String,
) : Response
