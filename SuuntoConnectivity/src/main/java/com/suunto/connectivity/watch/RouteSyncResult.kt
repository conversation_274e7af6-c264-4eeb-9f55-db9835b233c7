package com.suunto.connectivity.watch

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.suunto.connectivity.repository.SyncResult
import kotlinx.parcelize.Parcelize
import kotlin.jvm.JvmStatic

@Parcelize
data class RouteSyncResult internal constructor(
    /**
     * fetching & storing routes sync result
     */
    @SerializedName("routesResult")
    val routesResult: SyncResult,
    /**
     * List of pairs sync results for every route that was tried to be synchronized.
     */
    @SerializedName("singleRouteSyncResults")
    val singleRouteSyncResults: List<SyncResult>,
    /**
     * Route sync duration in milliseconds
     */
    @SerializedName("syncDuration")
    val syncDuration: Long?
) : Parcelable {

    class Builder internal constructor(
        private var routesResult: SyncResult = SyncResult.unknown(),
        private var singleRouteSyncResults: List<SyncResult> = listOf(),
        private var syncDuration: Long? = null,
    ) {
        fun routesResult(result: SyncResult): Builder = apply { this.routesResult = result }

        fun singleRouteSyncResults(results: List<SyncResult>): Builder =
            apply { this.singleRouteSyncResults = results }

        fun syncDuration(syncDuration: Long): Builder = apply { this.syncDuration = syncDuration }

        fun build(): RouteSyncResult =
            RouteSyncResult(
                routesResult = routesResult,
                singleRouteSyncResults = singleRouteSyncResults,
                syncDuration = syncDuration,
            )
    }

    companion object {
        @JvmStatic
        fun builder(): Builder = Builder()

        @JvmStatic
        fun skipped() = Builder(SyncResult.skipped()).build()

        @JvmStatic
        fun unknown() = Builder(SyncResult.unknown()).build()
    }
}
