package com.suunto.connectivity.watch

import android.annotation.SuppressLint
import com.google.gson.Gson
import com.suunto.connectivity.repository.commands.SleepTrackingMode
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.settings.Setting
import rx.Completable
import rx.Single

class SpartanSleepTrackingSetting internal constructor(
    serialNumber: String,
    mdsRx: MdsRx,
    gson: Gson
) : Setting<SleepTrackingMode> {
    @Suppress("PLATFORM_CLASS_MAPPED_TO_KOTLIN")
    private val setting: Setting<Integer> =
        SpartanSetting(URI, NAME, Integer::class.java, serialNumber, mdsRx, gson)

    override fun getValue(): Single<SleepTrackingMode> =
        setting.value
            .map { it.toInt().toSleepTrackingMode() }

    @Suppress("DEPRECATION")
    @SuppressLint("UseValueOf")
    override fun setValue(mode: SleepTrackingMode): Completable =
        setting.setValue(Integer(mode.wbValue()))

    override fun getCachedValue(): SleepTrackingMode? =
        setting.cachedValue
            ?.toInt()
            ?.toSleepTrackingMode()

    override fun clearCachedValue() {
        setting.clearCachedValue()
    }

    private companion object {
        val URI = "Ui/SleepTrackingMode"
        val NAME = "value"

        fun Int.toSleepTrackingMode(): SleepTrackingMode = when (this) {
            0 -> SleepTrackingMode.OFF
            1 -> SleepTrackingMode.AUTO
            2 -> SleepTrackingMode.MANUAL
            else -> throw IllegalArgumentException("Can't convert '$this' to SleepTrackingMode")
        }

        fun SleepTrackingMode.wbValue(): Int = when (this) {
            SleepTrackingMode.OFF -> 0
            SleepTrackingMode.AUTO -> 1
            SleepTrackingMode.MANUAL -> 2
        }
    }
}
