package com.suunto.connectivity.watch

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice

class BluetoothAdapterWrapper(private val bluetoothAdapter: BluetoothAdapter) {

    fun getRemoteDevice(address: String): BluetoothDevice =
        bluetoothAdapter.getRemoteDevice(address)

    fun checkBluetoothAddress(address: String): Boolean =
        BluetoothAdapter.checkBluetoothAddress(address)
}
