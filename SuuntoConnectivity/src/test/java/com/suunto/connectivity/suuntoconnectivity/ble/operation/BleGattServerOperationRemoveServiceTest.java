package com.suunto.connectivity.suuntoconnectivity.ble.operation;

import android.bluetooth.BluetoothGattServer;
import android.bluetooth.BluetoothGattService;

import com.suunto.connectivity.suuntoconnectivity.ble.exception.GattServerRemoveServiceException;
import com.suunto.connectivity.util.workqueue.QueueOperation;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BleGattServerOperationRemoveServiceTest {

    @Mock
    private BluetoothGattServer gattServer;

    @Mock
    private BluetoothGattService gattService;

    private BleGattServerOperationRemoveService operationRemoveService;

    @Before
    public void initialize() {
        operationRemoveService = spy(new BleGattServerOperationRemoveService(gattServer, gattService));
    }

    @Test
    public void removeServiceSuccess() {
        when(gattServer.removeService(gattService)).thenReturn(true);

        operationRemoveService.run();

        assertEquals(QueueOperation.STATE_FINISHED, operationRemoveService.getState());
        verify(operationRemoveService).onCompleted();
        verify(gattServer).removeService(gattService);
    }

    @Test
    public void removeServiceFailure() {
        when(gattServer.removeService(gattService)).thenReturn(false);

        operationRemoveService.run();

        assertEquals(QueueOperation.STATE_FINISHED, operationRemoveService.getState());
        verify(operationRemoveService).onError(any(GattServerRemoveServiceException.class));
        verify(gattServer).removeService(gattService);
    }
}
