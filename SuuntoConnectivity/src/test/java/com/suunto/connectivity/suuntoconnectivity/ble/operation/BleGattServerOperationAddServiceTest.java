package com.suunto.connectivity.suuntoconnectivity.ble.operation;

import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattServer;
import android.bluetooth.BluetoothGattService;

import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattServerServiceAddedEvent;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.GattServerAddServiceException;
import com.suunto.connectivity.util.workqueue.QueueOperation;

import org.greenrobot.eventbus.EventBus;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BleGattServerOperationAddServiceTest {

    private static final String SERVICE_UUID = "a647661a-6deb-11e6-8b77-86f30ca893d3";
    private static final String SERVICE_UUID_ANOTHER = "a3fe0ab0-6ded-11e6-8b77-86f30ca893d3";

    @Mock
    private BluetoothGattServer gattServer;

    @Mock
    private BluetoothGattService gattService;

    private BleGattServerOperationAddService operationAddService;

    @Before
    public void initialize() {
        operationAddService = spy(new BleGattServerOperationAddService(gattServer, gattService));

        when(gattService.getUuid()).thenReturn(UUID.fromString(SERVICE_UUID));
    }

    @Test
    public void addServiceSuccess() {
        when(gattServer.addService(gattService)).thenReturn(true);

        operationAddService.run();

        assertEquals(QueueOperation.STATE_RUNNING, operationAddService.getState());
        verify(gattServer).addService(gattService);

        EventBus.getDefault().post(new BleGattServerServiceAddedEvent(
                BluetoothGatt.GATT_SUCCESS, gattService));

        assertEquals(QueueOperation.STATE_FINISHED, operationAddService.getState());
        verify(operationAddService).onCompleted();
    }

    @Test
    public void addServiceStartFailure() {
        when(gattServer.addService(gattService)).thenReturn(false);

        operationAddService.run();

        assertEquals(QueueOperation.STATE_FINISHED, operationAddService.getState());
        verify(operationAddService).onError(any(GattServerAddServiceException.class));
    }

    @Test
    public void addServiceFailure() {
        when(gattServer.addService(gattService)).thenReturn(true);

        operationAddService.run();

        assertEquals(QueueOperation.STATE_RUNNING, operationAddService.getState());
        verify(gattServer).addService(gattService);

        EventBus.getDefault().post(new BleGattServerServiceAddedEvent(
                BluetoothGatt.GATT_FAILURE, gattService));

        assertEquals(QueueOperation.STATE_FINISHED, operationAddService.getState());
        verify(operationAddService).onError(any(GattServerAddServiceException.class));
    }

    @Test
    public void anotherServiceEventIgnored() {
        when(gattServer.addService(gattService)).thenReturn(true);

        operationAddService.run();

        assertEquals(QueueOperation.STATE_RUNNING, operationAddService.getState());
        verify(gattServer).addService(gattService);

        BluetoothGattService gattServiceAnother = mock(BluetoothGattService.class);
        when(gattServiceAnother.getUuid()).thenReturn(UUID.fromString(SERVICE_UUID_ANOTHER));
        EventBus.getDefault().post(new BleGattServerServiceAddedEvent(
                BluetoothGatt.GATT_SUCCESS, gattServiceAnother));

        assertEquals(QueueOperation.STATE_RUNNING, operationAddService.getState());
        verify(operationAddService, never()).onCompleted();

        EventBus.getDefault().post(new BleGattServerServiceAddedEvent(
                BluetoothGatt.GATT_SUCCESS, gattService));

        assertEquals(QueueOperation.STATE_FINISHED, operationAddService.getState());
        verify(operationAddService).onCompleted();
    }
}
