package com.suunto.connectivity.suuntoconnectivity.ble.operation

import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattServer
import com.google.common.truth.Truth.assertThat
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattServerNotificationSentEvent
import com.suunto.connectivity.suuntoconnectivity.ble.exception.GattServerNotifyCharacteristicChangedException
import com.suunto.connectivity.suuntoconnectivity.utils.MockHandlerProvider
import com.suunto.connectivity.util.workqueue.QueueOperation
import org.greenrobot.eventbus.EventBus
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.spy
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class BleGattServerOperationNotifyCharacteristicChangedTest {

    private val bluetoothGattServer = mock<BluetoothGattServer>()
    private val bluetoothDevice = mock<BluetoothDevice>()
    private val characteristic = mock<BluetoothGattCharacteristic>()
    private lateinit var operationNotify: BleGattServerOperationNotifyCharacteristicChanged
    private lateinit var operationNotifyAutoComplete: BleGattServerOperationNotifyCharacteristicChanged
    private val originalHandlerProvider = QueueOperation.handlerProvider
    private val handlerProvider = MockHandlerProvider()
    private val handlerDelayController = handlerProvider.handlerDelayController

    @Before
    fun setUp() {
        QueueOperation.handlerProvider = handlerProvider

        operationNotify = spy(
            BleGattServerOperationNotifyCharacteristicChanged(
                bluetoothGattServer,
                bluetoothDevice,
                characteristic,
                true,
                VALUE,
                handlerDelayController.handler
            )
        )

        operationNotifyAutoComplete = spy(
            BleGattServerOperationNotifyCharacteristicChanged(
                bluetoothGattServer,
                bluetoothDevice,
                characteristic,
                true,
                VALUE,
                handlerDelayController.handler
            )
        )

        whenever(characteristic.setValue(any<ByteArray>()))
            .thenReturn(true)
    }

    @After
    fun tearDown() {
        QueueOperation.handlerProvider = originalHandlerProvider
    }

    @Test
    fun notifySuccess() {
        whenever(
            bluetoothGattServer.notifyCharacteristicChanged(
                bluetoothDevice,
                characteristic,
                true
            )
        ).thenReturn(true)
        operationNotify.run()
        verify(characteristic).value = VALUE
        verify(bluetoothGattServer).notifyCharacteristicChanged(
            bluetoothDevice,
            characteristic,
            true
        )
        assertThat(operationNotify.state).isEqualTo(QueueOperation.STATE_RUNNING)
        verify(operationNotify, never()).onCompleted(any())
        EventBus.getDefault().post(
            BleGattServerNotificationSentEvent(
                bluetoothDevice,
                BluetoothGatt.GATT_SUCCESS
            )
        )
        assertThat(operationNotify.state).isEqualTo(QueueOperation.STATE_FINISHED)
        verify(operationNotify).onCompleted(BluetoothGatt.GATT_SUCCESS)
    }

    @Test
    fun notifyAutoComplete() {
        whenever(
            bluetoothGattServer.notifyCharacteristicChanged(
                bluetoothDevice,
                characteristic,
                true
            )
        ).thenReturn(true)

        BleGattServerOperationNotifyCharacteristicChanged.notificationSentCallbackSupported = false

        operationNotifyAutoComplete.run()
        verify(characteristic).value = VALUE
        verify(bluetoothGattServer).notifyCharacteristicChanged(
            bluetoothDevice,
            characteristic,
            true
        )
        assertThat(operationNotifyAutoComplete.state).isEqualTo(QueueOperation.STATE_RUNNING)
        verify(operationNotifyAutoComplete, never()).onCompleted(any())
        handlerDelayController.runAll()
        assertThat(operationNotifyAutoComplete.state).isEqualTo(QueueOperation.STATE_FINISHED)
        verify(operationNotifyAutoComplete).onCompleted(BluetoothGatt.GATT_SUCCESS)
    }

    @Test
    fun notifyStartFailure() {
        whenever(
            bluetoothGattServer.notifyCharacteristicChanged(
                bluetoothDevice,
                characteristic,
                true
            )
        ).thenReturn(false)
        operationNotify.run()
        assertThat(operationNotify.state).isEqualTo(QueueOperation.STATE_FINISHED)
        verify(operationNotify).onError(any<GattServerNotifyCharacteristicChangedException>())
    }

    @Test
    fun notifyFailure() {
        whenever(
            bluetoothGattServer.notifyCharacteristicChanged(
                bluetoothDevice,
                characteristic,
                true
            )
        ).thenReturn(true)
        operationNotify.run()
        assertThat(operationNotify.state).isEqualTo(QueueOperation.STATE_RUNNING)
        EventBus.getDefault().post(
            BleGattServerNotificationSentEvent(bluetoothDevice, BluetoothGatt.GATT_FAILURE)
        )
        assertThat(operationNotify.state).isEqualTo(QueueOperation.STATE_FINISHED)
        verify(operationNotify).onError(any<GattServerNotifyCharacteristicChangedException>())
    }

    companion object {
        private val VALUE = ByteArray(10)
    }
}
