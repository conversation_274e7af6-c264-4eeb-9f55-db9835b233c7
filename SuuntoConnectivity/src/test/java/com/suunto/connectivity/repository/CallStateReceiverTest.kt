package com.suunto.connectivity.repository

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import com.stt.android.testutils.HandlerDelayController
import org.junit.Before
import org.junit.Test
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.eq
import org.mockito.Mockito.never
import org.mockito.Mockito.spy
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.Mockito.verifyNoMoreInteractions
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.Mockito.`when` as whenever

private const val CALLER = "caller"
private const val PHONE_NUMBER = "12345"

class CallStateReceiverTest {

    private val telephonyManagerConstants = object : TelephonyManagerConstants {
        override val actionPhoneStateChanged = "actionPhoneStateChanged"
        override val extraState = "extraState"
        override val extraStateRinging = "extraStateRinging"
        override val extraIncomingNumber = "extraIncomingNumber"
        override val extraStateOffhook = "extraStateOffhook"
        override val extraStateIdle = "extraStateIdle"
    }

    private val context = mock<Context>()
    private val contactNameProvider = mock<ContactNameProvider>()
    private val ringingIntent = createRingingMockIntent(PHONE_NUMBER)
    private val ringingIntentNoPhoneNumber = createRingingMockIntent(null)
    private val offHookIntent = createMockIntent(telephonyManagerConstants.extraStateOffhook)
    private val idleIntent = createMockIntent(telephonyManagerConstants.extraStateIdle)
    private val handlerDelayController = HandlerDelayController()
    private lateinit var callStateReceiver: CallStateReceiver

    private fun createMockIntent(state: String): Intent = mock {
        on { action } doReturn telephonyManagerConstants.actionPhoneStateChanged
        on { getStringExtra(telephonyManagerConstants.extraState) } doReturn state
    }

    private fun createRingingMockIntent(phoneNumber: String?): Intent =
        createMockIntent(telephonyManagerConstants.extraStateRinging).apply {
            whenever(this.getStringExtra(telephonyManagerConstants.extraIncomingNumber))
                .thenReturn(phoneNumber)
        }

    @Before
    fun setUp() {
        callStateReceiver = spy(
            CallStateReceiver(
                context,
                contactNameProvider,
                telephonyManagerConstants,
                handlerDelayController.handler
            )
        )

        whenever(contactNameProvider.get(PHONE_NUMBER)).thenReturn(CALLER)

        whenever(
            context.checkPermission(
                ArgumentMatchers.eq(android.Manifest.permission.READ_CALL_LOG),
                anyInt(),
                anyInt()
            )
        ).thenReturn(
            PackageManager.PERMISSION_DENIED
        )

        doReturn(false).`when`(callStateReceiver).isReadCallLogPermissionGranted()
    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    @Test
    fun `Should register phone state receiver when listener added`() {
        callStateReceiver.addListener(DummyListener())
        verify(context).registerReceiver(any(), any(), eq(null), eq(null))
    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    @Test
    fun `Should unregister phone state receiver when last listener removed`() {
        val listener1 = DummyListener()
        val listener2 = DummyListener()

        callStateReceiver.addListener(listener1)
        callStateReceiver.addListener(listener2)
        verify(context).registerReceiver(any(), any(), eq(null), eq(null))

        callStateReceiver.removeListener(listener1)
        verify(context, never()).unregisterReceiver(any())

        callStateReceiver.removeListener(listener2)
        verify(context).unregisterReceiver(any())
    }

    @Test
    fun `Should notify incoming call`() {
        val listener = spy(DummyListener())
        callStateReceiver.addListener(listener)
        callStateReceiver.onReceive(context, ringingIntent)
        verify(listener).onIncomingCall(CALLER)
    }

    private fun readCallLogPermissionGranted() {
        doReturn(true).`when`(callStateReceiver).isReadCallLogPermissionGranted()
    }

    @Test
    fun `Should notify incoming call when READ_CALL_LOG granted 1`() {
        readCallLogPermissionGranted()
        val listener = spy(DummyListener())
        callStateReceiver.addListener(listener)
        callStateReceiver.onReceive(context, ringingIntent)
        verify(listener, never()).onIncomingCall(ArgumentMatchers.anyString())
        callStateReceiver.onReceive(context, ringingIntentNoPhoneNumber)
        verify(listener).onIncomingCall(CALLER)
    }

    @Test
    fun `Should notify incoming call when READ_CALL_LOG granted 2`() {
        readCallLogPermissionGranted()
        val listener = spy(DummyListener())
        callStateReceiver.addListener(listener)
        callStateReceiver.onReceive(context, ringingIntentNoPhoneNumber)
        verify(listener, never()).onIncomingCall(ArgumentMatchers.anyString())
        callStateReceiver.onReceive(context, ringingIntent)
        verify(listener).onIncomingCall(CALLER)
    }

    @Test
    fun `Should notify incoming call when READ_CALL_LOG granted 3`() {
        readCallLogPermissionGranted()
        val listener = spy(DummyListener())
        callStateReceiver.addListener(listener)
        callStateReceiver.onReceive(context, ringingIntentNoPhoneNumber)
        verify(listener, never()).onIncomingCall(ArgumentMatchers.anyString())
        callStateReceiver.onReceive(context, ringingIntentNoPhoneNumber)
        verify(listener).onIncomingCall("")
    }

    @Test
    fun `Should notify incoming call delayed when READ_CALL_LOG granted 1`() {
        readCallLogPermissionGranted()
        val listener = spy(DummyListener())
        callStateReceiver.addListener(listener)
        callStateReceiver.onReceive(context, ringingIntent)
        verify(listener, never()).onIncomingCall(ArgumentMatchers.anyString())

        handlerDelayController.runAll()
        verify(listener).onIncomingCall(CALLER)
    }

    @Test
    fun `Should notify incoming call delayed when READ_CALL_LOG granted 2`() {
        readCallLogPermissionGranted()
        val listener = spy(DummyListener())
        callStateReceiver.addListener(listener)
        callStateReceiver.onReceive(context, ringingIntentNoPhoneNumber)
        verify(listener, never()).onIncomingCall(ArgumentMatchers.anyString())

        handlerDelayController.runAll()
        verify(listener).onIncomingCall("")
    }

    @Test
    fun `verify delayed ring is omitted if another ring is received`() {
        readCallLogPermissionGranted()
        val listener = spy(DummyListener())
        callStateReceiver.addListener(listener)
        callStateReceiver.onReceive(context, ringingIntentNoPhoneNumber)
        verify(listener, never()).onIncomingCall(ArgumentMatchers.anyString())
        callStateReceiver.onReceive(context, ringingIntent)
        verify(listener).onIncomingCall(CALLER)

        // Verify that delayed ring is not generating additional incoming calls.
        handlerDelayController.runAll()
        verify(listener, times(1)).onIncomingCall(ArgumentMatchers.anyString())
    }

    @Test
    fun `Should notify call answered`() {
        val listener = spy(DummyListener())
        callStateReceiver.addListener(listener)
        callStateReceiver.onReceive(context, ringingIntent)
        verify(listener).onIncomingCall(CALLER)
        callStateReceiver.onReceive(context, offHookIntent)
        verify(listener).onCallAnswered(CALLER)
    }

    @Test
    fun `Should notify missed call`() {
        val listener = spy(DummyListener())
        callStateReceiver.addListener(listener)
        callStateReceiver.onReceive(context, ringingIntent)
        verify(listener).onIncomingCall(CALLER)
        callStateReceiver.onReceive(context, idleIntent)
        verify(listener).onCallMissedOrRejected(context, CALLER)
    }

    @Test
    fun `Should not notify outgoing call`() {
        val listener = spy(DummyListener())
        callStateReceiver.addListener(listener)
        callStateReceiver.onReceive(context, offHookIntent)
        verifyNoInteractions(listener)
    }

    @Test
    fun `Should notify multiple listeners`() {
        val listener1 = spy(DummyListener())
        val listener2 = spy(DummyListener())
        callStateReceiver.addListener(listener1)
        callStateReceiver.addListener(listener2)
        callStateReceiver.onReceive(context, ringingIntent)
        verify(listener1).onIncomingCall(CALLER)
        verify(listener2).onIncomingCall(CALLER)
    }

    @Test
    fun `Should not notify after removing listeners`() {
        val listener1 = spy(DummyListener())
        val listener2 = spy(DummyListener())
        callStateReceiver.addListener(listener1)
        callStateReceiver.addListener(listener2)

        callStateReceiver.onReceive(context, ringingIntent)
        verify(listener1).onIncomingCall(CALLER)
        verify(listener2).onIncomingCall(CALLER)

        callStateReceiver.removeListener(listener2)

        callStateReceiver.onReceive(context, offHookIntent)
        verify(listener1).onCallAnswered(CALLER)
        verifyNoMoreInteractions(listener2)
    }

    @Test
    fun `Should not call unregister if the list is initially empty`() {
        val listener1 = DummyListener()
        callStateReceiver.removeListener(listener1)
        verify(context, never()).unregisterReceiver(any())
    }

    open class DummyListener : CallStateReceiver.CallStateListener {
        override fun onIncomingCall(caller: String) {
        }

        override fun onCallAnswered(caller: String) {
        }

        override fun onCallMissedOrRejected(context: Context, caller: String) {
        }
    }
}
