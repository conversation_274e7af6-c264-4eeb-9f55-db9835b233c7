package com.suunto.connectivity.repository;

import android.content.Context;
import com.suunto.connectivity.R;
import com.suunto.connectivity.notifications.AncsMessage;
import com.suunto.connectivity.notifications.NotificationsDevice;
import com.suunto.connectivity.notifications.PostNotificationFilter;
import com.suunto.connectivity.suuntoconnectivity.ancs.AncsConstants;
import static com.suunto.connectivity.suuntoconnectivity.ancs.AncsConstants.DEFAULT_CATEGORY_COUNT;
import com.suunto.connectivity.util.NotificationSettingsHelper;
import com.suunto.connectivity.util.NotificationSettingsStorage;
import com.suunto.connectivity.watch.SpartanBt;
import java.util.Collections;
import static org.junit.Assert.assertEquals;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import org.mockito.Captor;
import org.mockito.Mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.MockitoJUnitRunner;
import rx.Completable;
import rx.observers.TestSubscriber;

/**
 * Tests for class NotificationManager
 */
@RunWith(MockitoJUnitRunner.class)
public class NotificationManagerTest {
    private static final String TEST_TITLE = "Title";
    private static final String TEST_MSG = "Message";
    private static final String PKG_TEST = "com.test";

    @Mock
    private Context context;

    @Mock
    private NotificationsDevice notificationsDevice;

    @Mock
    private SpartanBt spartanBt;

    @Mock
    private ActiveDevices activeDevices;

    @Mock
    private NotificationSettingsStorage notificationSettings;

    @Mock
    private NotificationSettingsHelper notificationSettingsHelper;

    @Mock
    private PostNotificationFilter postNotificationFilter;

    @Captor
    private ArgumentCaptor<AncsMessage> ancsMessageCaptor;

    private NotificationManager notificationManager;

    @Before
    public void setUp() {
        when(context.getString(R.string.watch_notification_action_dismiss)).thenReturn("");

        // Setup settings to proper values
        when(notificationSettingsHelper.notificationsEnabled(any())).thenReturn(true);
        when(notificationSettings.incomingCallNotificationsEnabled()).thenReturn(true);
        when(notificationSettings.missedCallNotificationsEnabled()).thenReturn(true);

        when(postNotificationFilter.filterByState(any(), any())).thenReturn(true);

        // Setup ActiveDevices to return a SpartanBt that just completes everything
        when(notificationsDevice.postNotification(any(AncsMessage.class)))
            .thenReturn(Completable.complete());
        when(notificationsDevice.removeNotification(anyInt()))
            .thenReturn(Completable.complete());
        when(activeDevices.getBtDevices()).thenReturn(Collections.singletonList(spartanBt));

        when(spartanBt.getNotificationsDevice()).thenReturn(notificationsDevice);

        notificationManager = new NotificationManager(context, activeDevices,
                notificationSettings, notificationSettingsHelper, postNotificationFilter);
    }

    @Test
    public void shouldPostNotificationToDevice() {
        when(notificationSettings.notificationAllowed(PKG_TEST)).thenReturn(true);

        AncsMessage message = AncsMessage.create(0, TEST_TITLE, TEST_MSG,
            AncsConstants.CategoryID.OTHER, PKG_TEST, DEFAULT_CATEGORY_COUNT, false,
            Collections.emptyList());

        TestSubscriber<Void> testSubscriber = new TestSubscriber<>();
        notificationManager.postNotification(message).subscribe(testSubscriber);

        testSubscriber.assertCompleted();
        verify(notificationsDevice).postNotification(any(AncsMessage.class));
    }

    @Test
    public void shouldRemoveNotificationFromDevice() {
        TestSubscriber<Void> testSubscriber = new TestSubscriber<>();
        notificationManager.removeNotification(0).subscribe(testSubscriber);

        testSubscriber.assertCompleted();
        verify(notificationsDevice).removeNotification(anyInt());
    }

    @Test
    public void shouldSendIncomingCallNotification() {
        notificationManager.onIncomingCall("caller");
        verify(notificationsDevice).postNotification(ancsMessageCaptor.capture());
        AncsMessage message = ancsMessageCaptor.getValue();
        assertEquals(AncsConstants.CategoryID.INCOMING_CALL, message.getCategoryId());
        assertEquals("caller", message.getTitle());
    }

    @Test
    public void shouldSendMissedCallNotification() {
        notificationManager.onCallMissedOrRejected(context, "caller");

        verify(notificationsDevice).removeNotification(1553907314);

        verify(notificationsDevice).postNotification(ancsMessageCaptor.capture());
        AncsMessage message = ancsMessageCaptor.getValue();
        assertEquals(AncsConstants.CategoryID.MISSED_CALL, message.getCategoryId());
        assertEquals("caller", message.getTitle());
    }

    @Test
    public void shouldRemoveNotificationWhenPhoneIsAnswered() {
        notificationManager.onCallAnswered("caller");
        verify(notificationsDevice, never()).postNotification(any(AncsMessage.class));
        verify(notificationsDevice).removeNotification(1553907314);
    }

    @Test
    public void shouldNotSendCallNotificationsWhenNotificationsDisabled() {
        when(notificationSettingsHelper.notificationsEnabled(any())).thenReturn(false);
        notificationManager.onIncomingCall("caller");
        notificationManager.onCallAnswered("caller");
        notificationManager.onCallMissedOrRejected(context, "caller");
        verify(notificationsDevice, never()).postNotification(any(AncsMessage.class));
        verify(notificationsDevice, never()).removeNotification(anyInt());
    }
}
