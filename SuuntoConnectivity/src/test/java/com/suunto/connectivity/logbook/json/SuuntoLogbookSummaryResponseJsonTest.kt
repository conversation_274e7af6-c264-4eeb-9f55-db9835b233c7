package com.suunto.connectivity.logbook.json

import com.stt.android.logbook.SmlParser
import com.stt.android.logbook.SuuntoLogbookFeeling
import com.stt.android.logbook.SuuntoLogbookSummary
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.time.ZonedDateTime

/**
 * Tests for SuuntoLogbookSummaryResponseJson parsing
 */
class SuuntoLogbookSummaryResponseJsonTest {
    private lateinit var summary: SuuntoLogbookSummary

    @Before
    fun setUp() {
        val data = SmlParser().parseSmlSummary(DATA)
        summary = data.summary
    }

    @Test
    fun shouldParseActivity() {
        Assert.assertEquals("TestActivity", summary.activityName)
        Assert.assertEquals(78, summary.activityType)
    }

    @Test
    fun shouldParseAltitude() {
        Assert.assertEquals(3f, summary.altitudeRange!!.min!!, .001f)
        Assert.assertEquals(7.6f, summary.altitudeRange!!.max!!, .001f)
    }

    @Test
    fun shouldParseSuuntoLogbookDateTime() {
        val zonedDateTime = ZonedDateTime.parse("2017-04-07T10:30:51.952+03:00")
        Assert.assertEquals(zonedDateTime, summary.zonedDateTime)
    }

    @Test
    fun shouldParseAscentAndDescent() {
        Assert.assertEquals(1f, summary.ascent!!, .001f)
        Assert.assertEquals(2f, summary.ascentTime!!, .001f)
        Assert.assertEquals(3.3f, summary.descent!!, .001f)
        Assert.assertEquals(4.4f, summary.descentTime!!, .001f)
    }

    @Test
    fun shouldParseDeviceInfo() {
        val device = summary.device
        Assert.assertNotNull(device)
        Assert.assertEquals("LankkuD", device!!.deviceInfo!!.hwName)
        Assert.assertEquals("1.9.2", device.deviceInfo!!.swVersion)
        Assert.assertEquals("Cairo", device.name)
        Assert.assertEquals("016411300080", device.serialNumber)
    }

    @Test
    fun shouldParsePoolData() {
        Assert.assertEquals(25f, summary.poolLength!!, .001f)
        Assert.assertEquals(2, summary.poolLengths!!.toInt().toLong())
    }

    @Test
    fun shouldParseDistance() {
        Assert.assertEquals(5.2f, summary.distance!!, .001f)
    }

    @Test
    fun shouldParseRecoveryTime() {
        Assert.assertEquals(.5f, summary.recoveryTime!!, .001f)
    }

    @Test
    fun shouldParseDuration() {
        Assert.assertEquals(2f, summary.pauseDuration!!, .001f)
    }

    @Test
    fun shouldParseFeeling() {
        Assert.assertEquals(SuuntoLogbookFeeling.Average, summary.feeling)
    }

    @Test
    fun shouldParseAltitudeRange() {
        Assert.assertEquals(7.6f, summary.altitudeRange!!.max!!, .001f)
    }

    companion object {
        private const val DATA = """
{
  "Samples": [
    {
      "Attributes": {
        "suunto/sml": {
          "Windows": [
            {
              "ActivityId": 78,
              "Altitude": [
                {
                  "Avg": null,
                  "Max": null,
                  "Min": null
                }
              ],
              "Ascent": 0,
              "AscentTime": 0,
              "Cadence": [
                {
                  "Avg": 1.5,
                  "Max": 1.8,
                  "Min": 1.8
                }
              ],
              "Descent": 0,
              "DescentTime": 0,
              "Distance": 0,
              "Duration": 4.4000000000000004,
              "Energy": 0,
              "HR": [
                {
                  "Avg": null,
                  "Max": null,
                  "Min": null
                }
              ],
              "Power": [
                {
                  "Avg": null,
                  "Max": null,
                  "Min": null
                }
              ],
              "RecoveryTime": 0,
              "Speed": [
                {
                  "Avg": 0,
                  "Max": 0,
                  "Min": 0
                }
              ],
              "StrokeRate": [
                {
                  "Avg": null,
                  "Max": null,
                  "Min": null
                }
              ],
              "Strokes": [
                {
                  "Avg": null,
                  "Max": null,
                  "Min": null
                }
              ],
              "SwimStyle": null,
              "Swolf": [
                {
                  "Avg": null,
                  "Max": null,
                  "Min": null
                }
              ],
              "Temperature": [
                {
                  "Avg": null,
                  "Max": null,
                  "Min": null
                }
              ],
              "Type": "Activity",
              "VerticalSpeed": [
                {
                  "Avg": null,
                  "Max": null,
                  "Min": null
                }
              ]
            }
          ]
        }
      },
      "Source": "suunto-016411300080",
      "TimeISO8601": "2017-04-07T10:30:57.270+03:00"
    },
    {
      "Attributes": {
        "suunto/sml": {
          "Header": {
            "Activity": "TestActivity",
            "ActivityType": 78,
            "Altitude": {
              "Max": 7.6,
              "Min": 3
            },
            "Ascent": 1,
            "AscentTime": 2,
            "DateTime": "2017-04-07T10:30:51.952+03:00",
            "Descent": 3.3,
            "DescentTime": 4.4,
            "Device": {
              "Info": {
                "HW": "LankkuD",
                "SW": "1.9.2"
              },
              "Name": "Cairo",
              "SerialNumber": "016411300080"
            },
            "Distance": 5.2,
            "Duration": 4.4219999999999997,
            "EPOC": 0,
            "Energy": 0,
            "Feeling": 2,
            "HrZone1": 10,
            "HrZone2": 20,
            "HrZone3": 30,
            "HrZone4": 40,
            "HrZone5": 50,
            "MoveType": null,
            "Notes": "",
            "PauseDuration": 2,
            "PeakTrainingEffect": 1,
            "PoolLength": 25,
            "PoolLengths": 2,
            "RecoveryTime": 0.5,
            "VerticalSpeed": 0
          }
        }
      },
      "Source": "suunto-016411300080",
      "TimeISO8601": "2017-04-07T10:30:56.374+03:00"
    }
  ]
}
"""
    }
}
