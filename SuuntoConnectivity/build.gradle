plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.moshi"
    id 'com.google.devtools.ksp'
}

android {

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility rootProject.ext.sourceJavaVersion
        targetCompatibility rootProject.ext.targetJavaVersion
    }

    defaultConfig {
        minSdkVersion Versions.defaultMinSdkVersion
        targetSdkVersion Versions.targetSdkVersion
        testApplicationId "com.stt.android.test"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
        }
    }

    testOptions {
        unitTests {
            returnDefaultValues = true
            includeAndroidResources = true
            all {
                jvmArgs "-Xmx2g"
                maxParallelForks = Runtime.runtime.availableProcessors()
                forkEvery = 50
            }
        }
    }
    namespace 'com.suunto.connectivity'
    buildFeatures.buildConfig = true
}

kapt {
    arguments {
        // Source dir to output files to. This should usually be the src/main/java or src/main/kotlin
        // path of the project you’re running this in.
        // REQUIRED
        arg("avkSrc", "src/main/java")
    }
}

dependencies {
    implementation libs.rxjava2
    implementation libs.rxjava2.android
    implementation libs.rxjava2.interop

    implementation libs.androidx.corektx

    // Kotlin Coroutines
    implementation libs.kotlin.coroutines
    implementation libs.kotlin.coroutines.android
    implementation libs.kotlin.coroutines.rxjava

    // GSON
    implementation libs.gson

    // KSP and code generation
    implementation project(':mdsapi-annotations')
    ksp project(':mdsapi-codegen')
    
    // Inject annotations
    
    //Android architecture components
    implementation libs.androidx.lifecycle.process

    // Greenrobot EventBus
    implementation libs.eventbus

    // JDeferred for inversion of control in simple async operations
    implementation libs.jdeferred

    // Stateless4J state machine library.
    implementation libs.stateless4j

    // Wearable services
    implementation libs.play.wearable

    // Firebase Crashlytics
    implementation platform(libs.firebase.bom)
    implementation libs.firebase.crashlytics

    // todo migrate to androidx.media3
    implementation libs.androidx.media
    implementation libs.androidx.lifecycle.service

    // to download fw files
    implementation libs.okhttp
    
    // ATTENTION: DO NOT add PowerMock to this project!
    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    // Read https://amersportsdigital.atlassian.net/wiki/spaces/STSMA/pages/956563497/Coding+Convention+and+Policies?atlOrigin=eyJpIjoiOGM4ODE2ODY3ZDc3NDczNWExY2YxZmM3ZWVlYzlkNmUiLCJwIjoiYyJ9
    // For more info about how to test static methods and unit testing best practices

    // Test dependencies
    testImplementation libs.androidx.test.core
    testImplementation project(Deps.testUtils)
    testImplementation libs.soy.algorithms
    testImplementation libs.kotlin.coroutines.test
    testImplementation libs.mockk

    androidTestImplementation libs.androidx.test.ext

    // Other modules
    implementation project(Deps.mds)
    implementation project(Deps.timeline)
    implementation project(Deps.domainBase)

    // leaking for SCSSampleApp
    api project(Deps.analytics)
}

allOpen {
    annotation("com.stt.android.TestOpen")
}
