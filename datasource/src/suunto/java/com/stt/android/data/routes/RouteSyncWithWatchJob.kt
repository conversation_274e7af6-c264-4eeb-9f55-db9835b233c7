package com.stt.android.data.routes

import android.annotation.SuppressLint
import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatchingWithTimeout
import timber.log.Timber
import javax.inject.Inject

class RouteSyncWithWatchJob(
    private val routeSyncProvider: RouteSyncProvider,
    appContext: Context,
    params: WorkerParameters
) : CoroutineWorker(appContext, params) {

    class Factory
    @Inject constructor(
        private val routeSyncProvider: RouteSyncProvider,
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return RouteSyncWith<PERSON>atch<PERSON>ob(
                routeSyncProvider,
                context,
                params,
            )
        }
    }

    @SuppressLint("CheckResult")
    override suspend fun doWork(): Result {
        runSuspendCatchingWithTimeout(TIMEOUT_IN_MILLIS) {
            routeSyncProvider.syncRoutes(
                inputData.getString(EXTRA_ROUTE_ID_NAVIGATION),
                inputData.getBoolean(EXTRA_NAVIGATE_ONGOING, false)
            )
        }.onFailure { e ->
            Timber.w(e, "Unable to sync routes with watch")
        }
        return Result.success()
    }

    companion object {
        private const val TIMEOUT_IN_MILLIS = 5L * 60 * 1000
        private const val EXTRA_ROUTE_ID_NAVIGATION = "route_id_navigation"
        private const val EXTRA_NAVIGATE_ONGOING = "extra_navigate_ongoing"
        const val TAG = "RouteSyncWithWatchJob"

        fun schedule(workManager: WorkManager, navigateOngoing: Boolean, navigateRouteId: String? = null) {
            workManager.enqueueUniqueWork(
                TAG,
                ExistingWorkPolicy.REPLACE,
                OneTimeWorkRequest.Builder(RouteSyncWithWatchJob::class.java)
                    .setInputData(workDataOf(EXTRA_ROUTE_ID_NAVIGATION to navigateRouteId, EXTRA_NAVIGATE_ONGOING to navigateOngoing))
                    .build()
            )
        }
    }
}
