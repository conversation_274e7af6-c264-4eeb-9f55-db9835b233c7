package com.stt.android.data.jumps

import com.squareup.moshi.Moshi
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import com.stt.android.remote.headsetjump.JumpAssessmentApi
import com.stt.android.remote.otp.GenerateOTPUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
abstract class JumpAssessmentRemoteModule {
    companion object {
        @Provides
        fun provideJumpAssessmentApi(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: <PERSON><PERSON>
        ): JumpAssessmentApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                JumpAssessmentApi::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }
}

class JumpAssessmentRemoteDatasource @Inject constructor(
    private val jumpAssessmentApi: JumpAssessmentApi,
    private val generateOTPUseCase: GenerateOTPUseCase
) {
    suspend fun saveJumpAssessment(assessmentValue: JumpAssessmentLocalValue): JumpAssessmentLocalValue {
        return jumpAssessmentApi.saveJumpAssessmentValues(
            generateOTPUseCase.generateTOTP(),
            assessmentValue.toRemote()
        )
            .payloadOrThrow()
            .toLocal()
    }

    suspend fun getStandardJumpAssessment(): JumpAssessmentLocalValue? {
        return jumpAssessmentApi.getStandardAssessmentValue().payloadNullableOrThrow()?.toLocal()
    }

    suspend fun getHistoryJumpAssessments(): List<JumpAssessmentLocalValue> {
        return jumpAssessmentApi.getHistoryAssessmentValues().payloadOrThrow().map { it.toLocal() }
    }

    suspend fun deleteJumpData(jumpId: String) {
        jumpAssessmentApi.deleteJumpData(jumpId)
    }
}
