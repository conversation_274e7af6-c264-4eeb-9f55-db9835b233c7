package com.stt.android.data.necks

import com.stt.android.remote.headsetneck.NeckRemoteAssessmentValues

data class NeckLocalAssessmentValues(
    val leftRotation: Int,
    val rightRotation: Int,
    val flexion: Int,
    val extension: Int,
    val leftExtension: Int,
    val rightExtension: Int,
    val id: String = "",
    val createdDate: Long = 0L
)

fun NeckLocalAssessmentValues.toRemote(): NeckRemoteAssessmentValues {
    return NeckRemoteAssessmentValues(
        leftRotation,
        rightRotation,
        flexion,
        extension,
        leftExtension,
        rightExtension
    )
}

fun NeckRemoteAssessmentValues.toLocal(): NeckLocalAssessmentValues {
    return NeckLocalAssessmentValues(
        leftRotation,
        rightRotation,
        flexion,
        extension,
        leftExtension,
        rightExtension,
        id ?: "",
        createdate ?: 0L
    )
}
