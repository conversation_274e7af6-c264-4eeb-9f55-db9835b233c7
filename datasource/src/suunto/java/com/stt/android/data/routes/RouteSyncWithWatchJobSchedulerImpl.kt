package com.stt.android.data.routes

import androidx.work.WorkManager
import com.stt.android.domain.routes.NavigateLoader
import com.stt.android.domain.routes.RouteSyncWithWatchJobScheduler
import javax.inject.Inject

class RouteSyncWithWatchJobSchedulerImpl
@Inject constructor(
    private val workManager: WorkManager,
    private val navigateLoader: NavigateLoader
) : RouteSyncWithWatchJobScheduler {
    override fun schedule(navigateRouteId: String?) {
        val latestStatus = navigateLoader.getNavigateStateFlow().value
        val isOngoing = latestStatus?.isNavigateOngoing() ?: false

        RouteSyncWithWatchJob.schedule(
            workManager,
            isOngoing,
            navigateRouteId
        )
    }
}
