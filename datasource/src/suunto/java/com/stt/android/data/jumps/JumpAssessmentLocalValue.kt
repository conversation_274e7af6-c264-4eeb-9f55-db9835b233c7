package com.stt.android.data.jumps

import com.stt.android.remote.headsetjump.CurrentJumpAssessmentResponseValue
import com.stt.android.remote.headsetjump.JumpAssessmentBaselineResponseValue
import com.stt.android.remote.headsetjump.JumpAssessmentRemoteValue
import com.stt.android.remote.headsetjump.HistoryJumpAssessmentResponseValue
import kotlin.math.roundToInt

data class JumpAssessmentLocalValue(
    val id: String = "",
    val username: String = "",
    val jumpHeightAvg: Float = 0f,
    val flightTimeAvg: Int = 0,
    val takeoffVelocityAvg: Float = 0f,
    // unit: cm
    val jumpHeightInCm: Float = 0f,
    // unit: ms
    val flightTimeInMs: Int = 0,
    // unit: m/s
    val takeoffVelocityInMeterPerSecond: Float = 0f,
    val fatigueIndex: Float = 0f,
    val standardFlag: Boolean = false,
    val createdate: Long = 0L
)

fun JumpAssessmentLocalValue.toRemote(): JumpAssessmentRemoteValue {
    return JumpAssessmentRemoteValue(
        jumpHeightInCm = jumpHeightInCm.roundToInt(),
        flightTimeInMs = flightTimeInMs,
        takeoffVelocityInMeterPerSecond = takeoffVelocityInMeterPerSecond,
        fatigueIndex = fatigueIndex,
        standardFlag = standardFlag
    )
}

fun HistoryJumpAssessmentResponseValue.toLocal(): JumpAssessmentLocalValue {
    return JumpAssessmentLocalValue(
        id,
        username,
        jumpHeightAvg,
        flightTimeAvg,
        takeoffVelocityAvg,
        jumpHeightInCm,
        flightTimeInMs,
        takeoffVelocityInMeterPerSecond,
        fatigueIndex,
        standardFlag,
        createdate
    )
}

fun JumpAssessmentBaselineResponseValue.toLocal(): JumpAssessmentLocalValue {
    return JumpAssessmentLocalValue(
        jumpHeightInCm = jumpHeightInCm,
        flightTimeInMs = flightTimeInMs,
        takeoffVelocityInMeterPerSecond = takeoffVelocityInMeterPerSecond
    )
}

fun CurrentJumpAssessmentResponseValue.toLocal(): JumpAssessmentLocalValue {
    return JumpAssessmentLocalValue(
        id = id,
        username = username,
        jumpHeightInCm = jumpHeightInCm,
        flightTimeInMs = flightTimeInMs,
        takeoffVelocityInMeterPerSecond = takeoffVelocityInMeterPerSecond,
        fatigueIndex = fatigueIndex,
        standardFlag = standardFlag,
        createdate = createdate
    )
}
