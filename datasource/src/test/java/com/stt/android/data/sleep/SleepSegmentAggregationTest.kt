package com.stt.android.data.sleep

import com.stt.android.data.toEpochMilli
import com.stt.android.domain.sleep.SleepSegment
import com.stt.android.domain.sleep.aggregateToSleepNights
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import java.time.ZonedDateTime
import kotlin.time.Duration.Companion.seconds

@RunWith(MockitoJUnitRunner::class)
class SleepSegmentAggregationTest {
    @Test
    fun `Aggregate sleep segments by 8 hour awake periods`() {
        val segments = listOf(
            segment(
                startIso8601 = "2019-07-01T22:15:30+01:00[Europe/Paris]",
                endIso8601 = "2019-07-01T23:15:30+01:00[Europe/Paris]"
            ),
            segment(
                startIso8601 = "2019-07-01T23:20:30+01:00[Europe/Paris]",
                endIso8601 = "2019-07-01T23:55:30+01:00[Europe/Paris]"
            ),
            // 8 hours of awake time here
            segment(
                startIso8601 = "2019-07-02T08:10:30+01:00[Europe/Paris]",
                endIso8601 = "2019-07-02T08:15:30+01:00[Europe/Paris]"
            ),
            segment(
                startIso8601 = "2019-07-02T08:55:30+01:00[Europe/Paris]",
                endIso8601 = "2019-07-02T10:15:30+01:00[Europe/Paris]"
            )
        )

        val actual = segments.aggregateToSleepNights()
        assertThat(actual.size).isEqualTo(2)
        assertThat(actual[0].longSleep?.fellAsleep).isEqualTo(
            ZonedDateTime.parse("2019-07-01T22:15:30+01:00[Europe/Paris]").toEpochMilli()
        )
        assertThat(actual[1].longSleep?.fellAsleep).isEqualTo(
            ZonedDateTime.parse("2019-07-02T08:10:30+01:00[Europe/Paris]").toEpochMilli()
        )
    }

    private fun segment(
        startIso8601: String,
        endIso8601: String,
        bedtimeStartIso8601: String? = null,
        bedtimeEndIso8601: String? = null
    ): SleepSegment {
        val start = ZonedDateTime.parse(startIso8601)
        val end = ZonedDateTime.parse(endIso8601)
        val bedtimeStart = bedtimeStartIso8601?.let { ZonedDateTime.parse(it) }
        val bedtimeEnd = bedtimeEndIso8601?.let { ZonedDateTime.parse(it) }

        val duration = (end.toEpochSecond() - start.toEpochSecond()).seconds

        return SleepSegment(
            serial = "",
            timestamp = start.toEpochMilli(),
            quality = null,
            avgHr = null,
            minHr = null,
            feeling = null,
            duration = duration,
            deepSleepDuration = null,
            timeISO8601 = start,
            bedtimeStart = bedtimeStart,
            bedtimeEnd = bedtimeEnd
        )
    }
}
