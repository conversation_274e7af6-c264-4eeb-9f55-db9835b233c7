package com.stt.android.data.report

import com.stt.android.domain.report.block.BlockStatus
import com.stt.android.remote.report.ReportRemoteApi
import com.stt.android.remote.response.AskoResponse
import com.stt.android.remote.user.follow.FollowRestApi
import com.stt.android.remote.user.follow.RemoteFollowInfo
import com.stt.android.remote.user.follow.RemoteFollowUser
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Rule
import org.junit.Test
import org.mockito.Mock
import org.mockito.junit.MockitoJUnit
import org.mockito.junit.MockitoRule
import org.mockito.kotlin.given

class BlockUserRepositoryTest {
    @JvmField
    @Rule
    val mockitoRule: MockitoRule = MockitoJUnit.rule()

    @Mock
    private lateinit var reportRemoteApi: ReportRemoteApi

    @Mock
    private lateinit var followRestApi: FollowRestApi

    @Test
    fun `getUserBlockStatus should return correct BlockStatus when the user is blocked`() =
        runTest {
            val blockUserRepository = BlockUserRepository(reportRemoteApi, followRestApi)
            val userName = "userName"
            val blockedUser = RemoteFollowUser(userName, "realName", null, null, null)
            given(followRestApi.fetchFollow())
                .willReturn(
                    AskoResponse(
                        null,
                        null,
                        RemoteFollowInfo(null, null, listOf(null, blockedUser), null)
                    )
                )

            assertThat(blockUserRepository.getUserBlockStatus(userName)).isEqualTo(
                BlockStatus(
                    isUserBlocked = true,
                    isBlockedByUser = false
                )
            )
        }

    @Test
    fun `getUserBlockStatus should return correct BlockStatus when blocked by the user`() =
        runTest {
            val blockUserRepository = BlockUserRepository(reportRemoteApi, followRestApi)
            val userName = "userName"
            val blockedByUser = RemoteFollowUser(userName, "realName", null, null, null)
            given(followRestApi.fetchFollow())
                .willReturn(
                    AskoResponse(
                        null,
                        null,
                        RemoteFollowInfo(null, null, null, listOf(null, blockedByUser))
                    )
                )

            assertThat(blockUserRepository.getUserBlockStatus(userName)).isEqualTo(
                BlockStatus(
                    isUserBlocked = false,
                    isBlockedByUser = true
                )
            )
        }
}
