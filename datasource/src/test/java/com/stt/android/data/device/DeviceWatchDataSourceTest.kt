package com.stt.android.data.device

import com.stt.android.domain.device.ConnectedWatchConnectionQuality
import com.stt.android.domain.device.ConnectedWatchConnectionState
import com.stt.android.domain.device.ConnectedWatchState
import com.stt.android.domain.device.ConnectedWatchSyncState
import com.stt.android.domain.device.SyncState
import com.stt.android.domain.device.UploadProgressState
import io.reactivex.Flowable
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class DeviceWatchDataSourceTest {

    @Mock
    private lateinit var deviceInfoApi: DeviceInfoApi

    private lateinit var deviceWatchDataSource: DeviceWatchDataSource

    @Before
    fun setup() {
        deviceWatchDataSource = DeviceWatchDataSource(deviceInfoApi)
    }

    @Test
    fun `should access device info api while providing connected watch state`() {
        // Prepare
        `when`(deviceInfoApi.connectedWatchState())
            .thenReturn(
                Flowable.fromIterable(
                    listOf(
                        ConnectedWatchState(
                            ConnectedWatchSyncState(SyncState.NOT_SYNCING, 0, 0),
                            ConnectedWatchConnectionState.CONNECTED,
                            ConnectedWatchConnectionQuality.NORMAL,
                            null,
                            true,
                            true,
                            false,
                            true,
                            UploadProgressState.EMPTY
                        )
                    )
                )
            )

        // Verify
        deviceInfoApi.connectedWatchState()
            .test()
            .assertNoErrors()
            .assertValueCount(1)
            .assertComplete()

        verify(deviceInfoApi).connectedWatchState()
    }
}
