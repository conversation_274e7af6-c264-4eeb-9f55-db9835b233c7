package com.stt.android.data.sportmodes

import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.domain.sportmodes.Display
import com.stt.android.domain.sportmodes.Field
import com.stt.android.domain.sportmodes.Group
import com.stt.android.domain.sportmodes.Icon
import com.stt.android.domain.sportmodes.Mode
import com.stt.android.domain.sportmodes.Setting
import com.stt.android.domain.sportmodes.SettingHeader
import com.stt.android.domain.sportmodes.SportModesRepository
import com.stt.android.domain.sportmodes.SupportMode
import com.stt.android.domain.sportmodes.WatchInfo
import com.stt.android.domain.sportmodes.WatchSportMode
import com.stt.android.exceptions.remote.ClientError
import com.stt.android.remote.sportmodes.SportModesRemoteApi
import io.reactivex.Completable
import io.reactivex.Single
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyBoolean
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mock
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.never

@RunWith(MockitoJUnitRunner::class)
class SportModesRepositoryTest {
    @Mock
    private lateinit var sportModesRemoteApi: SportModesRemoteApi

    @Mock
    private lateinit var sportModesLocalDataSource: SportModesLocalDataSource

    @Mock
    private lateinit var sportModesWatchDataSource: SportModesWatchDataSource

    private lateinit var sportModesDataRepository: SportModesRepository

    @Mock
    private lateinit var notModifiedErrorMock: ClientError.NotModified

    @Mock
    private lateinit var datahubAnalyticsTracker: DatahubAnalyticsTracker

    @Before
    fun setup() {
        sportModesDataRepository = SportModesRepositoryImpl(
            sportModesLocalDataSource,
            sportModesRemoteApi,
            sportModesWatchDataSource,
            datahubAnalyticsTracker,
        )
    }

    @Test
    fun `init sport mode component should access watch and local data source`() = runTest {
        // prepare
        val watchInfo = WatchInfo("", "", "")
        `when`(sportModesWatchDataSource.fetchWatchInfo())
            .thenReturn(watchInfo)
        `when`(sportModesLocalDataSource.initSportModeComponent("", watchInfo))
            .thenReturn(Unit)
        // verify
        sportModesDataRepository.initSportModeComponent("")

        verifyNoInteractions(sportModesRemoteApi)
        verify(sportModesWatchDataSource).fetchWatchInfo()
        verify(sportModesLocalDataSource).initSportModeComponent("", watchInfo)
    }

    @Test
    fun `fetch sport modes fte completed should access only local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.fetchSportModeFteCompleted())
            .thenReturn(Single.just(false))

        // verify
        sportModesDataRepository.fetchSportModeFteCompleted()
            .test()
            .assertNoErrors()
            .assertComplete()

        verifyNoInteractions(sportModesRemoteApi)
        verifyNoInteractions(sportModesWatchDataSource)
        verify(sportModesLocalDataSource).fetchSportModeFteCompleted()
    }

    @Test
    fun `set sport modes fte completed should access only local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.setSportModeFteCompleted(anyBoolean()))
            .thenReturn(Completable.complete())

        // verify
        sportModesDataRepository.setSportModeFteCompleted(anyBoolean())
            .test()
            .assertNoErrors()
            .assertComplete()

        verifyNoInteractions(sportModesRemoteApi)
        verifyNoInteractions(sportModesWatchDataSource)
        verify(sportModesLocalDataSource).setSportModeFteCompleted(anyBoolean())
    }

    @Test
    fun `fetch sport modes  should access only local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.fetchSportModes())
            .thenReturn(Single.just(emptyList()))

        // verify
        sportModesDataRepository.fetchSportModes()
            .test()
            .assertNoErrors()
            .assertComplete()

        verifyNoInteractions(sportModesRemoteApi)
        verifyNoInteractions(sportModesWatchDataSource)
        verify(sportModesLocalDataSource).fetchSportModes()
    }

    @Test
    fun `fetch group should access only watch data source`() {
        // prepare
        `when`(sportModesWatchDataSource.fetchGroup(anyInt()))
            .thenReturn(Single.just(""))

        // verify
        sportModesWatchDataSource.fetchGroup(anyInt())
            .test()
            .assertNoErrors()
            .assertComplete()

        verifyNoInteractions(sportModesRemoteApi)
        verifyNoInteractions(sportModesLocalDataSource)
        verify(sportModesWatchDataSource).fetchGroup(anyInt())
    }

    @Test
    fun `fetch sport mode template should access watch and local data source`() {
        // prepare
        `when`(sportModesWatchDataSource.fetchSportModesListJson())
            .thenReturn(Single.just(""))
        `when`(sportModesLocalDataSource.fetchSportModeTemplate(anyInt(), anyString()))
            .thenReturn(Single.just(WatchSportMode(Group(0, ""), listOf(Mode(0, "", "")))))
        // verify
        sportModesDataRepository.fetchSportModeTemplate(1)
            .test()
            .assertNoErrors()
            .assertComplete()

        verifyNoInteractions(sportModesRemoteApi)
        verify(sportModesWatchDataSource).fetchSportModesListJson()
        verify(sportModesLocalDataSource).fetchSportModeTemplate(anyInt(), anyString())
    }

    @Test
    fun `save sport mode should access watch data source`() {
        // prepare
        `when`(sportModesWatchDataSource.saveDisplays(anyInt(), anyString()))
            .thenReturn(Completable.complete())
        `when`(sportModesWatchDataSource.saveSettings(anyInt(), anyString()))
            .thenReturn(Completable.complete())

        // verify
        sportModesDataRepository.saveSportMode(Group(0, ""), arrayOf(Mode(0, "test", "test")), true, true, true)
            .test()
            .assertNoErrors()
            .assertComplete()

        verifyNoInteractions(sportModesRemoteApi)
        verify(sportModesWatchDataSource).saveDisplays(anyInt(), anyString())
        verify(sportModesWatchDataSource).saveSettings(anyInt(), anyString())
    }

    @Test
    fun `fetch max displays should access only local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.fetchMaxNumberOfDisplays(anyString()))
            .thenReturn(Single.just(1))

        // verify
        sportModesDataRepository.fetchMaxNumberOfDisplays(anyString())
            .test()
            .assertNoErrors()
            .assertComplete()

        verifyNoInteractions(sportModesRemoteApi)
        verifyNoInteractions(sportModesWatchDataSource)
        verify(sportModesLocalDataSource).fetchMaxNumberOfDisplays(anyString())
    }

    @Test
    fun `fetch min displays should access only local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.fetchMinNumberOfDisplays(anyString()))
            .thenReturn(Single.just(1))

        // verify
        sportModesDataRepository.fetchMinNumberOfDisplays(anyString())
            .test()
            .assertNoErrors()
            .assertComplete()

        verifyNoInteractions(sportModesRemoteApi)
        verifyNoInteractions(sportModesWatchDataSource)
        verify(sportModesLocalDataSource).fetchMinNumberOfDisplays(anyString())
    }

    @Test
    fun `fetch sportModes should only access watch datasource`() {
        // prepare
        `when`(sportModesWatchDataSource.fetchSportModesListJson())
            .thenReturn(Single.just(""))
        // verify
        sportModesDataRepository.fetchSportModesListJson()
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModesWatchDataSource).fetchSportModesListJson()
        verifyNoInteractions(sportModesLocalDataSource)
        verifyNoInteractions(sportModesRemoteApi)
    }

    @Test
    fun `fetch settings should only access watch datasource`() {
        // prepare
        `when`(sportModesWatchDataSource.fetchSportModeSettingsJson(anyInt()))
            .thenReturn(Single.just(""))
        // verify
        sportModesDataRepository.fetchSportModeSettingsJson(1)
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModesWatchDataSource).fetchSportModeSettingsJson(anyInt())
        verifyNoInteractions(sportModesLocalDataSource)
        verifyNoInteractions(sportModesRemoteApi)
    }

    @Test
    fun `fetch current sport modes only access local data source`() {
        // prepare
        `when`(sportModesWatchDataSource.fetchSportModesListJson())
            .thenReturn(Single.just(""))
        `when`(sportModesLocalDataSource.fetchCurrentSportModesList(anyString()))
            .thenReturn(Single.just(emptyList()))

        // verify
        sportModesDataRepository.fetchCurrentSportModesList()
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModesLocalDataSource).fetchCurrentSportModesList(anyString())
        verifyNoInteractions(sportModesRemoteApi)
        verify(sportModesWatchDataSource).fetchSportModesListJson()
    }

    @Test
    fun `fetch displays should access watch and local datasource`() {
        val displayList = mutableListOf<Display>()
        for (i in 0..4) {
            val dummyReply = Display("$i", "dummy display $i", false, Icon("", ""), true)
            displayList.add(dummyReply)
        }
        `when`(sportModesLocalDataSource.fetchCurrentDisplays(anyString(), anyString()))
            .thenReturn(Single.just(displayList))
        `when`(sportModesLocalDataSource.fetchCurrentFields(anyString(), anyInt()))
            .thenReturn(Single.just(listOf(Field("1", "name", null))))
        sportModesDataRepository.fetchCurrentDisplays(anyString(), anyString())
            .test()
            .assertValueCount(1)
            .assertNoErrors()
            .assertComplete()
        verify(sportModesLocalDataSource).fetchCurrentDisplays(anyString(), anyString())
        verify(sportModesLocalDataSource, times(displayList.size)).fetchCurrentFields(anyString(), anyInt())
        verifyNoInteractions(sportModesRemoteApi)
    }

    @Test
    fun `fetch fields should access local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.fetchFieldList(anyInt(), anyString(), anyInt()))
            .thenReturn(Single.just(emptyList()))

        // verify
        sportModesDataRepository.fetchFieldList(anyInt(), anyString(), anyInt())
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModesLocalDataSource).fetchFieldList(anyInt(), anyString(), anyInt())
        verifyNoInteractions(sportModesRemoteApi)
        verifyNoInteractions(sportModesWatchDataSource)
    }

    @Test
    fun `add display should access only local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.addDisplay(anyString(), anyString(), anyInt(), anyString()))
            .thenReturn(Single.just(WatchSportMode(Group(0, ""), emptyList())))

        // verify
        sportModesDataRepository.addDisplay(anyString(), anyString(), anyInt(), anyString())
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModesLocalDataSource).addDisplay(anyString(), anyString(), anyInt(), anyString())
        verifyNoInteractions(sportModesRemoteApi)
        verifyNoInteractions(sportModesWatchDataSource)
    }

    @Test
    fun `change display should access only local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.changeDisplay(anyString(), anyString(), anyInt(), anyString()))
            .thenReturn(Single.just(WatchSportMode(Group(0, ""), emptyList())))

        // verify
        sportModesDataRepository.changeDisplay(anyString(), anyString(), anyInt(), anyString())
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModesLocalDataSource).changeDisplay(anyString(), anyString(), anyInt(), anyString())
        verifyNoInteractions(sportModesRemoteApi)
        verifyNoInteractions(sportModesWatchDataSource)
    }

    @Test
    fun `delete display should access only local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.deleteDisplay(anyString(), anyString(), anyInt()))
            .thenReturn(Single.just(WatchSportMode(Group(0, ""), emptyList())))

        // verify
        sportModesDataRepository.deleteDisplay("", "", 1)
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModesLocalDataSource).deleteDisplay(anyString(), anyString(), anyInt())
        verifyNoInteractions(sportModesRemoteApi)
        verifyNoInteractions(sportModesWatchDataSource)
    }

    @Test
    fun `change field should access only local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.changeField(anyString(), anyInt(), anyInt(), anyString()))
            .thenReturn(Single.just(""))
        val displayList = mutableListOf<Display>()
        for (i in 0..4) {
            val dummyReply = Display("$i", "dummy display $i", false, Icon("", ""), true)
            displayList.add(dummyReply)
        }
        // verify
        sportModesDataRepository.changeField(anyString(), anyInt(), anyInt(), anyString())
            .test()
            .assertNoErrors()
            .assertComplete()
        verify(sportModesLocalDataSource).changeField(anyString(), anyInt(), anyInt(), anyString())
        verifyNoInteractions(sportModesRemoteApi)
        verifyNoInteractions(sportModesWatchDataSource)
    }

    @Test
    fun `delete sport mode should access only watch data source`() {
        // prepare
        `when`(sportModesWatchDataSource.deleteSportMode(anyInt()))
            .thenReturn(Completable.complete())

        // verify
        sportModesDataRepository.deleteSportMode(anyInt())
            .test()
            .assertNoErrors()
            .assertComplete()

        verifyNoInteractions(sportModesLocalDataSource)
        verifyNoInteractions(sportModesRemoteApi)
        verify(sportModesWatchDataSource).deleteSportMode(anyInt())
    }

    @Test
    fun `fetch sport mode setting should access only local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.fetchSetting(anyString(), anyString(), anyString()))
            .thenReturn(Single.just(Setting(SettingHeader(), Any())))

        // verify
        sportModesDataRepository.fetchSetting(anyString(), anyString(), anyString())
            .test()
            .assertNoErrors()
            .assertComplete()

        verifyNoInteractions(sportModesWatchDataSource)
        verifyNoInteractions(sportModesRemoteApi)
        verify(sportModesLocalDataSource).fetchSetting(anyString(), anyString(), anyString())
    }

    @Test
    fun `fetch device supported should access only local data source`() {
        // prepare
        `when`(sportModesLocalDataSource.fetchDeviceSupported())
            .thenReturn(Single.just(SupportMode.SUPPORTED))

        // verify
        sportModesDataRepository.fetchDeviceSupported()
            .test()
            .assertNoErrors()
            .assertComplete()

        verifyNoInteractions(sportModesWatchDataSource)
        verifyNoInteractions(sportModesRemoteApi)
        verify(sportModesLocalDataSource).fetchDeviceSupported()
    }

    @Test
    fun `download sport mode component should access watch, remote, local data sources`() = runTest {
        // prepare
        `when`(sportModesWatchDataSource.fetchVersionHash())
            .thenReturn("hash")
        `when`(sportModesLocalDataSource.fetchLastSportModeComponentEtag())
            .thenReturn("")
        `when`(sportModesRemoteApi.fetchSportModeBundle(anyString(), anyString()))
            .thenReturn(Pair(null, "etag"))
        `when`(sportModesLocalDataSource.saveSportModeComponent(Pair(null, "etag")))
            .thenReturn(Unit)
        // verify
        sportModesDataRepository.downloadSportModeComponentIfNeeded()

        verify(sportModesWatchDataSource).fetchVersionHash()
        verify(sportModesLocalDataSource).fetchLastSportModeComponentEtag()
        verify(sportModesRemoteApi).fetchSportModeBundle(anyString(), anyString())
        verify(sportModesLocalDataSource).saveSportModeComponent(Pair(null, "etag"))
    }

    @Test
    fun `download sport mode component should complete if we get notmodified while fetching component`() = runTest {
        // prepare
        `when`(sportModesWatchDataSource.fetchVersionHash())
            .thenReturn("hash")
        `when`(sportModesLocalDataSource.fetchLastSportModeComponentEtag())
            .thenReturn("")
        `when`(sportModesRemoteApi.fetchSportModeBundle(anyString(), anyString()))
            .thenThrow(notModifiedErrorMock)
        // verify
        sportModesDataRepository.downloadSportModeComponentIfNeeded()

        verify(sportModesWatchDataSource).fetchVersionHash()
        verify(sportModesLocalDataSource).fetchLastSportModeComponentEtag()
        verify(sportModesRemoteApi).fetchSportModeBundle(anyString(), anyString())
        verify(sportModesLocalDataSource, times(0)).saveSportModeComponent(Pair(null, "etag"))
    }

    @Test
    fun `download sport mode component should complete if we have some bundle downloaded and get an error while fetching component`() = runTest {
        // prepare
        `when`(sportModesWatchDataSource.fetchVersionHash())
            .thenReturn("hash")
        `when`(sportModesLocalDataSource.fetchLastSportModeComponentEtag())
            .thenReturn("")
        `when`(sportModesRemoteApi.fetchSportModeBundle(anyString(), anyString()))
            .thenThrow(RuntimeException())
        `when`(sportModesLocalDataSource.fetchSportModeComponentExistsOnFileSystem())
            .thenReturn(true)
        // verify
        sportModesDataRepository.downloadSportModeComponentIfNeeded()

        verify(sportModesWatchDataSource).fetchVersionHash()
        verify(sportModesLocalDataSource).fetchLastSportModeComponentEtag()
        verify(sportModesRemoteApi).fetchSportModeBundle(anyString(), anyString())
        verify(sportModesLocalDataSource).fetchSportModeComponentExistsOnFileSystem()
        verify(sportModesLocalDataSource, times(0)).saveSportModeComponent(Pair(null, "etag"))
    }

    @Test
    fun `empty hash should skip calls to fetchLastSportModeComponentEtag and fetchSportModeBundle`() = runTest {
        // prepare
        `when`(sportModesWatchDataSource.fetchVersionHash())
            .thenReturn("")
        `when`(sportModesLocalDataSource.fetchSportModeComponentExistsOnFileSystem())
            .thenReturn(true)
        // verify
        sportModesDataRepository.downloadSportModeComponentIfNeeded()

        verify(sportModesWatchDataSource).fetchVersionHash()
        verify(sportModesLocalDataSource, never()).fetchLastSportModeComponentEtag()
        verify(sportModesRemoteApi, never()).fetchSportModeBundle(anyString(), anyString())
        verify(sportModesLocalDataSource).fetchSportModeComponentExistsOnFileSystem()
        verify(sportModesLocalDataSource, times(0)).saveSportModeComponent(Pair(null, "etag"))
    }
}
