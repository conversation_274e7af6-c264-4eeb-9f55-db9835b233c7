package com.stt.android.data.usersettings

import com.stt.android.remote.response.AskoResponse
import com.stt.android.remote.usersettings.RemoteChangeUserEmailRequest
import com.stt.android.remote.usersettings.RemoteSaveUserPhoneNumberRequest
import com.stt.android.remote.usersettings.RemoteUserSettingsRequest
import com.stt.android.remote.usersettings.RemoteUserSettingsResponse
import com.stt.android.remote.usersettings.UserSettingsRestApi

@Suppress("DeferredIsResult")
open class FakeUserSettingsRestApi : UserSettingsRestApi {
    override suspend fun fetchUserSettings(): AskoResponse<RemoteUserSettingsResponse> {
        val payload = RemoteUserSettingsResponse(
            measurementUnit = "",
            hrMaximum = 0,
            gender = "",
            height = 0,
            weight = 0,
            birthDate = 0,
            email = "",
            screenBacklightSetting = "",
            gpsFiltering = false,
            altitudeOffset = 0.0f,
            selectedMapType = "",
            notifyNewFollower = 0,
            notifyWorkoutComment = 0,
            notifyWorkoutFollowingShare = 0,
            autoApproveFollowers = false,
            emailDigest = 0,
            optinAccepted = 0,
            optinRejected = 0,
            optinLastShown = 0,
            optinShowCount = 0,
            analyticsUUID = "",
            country = "",
            countrySubdivision = "",
            language = "",
            realName = "",
            description = "",
            sharingFlagPreference = 0,
            hasOutboundPartnerConnections = false,
            phoneNumber = null,
            predefinedReplies = listOf(),
            preferredTssCalculationMethods = mapOf(),
            firstDayOfTheWeek = null,
            tagAutomation = null,
            favoriteSports = emptyList(),
            motivations = emptyList(),
            disabledAppRatingSuggestions = null,
            automaticUpdateDisabledWatches = emptyList(),
            samplingBucketValue = 0.0,
            privateAccount = false,
            menstrualCycleSettings = null,
            hrRest = 0,
            intensityZones = null,
            showLocale = false,
        )
        return AskoResponse(error = null, metadata = null, payload)
    }

    override suspend fun saveUserSettings(userSettingsRequest: RemoteUserSettingsRequest) {
    }

    override suspend fun saveUserSettingsSafe(totp: String, userSettingsRequest: RemoteUserSettingsRequest): AskoResponse<Boolean> {
        return AskoResponse(error = null, metadata = null, payload = true)
    }

    override suspend fun changeUserEmail(totp: String, remoteChangeUserEmailRequest: RemoteChangeUserEmailRequest) {
    }

    override suspend fun saveUserPhoneNumber(totp: String, verificationToken: String, remoteSaveUserPhoneNumberRequest: RemoteSaveUserPhoneNumberRequest) {
    }

    override suspend fun setUserPOIFormat(poiFormat: String) {
    }
}
