package com.stt.android.data.sportmodes

import com.stt.android.data.device.DeviceInfoApi
import com.stt.android.domain.sportmodes.WatchInfo
import io.reactivex.Completable
import io.reactivex.Single
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.withContext
import java.util.Locale
import javax.inject.Inject

class SportModesWatchDataSource @Inject constructor(
    val deviceInfoApi: DeviceInfoApi,
    val sportModesInfoProvider: SportModesApi,
) {
    suspend fun fetchVersionHash(): String = deviceInfoApi.versionHash()

    fun fetchGroup(modeId: Int): Single<String> {
        return sportModesInfoProvider.fetchSportModeGroup(modeId)
    }

    fun deleteSportMode(modeId: Int): Completable {
        return sportModesInfoProvider.deleteSportModeGroup(modeId)
    }

    fun saveSettings(modeId: Int, json: String): Completable {
        return sportModesInfoProvider.putSportModeSettings(modeId, json)
    }

    fun saveDisplays(modeId: Int, json: String): Completable {
        return sportModesInfoProvider.putSportModeDisplays(modeId, json)
    }

    fun saveGroup(groupId: Int?, json: String?): Completable {
        return sportModesInfoProvider.postSportModeGroup(groupId, json)
    }

    suspend fun fetchWatchInfo(): WatchInfo = withContext(Dispatchers.IO) {
        val variant = async { deviceInfoApi.variant() }
        val version = deviceInfoApi.version()
        WatchInfo(
            variant = variant.await(),
            version = version,
            locale = Locale.getDefault().language,
        )
    }

    fun fetchSportModesListJson(): Single<String> {
        return sportModesInfoProvider.fetchSportModes()
    }

    fun fetchSportModeDisplaysJson(modeId: Int): Single<String> {
        return sportModesInfoProvider.fetchSportModeDisplays(modeId)
    }

    fun fetchSportModeSettingsJson(modeId: Int): Single<String> {
        return sportModesInfoProvider.fetchSportModeSettings(modeId)
    }

    fun supportsMultisportCustomization(): Flow<Boolean> {
        return deviceInfoApi.connectedWatchState()
            .map { state ->
                state.deviceInfo?.capabilities
                    ?.contains(MULTISPORT_MODE_CUSTOMIZATION_CAPABILITY) == true
            }
            .asFlow()
    }

    private companion object {
        private const val MULTISPORT_MODE_CUSTOMIZATION_CAPABILITY = "feat_multisportmodes_1"
    }
}
