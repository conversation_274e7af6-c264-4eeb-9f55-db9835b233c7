package com.stt.android.data.recovery

import com.stt.android.domain.recovery.RecoveryData
import kotlinx.coroutines.flow.Flow
import java.time.ZonedDateTime

interface RecoveryDataLocalDataSource {

    /**
     * Get list of [RecoveryData] entities that require sync to backend.
     * @return List of [RecoveryData] ordered by timestamp
     */
    suspend fun fetchRecoveryDataForBackendSync(): List<RecoveryData>

    /**
     * Get a list of [RecoveryData] entities between the specified dates
     * @param fromTimestamp UTC timestamp in ms of the start of the day to query from
     * @param toTimestamp UTC timestamp in ms of the start of the day to query to
     * @return List of [RecoveryData] for the requested dates range if any exist
     */
    fun fetchRecoveryDataForDateRange(
        fromTimestamp: Long,
        toTimestamp: Long
    ): Flow<List<RecoveryData>>

    /**
     * Save a list of [RecoveryData] in the data source
     * @param recoveryDataList list of [RecoveryData] to save in the data source
     * @param replaceConflicts true if data needs to be replaced in case of conflict
     * @param requireBackendSync true if data has to be synced to backend in the future
     * @return If any RecoveryData entries were stored in the local database. Can be false if the
     * list is empty or [replaceConflicts] is false and all of entries had been inserted already.
     */
    suspend fun saveRecoveryData(
        recoveryDataList: List<RecoveryData>,
        replaceConflicts: Boolean,
        requireBackendSync: Boolean
    ): Boolean

    suspend fun getLatestSyncedTimestamp(): ZonedDateTime?

    suspend fun getRecoveryDataExists(): Boolean

    /**
     * @param checkLimit - Stop the count at threshold value. If user has data for longer periods
     *   of time and we just want to see if some threshold is met, using this optimizes the
     *   fetching to stop at the threshold value and not go through all of the data.
     */
    suspend fun fetchNumDaysWithRecoveryData(checkLimit: Int = Int.MAX_VALUE): Int
}
