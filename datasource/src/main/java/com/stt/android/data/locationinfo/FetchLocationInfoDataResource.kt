package com.stt.android.data.locationinfo

import com.stt.android.remote.locationinfo.FetchLocationInfoApi
import com.stt.android.remote.locationinfo.LocationInfo
import javax.inject.Inject

class FetchLocationInfoDataResource @Inject constructor(private val fetchLocationInfoApi: FetchLocationInfoApi) {

    suspend fun fetchLocationInfo(): LocationInfo =
        fetchLocationInfoApi.fetchLocationInfo().payloadOrThrow()
}
