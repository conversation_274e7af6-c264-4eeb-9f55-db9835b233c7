package com.stt.android.data.usersettings

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.stt.android.domain.user.CombinedIntensityZones
import com.stt.android.domain.user.DomainMenstrualCycleSettings
import com.stt.android.domain.user.DomainUserSettings
import com.stt.android.domain.user.DomainUserTagAutomationSettings
import com.stt.android.domain.user.HrType
import com.stt.android.domain.user.HrZoneType
import com.stt.android.domain.user.HrZones
import com.stt.android.domain.user.HrZonesSwitch
import com.stt.android.domain.user.IntensityZones
import com.stt.android.domain.user.MenstrualCycleRegularity
import com.stt.android.domain.user.Zones
import com.stt.android.remote.usersettings.RemoteHrZones
import com.stt.android.remote.usersettings.RemoteIntensityZones
import com.stt.android.remote.usersettings.RemoteMenstrualCycleSettings
import com.stt.android.remote.usersettings.RemoteUserNotificationsSettings
import com.stt.android.remote.usersettings.RemoteUserNotificationsSettingsAttrs
import com.stt.android.remote.usersettings.RemoteUserSettingsRequest
import com.stt.android.remote.usersettings.RemoteUserSettingsResponse
import com.stt.android.remote.usersettings.RemoteUserTagAutomationSettings
import com.stt.android.remote.usersettings.RemoteZones

internal fun RemoteUserSettingsResponse.toDomainEntity(
    notificationsSettings: RemoteUserNotificationsSettingsAttrs
): DomainUserSettings {
    return DomainUserSettings(
        measurementUnit = this.measurementUnit,
        hrMaximum = this.hrMaximum ?: 0,
        gender = this.gender,
        height = this.height ?: 0,
        weight = this.weight ?: 0,
        birthDate = this.birthDate ?: 0L,
        email = this.email,
        phoneNumber = this.phoneNumber,
        screenBacklightSetting = this.screenBacklightSetting,
        gpsFiltering = this.gpsFiltering ?: false,
        altitudeOffset = this.altitudeOffset ?: 0f,
        selectedMapType = this.selectedMapType,
        notifyNewFollower = this.notifyNewFollower?.toBoolean() ?: false,
        notifyWorkoutComment = this.notifyWorkoutComment?.toBoolean() ?: false,
        notifyWorkoutFollowingShare = this.notifyWorkoutFollowingShare?.toBoolean() ?: false,
        autoApproveFollowers = this.autoApproveFollowers ?: false,
        emailDigest = this.emailDigest?.toBoolean() ?: false,
        optinAccepted = this.optinAccepted ?: 0L,
        optinRejected = this.optinRejected ?: 0L,
        optinLastShown = this.optinLastShown ?: 0L,
        optinShowCount = this.optinShowCount ?: 0L,
        analyticsUUID = this.analyticsUUID ?: "",
        country = this.country,
        countrySubdivision = this.countrySubdivision,
        language = this.language ?: "",
        realName = this.realName,
        description = this.description,
        sharingFlagPreference = this.sharingFlagPreference,
        facebookFriendJoinNotificationEnabled = notificationsSettings.facebookFriendJoin,
        newFollowerNotificationEnabled = notificationsSettings.newFollower,
        workoutCommentNotificationEnabled = notificationsSettings.workoutComment,
        workoutReactionNotificationEnabled = notificationsSettings.workoutReaction,
        workoutShareNotificationEnabled = notificationsSettings.workoutShare,
        hasOutboundPartnerConnections = this.hasOutboundPartnerConnections ?: false,
        predefinedReplies = this.predefinedReplies ?: listOf(),
        preferredTssCalculationMethods = this.preferredTssCalculationMethods ?: mapOf(),
        firstDayOfTheWeek = this.firstDayOfTheWeek,
        tagAutomation = this.tagAutomation?.toDomainUserTagAutomationSettings(),
        favoriteSports = this.favoriteSports ?: emptyList(),
        motivations = this.motivations ?: emptyList(),
        disabledAppRatingSuggestions = this.disabledAppRatingSuggestions ?: emptyList(),
        automaticUpdateDisabledWatches = this.automaticUpdateDisabledWatches ?: emptyList(),
        samplingBucketValue = this.samplingBucketValue ?: 0.0,
        privateAccount = this.privateAccount ?: false,
        menstrualCycleSettings = this.menstrualCycleSettings?.toDomainMenstrualCycleSettings(),
        hrRest = this.hrRest,
        combinedIntensityZones = this.intensityZones?.toCombinedIntensityZones(),
        newActivitySyncedNotificationEnabled = notificationsSettings.newActivitySynced,
        showLocale = this.showLocale ?: false
    )
}

fun DomainUserSettings.toRemoteUserSettingsRequest(): RemoteUserSettingsRequest {
    return RemoteUserSettingsRequest(
        measurementUnit = this.measurementUnit,
        hrMaximum = this.hrMaximum,
        gender = this.gender,
        height = this.height,
        weight = this.weight,
        birthDate = this.birthDate,
        email = this.email,
        screenBacklightSetting = this.screenBacklightSetting,
        gpsFiltering = this.gpsFiltering,
        altitudeOffset = this.altitudeOffset,
        selectedMapType = this.selectedMapType,
        notifyNewFollower = this.notifyNewFollower.toInt(),
        notifyWorkoutComment = this.notifyWorkoutComment.toInt(),
        notifyWorkoutFollowingShare = this.notifyWorkoutFollowingShare.toInt(),
        autoApproveFollowers = this.autoApproveFollowers,
        emailDigest = this.emailDigest.toInt(),
        analyticsUUID = this.analyticsUUID,
        country = this.country,
        countrySubdivision = this.countrySubdivision,
        language = this.language,
        realName = this.realName,
        description = this.description,
        sharingFlagPreference = this.sharingFlagPreference,
        phoneNumber = this.phoneNumber,
        predefinedReplies = this.predefinedReplies,
        preferredTssCalculationMethods = this.preferredTssCalculationMethods,
        firstDayOfTheWeek = this.firstDayOfTheWeek,
        tagAutomation = this.tagAutomation?.toRemoteUserTagAutomationSettings(),
        favoriteSports = this.favoriteSports.ifEmpty { null },
        motivations = this.motivations.ifEmpty { null },
        disabledAppRatingSuggestions = this.disabledAppRatingSuggestions.ifEmpty { null },
        automaticUpdateDisabledWatches = this.automaticUpdateDisabledWatches,
        privateAccount = this.privateAccount,
        showLocale = this.showLocale,
        menstrualCycleSettings = this.menstrualCycleSettings?.toRemoteMenstrualCycleSettings(),
        hrRest = this.hrRest,
        intensityZones = this.combinedIntensityZones?.toRemoteIntensityZones(),
    )
}

internal fun DomainUserSettings.toRemoteUserNotificationsSettings(
    fcmToken: String?
): RemoteUserNotificationsSettings {
    return RemoteUserNotificationsSettings(
        deviceId = fcmToken,
        attrs = RemoteUserNotificationsSettingsAttrs(
            newFollower = this.newFollowerNotificationEnabled,
            facebookFriendJoin = this.facebookFriendJoinNotificationEnabled,
            workoutComment = this.workoutCommentNotificationEnabled,
            workoutReaction = this.workoutReactionNotificationEnabled,
            workoutShare = this.workoutShareNotificationEnabled,
            newActivitySynced = this.newActivitySyncedNotificationEnabled,
        )
    )
}

internal fun RemoteUserTagAutomationSettings.toDomainUserTagAutomationSettings(): DomainUserTagAutomationSettings {
    return DomainUserTagAutomationSettings(
        autoTagCommute = this.autoTagCommute
    )
}

internal fun DomainUserTagAutomationSettings.toRemoteUserTagAutomationSettings(): RemoteUserTagAutomationSettings {
    return RemoteUserTagAutomationSettings(
        autoTagCommute = this.autoTagCommute
    )
}

internal fun Int.toBoolean(): Boolean {
    return when {
        this == 0 -> false
        this == 1 -> true
        else -> throw IllegalArgumentException("Value must be either 0 or 1 to convert to boolean")
    }
}

internal fun Boolean.toInt(): Int = if (this) 1 else 0

internal fun RemoteMenstrualCycleSettings.toDomainMenstrualCycleSettings() =
    DomainMenstrualCycleSettings(
        cycleRegularity = MenstrualCycleRegularity.entries.firstOrNull { it.name == this.cycleRegularity.uppercase() }
            ?: MenstrualCycleRegularity.NOT_SURE,
        cycleLength = this.cycleLength,
        periodDuration = this.periodDuration
    )

private fun DomainMenstrualCycleSettings.toRemoteMenstrualCycleSettings() =
    RemoteMenstrualCycleSettings(
        cycleRegularity = this.cycleRegularity.name,
        cycleLength = this.cycleLength,
        periodDuration = this.periodDuration
    )

internal fun RemoteIntensityZones.toCombinedIntensityZones() =
    CombinedIntensityZones(
        hrZoneType = HrZoneType(
            HrType.entries.firstOrNull { it.type.uppercase() == this.hrZoneType?.uppercase() }
                ?: HrType.MAX,
            System.currentTimeMillis()
        ),
        IntensityZones(
            defaultHrZones = this.defaultHrZones?.toHrZones(),
            runningHrZones = this.runningHrZones?.toHrZones(),
            cyclingHrZones = this.cyclingHrZones?.toHrZones(),
            runningPaceZones = this.runningPaceZones?.checkPaceZoneIsValid()?.toZones(),
            runningPowerZones = this.runningPowerZones?.toZones(),
            cyclingPowerZones = this.cyclingPowerZones?.toZones(),
            runningHrZonesEnable = HrZonesSwitch(this.runningHeartRateZonesEnabled),
            cyclingHrZonesEnable = HrZonesSwitch(this.cyclingHeartRateZonesEnabled),
        )
    )

private fun RemoteZones?.checkPaceZoneIsValid(): RemoteZones? {
    this?.let {
        if (zone5.isValidPaceValue() && zone4.isValidPaceValue() && zone3.isValidPaceValue() && zone2.isValidPaceValue()) {
            return this
        }
    }
    return null
}

private fun Int.isValidPaceValue(): Boolean {
    // The unit of the old version was seconds, and the maximum value might be 18 * 60 + 18 (1098).
    // If the pace less than 10 seconds, it is the old version data.
    // If the pace is more than 3,000 seconds (50 minutes per kilometer/mile), it is also invalid.
    return this > 10 * 1000 && this < 3000 * 1000
}

private fun CombinedIntensityZones.toRemoteIntensityZones() =
    RemoteIntensityZones(
        hrZoneType = this.hrZoneType?.hrType?.type,
        defaultHrZones = this.intensityZones?.defaultHrZones?.toRemoteHrZones(),
        runningHrZones = this.intensityZones?.runningHrZones?.toRemoteHrZones(),
        cyclingHrZones = this.intensityZones?.cyclingHrZones?.toRemoteHrZones(),
        runningPaceZones = this.intensityZones?.runningPaceZones?.toRemoteZones(),
        runningPowerZones = this.intensityZones?.runningPowerZones?.toRemoteZones(),
        cyclingPowerZones = this.intensityZones?.cyclingPowerZones?.toRemoteZones(),
        runningHeartRateZonesEnabled = this.intensityZones?.runningHrZonesEnable?.enable ?: false,
        cyclingHeartRateZonesEnabled = this.intensityZones?.cyclingHrZonesEnable?.enable ?: false
    )

private fun RemoteHrZones.toHrZones() = HrZones(
    max = this.max?.toZones(),
    reserve = this.reserve?.toZones(),
    aet = this.aet?.toZones()
)

private fun RemoteZones.toZones() = Zones(this.zone2, this.zone3, this.zone4, this.zone5)

private fun HrZones.toRemoteHrZones() = RemoteHrZones(
    max = this.max?.toRemoteZones(),
    reserve = this.reserve?.toRemoteZones(),
    aet = this.aet?.toRemoteZones()
)

private fun Zones.toRemoteZones() = RemoteZones(this.zone1, this.zone2, this.zone3, this.zone4)

@JsonClass(generateAdapter = true)
data class MenstrualCycleSettings(
    @Json(name = "cycleRegularity") val cycleRegularity: MenstrualCycleRegularity,
    @Json(name = "cycleLength") val cycleLength: Int?,
    @Json(name = "periodDuration") val periodDuration: Int
)
