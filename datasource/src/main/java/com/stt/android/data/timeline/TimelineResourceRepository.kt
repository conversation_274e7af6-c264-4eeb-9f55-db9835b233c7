package com.stt.android.data.timeline

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.stt.android.data.TimeUtils
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.activitydata.dailyvalues.asStressState
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.recovery.RecoveryDataRepository
import com.stt.android.domain.sleep.SleepRepository
import com.stt.android.domain.sleep.SleepSegment
import com.stt.android.domain.sleep.SleepStage
import com.stt.android.domain.sleep.SleepStageInterval
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.timeline.RemoteTimelineJson
import com.stt.android.timeline.TimelineResourceLocalDataSource
import com.stt.android.timeline.entity.SleepSampleAttributesEntity
import com.stt.android.timeline.entity.SleepStageSml
import com.suunto.algorithms.data.Energy.Companion.joules
import com.suunto.algorithms.data.HeartRate.Companion.hz
import com.suunto.connectivity.recovery.SuuntoRecoveryDataEntry
import com.suunto.connectivity.trenddata.SuuntoTrendDataEntry
import java.time.ZoneId
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.time.Duration.Companion.seconds

class TimelineResourceRepository
@Inject constructor(
    private val sleepRepository: SleepRepository,
    private val trendDataRepository: TrendDataRepository,
    private val recoveryDataRepository: RecoveryDataRepository,
    moshi: Moshi
) : TimelineResourceLocalDataSource {

    private val sleepAttributesEntityAdapter: JsonAdapter<SleepSampleAttributesEntity> =
        moshi.adapter(SleepSampleAttributesEntity::class.java)

    override suspend fun storeSleepEntries(entries: List<RemoteTimelineJson>): Boolean {
        val timelineEntities = entries.mapNotNull { timelineEntry ->
            val entity = sleepAttributesEntityAdapter.fromJson(timelineEntry.attributes)
            entity?.let { timelineEntry.source to entity }
        }
        return sleepRepository.saveSleep(
            timelineEntities.mapNotNull(::convertTimelineToSleepSegment),
            requireBackendSync = true
        ) && sleepRepository.saveSleepStages(
            timelineEntities.map(::convertTimelineToSleepStages).flatten(),
            replaceConflicts = true,
            requireBackendSync = true
        )
    }

    override suspend fun storeTrendDataEntries(entries: List<SuuntoTrendDataEntry>): Boolean =
        trendDataRepository.saveTrendData(
            entries.map(::convertToTrendData)
                // filter out trend data that has no energy, steps nor heart rate
                .filter {
                    it.energy.inCal > 0.0 || it.steps > 0 || it.hasHr
                },
            replaceConflicts = false,
            requireBackendSync = true
        )

    override suspend fun storeRecoveryEntries(entries: List<SuuntoRecoveryDataEntry>): Boolean =
        recoveryDataRepository.saveRecoveryData(
            entries.map(::convertToRecoveryData),
            replaceConflicts = false,
            requireBackendSync = true
        )

    private fun convertTimelineToSleepSegment(
        sourceAndTimelineEntity: Pair<String, SleepSampleAttributesEntity>
    ): SleepSegment? {
        val (source, timelineEntity) = sourceAndTimelineEntity
        val header = timelineEntity.sleepSmlEntity.header ?: return null
        // S7 provides dateTimeLocal with actual correct timezone, while
        // NG1 watches provide only dateTime value always in UTC timezone,
        // so we convert it to local timezone
        val timeISO8601 = header.dateTimeLocal
            ?: header.dateTime.withZoneSameInstant(ZoneId.systemDefault())
        return SleepSegment(
            serial = source, // serial is in the source from mds
            timestamp = timeISO8601.toEpochMilli(),
            quality = header.quality?.div(100f),
            avgHr = header.hr?.avg?.hz,
            minHr = header.hr?.min?.hz,
            feeling = header.feeling,
            duration = header.duration.toDouble().seconds,
            deepSleepDuration = header.deepSleepDuration?.toDouble()?.seconds,
            remSleepDuration = header.remSleepDuration?.toDouble()?.seconds,
            lightSleepDuration = header.lightSleepDuration?.toDouble()?.seconds,
            timeISO8601 = timeISO8601,
            bedtimeStart = header.bedtimeStart,
            bedtimeEnd = header.bedtimeEnd,
            bodyResourcesInsightId = header.bodyResourcesInsightId,
            sleepId = header.sleepId,
            maxSpO2 = header.spo2?.max,
            altitude = header.altitude,
            avgHrv = header.hrv?.avg,
            avgHrvSampleCount = header.hrv?.count,
            isNap = header.isNap,
            sleepOnsetLatencyDuration = header.sleepOnsetLatencyDuration?.toDouble()?.seconds,
            wakeAfterSleepOnsetDuration = header.wakeAfterSleepOnsetDuration?.toDouble()?.seconds,
            wakeBeforeOffBedDuration = header.wakeBeforeOffBedDuration?.toDouble()?.seconds,
        )
    }

    private fun convertTimelineToSleepStages(
        sourceAndTimelineEntity: Pair<String, SleepSampleAttributesEntity>
    ): List<SleepStageInterval> {
        val (_, timelineEntity) = sourceAndTimelineEntity
        val stages = timelineEntity.sleepSmlEntity.stageEvents ?: return listOf()
        return stages.map {
            SleepStageInterval(
                duration = it.duration.toDouble().seconds,
                stage = when (it.stage) {
                    SleepStageSml.AWAKE -> SleepStage.AWAKE
                    SleepStageSml.REM -> SleepStage.REM
                    SleepStageSml.LIGHT -> SleepStage.LIGHT
                    SleepStageSml.DEEP -> SleepStage.DEEP
                },
                timeISO8601 = it.startTimestamp,
            )
        }
    }

    private fun convertToTrendData(suuntoTrendDataEntry: SuuntoTrendDataEntry) = TrendData(
        serial = suuntoTrendDataEntry.serial,
        timestamp = suuntoTrendDataEntry.timestamp,
        energy = suuntoTrendDataEntry.energy.joules,
        steps = suuntoTrendDataEntry.steps,
        // HR data from watch trend data is in BPM, we convert it to Hz
        // also we treat hr=0 as null
        hr = suuntoTrendDataEntry.hr.takeIf { it != null && it > 0f }?.div(60f),
        hrMin = suuntoTrendDataEntry.hrMin?.div(60f),
        hrMax = suuntoTrendDataEntry.hrMax?.div(60f),
        spo2 = suuntoTrendDataEntry.spo2,
        altitude = suuntoTrendDataEntry.altitude,
        hrv = suuntoTrendDataEntry.hrv,
    )

    private fun convertToRecoveryData(suuntoRecoveryDataEntry: SuuntoRecoveryDataEntry): RecoveryData {
        val timestampMillis = TimeUnit.SECONDS.toMillis(suuntoRecoveryDataEntry.timestampSeconds)
        return RecoveryData(
            serial = suuntoRecoveryDataEntry.serial,
            timestamp = timestampMillis,
            balance = (suuntoRecoveryDataEntry.balance / 100f).coerceIn(0f..1f), // Buggy watches may give values larger than 100%
            stressState = suuntoRecoveryDataEntry.stressState.asStressState(),
            timeISO8601 = TimeUtils.epochToLocalZonedDateTime(timestampMillis)
        )
    }
}
