package com.stt.android.data.routes

import com.stt.android.domain.routes.ExportRepository
import com.stt.android.remote.routes.ExportRemoteApi
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
abstract class ExportRepositoryModule {

    @Binds
    abstract fun bindExportRepositoryImpl(repositoryImpl: ExportRepositoryImpl): ExportRepository
}

class ExportRepositoryImpl @Inject constructor(private val exportRemoteApi: ExportRemoteApi) :
    ExportRepository {

    override suspend fun exportGpxTrack(userName: String, workoutKey: String): String? {
        return exportRemoteApi.exportGpxTrack(userName, workoutKey)
    }

    override suspend fun exportGpxRoute(userName: String, workoutKey: String): String? {
        return exportRemoteApi.exportGpxRoute(userName, workoutKey)
    }

    override suspend fun exportKmlRoute(userName: String, workoutKey: String): String? {
        return exportRemoteApi.exportKmlRoute(userName, workoutKey)
    }
}
