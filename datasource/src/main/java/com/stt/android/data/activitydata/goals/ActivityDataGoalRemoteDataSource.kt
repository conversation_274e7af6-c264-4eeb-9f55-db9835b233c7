package com.stt.android.data.activitydata.goals

import com.stt.android.TestOpen
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.timeout
import javax.inject.Inject
import kotlin.time.Duration.Companion.seconds

@TestOpen
class ActivityDataGoalRemoteDataSource
@Inject constructor(
    private val activityDataGoalController: ActivityDataGoalController,
    private val isWatchConnectedUseCase: IsWatchConnectedUseCase,
) : ActivityDataGoalDataSource {
    override suspend fun fetchStepsGoal(): Int = doWhenWatchConnected {
        activityDataGoalController.getStepsGoal()
    }

    override suspend fun fetchEnergyGoal(): Int = doWhenWatchConnected {
        activityDataGoalController.getEnergyGoal()
    }

    override suspend fun fetchSleepGoal(): Int = doWhenWatchConnected {
        activityDataGoalController.getSleepGoal()
    }

    override suspend fun fetchBedtimeStart(): Int = doWhenWatchConnected {
        activityDataGoalController.getBedtimeStart()
    }

    override suspend fun fetchBedtimeEnd(): Int = doWhenWatchConnected {
        activityDataGoalController.getBedtimeEnd()
    }

    override suspend fun setStepsGoal(goal: Int) = doWhenWatchConnected {
        activityDataGoalController.setStepsGoal(goal)
    }

    override suspend fun setEnergyGoal(goal: Int) = doWhenWatchConnected {
        activityDataGoalController.setEnergyGoal(goal)
    }

    override suspend fun setSleepGoal(goal: Int) = doWhenWatchConnected {
        activityDataGoalController.setSleepGoal(goal)
    }

    override suspend fun setBedtimes(bedtimeStart: Int, bedtimeEnd: Int) = doWhenWatchConnected {
        activityDataGoalController.setBedtimes(bedtimeStart, bedtimeEnd)
    }

    override suspend fun setBedtimeStart(bedtimeStart: Int) = doWhenWatchConnected {
        activityDataGoalController.setBedtimeStart(bedtimeStart)
    }

    override suspend fun setBedtimeEnd(bedtimeEnd: Int) = doWhenWatchConnected {
        activityDataGoalController.setBedtimeEnd(bedtimeEnd)
    }

    @OptIn(FlowPreview::class)
    private suspend inline fun <T> doWhenWatchConnected(block: () -> T): T {
        isWatchConnectedUseCase.invoke()
            .timeout(20.seconds)
            .filter { it }
            .first()
        return block()
    }
}
