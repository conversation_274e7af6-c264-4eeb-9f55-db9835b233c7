package com.stt.android.data.device

import java.io.File
import javax.inject.Inject

class DeviceLogWatchDataSource
@Inject constructor(
    private val deviceLogApi: DeviceLogApi
) : DeviceLogDataSource {
    override suspend fun fetchLogs(logType: Int): List<File> = deviceLogApi.getLogs(logType)

    override suspend fun sendLogs(logFiles: List<File>, toFile: Boolean, remoteLogId: String) {
        throw NotImplementedError("Not implemented")
    }

    override suspend fun getHeadsetLogs(): List<File> = deviceLogApi.getHeadsetCacheLogs()
}
