package com.stt.android.data.tags

import com.stt.android.domain.tags.UserTagsRepository
import javax.inject.Inject

class SyncNewUserTags @Inject constructor(
    private val tagsRepository: UserTagsRepository
) {
    suspend operator fun invoke() {
        val newUserTags = tagsRepository.findUnsyncedUserTags()

        for (userTag in newUserTags) {
            val remoteUserTagId = tagsRepository.createNewUserTagRemotely(userTag.name)

            if (!remoteUserTagId.isNullOrBlank()) {
                tagsRepository.upsertUserTag(
                    userTag.copy(
                        key = remoteUserTagId
                    )
                )
            }
        }
    }
}
