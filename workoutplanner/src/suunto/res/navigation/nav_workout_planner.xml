<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/nav_workout_planner"
    app:startDestination="@id/plannedWorkoutsListFragment">

    <fragment
        android:id="@+id/plannedWorkoutsListFragment"
        android:name="com.stt.android.workout.planner.plans.PlannedWorkoutsListFragment">
        <action
            android:id="@+id/newWorkoutPlan"
            app:destination="@id/workoutPlanStepsListFragment"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:exitAnim="@anim/slide_out_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right"
            app:popExitAnim="@anim/slide_out_left_to_right" />
        <action
            android:id="@+id/editExistingWorkoutPlan"
            app:destination="@id/workoutPlanStepsListFragment"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:exitAnim="@anim/slide_out_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right"
            app:popExitAnim="@anim/slide_out_left_to_right" />
    </fragment>

    <fragment
        android:id="@+id/workoutPlanStepsListFragment"
        android:name="com.stt.android.workout.planner.steps.WorkoutPlanStepsListFragment">
        <argument
            android:name="guideId"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <action
            android:id="@+id/editExerciseStep"
            app:destination="@id/editStepFragment"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:exitAnim="@anim/slide_out_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right"
            app:popExitAnim="@anim/slide_out_left_to_right">
            <argument
                android:name="workoutStepUuid"
                app:argType="string"
                app:nullable="false" />
        </action>
        <action
            android:id="@+id/showSummary"
            app:destination="@id/summaryFragment"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:exitAnim="@anim/slide_out_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right"
            app:popExitAnim="@anim/slide_out_left_to_right" />
    </fragment>

    <fragment
        android:id="@+id/editStepFragment"
        android:name="com.stt.android.workout.planner.editstep.EditStepFragment">
        <argument
            android:name="workoutStepUuid"
            app:argType="string"
            app:nullable="false" />
    </fragment>

    <fragment
        android:id="@+id/summaryFragment"
        android:name="com.stt.android.workout.planner.summary.SummaryFragment" />

</navigation>
