package com.stt.android.workout.planner.editstep

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.soy.algorithms.planner.DefaultMessagesFormatter
import com.soy.algorithms.planner.GuideMessagesFormatter
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.common.buildAnnotatedStringWithStyledNumbers

@Composable
fun CurrentTargetFromMinMaxText(
    target: WorkoutStep.Exercise.Target?,
    formatter: GuideMessagesFormatter,
    modifier: Modifier = Modifier
) {
    val (value, unit) = formatTargetValueAndUnit(target, formatter)
    val text: AnnotatedString = if (value != null) {
        buildAnnotatedStringWithStyledNumbers(
            text = stringResource(
                R.string.workout_planner_step_calculated_target_value_with_unit,
                value,
                unit
            ),
            numberStyle = SpanStyle(fontWeight = FontWeight.Bold)
        )
    } else {
        AnnotatedString("")
    }

    Column(modifier = modifier) {
        Divider(
            modifier = Modifier
                .fillMaxWidth()
                .height(2.dp),
            color = MaterialTheme.colors.surface
        )

        Text(
            text,
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colors.background)
                .padding(vertical = MaterialTheme.spacing.small),
            textAlign = TextAlign.Center
        )
    }
}

private data class TargetValueAndUnit(
    val value: String?,
    val unit: String
) {
    constructor(valueAndUnit: Pair<String, String>) : this(valueAndUnit.first, valueAndUnit.second)

    companion object {
        val EMPTY = TargetValueAndUnit(null, "")
    }
}

// TODO: Refactor handling for different types of targets
@Composable
private fun formatTargetValueAndUnit(
    target: WorkoutStep.Exercise.Target?,
    formatter: GuideMessagesFormatter
): TargetValueAndUnit =
    when (target) {
        is WorkoutStep.Exercise.Target.HeartRate ->
            target.value?.let { TargetValueAndUnit(formatter.formatHeartRate(it)) }
        is WorkoutStep.Exercise.Target.Power ->
            target.value?.let { TargetValueAndUnit(formatter.formatPower(it)) }
        is WorkoutStep.Exercise.Target.Pace ->
            target.value?.let { TargetValueAndUnit(formatter.formatPaceFromSpeed(it)) }
        is WorkoutStep.Exercise.Target.Speed ->
            target.value?.let { TargetValueAndUnit(formatter.formatSpeed(it)) }
        else -> null
    } ?: TargetValueAndUnit.EMPTY

@Composable
@Preview
private fun CurrentTargetFromMinMaxTextPreview() {
    AppTheme {
        CurrentTargetFromMinMaxText(
            target = WorkoutStep.Exercise.Target.Power(value = 350.0, min = null, max = null),
            formatter = DefaultMessagesFormatter(),
            modifier = Modifier.fillMaxWidth()
        )
    }
}
