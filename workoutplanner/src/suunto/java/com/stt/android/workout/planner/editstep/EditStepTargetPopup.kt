package com.stt.android.workout.planner.editstep

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.soy.algorithms.planner.DefaultMessagesFormatter
import com.soy.algorithms.planner.GuideMessagesFormatter
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.compose.theme.header
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.workout.planner.R
import java.util.Locale
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

/**
 * A popup for setting the target value for a step. Includes title and an Ok button
 * for confirming the target value.
 *
 * This composable also converts the inputted values into a proper
 * [WorkoutStep.Exercise.Target] object.
 */
@Composable
fun EditStepTargetPopup(
    unit: MeasurementUnit,
    formatter: GuideMessagesFormatter,
    phase: WorkoutStep.Exercise.Phase,
    originalTarget: WorkoutStep.Exercise.Target?,
    onNewTarget: (WorkoutStep.Exercise.Target?) -> Unit,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val heartRateUnit = stringResource(CR.string.TXT_BPM)
    val powerUnit = stringResource(CR.string.watt)
    val speedUnit = stringResource(unit.speedUnit)
    val paceUnit = stringResource(unit.paceUnit)

    var targetState by rememberSaveable(stateSaver = EditStepTargetState.Saver) {
        mutableStateOf(
            originalTarget.asEditStepTargetState(
                heartRateUnitString = heartRateUnit,
                powerUnitString = powerUnit,
                speedUnitString = speedUnit,
                paceUnitString = paceUnit,
                unit = unit,
                phase = phase
            )
        )
    }

    fun confirmTargetAndDismiss() {
        onNewTarget(targetState.asWorkoutStepTarget(unit))
        onDismissRequest()
    }

    Dialog(
        onDismissRequest = { confirmTargetAndDismiss() },
        // work around https://issuetracker.google.com/issues/221643630
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Surface(
            modifier = modifier
                .widthIn(max = dimensionResource(CR.dimen.dialog_max_width))
                .padding(horizontal = MaterialTheme.spacing.large)
                .fillMaxWidth(),
            shape = MaterialTheme.shapes.medium
        ) {
            Column(
                modifier = Modifier.verticalScroll(rememberScrollState())
            ) {
                Text(
                    text = stringResource(R.string.workout_planner_edit_step_target_title)
                        .uppercase(Locale.getDefault()),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(MaterialTheme.spacing.medium),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.header
                )

                EditStepTargetOptions(
                    modifier = Modifier.fillMaxWidth(),
                    state = targetState,
                    onStateUpdate = { targetState = targetState.withUpdateApplied(it) },
                    formatter = formatter,
                    unit = unit,
                )

                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    TextButton(
                        onClick = { confirmTargetAndDismiss() },
                        modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    ) {
                        Text(stringResource(BaseR.string.ok))
                    }
                }
            }
        }
    }
}

@Composable
@Preview
private fun EditStepTargetPopupPreview() {
    EditStepTargetPopup(
        unit = MeasurementUnit.METRIC,
        formatter = DefaultMessagesFormatter(),
        phase = WorkoutStep.Exercise.Phase.INTERVAL,
        originalTarget = WorkoutStep.Exercise.Target.Speed(
            value = 5.0, // 5 meters per second = 18 km/h = 3'20/km
            min = null,
            max = null,
        ),
        onNewTarget = {},
        onDismissRequest = {}
    )
}
