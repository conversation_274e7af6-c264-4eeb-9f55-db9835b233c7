package com.stt.android.workout.planner.steps

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.SnackbarDuration
import androidx.compose.material.SnackbarResult
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.WorkoutPlanStepsListItem
import com.stt.android.workout.planner.WorkoutPlannerActivity
import com.stt.android.workout.planner.WorkoutPlannerViewModel
import com.stt.android.workout.planner.common.AppBarWithBackNavigation
import com.stt.android.workout.planner.common.DurationAndDistanceInfoRow
import com.stt.android.SimGuideMessagesFormatter
import com.stt.android.workout.planner.common.WorkoutPlannerBottomButtons
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class WorkoutPlanStepsListFragment : Fragment() {
    private val viewModel: WorkoutPlannerViewModel by activityViewModels()

    private val args: WorkoutPlanStepsListFragmentArgs by navArgs()

    @Inject
    lateinit var formatter: SimGuideMessagesFormatter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (savedInstanceState == null) {
            when (val guideId = args.guideId) {
                null -> {
                    viewModel.startNewPlan()
                }

                else -> {
                    viewModel.startEditingExistingPlan(SuuntoPlusGuideId(guideId))
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContent {
            AppTheme {
                val loadingState by viewModel.loadingState.collectAsState()

                var showDiscardConfirmationDialog by rememberSaveable { mutableStateOf(false) }

                val scaffoldState = rememberScaffoldState()

                val onBack = {
                    if (viewModel.shouldShowDiscardChangesConfirmation()) {
                        showDiscardConfirmationDialog = true
                    } else {
                        clearPlanAndPopBackOrFinish()
                    }
                }

                Scaffold(
                    scaffoldState = scaffoldState,
                    topBar = {
                        val titleResource by viewModel.titleResource.collectAsState()
                        AppBarWithBackNavigation(
                            title = stringResource(titleResource),
                            fragment = this@WorkoutPlanStepsListFragment,
                            onBack = onBack,
                        )
                    },
                    bottomBar = {
                        val viewState by viewModel.stepsState.collectAsState()
                        val duration by viewModel.totalDuration.collectAsState()
                        val distance by viewModel.totalDistance.collectAsState()

                        Column {
                            Divider(color = MaterialTheme.colors.dividerColor)
                            DurationAndDistanceInfoRow(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(MaterialTheme.colors.surface),
                                duration = duration,
                                distance = distance,
                            )

                            WorkoutPlannerBottomButtons(
                                primaryButtonEnabled = viewState.stepsValid,
                                primaryButtonText = stringResource(R.string.workout_planner_bottom_button_next),
                                onPrimaryButtonClick = {
                                    findNavController().navigate(
                                        WorkoutPlanStepsListFragmentDirections.showSummary()
                                    )
                                }
                            )
                        }
                    }
                ) { internalPadding ->
                    val stepsState = viewModel.stepsState.collectAsState()

                    ContentCenteringColumn(Modifier.padding(internalPadding)) {
                        BackHandler(onBack = onBack)

                        when (loadingState) {
                            WorkoutPlannerViewModel.PlanLoadingState.LOADING -> {
                                Box(
                                    modifier = Modifier.fillMaxSize(),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.padding(MaterialTheme.spacing.large)
                                    )
                                }
                            }

                            WorkoutPlannerViewModel.PlanLoadingState.FAILED -> {
                                LaunchedEffect(loadingState) {
                                    val snackResult = scaffoldState.snackbarHostState.showSnackbar(
                                        message = getString(R.string.workout_planner_failed_to_open_workout_plan_for_editing),
                                        actionLabel = getString(com.stt.android.R.string.retry_action),
                                        duration = SnackbarDuration.Indefinite
                                    )

                                    if (snackResult == SnackbarResult.ActionPerformed) {
                                        args.guideId?.let {
                                            viewModel.startEditingExistingPlan(SuuntoPlusGuideId(it))
                                        }
                                    }
                                }
                            }

                            else -> {
                                WorkoutPlanStepsList(
                                    formatter = formatter,
                                    listItems = stepsState.value.steps,
                                    canMoveStep = { index ->
                                        stepsState.value.steps.getOrNull(index) is WorkoutPlanStepsListItem.ExerciseStep
                                    },
                                    canMoveStepOverItem = { index ->
                                        when (stepsState.value.steps.getOrNull(index)) {
                                            is WorkoutPlanStepsListItem.ExerciseStep -> true
                                            is WorkoutPlanStepsListItem.RepeatStart -> true
                                            is WorkoutPlanStepsListItem.RepeatEnd -> true
                                            else -> false
                                        }
                                    },
                                    onStepClick = {
                                        findNavController().navigate(
                                            WorkoutPlanStepsListFragmentDirections
                                                .editExerciseStep(it.uuid)
                                        )
                                    },
                                    onAddExerciseClick = {
                                        viewModel.addExerciseStep()
                                    },
                                    onAddRepeatClick = {
                                        viewModel.addRepeatStep()
                                    },
                                    onMoveStep = { fromIndex, toIndex ->
                                        val from =
                                            stepsState.value.steps[fromIndex] as WorkoutPlanStepsListItem.IndexedStepsListItem
                                        val to =
                                            stepsState.value.steps[toIndex] as WorkoutPlanStepsListItem.IndexedStepsListItem
                                        viewModel.moveExercise(
                                            from.plannerOverallIndex,
                                            to.plannerOverallIndex
                                        )
                                    },
                                    onDecreaseCountClick = { step ->
                                        viewModel.decreaseRepeatCount(step)
                                    },
                                    onIncreaseCountClick = { step ->
                                        viewModel.increaseRepeatCount(step)
                                    },
                                    onDragInterrupted = {
                                        // In case user moved the last step from a repeat block
                                        viewModel.deleteEmptyRepeatBlocks()
                                    },
                                    onDeleteClick = {
                                        viewModel.deleteStep(it.uuid)
                                        viewModel.deleteEmptyRepeatBlocks()
                                    },
                                    onDuplicateClick = {
                                        viewModel.duplicateStep(it.uuid)
                                    }
                                )
                            }
                        }
                    }

                    if (showDiscardConfirmationDialog) {
                        ConfirmationDialog(
                            text = stringResource(R.string.workout_planner_discard_changes_confirmation),
                            confirmButtonText = stringResource(com.stt.android.R.string.discard),
                            cancelButtonText = stringResource(com.stt.android.R.string.cancel),
                            onDismissRequest = { showDiscardConfirmationDialog = false },
                            onConfirm = {
                                clearPlanAndPopBackOrFinish()
                            },
                            useDestructiveColorForConfirm = true
                        )
                    }
                }
            }
        }
    }

    private fun clearPlanAndPopBackOrFinish() {
        viewModel.clearPlan()
        (requireActivity() as? WorkoutPlannerActivity)?.finishOrReturnToPlansList(
            savedSuccessfully = false
        )
    }
}
