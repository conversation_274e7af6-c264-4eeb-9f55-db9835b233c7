package com.stt.android.remote.usersettings

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.stt.android.settings.UserSettingsProperty
import java.util.Locale

@JsonClass(generateAdapter = true)
data class RemoteUserSettingsRequest(
    @Json(name = "measurementUnit") val measurementUnit: String?,
    @<PERSON><PERSON>(name = "hr_max") val hrMaximum: Int,
    @<PERSON><PERSON>(name = "gender") val gender: String?,
    @<PERSON><PERSON>(name = "height") val height: Int?,
    @<PERSON><PERSON>(name = "weight") val weight: Int,
    @<PERSON><PERSON>(name = "birthdate") val birthDate: Long,
    @<PERSON><PERSON>(name = "email") val email: String?,
    @<PERSON><PERSON>(name = "screenBacklight") val screenBacklightSetting: String?,
    @<PERSON><PERSON>(name = "gpsFiltering") val gpsFiltering: Boolean,
    @<PERSON><PERSON>(name = "altitudeOffset") val altitudeOffset: Float,
    @<PERSON><PERSON>(name = "default_maptype") val selectedMapType: String?,
    @Json(name = "notifyFriendInvite") val notifyNewFollower: Int,
    @<PERSON><PERSON>(name = "notifyWorkoutComment") val notifyWorkoutComment: Int,
    @Json(name = "notifyWorkoutFriendShare") val notifyWorkoutFollowingShare: Int,
    @Json(name = "automaticallyApproveFollowers") val autoApproveFollowers: Boolean,
    @Json(name = "emailDigest") val emailDigest: Int,
    @Json(name = "UUID") val analyticsUUID: String,
    @Json(name = "country") val country: String?,
    @Json(name = "countrySubdivision") val countrySubdivision: String?,
    @Json(name = "language") val language: String,
    @Json(name = "realName") val realName: String?,
    @Json(name = "description") val description: String?,
    @Json(name = "sharingFlagPreference") val sharingFlagPreference: Int?,
    @Json(name = "phoneNumber") val phoneNumber: String?,
    @Json(name = "predefinedReplies") val predefinedReplies: List<String>?, // todo clarify spec
    @Json(name = "preferredTssCalculationMethods") val preferredTssCalculationMethods: Map<Int, String>,
    @Json(name = "firstDayOfTheWeek") val firstDayOfTheWeek: Int?, // 1-7, 1 = Monday
    @Json(name = "tagAutomation") val tagAutomation: RemoteUserTagAutomationSettings?,
    @Json(name = UserSettingsProperty.FAVORITE_SPORTS) val favoriteSports: List<Int>?,
    @Json(name = UserSettingsProperty.MOTIVATIONS) val motivations: List<String>?,
    @Json(name = UserSettingsProperty.DISABLED_APP_RATING_SUGGESTIONS) val disabledAppRatingSuggestions: List<String>?,
    @Json(name = UserSettingsProperty.AUTOMATIC_UPDATE_DISABLED_WATCHES) val automaticUpdateDisabledWatches: List<String>?,
    @Json(name = "searchHidden") val privateAccount: Boolean,
    @Json(name = "menstrualCycle") val menstrualCycleSettings: RemoteMenstrualCycleSettings?,
    @Json(name = "hr_rest") val hrRest: Int?,
    @Json(name = "intensityZones") val intensityZones: RemoteIntensityZones?,
    @Json(name = "showLocale") val showLocale: Boolean,
)

@JsonClass(generateAdapter = true)
data class RemoteUserSettingsResponse(
    @Json(name = "measurementUnit") val measurementUnit: String?,
    @Json(name = "hr_max") val hrMaximum: Int?,
    @Json(name = "gender") val gender: String?,
    @Json(name = "height") val height: Int?,
    @Json(name = "weight") val weight: Int?,
    @Json(name = "birthdate") val birthDate: Long?,
    @Json(name = "email") val email: String?,
    @Json(name = "screenBacklight") val screenBacklightSetting: String?,
    @Json(name = "gpsFiltering") val gpsFiltering: Boolean?,
    @Json(name = "altitudeOffset") val altitudeOffset: Float?,
    @Json(name = "default_maptype") val selectedMapType: String?,
    @Json(name = "notifyFriendInvite") val notifyNewFollower: Int?,
    @Json(name = "notifyWorkoutComment") val notifyWorkoutComment: Int?,
    @Json(name = "notifyWorkoutFriendShare") val notifyWorkoutFollowingShare: Int?,
    @Json(name = "automaticallyApproveFollowers") val autoApproveFollowers: Boolean?,
    @Json(name = "emailDigest") val emailDigest: Int?,
    @Json(name = "optinAccepted") val optinAccepted: Long?,
    @Json(name = "optinRejected") val optinRejected: Long?,
    @Json(name = "optinLastShown") val optinLastShown: Long?,
    @Json(name = "optinShowCount") val optinShowCount: Long?,
    @Json(name = "UUID") val analyticsUUID: String?,
    @Json(name = "country") val country: String?,
    @Json(name = "countrySubdivision") val countrySubdivision: String?,
    @Json(name = "language") val language: String?,
    @Json(name = "realName") val realName: String?,
    @Json(name = "description") val description: String?,
    @Json(name = "sharingFlagPreference") val sharingFlagPreference: Int?,
    @Json(name = "hasOutboundPartnerConnections") val hasOutboundPartnerConnections: Boolean?,
    @Json(name = "phoneNumber") val phoneNumber: String?,
    @Json(name = "predefinedReplies") val predefinedReplies: List<String>?,
    @Json(name = "preferredTssCalculationMethods") val preferredTssCalculationMethods: Map<Int, String>?,
    @Json(name = "firstDayOfTheWeek") val firstDayOfTheWeek: Int?, // 1-7, 1 = Monday
    @Json(name = "tagAutomation") val tagAutomation: RemoteUserTagAutomationSettings?,
    @Json(name = UserSettingsProperty.FAVORITE_SPORTS) val favoriteSports: List<Int>?,
    @Json(name = UserSettingsProperty.MOTIVATIONS) val motivations: List<String>?,
    @Json(name = UserSettingsProperty.DISABLED_APP_RATING_SUGGESTIONS) val disabledAppRatingSuggestions: List<String>?,
    @Json(name = UserSettingsProperty.AUTOMATIC_UPDATE_DISABLED_WATCHES) val automaticUpdateDisabledWatches: List<String>?,
    @Json(name = UserSettingsProperty.SAMPLING_BUCKET_VALUE) val samplingBucketValue: Double?, // 0 - 1
    @Json(name = "searchHidden") val privateAccount: Boolean?,
    @Json(name = "menstrualCycle") val menstrualCycleSettings: RemoteMenstrualCycleSettings?,
    @Json(name = "hr_rest") val hrRest: Int?,
    @Json(name = "intensityZones") val intensityZones: RemoteIntensityZones?,
    @Json(name = "showLocale") val showLocale: Boolean?,
)

@JsonClass(generateAdapter = true)
data class RemoteUserNotificationsSettings(
    @Json(name = "deviceid")
    val deviceId: String?,

    @Json(name = "attrs")
    val attrs: RemoteUserNotificationsSettingsAttrs
)

@JsonClass(generateAdapter = true)
data class RemoteUserNotificationsSettingsAttrs(
    @Json(name = "friend_invite")
    val newFollower: Boolean,

    @Json(name = "workout_comment")
    val workoutComment: Boolean,

    @Json(name = "workout_friend_share")
    val workoutShare: Boolean,

    @Json(name = "workout_reaction")
    val workoutReaction: Boolean,

    @Json(name = "facebook_friend_joined")
    val facebookFriendJoin: Boolean,

    @Json(name = "new_activity_synced")
    val newActivitySynced: Boolean,
)

@JsonClass(generateAdapter = true)
data class FcmTokenInfo(
    @Json(name = "platform") val platform: String = "FCM",
    @Json(name = "deviceid") val token: String?,
    @Json(name = "language") val language: String = Locale.getDefault().toString(),
    @Json(name = "app") val app: String
)

@JsonClass(generateAdapter = true)
data class RemoteChangeUserEmailRequest(
    @Json(name = "email") val email: String
)

@JsonClass(generateAdapter = true)
data class RemoteSaveUserPhoneNumberRequest(
    @Json(name = "phoneNumber") val phoneNumber: String
)

@JsonClass(generateAdapter = true)
data class RemoteUserTagAutomationSettings(
    @Json(name = "autoTagCommute") val autoTagCommute: Boolean
)

@JsonClass(generateAdapter = true)
data class RemoteMenstrualCycleSettings(
    @Json(name = "cycleRegularity") val cycleRegularity: String,
    @Json(name = "cycleLength") val cycleLength: Int,
    @Json(name = "periodDuration") val periodDuration: Int
)

@JsonClass(generateAdapter = true)
data class RemoteIntensityZones(
    @Json(name = "heartRateZoneType") val hrZoneType: String?,
    @Json(name = "defaultHeartRateZones") val defaultHrZones: RemoteHrZones? = null,
    @Json(name = "runningHeartRateZones") val runningHrZones: RemoteHrZones? = null,
    @Json(name = "cyclingHeartRateZones") val cyclingHrZones: RemoteHrZones? = null,
    @Json(name = "runningPaceZones") val runningPaceZones: RemoteZones? = null,
    @Json(name = "cyclingPowerZones") val cyclingPowerZones: RemoteZones? = null,
    @Json(name = "runningPowerZones") val runningPowerZones: RemoteZones? = null,
    @Json(name = "runningHeartRateZonesEnabled") val runningHeartRateZonesEnabled: Boolean = false,
    @Json(name = "cyclingHeartRateZonesEnabled") val cyclingHeartRateZonesEnabled: Boolean = false
)

@JsonClass(generateAdapter = true)
data class RemoteHrZones(
    val max: RemoteZones? = null,
    val reserve: RemoteZones? = null,
    val aet: RemoteZones? = null
)

@JsonClass(generateAdapter = true)
data class RemoteZones(
    val zone2: Int,
    val zone3: Int,
    val zone4: Int,
    val zone5: Int
)
