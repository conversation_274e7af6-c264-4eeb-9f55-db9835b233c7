package com.stt.android.remote.usersettings

import com.stt.android.TestOpen
import com.stt.android.exceptions.remote.ClientError
import com.stt.android.exceptions.remote.STTError
import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.remote.otp.OTPGenerationException
import timber.log.Timber
import javax.inject.Inject

@TestOpen
class UserSettingsRemoteApi
@Inject constructor(
    private val userSettingsRestApi: UserSettingsRestApi,
    private val generateOTPUseCase: GenerateOTPUseCase
) {

    suspend fun fetchUserSettings(): RemoteUserSettingsResponse =
        userSettingsRestApi.fetchUserSettings().payloadOrThrow()

    suspend fun saveUserSettings(userSettingsRequest: RemoteUserSettingsRequest) {
        try {
            val totp = generateOTPUseCase.generateTOTP()
            userSettingsRestApi.saveUserSettingsSafe(totp, userSettingsRequest).payloadOrThrow()
        } catch (e: Exception) {
            if (e is OTPGenerationException) {
                Timber.w("Unable to generate OTP")
                userSettingsRestApi.saveUserSettings(userSettingsRequest)
            } else {
                throw e
            }
        }
    }

    suspend fun saveUserPhoneNumber(
        phoneNumberVerification: String,
        phoneNumber: String
    ) {
        try {
            userSettingsRestApi.saveUserPhoneNumber(
                generateOTPUseCase.generateTOTP(),
                phoneNumberVerification,
                RemoteSaveUserPhoneNumberRequest(phoneNumber)
            )
        } catch (e: Exception) {
            if (e is ClientError.Conflict) {
                // We need to do the exception mapping here because BE returns HTTP 409 (conflict) if phone number already exists
                throw STTError.PhoneNumberAlreadyExists()
            } else {
                throw e
            }
        }
    }

    suspend fun setUserPOIFormatToSIM() {
        userSettingsRestApi.setUserPOIFormat("\"SIM\"")
    }
}
