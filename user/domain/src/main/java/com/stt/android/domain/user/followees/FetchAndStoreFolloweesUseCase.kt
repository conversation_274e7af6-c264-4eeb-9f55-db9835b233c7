package com.stt.android.domain.user.followees

import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for fetching user's followees and then storing them to local store
 */
class FetchAndStoreFolloweesUseCase
@Inject constructor(
    private val userDataSource: FolloweeDataSource
) {
    suspend operator fun invoke() {
        Timber.d("Fetching followees metadata")
        userDataSource.storeFolloweesToLocalStore(userDataSource.fetchFolloweesFromRemoteStore())
    }
}
