package com.stt.android.device.remote.suuntoplusguide

import com.stt.android.remote.response.AskoResponse
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.PATCH
import retrofit2.http.Path
import retrofit2.http.Query

interface TrainingPlanRestAPI {
    @GET("trainingplans/guides/items")
    suspend fun fetchAll(): AskoResponse<List<RemoteTrainingPlanInfo>>

    @PATCH("trainingplans/guides/pinned/{id}")
    suspend fun updatePinnedStatus(
        @Path("id") id: String,
        pinned: Boolean,
    ): AskoResponse<RemoteTrainingPlanInfo>

    @GET("trainingplans/guides/plugins/{id}/{courseId}")
    suspend fun downloadZAPPFile(
        @Path("id") id: String,
        @Path("courseId") courseId: String,
        @Query("capabilities") capabilities: String
    ): Response<ResponseBody>

    @DELETE("trainingplans/guides/plugin/{planId}")
    suspend fun deleteMyPlan(
        @Path("planId") planId: String
    ): Response<ResponseBody>
}
