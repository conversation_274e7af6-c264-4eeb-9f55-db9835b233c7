package com.stt.android.device.suuntoplusguide.howto

import com.stt.android.common.viewstate.ViewStateEpoxyController
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent

@Module
@InstallIn(FragmentComponent::class)
abstract class SuuntoPlusGuideHowToModule {

    @Binds
    abstract fun bindController(controller: SuuntoPlusGuideHowToEpoxyController): ViewStateEpoxyController<SuuntoPlusGuideHowToContainer>
}
