package com.stt.android.device.suuntoplusguide.partners

import com.stt.android.domain.connectedservices.ServiceMetadata

data class SuuntoPlusGuidePartnersListContainer(
    val connectedPartners: List<Partner>,
    val otherPartners: List<Partner>,
    val onClick: (ServiceMetadata) -> Unit
)

data class Partner(
    val listItemId: Long,
    val iconUrl: String,
    val title: String,
    val isConnected: Boolean,
    val serviceMetadata: ServiceMetadata,
)
