package com.stt.android.device.di

import com.stt.android.device.domain.watchface.ClearWatchFaceStatusUseCase
import com.stt.android.device.domain.watchface.ClearWatchFacesUseCase
import com.stt.android.watch.ClearWatchFaceStatus
import com.stt.android.watch.ClearWatchFaces
import dagger.Binds
import dagger.Module

@Module
abstract class ClearWatchFaceDataModule {
    @Binds
    abstract fun bindClearWatchFaceStatus(
        clearWatchFaceStatusUseCase: ClearWatchFaceStatusUseCase
    ): ClearWatchFaceStatus

    @Binds
    abstract fun bindClearWatchFaces(
        clearWatchFacesUseCase: ClearWatchFacesUseCase
    ): ClearWatchFaces
}
