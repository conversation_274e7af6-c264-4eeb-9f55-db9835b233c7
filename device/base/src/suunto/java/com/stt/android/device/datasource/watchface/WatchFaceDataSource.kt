package com.stt.android.device.datasource.watchface

import com.stt.android.device.domain.watchface.InstallWatchFace
import com.stt.android.device.domain.watchface.WatchFaceStatusDataSource
import com.suunto.connectivity.watchface.MdsWatchFace
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import javax.inject.Inject

class WatchFaceDataSource @Inject constructor(
    private val watchFaceApi: WatchFaceApi,
    private val statusDataSource: WatchFaceStatusDataSource,
) {
    suspend fun getInstalledWatchFaces(): List<MdsWatchFace> {
        return watchFaceApi.getInstalledWatchFaceList()
    }

    suspend fun setAsCurrentWatchface(watchFaceId: String) {
        watchFaceApi.setAsCurrentWatchFace(watchFaceId)
    }

    suspend fun installWatchFace(
        id: String,
        serial: String,
        watchFaceId: String,
        watchFaceName: String,
        fileLocalPath: String
    ): Boolean {
        val deviceStatus = statusDataSource.getWatchFaceDeviceStatus(serial, id)
            ?: throw kotlin.NullPointerException("Device status not found")
        val installWatchFace = InstallWatchFace(
            watchFaceId = watchFaceId,
            name = watchFaceName,
            installCapability = deviceStatus.installCapability,
            installVersion = deviceStatus.installVersion,
            preImgName = deviceStatus.preImgName,
            fileSize = deviceStatus.fileSize,
            md5 = deviceStatus.fileMd5,
        )
        return watchFaceApi.startWatchFaceInstall(fileLocalPath, installWatchFace)
    }

    suspend fun uninstallWatchFace(watchFaceId: String) {
        watchFaceApi.uninstallWatchFace(watchFaceId)
    }

    fun subscribeCurrentWatchFaceId(): Flow<String> =
        watchFaceApi.subscribeCurrentWatchFaceId().distinctUntilChanged()
}
