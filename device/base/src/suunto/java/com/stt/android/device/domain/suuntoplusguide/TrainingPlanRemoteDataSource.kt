package com.stt.android.device.domain.suuntoplusguide

import com.stt.android.device.remote.suuntoplusguide.TrainingPlanRemoteContainer
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities

interface TrainingPlanRemoteDataSource {
    suspend fun fetchTrainingPlan(
        planId: SuuntoPlusStorePlanId,
        watchCapabilities: SuuntoWatchCapabilities
    ): TrainingPlanRemoteContainer

    suspend fun fetchTrainingPlanDay(
        planId: String,
        weekNumber: Int,
        dayNumber: Int,
    ): ByteArray

    suspend fun addPlanToWatch(planStart: SuuntoPlusStorePlanStart)

    suspend fun removePlanFromLibrary(planId: SuuntoPlusStorePlanId)
}

@JvmInline
value class SuuntoPlusStorePlanId(val id: String)

data class SuuntoPlusStorePlanStart(val id: String, val startDate: String, val endDate: String)
