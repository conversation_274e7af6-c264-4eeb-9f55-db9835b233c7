package com.stt.android.device.suuntoplusfeature.report

import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.RadioButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.device.R
import com.stt.android.R as BaseR

private const val CHARACTER_LIMIT = 1000 // Amplitude allows 1024, but we round to 1000

@Composable
fun ReportSportsAppScreen(
    androidVersion: String,
    suuntoAppVersionName: String,
    suuntoAppVersionCode: String,
    watchModel: String,
    watchFirmware: String,
    onReportClick: (analyticsReason: String, description: String) -> Unit,
    modifier: Modifier = Modifier,
) {
    var selectedReasonIndex by rememberSaveable { mutableStateOf<Int?>(null) }

    Column(
        modifier = modifier.verticalScroll(rememberScrollState())
    ) {
        for ((index, option) in ReportSportsAppReason.entries.withIndex()) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(
                    start = MaterialTheme.spacing.medium,
                    top = MaterialTheme.spacing.medium,
                    bottom = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.small,
                )
            ) {
                Text(
                    text = stringResource(id = option.stringRes),
                    modifier = Modifier.weight(1f)
                )

                RadioButton(
                    selected = index == selectedReasonIndex,
                    onClick = { selectedReasonIndex = index }
                )
            }

            Divider(color = MaterialTheme.colors.dividerColor)
        }

        // Show free text input after selection is made
        AnimatedVisibility(visible = selectedReasonIndex != null) {
            Column {
                var description by rememberSaveable(stateSaver = TextFieldValue.Saver) {
                    mutableStateOf(TextFieldValue())
                }

                TextField(
                    value = description,
                    onValueChange = {
                        description = if (it.text.length <= CHARACTER_LIMIT) {
                            it
                        } else {
                            val text = it.text.take(CHARACTER_LIMIT)
                            TextFieldValue(
                                text = text,
                                selection = TextRange(text.lastIndex + 1) // cursor at end
                            )
                        }
                    },
                    label = { Text(stringResource(R.string.sports_app_report_description_label)) },
                    placeholder = { Text(stringResource(R.string.sports_app_report_description_placeholder)) },
                    colors = TextFieldDefaults.textFieldColors(
                        backgroundColor = Color.Unspecified,
                        unfocusedIndicatorColor = Color.Unspecified
                    ),
                    modifier = Modifier.fillMaxWidth()
                )

                Divider(color = MaterialTheme.colors.dividerColor)

                // Character count
                Text(
                    text = "${description.text.length} / $CHARACTER_LIMIT",
                    modifier = Modifier
                        .align(Alignment.End)
                        .padding(
                            top = MaterialTheme.spacing.xsmall,
                            bottom = MaterialTheme.spacing.medium,
                            end = MaterialTheme.spacing.medium,
                        ),
                    style = MaterialTheme.typography.bodySmall,
                    color = if (description.text.length < CHARACTER_LIMIT) {
                        MaterialTheme.colors.darkGrey
                    } else {
                        MaterialTheme.colors.error
                    },
                )

                Divider(color = MaterialTheme.colors.dividerColor)

                val text = buildString {
                    append(stringResource(id = R.string.sports_app_report_metadata))
                    append("\n")
                    append(
                        stringResource(
                            id = R.string.sports_app_report_metadata_android_version,
                            androidVersion
                        )
                    )
                    append("\n")
                    append(
                        stringResource(
                            id = R.string.sports_app_report_metadata_suunto_app_version,
                            suuntoAppVersionName,
                            suuntoAppVersionCode
                        )
                    )
                    append("\n")
                    append(
                        stringResource(
                            id = R.string.sports_app_report_metadata_watch_model_and_firmware_version,
                            watchModel,
                            watchFirmware
                        )
                    )
                    append("\n")
                }

                Text(
                    text = text,
                    modifier = Modifier
                        .padding(
                            top = MaterialTheme.spacing.xlarge,
                            bottom = MaterialTheme.spacing.xlarge,
                            start = MaterialTheme.spacing.medium,
                            end = MaterialTheme.spacing.medium
                        ),
                    color = MaterialTheme.colors.darkGrey
                )

                PrimaryButton(
                    text = stringResource(id = BaseR.string.report),
                    onClick = {
                        onReportClick(
                            ReportSportsAppReason.entries[selectedReasonIndex!!].analyticsName,
                            description.text
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            start = MaterialTheme.spacing.large,
                            end = MaterialTheme.spacing.large,
                            top = MaterialTheme.spacing.large,
                            bottom = MaterialTheme.spacing.xxlarge,
                        )
                        .align(Alignment.CenterHorizontally)
                )
            }
        }
    }
}

enum class ReportSportsAppReason(val analyticsName: String, @StringRes val stringRes: Int) {
    INAPPROPRIATE("Inappropriate", R.string.sports_app_report_reason_inappropriate),
    SYNC_PROBLEM("SyncProblem", R.string.sports_app_report_reason_sync_problem),
    DOES_NOT_WORK("DoesNotWork", R.string.sports_app_report_reason_does_not_work),
    UI_ISSUE("UserInterfaceIssue", R.string.sports_app_report_reason_ui_issue),
    DUPLICATE_APP("DuplicateApp", R.string.sports_app_report_reason_duplicate_app),
    IMPROVEMENT_SUGGESTION(
        "ImprovementSuggestion",
        R.string.sports_app_report_reason_improvement_suggestion
    ),
    OTHER("Other", R.string.sports_app_report_reason_other)
}

@Preview
@Composable
private fun ReportSportsAppScreenPreview() {
    AppTheme {
        Surface {
            ReportSportsAppScreen(
                androidVersion = "13",
                suuntoAppVersionName = "4.67.2",
                suuntoAppVersionCode = "4067002",
                watchModel = "Suunto Vertical",
                watchFirmware = "2.25.25",
                onReportClick = { _, _ -> },
            )
        }
    }
}
