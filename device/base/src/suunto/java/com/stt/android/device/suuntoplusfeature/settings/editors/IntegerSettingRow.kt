package com.stt.android.device.suuntoplusfeature.settings.editors

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.relocation.BringIntoViewRequester
import androidx.compose.foundation.relocation.bringIntoViewRequester
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalResources
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.util.withAllSelected
import com.stt.android.device.R
import kotlinx.coroutines.launch

@OptIn(ExperimentalFoundationApi::class)
@Composable
internal fun IntegerSettingRow(
    name: String,
    initialValue: Int?,
    nonce: Int, // Reset text field to initialValue when nonce changes
    minimum: Int?,
    maximum: Int?,
    unit: String?,
    mandatoryMessage: String?,
    onSettingChange: (newValue: Int) -> Unit,
    enabled: Boolean,
    modifier: Modifier = Modifier
) {
    // Note that we do not respect changes to the initialValue parameter since state is remembered
    // here. [BasicTextField] does have an implementation to keep TextFieldValue and the plain String
    // value in sync but it seems complicated and unnecessary here. If nonce changes, then reset to
    // initialValue.
    var textFieldValue by rememberSaveable(nonce, stateSaver = TextFieldValue.Saver) {
        mutableStateOf(
            TextFieldValue(
                text = initialValue?.toString().orEmpty()
            )
        )
    }

    val focusManager = LocalFocusManager.current
    val focusRequester = remember { FocusRequester() }
    val bringIntoViewRequester = remember { BringIntoViewRequester() }
    val coroutineScope = rememberCoroutineScope()

    val resources = LocalResources.current
    val helperText by remember {
        derivedStateOf {
            val text = textFieldValue.text
            val value = text.toIntOrNull()

            when {
                mandatoryMessage != null && (text.isEmpty() || value == null) ->
                    resources.getString(R.string.suunto_plus_number_validation_required_value)

                minimum != null && maximum != null ->
                    resources.getString(
                        R.string.suunto_plus_number_validation_not_between_minimum_and_maximum_value,
                        minimum.toString(),
                        maximum.toString()
                    )

                minimum != null ->
                    resources.getString(
                        R.string.suunto_plus_number_validation_smaller_than_minimum_value,
                        minimum.toString()
                    )

                maximum != null ->
                    resources.getString(
                        R.string.suunto_plus_number_validation_larger_than_maximum_value,
                        maximum.toString()
                    )

                else ->
                    null
            }
        }
    }

    val isError by remember {
        derivedStateOf {
            val value = textFieldValue.text.toIntOrNull()
            val valueValid = value != null &&
                (minimum == null || value >= minimum) &&
                (maximum == null || value <= maximum)

            if (mandatoryMessage != null) {
                !valueValid || textFieldValue.text.isEmpty() || value == null
            } else {
                textFieldValue.text.isNotBlank() && !valueValid
            }
        }
    }

    BoxedSettingRow(
        name = name,
        onClick = {
            focusRequester.requestFocus()
            textFieldValue = textFieldValue.withAllSelected()
        },
        helperText = helperText?.let { { Text(it) } },
        isError = isError,
        enabled = enabled,
        modifier = modifier
            .wrapContentHeight()
            .bringIntoViewRequester(bringIntoViewRequester),
    ) {
        TextField(
            value = textFieldValue,
            onValueChange = { fieldValue ->
                val intValue = fieldValue.text.toIntOrNull()
                if (fieldValue.text.isBlank() || fieldValue.text == "-" || intValue != null) {
                    textFieldValue = fieldValue
                    intValue?.let { onSettingChange(it) }
                }
            },
            enabled = enabled,
            isError = isError,
            textStyle = MaterialTheme.typography.bodyLarge.copy(textAlign = TextAlign.End),
            singleLine = true,
            colors = TextFieldDefaults.textFieldColors(
                textColor = if (isError) MaterialTheme.colors.error else MaterialTheme.colors.primary,
                backgroundColor = Color.Unspecified,
                unfocusedIndicatorColor = Color.Unspecified
            ),
            visualTransformation = UnitVisualTransformation(unit),
            keyboardActions = KeyboardActions { focusManager.clearFocus() },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier
                .focusRequester(focusRequester)
                .onFocusChanged {
                    if (it.hasFocus) {
                        textFieldValue = textFieldValue.withAllSelected()
                        coroutineScope.launch {
                            // Scroll to make sure helper text is visible on the screen
                            bringIntoViewRequester.bringIntoView()
                        }
                    }
                }
                .fillMaxSize()
        )
    }
}

@Preview
@Composable
private fun IntegerSettingRowPreview() {
    AppTheme {
        Surface {
            IntegerSettingRow(
                name = "Integer setting",
                unit = "unit",
                initialValue = 3,
                nonce = 0,
                minimum = null,
                maximum = null,
                mandatoryMessage = null,
                onSettingChange = {},
                enabled = true
            )
        }
    }
}

@Preview
@Composable
private fun IntegerSettingRowValueTooSmallPreview() {
    AppTheme {
        Surface {
            IntegerSettingRow(
                name = "Going to require something bigger",
                unit = null,
                initialValue = 1,
                nonce = 0,
                minimum = 5,
                maximum = null,
                mandatoryMessage = null,
                onSettingChange = {},
                enabled = true
            )
        }
    }
}

@Preview
@Composable
private fun IntegerSettingRowValueTooLargeValuePreview() {
    AppTheme {
        Surface {
            IntegerSettingRow(
                name = "This is too much",
                unit = null,
                initialValue = 3,
                nonce = 0,
                minimum = null,
                maximum = 2,
                mandatoryMessage = null,
                onSettingChange = {},
                enabled = true
            )
        }
    }
}

@Preview
@Composable
private fun IntegerSettingRowValueNotInRangeValuePreview() {
    AppTheme {
        Surface {
            IntegerSettingRow(
                name = "Value not in range",
                unit = null,
                initialValue = 3,
                nonce = 0,
                minimum = 4,
                maximum = 5,
                mandatoryMessage = null,
                onSettingChange = {},
                enabled = true
            )
        }
    }
}

@Preview
@Composable
private fun IntegerSettingRowMissingMandatoryValuePreview() {
    AppTheme {
        Surface {
            IntegerSettingRow(
                name = "Required integer setting",
                unit = null,
                initialValue = null,
                nonce = 0,
                minimum = null,
                maximum = null,
                mandatoryMessage = "Something has to be entered here",
                onSettingChange = {},
                enabled = true
            )
        }
    }
}
