package com.stt.android.device.domain

import com.stt.android.device.datasource.WatchSerialDataSource
import com.stt.android.device.datasource.suuntoplusguide.SuuntoWatchCapabilityStore
import com.stt.android.watch.GetWatchCapabilities
import com.stt.android.watch.WatchCapabilitiesResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class GetWatchCapabilitiesUseCase @Inject constructor(
    private val watchSerialDataSource: WatchSerialDataSource,
    private val capabilityStore: SuuntoWatchCapabilityStore,
) : GetWatchCapabilities {
    override fun getCurrentCapabilitiesAsFlow(): Flow<WatchCapabilitiesResult> =
        watchSerialDataSource.getCurrentWatchSerialAsFlow().map { serial ->
            WatchCapabilitiesResult(
                serial = serial,
                capabilities = serial?.let {
                    capabilityStore.loadCapabilitiesWithAugmentedScreenSize(it)
                }
            )
        }

    override suspend fun getCurrentCapabilities(): WatchCapabilitiesResult =
        watchSerialDataSource.getCurrentWatchSerial()?.let { serial ->
            WatchCapabilitiesResult(
                serial = serial,
                capabilities = capabilityStore.loadCapabilitiesWithAugmentedScreenSize(serial)
            )
        } ?: WatchCapabilitiesResult(null, null)

    override suspend fun getCapabilitiesForAllWatches(): List<WatchCapabilitiesResult> =
        capabilityStore.loadAllWithAugmentedScreenSizeCapability().map {
            WatchCapabilitiesResult(
                serial = it.serial,
                capabilities = it.capabilities
            )
        }
}
