package com.stt.android.device.remote.watchface

import com.squareup.moshi.JsonClass
import com.stt.android.device.domain.watchface.WatchFaceEntity
import com.stt.android.device.domain.watchface.WatchFaceListContainer
import com.stt.android.device.domain.watchface.WatchFace

interface OnlineWatchFaceRemoteAPI {

    suspend fun fetchAll(
        variantName: String,
        capabilities: String
    ): RemoteWatchFaceListContainer

    suspend fun getWatchFaceDetail(
        runFeatureCatalogueId: String,
        watchFaceCapabilities: String,
    ): RemoteWatchFaceEntity

    suspend fun getWatchFaceDetails(
        runFeatureCatalogueIds: List<String>,
        watchFaceCapabilities: String,
    ): List<RemoteWatchFaceEntity>

    suspend fun fetchWatchFaceFile(
        runFeatureCatalogueId: String,
        watchFaceCapabilities: String,
    ): ByteArray

    suspend fun addToLibrary(
        id: String,
        watchFaceCapabilities: String,
        addToFavorite: Boolean,
        addToWatch: Boolean
    ): Boolean

    suspend fun fetchUserLibrary(
        watchFaceCapabilities: String
    ): RemoteUserLibraryWatchFaces
}

@JsonClass(generateAdapter = true)
data class RemoteWatchFaceListContainer(
    val id: String,
    val title: String,
    val description: String?,
    val bannerImageUrl: String?,
    val runFeaturesCatalogues: List<RemoteWatchFaceItem>,
)

@JsonClass(generateAdapter = true)
data class RemoteWatchFaceItem(
    val id: String,
    val category: String,
    val type: String,
    val name: String,
    val description: String,
    val shortDescription: String?,
    val currentVersion: String,
    val watchCapability: String,
    val watchfaceId: String,
    val richText: String?,
    val labels: List<String>?,
    val iconUrl: String?,
    val tileBannerUrl: String?,
    val useDefaultImages: Boolean,
    val supported: Boolean,
    val addToFavorite: Boolean,
    val addToWatch: Boolean,
    val updated: Boolean,
    val updateTime: Long,
)

@JsonClass(generateAdapter = true)
data class RemoteWatchFaceEntity(
    val runFeatureCatalogueId: String,
    val type: String,
    val name: String,
    val description: String,
    val shortDescription: String?,
    val labels: List<String>?,
    val watchfaceId: String,
    val richText: String?,
    val iconUrl: String?,
    val tileBannerUrl: String?,
    val iconName: String,
    val supported: Boolean,
    val updated: Boolean,
    val addToWatch: Boolean,
    val addToFavorite: Boolean,
    val fileSize: Long,
    val md5: String,
    val latestInstalledVersion: String?,
    val targetInstallVersion: String,
    val latestInstalledCapability: String?,
    val targetInstallCapability: String,
    val updateTime: Long
)

@JsonClass(generateAdapter = true)
data class RemoteUserLibraryWatchFaces(
    val installSupportedRunFeatures: List<RemoteWatchFaceItem>?,
    val installNotSupportedRunFeatures: List<RemoteWatchFaceItem>?,
    val notInstalledRunFeatures: List<RemoteWatchFaceItem>?,
)

fun RemoteWatchFaceListContainer.toDomain() = WatchFaceListContainer(
    id = id,
    title = title,
    description = description,
    bannerImageUrl = bannerImageUrl,
    itemList = runFeaturesCatalogues.map { it.toDomain() }
)

fun RemoteWatchFaceItem.toDomain() = WatchFace(
    id = id,
    category = category,
    type = type,
    name = name,
    description = description,
    shortDescription = shortDescription,
    currentVersion = currentVersion,
    watchCapability = watchCapability,
    watchfaceId = watchfaceId,
    richText = richText,
    labels = labels,
    iconUrl = iconUrl,
    tileBannerUrl = tileBannerUrl,
    useDefaultImages = useDefaultImages,
    supported = supported,
    addToFavorite = addToFavorite,
    addToWatch = addToWatch,
    updated = updated,
    updateTime = updateTime
)

fun RemoteWatchFaceEntity.toDomain() = WatchFaceEntity(
    runFeatureCatalogueId = runFeatureCatalogueId,
    name = name,
    description = description,
    shortDescription = shortDescription,
    labels = labels,
    watchfaceId = watchfaceId,
    richText = richText,
    iconUrl = iconUrl,
    tileBannerUrl = tileBannerUrl,
    iconName = iconName,
    supported = supported,
    updated = updated,
    addToWatch = addToWatch,
    addToFavorite = addToFavorite,
    type = type,
    fileSize = fileSize,
    md5 = md5,
    latestInstalledVersion = latestInstalledVersion,
    targetInstallVersion = targetInstallVersion,
    latestInstalledCapability = latestInstalledCapability,
    targetInstallCapability = targetInstallCapability,
    updateTime = updateTime,
)

fun RemoteUserLibraryWatchFaces.toDomain() =
    installSupportedRunFeatures?.map { it.toDomain() }
        .orEmpty() + installNotSupportedRunFeatures?.map { it.toDomain() }
        .orEmpty() + notInstalledRunFeatures?.map { it.toDomain() }.orEmpty()
