package com.stt.android.device.remote.suuntoplusguide

import com.stt.android.device.domain.suuntoplusguide.TrainingPlan
import com.stt.android.device.domain.suuntoplusguide.TrainingPlanId
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import javax.inject.Inject

class TrainingPlanRemoteDataSource @Inject constructor(
    private val remoteTrainingPlanRemoteAPI: TrainingPlanRemoteAPI
) {
    suspend fun fetchAllTrainingPlan(): List<TrainingPlan> =
        remoteTrainingPlanRemoteAPI.fetchAll().map(RemoteTrainingPlanInfo::toDomain)

    suspend fun updatePinnedStatus(planId: TrainingPlanId, pinned: Boolean) =
        remoteTrainingPlanRemoteAPI.updatePinnedStatus(planId.id, pinned)

    suspend fun fetchZAPPFile(planId: String, guideId: String, capabilities: SuuntoWatchCapabilities) =
        remoteTrainingPlanRemoteAPI.fetchZAPPFile(planId, guideId, capabilities.stringValue)

    suspend fun deleteMyPlan(planId: String) = remoteTrainingPlanRemoteAPI.deleteMyPlan(planId)
}
