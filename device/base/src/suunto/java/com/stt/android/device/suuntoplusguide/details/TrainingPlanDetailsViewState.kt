package com.stt.android.device.suuntoplusguide.details

import androidx.compose.runtime.Immutable
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.remote.suuntoplusguide.TrainingPlanWeek
import com.stt.android.domain.workout.ActivityType
import kotlinx.collections.immutable.ImmutableList
import java.time.LocalDate

@Immutable
data class TrainingPlanDetailsViewState(
    val title: String,
    val description: String?,
    val richDescription: String?,
    val backgroundUrl: String?,
    val url: String?,
    val owner: String,
    val watchStatus: SuuntoPlusPluginStatus,
    val syncOngoing: Boolean,
    val modificationTime: String?,
    val startDate: LocalDate?,
    val endDate: LocalDate?,
    val activityTypes: ImmutableList<ActivityType>,
    val pinned: Boolean,
    val weeksNumber: Int?,
    val weeks: ImmutableList<TrainingPlanWeek>,
)
