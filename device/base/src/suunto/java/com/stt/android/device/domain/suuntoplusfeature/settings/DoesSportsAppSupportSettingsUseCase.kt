package com.stt.android.device.domain.suuntoplusfeature.settings

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.stt.android.device.datasource.suuntoplusfeature.SuuntoPlusFeaturesLocalDataSource
import com.stt.android.device.remote.suuntoplusfeature.manifest.SuuntoPlusManifest
import javax.inject.Inject

class DoesSportsAppSupportSettingsUseCase
@Inject constructor(
    private val featuresLocalDataSource: SuuntoPlusFeaturesLocalDataSource,
    moshi: <PERSON><PERSON>
) {
    private val adapter: JsonAdapter<SuuntoPlusManifest> =
        moshi.adapter(SuuntoPlusManifest::class.java)

    suspend fun doesSportsAppSupportSettings(sportsAppId: String): Boolean =
        featuresLocalDataSource.findById(sportsAppId)?.manifestJson?.let { manifestJson ->
            val manifest = adapter.fromJson(manifestJson)
            manifest?.settings?.isNotEmpty() == true || manifest?.variables?.isNotEmpty() == true
        } ?: false
}
