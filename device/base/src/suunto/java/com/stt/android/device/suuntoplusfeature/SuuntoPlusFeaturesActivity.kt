package com.stt.android.device.suuntoplusfeature

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.setupActionBarWithNavController
import com.stt.android.common.navigation.findNavController
import com.stt.android.device.R
import com.stt.android.device.databinding.ActivitySuuntoPlusFeaturesBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SuuntoPlusFeaturesActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySuuntoPlusFeaturesBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySuuntoPlusFeaturesBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.toolbar)
        val appBarConfiguration = AppBarConfiguration.Builder()
            .setFallbackOnNavigateUpListener {
                finish()
                true
            }
            .build()

        val navController = findNavController(R.id.nav_host_fragment_container)
        navController.setGraph(R.navigation.suunto_plus_features_nav, intent.extras)
        setupActionBarWithNavController(
            navController,
            appBarConfiguration
        )
    }

    override fun onSupportNavigateUp(): Boolean {
        if (!findNavController(R.id.nav_host_fragment_container).navigateUp()) {
            finish()
        }

        return true
    }

    companion object {

        // They are matched with the SuuntoPlusFeaturesListFragmentArgs.
        private const val ARG_EXCLUDED_WATCHFACE = "excludedWatchface"
        private const val ARG_TITLE = "title"
        private const val ARG_ONLY_WATCHFACE = "onlyWatchface"

        fun newExcludedWatchfaceStartIntent(context: Context): Intent {
            return Intent(context, SuuntoPlusFeaturesActivity::class.java).apply {
                putExtra(ARG_EXCLUDED_WATCHFACE, true)
                putExtra(ARG_TITLE, context.getString(R.string.suunto_plus_features_features_section_title))
            }
        }

        fun newOnlyWatchfaceStartIntent(context: Context): Intent {
            return Intent(context, SuuntoPlusFeaturesActivity::class.java).apply {
                putExtra(ARG_ONLY_WATCHFACE, true)
                putExtra(ARG_TITLE, context.getString(R.string.suunto_plus_store_watch_faces_in_my_watch_faces_label))
            }
        }
    }
}
