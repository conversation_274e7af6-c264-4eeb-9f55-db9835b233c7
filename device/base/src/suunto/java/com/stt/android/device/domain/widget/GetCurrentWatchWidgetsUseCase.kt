package com.stt.android.device.domain.widget

import com.stt.android.device.domain.widget.entities.WatchWidget
import com.stt.android.device.domain.widget.entities.toWatchWidget
import com.stt.android.watch.SuuntoWatchModel
import javax.inject.Inject

class GetCurrentWatchWidgetsUseCase @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel
) {
    suspend fun getCurrentWatchWidgets(): List<WatchWidget> {
        val widgetVersion = suuntoWatchModel.getWidgetVersion(true)
        return suuntoWatchModel.getWidgets().content.configs.map { it.toWatchWidget(widgetVersion) }
    }
}
