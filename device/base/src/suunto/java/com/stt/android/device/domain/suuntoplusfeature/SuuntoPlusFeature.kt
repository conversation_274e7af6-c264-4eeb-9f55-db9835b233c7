package com.stt.android.device.domain.suuntoplusfeature

data class SuuntoPlusFeature(
    val id: String,
    val pluginId: String?,
    val modifiedMillis: Long,
    val name: String,
    val description: String,
    val shortDescription: String?,
    val richDescription: String?,
    val owner: String,
    val url: String?,
    val iconUrl: String,
    val bannerUrl: String?,
    val ownerLogoUrl: String?,
    val enabled: Boolean,
    val expirationTimeMillis: Long?,
    val manifestJson: String?,
    val localizedRichText: String?,
    val localizedRichTextAutomatically: Boolean?,
    val type: String?,
    val rating: SuuntoPlusFeatureRating?,
) {
    fun hasExpired(currentTimeMillis: Long): Boolean =
        expirationTimeMillis != null && expirationTimeMillis < currentTimeMillis

    fun isWatchface(): Boolean = type == SuuntoPlusFeatureType.WATCHFACE.typeName

    fun getFeatureType(): SuuntoPlusFeatureType =
        SuuntoPlusFeatureType.entries.find { it.typeName == type } ?: SuuntoPlusFeatureType.FEATURE
}

data class SuuntoPlusFeatureRating(
    val avgRating: Double,
    val reviews: Int,
)

data class SuuntoPlusStoreUserRating(
    val rating: Int,
    val comment: String?,
)

enum class SuuntoPlusFeatureType(val typeName: String) {
    FEATURE("feature"),
    DEVICE("device"),
    WATCHFACE("watchface"),
}

fun SuuntoPlusFeatureType.isWatchface(): Boolean = this == SuuntoPlusFeatureType.WATCHFACE
