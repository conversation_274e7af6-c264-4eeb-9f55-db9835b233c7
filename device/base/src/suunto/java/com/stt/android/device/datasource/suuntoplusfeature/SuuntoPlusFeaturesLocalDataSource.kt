package com.stt.android.device.datasource.suuntoplusfeature

import com.stt.android.data.source.local.suuntoplusfeature.LocalSuuntoPlusFeature
import com.stt.android.data.source.local.suuntoplusfeature.LocalSuuntoPlusFeatureWithDeviceStatus
import com.stt.android.data.source.local.suuntoplusfeature.SuuntoPlusFeatureDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusPluginDeviceStatusDao
import com.stt.android.device.datasource.toDomain
import com.stt.android.device.domain.suuntoplusfeature.SuuntoPlusFeature
import com.stt.android.device.domain.suuntoplusfeature.SuuntoPlusFeatureWithStatus
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.exceptions.device.SuuntoPlusFeatureNotFound
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class SuuntoPlusFeaturesLocalDataSource
@Inject constructor(
    private val suuntoPlusFeatureDao: SuuntoPlusFeatureDao,
    private val deviceStatusDao: SuuntoPlusPluginDeviceStatusDao,
) {
    suspend fun findById(featureId: String): SuuntoPlusFeature? =
        suuntoPlusFeatureDao.findById(featureId)?.toDomain()

    fun findByIdAsFlow(featureId: String): Flow<SuuntoPlusFeature?> =
        suuntoPlusFeatureDao.findByIdAsFlow(featureId)
            .map { it?.toDomain() }
            .distinctUntilChanged()
            .flowOn(Dispatchers.IO)

    fun listSuuntoPlusFeatures(): Flow<List<SuuntoPlusFeature>> =
        suuntoPlusFeatureDao.fetchAllAsFlow()
            .map { features ->
                features
                    .map { feature -> feature.toDomain() }
            }
            .distinctUntilChanged()
            .flowOn(Dispatchers.IO)

    fun findSuuntoPlusFeatureWithWatchState(watchSerial: String, featureId: String): Flow<Pair<SuuntoPlusFeature?, SuuntoPlusPluginStatus?>> =
        suuntoPlusFeatureDao.findFeatureWithStateAsFlow(featureId)
            .map { result ->
                val status = result?.statuses
                    ?.firstOrNull { status -> status.watchSerial == watchSerial }
                    ?.status
                    ?.toDomain()
                result?.feature?.toDomain() to status
            }
            .distinctUntilChanged()
            .flowOn(Dispatchers.IO)

    suspend fun updateEnabledState(featureId: String, enabled: Boolean) {
        val changedRows = suuntoPlusFeatureDao.updateEnabledState(featureId, enabled)
        if (changedRows != 1) {
            throw SuuntoPlusFeatureNotFound("No feature with ID $featureId")
        }
    }

    suspend fun updateEnabledState(featureIds: Collection<String>, enabled: Boolean) {
        suuntoPlusFeatureDao.updateEnabledState(featureIds, enabled)
    }

    suspend fun updateManifest(featureId: String, manifestJson: String) {
        val changedRows = suuntoPlusFeatureDao.updateManifestJson(featureId, manifestJson)
        if (changedRows != 1) {
            throw SuuntoPlusFeatureNotFound("No feature with ID $featureId")
        }
    }

    suspend fun upsert(feature: SuuntoPlusFeature) {
        suuntoPlusFeatureDao.upsert(feature.toLocal())
    }

    fun listSuuntoPlusFeaturesWithStatus(watchSerial: String): Flow<List<SuuntoPlusFeatureWithStatus>> =
        suuntoPlusFeatureDao.fetchAllWithStateAsFlow()
            .map { list ->
                list.map { featureWithStatus ->
                    SuuntoPlusFeatureWithStatus(
                        feature = featureWithStatus.feature.toDomain(),
                        status = featureWithStatus.statusForSerial(watchSerial)
                    )
                }
            }
            .distinctUntilChanged()
            .flowOn(Dispatchers.IO)

    suspend fun listSuuntoPlusFeaturesInGivenStates(
        watchSerial: String,
        states: Set<SuuntoPlusPluginStatus>
    ): List<SuuntoPlusFeature> = withContext(Dispatchers.IO) {
        suuntoPlusFeatureDao.fetchAllWithStateAsFlow()
            .first()
            .filter { it.statusForSerial(watchSerial) in states }
            .map { it.feature.toDomain() }
    }

    suspend fun deleteByIds(featureIds: List<String>) {
        if (featureIds.isNotEmpty()) {
            Timber.d("Deleting local SuuntoPlus Features $featureIds")
            suuntoPlusFeatureDao.deleteByIds(featureIds)
            deviceStatusDao.deleteByPluginIds(featureIds)
        }
    }

    suspend fun havePluginIds(): Boolean = suuntoPlusFeatureDao.havePluginIds()

    suspend fun deleteAll() = suuntoPlusFeatureDao.deleteAll()
}

fun SuuntoPlusFeature.toLocal() = LocalSuuntoPlusFeature(
    id = id,
    pluginId = pluginId,
    modifiedMillis = modifiedMillis,
    name = name,
    owner = owner,
    expirationTimeMillis = expirationTimeMillis,
    url = url,
    iconUrl = iconUrl,
    bannerUrl = bannerUrl,
    ownerLogoUrl = ownerLogoUrl,
    description = description,
    shortDescription = shortDescription,
    richDescription = richDescription,
    enabled = enabled,
    manifestJson = manifestJson,
    localizedRichText = localizedRichText,
    localizedRichTextAutomatically = localizedRichTextAutomatically,
    type = type
)

private fun LocalSuuntoPlusFeature.toDomain() = SuuntoPlusFeature(
    id = id,
    pluginId = pluginId,
    modifiedMillis = modifiedMillis,
    name = name,
    owner = owner,
    expirationTimeMillis = expirationTimeMillis,
    url = url,
    iconUrl = iconUrl,
    bannerUrl = bannerUrl,
    ownerLogoUrl = ownerLogoUrl,
    description = description,
    shortDescription = shortDescription,
    richDescription = richDescription,
    enabled = enabled,
    manifestJson = manifestJson,
    localizedRichText = localizedRichText,
    localizedRichTextAutomatically = localizedRichTextAutomatically,
    type = type,
    rating = null,
)

private fun LocalSuuntoPlusFeatureWithDeviceStatus.statusForSerial(watchSerial: String): SuuntoPlusPluginStatus =
    statuses.firstOrNull { status -> status.watchSerial == watchSerial }?.status?.toDomain()
        ?: SuuntoPlusPluginStatus.UNKNOWN
