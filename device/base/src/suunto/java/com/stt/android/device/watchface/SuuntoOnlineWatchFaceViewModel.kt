package com.stt.android.device.watchface

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.device.domain.watchface.GetWatchFaceCapabilitiesUseCase
import com.stt.android.device.domain.watchface.WatchFace
import com.stt.android.device.domain.watchface.WatchFaceListContainer
import com.stt.android.device.remote.watchface.WatchFaceRemoteDataSource
import com.stt.android.domain.device.DeviceConnectionStateUseCase
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.takeIfNotEmpty
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.reactive.awaitFirstOrNull
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class SuuntoOnlineWatchFaceViewModel @Inject constructor(
    private val remoteDataSource: WatchFaceRemoteDataSource,
    private val deviceConnectionStateUseCase: DeviceConnectionStateUseCase,
    private val getWatchFaceCapabilitiesUseCase: GetWatchFaceCapabilitiesUseCase,
) : ViewModel() {

    sealed class ViewData {
        data object Loading : ViewData()

        data class Loaded(
            val data: OnlineWatchFaceViewData,
        ) : ViewData()

        data class Failed(val errorMsg: String) : ViewData()
    }

    data class OnlineWatchFaceViewData(
        val watchFaceListContainer: WatchFaceListContainer,
        val onWatchFaceClick: (WatchFace) -> Unit,
    )

    private val _viewState: MutableStateFlow<ViewData> = MutableStateFlow(
        ViewData.Loading
    )
    val viewState: StateFlow<ViewData> = _viewState.asStateFlow()

    val watchFaceClickEvent = SingleLiveEvent<WatchFace>()

    init {
        fetchOnlineWatchFaceContainer()
    }

    private fun fetchOnlineWatchFaceContainer() =
        viewModelScope.launch(Dispatchers.IO) {
            runSuspendCatching {
                val variantName = runSuspendCatching {
                    deviceConnectionStateUseCase.connectedWatchState()
                        .awaitFirstOrNull()?.deviceInfo?.variantName
                }.getOrNull().orEmpty()
                val watchCapabilities =
                    getWatchFaceCapabilitiesUseCase().takeIfNotEmpty()?.joinToString(",") ?: ""
                if (watchCapabilities.isBlank()) throw IllegalArgumentException("Watch capabilities are empty")
                remoteDataSource.fetchAll(variantName, watchCapabilities)
            }.onSuccess { watchFaceListContainer ->
                Timber.d("fetch watch face list successful.")
                _viewState.value = ViewData.Loaded(
                    OnlineWatchFaceViewData(
                        watchFaceListContainer = watchFaceListContainer,
                        onWatchFaceClick = ::onWatchFaceClick
                    )
                )
            }.onFailure {
                Timber.w(it, "fetch watch face list failed.")
                _viewState.value = ViewData.Failed(it.message ?: "Unknown error")
            }
        }

    private fun onWatchFaceClick(watchFace: WatchFace) {
        watchFaceClickEvent.value = watchFace
    }

    fun refresh() {
        if (_viewState.value != ViewData.Loading) {
            _viewState.value = ViewData.Loading
            fetchOnlineWatchFaceContainer()
        }
    }
}
