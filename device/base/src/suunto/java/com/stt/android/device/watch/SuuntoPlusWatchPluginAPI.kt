package com.stt.android.device.watch

interface SuuntoPlusWatchPluginAPI {
    suspend fun getPlugins(serial: String): List<WatchPluginInfo>
    suspend fun installPlugin(serial: String, pluginId: String, filePath: String)
    suspend fun hasEnoughSpace(serial: String, zappDataSize: Int): Boolean
    suspend fun uninstallPlugin(serial: String, pluginId: String)
    suspend fun getDefaultWatchfaceId(serial: String): String?
}

data class WatchPluginInfo(
    val id: String,
    val type: Int,
    val state: Int,
    val interestValue: Int,
    val modificationTimeSeconds: Long,
) {
    val isGuide: Boolean
        get() = type == PLUGIN_TYPE_SUUNTO_PLUS_GUIDE

    // SuuntoPlus™ device and watchface plug-ins are handled exactly the same as features by the sync logic
    val isFeatureOrDevice: Boolean
        get() = type == PLUGIN_TYPE_SUUNTO_PLUS_FEATURE ||
            type == PLUGIN_TYPE_SUUNTO_PLUS_DEVICE ||
            type == PLUGIN_TYPE_SUUNTO_PLUS_WATCHFACE

    val isWatchface: Boolean
        get() = type == PLUGIN_TYPE_SUUNTO_PLUS_WATCHFACE

    val isStatic: Boolean
        get() = state == PLUGIN_STATE_STATIC
}
