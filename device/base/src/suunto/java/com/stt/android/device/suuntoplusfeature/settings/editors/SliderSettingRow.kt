package com.stt.android.device.suuntoplusfeature.settings.editors

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Slider
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.spacing
import com.stt.android.device.suuntoplusfeature.settings.SettingFormatter

@Composable
internal fun SliderSettingRow(
    name: String,
    displayedValue: String,
    fraction: Float,
    unit: String?,
    onSettingChange: (newFraction: Float) -> Unit,
    enabled: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
    ) {
        Row(
            modifier = Modifier
                .heightIn(min = 56.dp)
                .padding(horizontal = MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = name,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.weight(1f)
            )

            Text(
                text = if (unit != null) {
                    "$displayedValue $unit"
                } else {
                    displayedValue
                },
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colors.primary
            )
        }

        Slider(
            value = fraction,
            onValueChange = onSettingChange,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
            enabled = enabled,
        )
    }
}

@Preview
@Composable
private fun SliderSettingRowPreview() {
    AppTheme {
        Surface {
            var fraction by remember { mutableFloatStateOf(0.0f) }
            val range = 40f..90f

            SliderSettingRow(
                name = "Estimated VO₂ max",
                fraction = fraction,
                displayedValue = SettingFormatter.format(
                    fraction * (range.endInclusive - range.start) + range.start
                ),
                unit = "ml/(kg⋅min)",
                onSettingChange = { fraction = it },
                enabled = true
            )
        }
    }
}
