package com.stt.android.device.domain.watchface

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.device.domain.GetWatchCapabilitiesUseCase
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.collections.count
import kotlin.getOrElse
import kotlin.to

class NumberOfEnabledWatchFacesUseCase @Inject constructor(
    private val localDataSource: WatchFaceLocalDataSource,
    private val currentWatchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
) {
    private class NoWatchPairedException(message: String) : IllegalStateException(message)
    private class WatchNotSupportedException(message: String) : IllegalStateException(message)

    suspend fun canOneMoreWatchFaceBeEnabled(): Boolean {
        val (enabled, max) = numberOfEnabledAndMaxWatchFaces()
        return enabled != null && enabled < max
    }

    suspend fun numberOfEnabledAndMaxWatchFaces(): Pair<Int?, Int> =
        withContext(Dispatchers.IO) {
            runSuspendCatching {
                val (serial, capabilities) = currentWatchCapabilitiesUseCase.getCurrentCapabilities()
                if (serial == null) {
                    throw NoWatchPairedException("Cannot check addToWatch watch face limit: Missing watch serial")
                }

                if (capabilities?.isRunWatchFaceSupported != true) {
                    throw WatchNotSupportedException("Watch $serial does not support watch faces (capabilities=$capabilities)")
                }

                val maxSupportedWatchFaceCount = capabilities.maxNumberOfEnabledWatchface
                val enabledSupportedWatchFaceCount =
                    localDataSource.listWatchFacesWithStatus(serial)
                        .map { watchFacesWithState ->
                            // Do not consider WatchFace in NOT_SUPPORTED state when
                            // counting the number of addToWatch watch faces
                            watchFacesWithState
                                .count { (watchFace, status) ->
                                    watchFace.addToWatch && status != WatchFaceStatus.NOT_SUPPORTED
                                }
                        }
                        .first()

                enabledSupportedWatchFaceCount to maxSupportedWatchFaceCount
            }.getOrElse {
                if (it !is NoWatchPairedException && it !is WatchNotSupportedException) {
                    Timber.w(it, "Failed to check if can addToWatch one more watch face")
                }

                val capabilities =
                    currentWatchCapabilitiesUseCase.getCurrentCapabilities().capabilities
                        ?: SuuntoWatchCapabilities.EMPTY
                val maxSupportedWatchFaceCount = capabilities.maxNumberOfEnabledWatchface
                null to maxSupportedWatchFaceCount
            }
        }
}
