package com.stt.android.device.onboarding

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import com.stt.android.device.onboarding.databinding.OnboardingPageBinding

class OnboardingPageFragment : Fragment() {

    interface Listener {
        fun onOnboardingPageCreated(page: OnboardingPage, fragment: OnboardingPageFragment)

        fun onOnboardingPageButtonClicked(page: OnboardingPage)

        fun onOnboardingPrimaryButtonClicked(page: OnboardingPage)

        fun onOnboardingSecondaryButtonClicked(page: OnboardingPage)

        fun onOnboardingThirdButtonClicked(page: OnboardingPage)
    }

    var page: OnboardingPage? = null
    var showAnim: Boolean = false
    var listener: Listener? = null

    private var _binding: OnboardingPageBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        showAnim = arguments?.getBoolean(ARG_ONBOARDING_SHOW_ANIM, false) ?: false
        page = arguments?.getSerializable(ARG_ONBOARDING_PAGE) as? OnboardingPage
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = DataBindingUtil.inflate(inflater, R.layout.onboarding_page, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (showAnim) {
            binding.onboardingPageAnimation.visibility = View.VISIBLE
            binding.onboardingPagePhoto.visibility = View.GONE
        } else {
            binding.onboardingPageAnimation.visibility = View.GONE
            binding.onboardingPagePhoto.visibility = View.VISIBLE
        }

        val page = this.page ?: return

        binding.onboardingPageTitle.setText(page.titleResId)
        binding.onboardingPageDetail.setText(page.detailTextResId)

        page.babdgeAnimationResId?.let {
            with(binding.onboardingPageBadgeAnimation) {
                setAnimation(it)
                visibility = View.VISIBLE
            }
            binding.onboardingPageBadge.visibility = View.VISIBLE
        } ?: page.badgeImageResId?.let {
            with(binding.onboardingPageBadgeImage) {
                setImageResource(it)
                visibility = View.VISIBLE
            }
            binding.onboardingPageBadge.visibility = View.VISIBLE
        } ?: run {
            binding.onboardingPageBadge.visibility = View.GONE
        }

        page.pageButtonResId?.let {
            with(binding.onboardingPageButton) {
                setText(it)
                setOnClickListener { _ -> listener?.onOnboardingPageButtonClicked(page) }
            }
        }

        page.primaryButtonResId?.let {
            with(binding.onboardingPrimaryButton) {
                setText(it)
                visibility = View.VISIBLE
                setOnClickListener { _ -> listener?.onOnboardingPrimaryButtonClicked(page) }
            }
        }

        page.secondaryButtonResId?.let {
            with(binding.onboardingSecondaryButton) {
                setText(it)
                visibility = View.VISIBLE
                setOnClickListener { _ -> listener?.onOnboardingSecondaryButtonClicked(page) }
            }
        }

        page.thirdButtonResId?.let {
            with(binding.onboardingThirdButton) {
                setText(it)
                visibility = View.VISIBLE
                setOnClickListener { _ -> listener?.onOnboardingThirdButtonClicked(page) }
            }
        }

        listener?.onOnboardingPageCreated(page, this)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.unbind()
        _binding = null
    }

    fun showPageButton(show: Boolean) {
        binding.onboardingPageButton.visibility = if (show) View.VISIBLE else View.GONE
    }

    fun playAnimation() {
        with(binding.onboardingPageBadgeAnimation) {
            if (progress == 0.0f) {
                playAnimation()
            } else {
                resumeAnimation()
            }
        }
    }

    fun pauseAnimation() {
        binding.onboardingPageBadgeAnimation.pauseAnimation()
    }

    companion object {
        const val ARG_ONBOARDING_PAGE = "ONBOARDING_PAGE"
        const val ARG_ONBOARDING_SHOW_ANIM = "ARG_ONBOARDING_SHOW_ANIM"

        fun newInstance(page: OnboardingPage, showAnim: Boolean = false): OnboardingPageFragment {
            return OnboardingPageFragment().apply {
                arguments = Bundle().apply {
                    putSerializable(ARG_ONBOARDING_PAGE, page)
                    putSerializable(ARG_ONBOARDING_SHOW_ANIM, showAnim)
                }
            }
        }
    }
}
