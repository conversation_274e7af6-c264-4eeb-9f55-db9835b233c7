package com.stt.android.device.onboarding.dive

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.core.net.toUri
import com.stt.android.R
import com.stt.android.device.onboarding.BaseOnboardingActivity
import com.stt.android.utils.PermissionUtils
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.AndroidEntryPoint
import pub.devrel.easypermissions.AfterPermissionGranted
import pub.devrel.easypermissions.EasyPermissions
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class DiveOnboardingActivity :
    BaseOnboardingActivity<DiveOnboardingView, DiveOnboardingPresenter>(),
    DiveOnboardingView,
    EasyPermissions.PermissionCallbacks {

    @Inject
    override lateinit var presenter: DiveOnboardingPresenter

    override val view = this

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding.onboardingSwitch.setOnClickListener {
            updateAutoLocationSetting()
        }
        presenter.loadData(
            EasyPermissions.hasPermissions(
                this,
                *PermissionUtils.LOCATION_PERMISSIONS
            )
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this)
    }

    @AfterPermissionGranted(LOCATION_PERMISSION_REQUEST)
    private fun updateAutoLocationSetting() {
        if (!EasyPermissions.hasPermissions(this, *PermissionUtils.LOCATION_PERMISSIONS)) {
            PermissionUtils.requestPermissionsIfNeeded(
                this,
                PermissionUtils.LOCATION_PERMISSIONS,
                getString(R.string.location_permission_rationale_for_location),
                LOCATION_PERMISSION_REQUEST
            )
        } else {
            presenter.autoLocationSwitchClicked(binding.onboardingSwitch.isChecked)
        }
    }

    override fun onPermissionsGranted(requestCode: Int, perms: MutableList<String>) {
        // do nothing
    }

    override fun onPermissionsDenied(requestCode: Int, perms: MutableList<String>) {
        binding.onboardingSwitch.isChecked = false
    }

    override fun getOnboardingPageChangeListener(bullets: Array<ImageView>) =
        DiveOnboardingPageChangeListener(bullets)

    override fun showHowToUse() {
        @Suppress("UnsafeImplicitIntentLaunch")
        startActivity(
            Intent(Intent.ACTION_VIEW).setData(
                getString(
                    R.string.how_to_use_link_dive,
                    when (presenter.suuntoDeviceType) {
                        SuuntoDeviceType.SuuntoD5 -> "d5"
                        SuuntoDeviceType.EonCore -> "eon-core"
                        SuuntoDeviceType.EonSteel -> "eon-steel"
                        SuuntoDeviceType.EonSteelBlack -> "eon-steel-black"
                        else -> {
                            Timber.w("Unable to build how-to-use link for ${presenter.suuntoDeviceType}")
                            ""
                        }
                    }
                ).toUri()
            )
        )

        finish()
    }

    override fun updateAutoLocationSwitch(value: Boolean) {
        binding.onboardingSwitch.isChecked = value
    }

    override fun onViewPodsClicked() {
        @Suppress("UnsafeImplicitIntentLaunch")
        startActivity(Intent(Intent.ACTION_VIEW).setData(getString(R.string.pods_link).toUri()))
    }

    override fun onViewStrapsClicked() {
        @Suppress("UnsafeImplicitIntentLaunch")
        startActivity(Intent(Intent.ACTION_VIEW).setData(getString(R.string.straps_link).toUri()))
    }

    override fun onHowToUpgradeClicked() {
        @Suppress("UnsafeImplicitIntentLaunch")
        startActivity(Intent(Intent.ACTION_VIEW).setData(getString(R.string.how_to_upgrade).toUri()))
    }

    companion object {
        private const val LOCATION_PERMISSION_REQUEST = 100

        @JvmStatic
        fun newStartIntent(context: Context, deviceType: SuuntoDeviceType): Intent {
            return Intent(context, DiveOnboardingActivity::class.java)
                .putExtra(KEY_EXTRA_SUUNTO_DEVICE_TYPE, deviceType)
        }
    }

    protected inner class DiveOnboardingPageChangeListener(bullets: Array<ImageView>) :
        OnboardingPageChangeListener(bullets) {
        override fun onPageSelected(position: Int) {
            super.onPageSelected(position)
            val switchVisibility = if (position == 1) View.VISIBLE else View.GONE
            binding.onboardingSwitch.visibility = switchVisibility
            binding.onboardingSwitchDivider.visibility = switchVisibility
        }
    }
}
