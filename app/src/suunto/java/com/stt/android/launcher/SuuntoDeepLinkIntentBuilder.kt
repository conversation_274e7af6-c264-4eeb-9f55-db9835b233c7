package com.stt.android.launcher

import android.content.Context
import android.content.Intent
import android.net.Uri
import com.stt.android.controllers.CurrentUserController
import com.stt.android.utils.ProxyActivityHelper
import com.stt.android.watch.IsRunDeviceUseCase
import javax.inject.Inject

/**
 * Suunto specific [DeepLinkIntentBuilder] implementation that supplement [DefaultDeepLinkIntentBuilder] with additional
 * deeplinks handling for Suunto specific features
 */
internal class SuuntoDeepLinkIntentBuilder @Inject constructor(
    private val defaultIntentBuilder: DefaultDeepLinkIntentBuilder,
    private val proxyActivityHelper: ProxyActivityHelper,
    private val userController: CurrentUserController,
    private val isRunDeviceUseCase: IsRunDeviceUseCase,
) : DeepLinkIntentBuilder {

    val isRun = isRunDeviceUseCase()

    override fun getDeepLinkIntent(
        context: Context,
        uri: Uri,
        currentUserController: CurrentUserController,
        fragmentOrPathParts: Array<String>,
        type: String
    ): Intent? {
        return when {
            type == ProxyActivityHelper.DEVICE -> proxyActivityHelper.getDeviceIntent(context, fragmentOrPathParts)
            type == BaseProxyActivity.PARTNERS -> proxyActivityHelper.getConnectedPartnersIntent(
                context = context,
                partnerType = fragmentOrPathParts.getOrNull(2),
            )
            type == ProxyActivityHelper.SUUNTO_PLUS_STORE -> proxyActivityHelper.getSuuntoPlusStoreIntent(context, fragmentOrPathParts)
            type == ProxyActivityHelper.DAYVIEW -> proxyActivityHelper.getDayViewIntent(context, fragmentOrPathParts)
            type == ProxyActivityHelper.MARKETING_INBOX -> proxyActivityHelper.getMarketingInboxIntent(context, fragmentOrPathParts)
            type == ProxyActivityHelper.SCANNER -> if (userController.isLoggedIn) {
                proxyActivityHelper.getDeviceIntent(context, fragmentOrPathParts, uri.getQueryParameter("c"))
            } else null
            type == ProxyActivityHelper.USER -> if (userController.isLoggedIn) {
                proxyActivityHelper.getUserProfileIntent(context, fragmentOrPathParts.lastOrNull())
            } else null
            else -> defaultIntentBuilder.getDeepLinkIntent(context, uri, currentUserController, fragmentOrPathParts, type)
        }
    }

    override fun getAuthIntent(context: Context, type: String, uri: Uri): Intent? =
        if (type == "apple") {
            defaultIntentBuilder.getAuthIntent(context, type, uri)
        } else {
            proxyActivityHelper.getConnectedPartnersIntent(context, type, uri.query ?: "")
        }

    override fun getTestIntent(context: Context, type: String): Intent? = when {
        type == ProxyActivityHelper.GETLOGS -> proxyActivityHelper.getSendLogsToFileIntent(context)
        else -> defaultIntentBuilder.getTestIntent(context, type)
    }

    override fun getOtaIntent(context: Context, uri: Uri): Intent =
        proxyActivityHelper.getOtaUpdateIntent(context, uri.toString())

    override fun getUserIntent(context: Context, uri: Uri): Intent =
        defaultIntentBuilder.getUserIntent(context, uri)

    override fun getFollowersIntent(context: Context): Intent =
        defaultIntentBuilder.getFollowersIntent(context)

    override fun getWorkoutIntent(context: Context, uri: Uri): Intent =
        defaultIntentBuilder.getWorkoutIntent(context, uri)
}
