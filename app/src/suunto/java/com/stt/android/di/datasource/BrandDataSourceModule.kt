package com.stt.android.di.datasource

import com.stt.android.data.Watch
import com.stt.android.data.activitydata.dailyvalues.ActivityDataProvider
import com.stt.android.data.activitydata.goals.ActivityDataGoalController
import com.stt.android.data.device.DeviceDataSource
import com.stt.android.data.device.DeviceInfoApi
import com.stt.android.data.device.DeviceWatchDataSource
import com.stt.android.data.sleep.SleepTrackingSettingsProvider
import com.stt.android.data.source.local.startup.ConfigFileStorage
import com.stt.android.data.source.local.startup.ConfigFileStorageNoOp
import com.stt.android.data.sportmodes.SportModesApi
import com.stt.android.data.trenddata.TrendDataSettingsProvider
import com.stt.android.device.datasource.watchface.WatchFaceApi
import com.stt.android.device.datasource.watchface.WatchFaceDeviceApi
import com.stt.android.device.di.ClearWatchFaceDataModule
import com.stt.android.device.di.WatchFaceRemoteSyncJobModule
import com.stt.android.device.domain.AreOfflineMapsSupportedUseCase
import com.stt.android.device.domain.WUIDumpCollectorImpl
import com.stt.android.device.domain.WatchBatteryCollectorImpl
import com.stt.android.device.domain.WatchLogFilesCollectorImpl
import com.stt.android.device.domain.suuntoplusguide.ClearSuuntoPlusDataModule
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideLogCollectorImpl
import com.stt.android.device.watchface.WatchFaceModule
import com.stt.android.di.activitydata.ActivityDataModule
import com.stt.android.di.firstpairing.FirstPairingDataSourceModule
import com.stt.android.di.recovery.RecoveryDataModule
import com.stt.android.di.sleep.SleepModule
import com.stt.android.di.systemevents.SystemEventsModule
import com.stt.android.di.trenddata.TrendDataModule
import com.stt.android.domain.device.SuuntoPlusGuideLogCollector
import com.stt.android.domain.device.WUIDumpCollector
import com.stt.android.domain.device.WatchBatteryCollector
import com.stt.android.domain.device.WatchLogFilesCollector
import com.stt.android.home.diary.diarycalendar.planner.WorkoutPlannerMoshiAdaptersModule
import com.stt.android.home.explore.offlinemaps.OfflineMapsTabVisibility
import com.stt.android.sportmode.datasource.RunSportModeWatchApi
import com.stt.android.sportmode.datasource.RunSportModesApi
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreMoshiAdaptersModule
import com.stt.android.watch.SuuntoWatchModel
import com.stt.android.watch.device.DeviceInfoWatchApi
import com.stt.android.watch.sportmodes.SportModeWatchApi
import com.stt.android.watch.watchcontrol.WatchControlApi
import com.stt.android.watch.watchcontrol.WatchControlApiImpl
import dagger.Binds
import dagger.Module

@Module(
    includes = [
        SystemEventsModule::class,
        TrendDataModule::class,
        RecoveryDataModule::class,
        SleepModule::class,
        ActivityDataModule::class,
        FirstPairingDataSourceModule::class,
        ClearSuuntoPlusDataModule::class,
        SuuntoPlusStoreMoshiAdaptersModule::class,
        WorkoutPlannerMoshiAdaptersModule::class,
        WatchFaceModule::class,
        WatchFaceRemoteSyncJobModule::class,
        ClearWatchFaceDataModule::class,
    ]
)
abstract class BrandDataSourceModule {
    @Binds
    abstract fun bindWatchActivityDataGoalController(suuntoWatchModel: SuuntoWatchModel): ActivityDataGoalController

    @Binds
    abstract fun bindWatchActivityDataProvider(suuntoWatchModel: SuuntoWatchModel): ActivityDataProvider

    @Binds
    abstract fun bindSleepTrackingSettingsProvider(suuntoWatchModel: SuuntoWatchModel): SleepTrackingSettingsProvider

    @Binds
    abstract fun bindTrendDataSettingsProvider(suuntoWatchModel: SuuntoWatchModel): TrendDataSettingsProvider

    @Binds
    abstract fun bindDeviceInfoWatchApi(deviceResource: DeviceInfoWatchApi): DeviceInfoApi

    @Binds
    abstract fun bindSportModesWatchApi(sportModeWatchApi: SportModeWatchApi): SportModesApi

    @Binds
    abstract fun bindRunSportModesWatchApi(sportModeWatchApi: RunSportModeWatchApi): RunSportModesApi

    @Binds
    abstract fun bindConfigFileStorage(
        configFileStorageNoOp: ConfigFileStorageNoOp
    ): ConfigFileStorage

    @Binds
    @Watch
    abstract fun bindDeviceWatchDataSource(deviceWatchDataSource: DeviceWatchDataSource): DeviceDataSource

    @Binds
    abstract fun bindSuuntoPlusGuideLogCollector(logCollector: SuuntoPlusGuideLogCollectorImpl): SuuntoPlusGuideLogCollector

    @Binds
    abstract fun bindWUIDumpCollector(wuiDumpCollectorImpl: WUIDumpCollectorImpl): WUIDumpCollector

    @Binds
    abstract fun bindOfflineMapsTabVisibility(useCase: AreOfflineMapsSupportedUseCase): OfflineMapsTabVisibility

    @Binds
    abstract fun bindwatchLogFilesCollector(watchLogFilesCollectorImpl: WatchLogFilesCollectorImpl): WatchLogFilesCollector

    @Binds
    abstract fun bindWatchControlApi(watchControlApiImpl: WatchControlApiImpl): WatchControlApi

    @Binds
    abstract fun bindWatchBatteryCollector(watchBatteryCollectorImpl: WatchBatteryCollectorImpl): WatchBatteryCollector

    @Binds
    abstract fun bindWatchFaceWatchApi(watchFaceDeviceApi: WatchFaceDeviceApi): WatchFaceApi
}
