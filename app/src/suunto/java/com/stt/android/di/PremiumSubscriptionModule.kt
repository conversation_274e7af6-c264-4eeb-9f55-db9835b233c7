package com.stt.android.di

import com.stt.android.premium.PremiumMapFeaturesAccessHandler
import com.stt.android.premium.PremiumMapFeaturesAccessHandlerNoOp
import com.stt.android.premium.PremiumPromotionNavigator
import com.stt.android.premium.PremiumPromotionNavigatorNoOp
import com.stt.android.premium.PremiumPurchaseFlowLauncher
import com.stt.android.premium.PremiumPurchaseFlowLauncherNoOp
import com.stt.android.premium.PremiumRequiredToAccessHandler
import com.stt.android.premium.PremiumRequiredToAccessHandlerNoOp
import dagger.Binds
import dagger.Module

@Module
abstract class PremiumSubscriptionModule {
    @Binds
    abstract fun bindPremiumRequiredToAccessHandler(
        premiumRequiredToAccessHandlerNoOp: PremiumRequiredToAccessHandlerNoOp
    ): PremiumRequiredToAccessHandler

    @Binds
    abstract fun bindPremiumMapFeaturesAccessHandler(
        premiumMapFeaturesAccessHandlerNoOp: PremiumMapFeaturesAccessHandlerNoOp
    ): PremiumMapFeaturesAccessHandler

    @Binds
    abstract fun bindPremiumPromotionNavigator(
        premiumPromotionNavigator: PremiumPromotionNavigatorNoOp
    ): PremiumPromotionNavigator

    @Binds
    abstract fun bindPremiumPurchaseFlowLauncher(
        premiumPurchaseFlowLauncher: PremiumPurchaseFlowLauncherNoOp
    ): PremiumPurchaseFlowLauncher
}
