package com.stt.android.utils

import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.time.DayOfWeek
import java.time.temporal.TemporalField
import java.time.temporal.WeekFields
import java.util.Calendar
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton

abstract class CalendarProvider {
    private val _calendarTypeChangedEvents = MutableSharedFlow<Unit>(
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    val calendarTypeChangedEvents: Flow<Unit> = _calendarTypeChangedEvents.asSharedFlow()

    abstract fun getCalendar(): Calendar

    open fun getFirstDayOfWeek(): DayOfWeek = getCalendar().firstDayOfWeek.toDayOfWeek()
    fun getWeekFields(): WeekFields =
        WeekFields.of(getFirstDayOfWeek(), getCalendar().minimalDaysInFirstWeek)

    fun getDayOfWeekField(): TemporalField = getWeekFields().dayOfWeek()

    protected fun notifyCalendarTypeUpdatedListeners() {
        _calendarTypeChangedEvents.tryEmit(Unit)
    }
}

@Singleton
class FixedFirstDayOfTheWeekCalendarProvider(
    locale: Locale,
    firstDayOfTheWeek: DayOfWeek
) : CalendarProvider() {
    var fixedLocale: Locale = locale
        private set
    var fixedFirstDayOfTheWeek: DayOfWeek = firstDayOfTheWeek
        private set

    @Inject
    constructor(locale: Locale) : this(
        locale,
        Calendar.getInstance(locale).firstDayOfWeek.toDayOfWeek()
    )

    override fun getCalendar(): Calendar = Calendar.getInstance(fixedLocale).apply {
        firstDayOfWeek = fixedFirstDayOfTheWeek.toCalendarInt()
    }

    override fun getFirstDayOfWeek(): DayOfWeek = fixedFirstDayOfTheWeek

    fun updateFixedFields(
        locale: Locale = fixedLocale,
        firstDayOfTheWeek: DayOfWeek = fixedFirstDayOfTheWeek
    ) {
        if (fixedLocale != locale || fixedFirstDayOfTheWeek != firstDayOfTheWeek) {
            fixedLocale = locale
            fixedFirstDayOfTheWeek = firstDayOfTheWeek
            notifyCalendarTypeUpdatedListeners()
        }
    }
}

private fun Int.toDayOfWeek(): DayOfWeek = when (this) {
    Calendar.MONDAY -> DayOfWeek.MONDAY
    Calendar.TUESDAY -> DayOfWeek.TUESDAY
    Calendar.WEDNESDAY -> DayOfWeek.WEDNESDAY
    Calendar.THURSDAY -> DayOfWeek.THURSDAY
    Calendar.FRIDAY -> DayOfWeek.FRIDAY
    Calendar.SATURDAY -> DayOfWeek.SATURDAY
    Calendar.SUNDAY -> DayOfWeek.SUNDAY
    else -> throw IllegalArgumentException("Day of week needs to be between 1-7")
}

fun DayOfWeek.toCalendarInt(): Int = when (this) {
    DayOfWeek.MONDAY -> Calendar.MONDAY
    DayOfWeek.TUESDAY -> Calendar.TUESDAY
    DayOfWeek.WEDNESDAY -> Calendar.WEDNESDAY
    DayOfWeek.THURSDAY -> Calendar.THURSDAY
    DayOfWeek.FRIDAY -> Calendar.FRIDAY
    DayOfWeek.SATURDAY -> Calendar.SATURDAY
    DayOfWeek.SUNDAY -> Calendar.SUNDAY
}
