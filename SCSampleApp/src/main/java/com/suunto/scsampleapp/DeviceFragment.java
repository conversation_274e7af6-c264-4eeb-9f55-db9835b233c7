package com.suunto.scsampleapp;

import android.app.Activity;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.text.format.DateUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.IdRes;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.suunto.connectivity.Spartan;
import com.suunto.connectivity.notifications.AncsMessage;
import com.suunto.connectivity.repository.ServiceStartInformation;
import com.suunto.connectivity.suuntoconnectivity.ancs.AncsConstants;
import com.suunto.connectivity.watch.SpartanSyncResult;
import com.suunto.connectivity.watch.WatchState;
import java.util.Calendar;
import java.util.Collections;
import java.util.Locale;
import java.util.UUID;
import rx.Emitter;
import rx.Observable;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.functions.Action1;
import rx.functions.Cancellable;
import rx.functions.Func1;
import rx.schedulers.Schedulers;
import rx.subscriptions.CompositeSubscription;

/**
 *
 */
public class DeviceFragment extends Fragment {

    private static final String TAG = DeviceFragment.class.getSimpleName();

    private SpartanProvider spartanProvider;
    private DeviceFragmentListener deviceFragmentListener;
    private NotificationsLauncher notificationsLauncher;
    private CompositeSubscription subscriptions;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Activity activity = getActivity();
        if (activity instanceof SpartanProvider) {
            spartanProvider = (SpartanProvider) activity;
        } else {
            throw new IllegalStateException(
                "Containing Activity does not implement SpartanProvider");
        }

        if (activity instanceof DeviceFragmentListener) {
            deviceFragmentListener = (DeviceFragmentListener) activity;
        } else {
            throw new IllegalStateException(
                "Containing Activity does not implement LogbookLauncher");
        }

        if (activity instanceof NotificationsLauncher) {
            notificationsLauncher = (NotificationsLauncher) activity;
        } else {
            throw new IllegalStateException(
                "Containing Activity does not implement LogbookLauncher");
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
        @Nullable Bundle savedInstanceState) {

        final Spartan spartan = spartanProvider.getCurrentSpartan();
        if (spartan == null) {
            // No spartan, go back
            getActivity().onBackPressed();
            return null;
        }

        subscriptions = new CompositeSubscription();

        View view = inflater.inflate(R.layout.fragment_device, container, false);

        TextView deviceType = view.findViewById(R.id.device_type);
        TextView deviceSerial = view.findViewById(R.id.device_serial);
        final TextView deviceSwVersion = view.findViewById(R.id.device_sw_version);
        final TextView deviceState = view.findViewById(R.id.device_state);
        final TextView latestSyncResult = view.findViewById(R.id.latest_sync);

        deviceType.setText(spartan.getSuuntoBtDevice().getDeviceType().name());
        deviceSerial.setText(spartan.getSuuntoBtDevice().getSerial());

        // As soon as we subscribe we'll receive a onNext that will set the initial device state
        Subscription deviceStateSubscription = spartan.getStateChangeObservable()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(new Action1<WatchState>() {
                @Override
                public void call(WatchState watchStateChange) {
                    deviceState.setText(watchStateToStr(watchStateChange));

                    String swVersion = watchStateChange.getDeviceInfo() != null ?
                            watchStateChange.getDeviceInfo().getSwVersion() : "N/A";
                    deviceSwVersion.setText(swVersion);
                }
            }, new Action1<Throwable>() {
                @Override
                public void call(Throwable throwable) {
                    if (getContext() != null) {
                        Toast.makeText(getContext().getApplicationContext(), throwable.getMessage(), Toast.LENGTH_LONG).show();
                    }
                }
            });
        subscriptions.add(deviceStateSubscription);

        // GNSS Version button
        final TextView gnssVersionTextView = view.findViewById(R.id.gnss_version);
        Subscription gnssSubscription =
            getViewClicks(R.id.btn_gnss, view).flatMap(new Func1<View, Observable<String>>() {
                @Override
                public Observable<String> call(View view) {
                    return spartan.getGNSSVersion().toObservable();
                }
            }).observeOn(AndroidSchedulers.mainThread()).subscribe(new Action1<String>() {
                @Override
                public void call(String gnssVersion) {
                    gnssVersionTextView.setText(gnssVersion);
                }
            }, new Action1<Throwable>() {
                @Override
                public void call(Throwable throwable) {
                    gnssVersionTextView.setText(throwable.getMessage());
                }
            });
        subscriptions.add(gnssSubscription);

        // Notify button
        final EditText notification = view.findViewById(R.id.notification);
        Subscription notifySubscription = getViewClicks(R.id.btn_send, view)
            .subscribe(new Action1<View>() {
                @Override
                public void call(View view) {
                    Editable editable = notification.getText();
                    String notificationText = editable != null ? editable.toString() : null;
                    String[] titleAndText = notificationText.split("\\s");
                    String title = titleAndText.length > 1 ? titleAndText[0] : "Title";
                    String pkg = "text.message";

                    // Don't send empty notification
                    if (TextUtils.isEmpty(notificationText) && getContext() != null) {
                        Toast.makeText(getContext().getApplicationContext(), "No notification text", Toast.LENGTH_LONG).show();
                        return;
                    }

                    // Todo: Hardcoding category count. Should it be something else?
                    // More information in https://developer.apple
                    // .com/library/content/documentation/CoreBluetooth/Reference
                    // /AppleNotificationCenterServiceSpecification/Specification/Specification.html
                    AncsMessage ancsMessage = AncsMessage.create(
                        UUID.randomUUID().hashCode(),
                        title, notificationText,
                        AncsConstants.CategoryID.OTHER, pkg,
                        AncsConstants.DEFAULT_CATEGORY_COUNT, false, Collections.emptyList());

                    // TODO Post the notification
                }
            }, new ThrowableLoggingAction(TAG, getContext()));
        subscriptions.add(notifySubscription);


        Subscription latestSyncSubscription = spartan.getLatestSyncResult()
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(new Action1<SpartanSyncResult>() {
                @Override
                public void call(SpartanSyncResult spartanSyncResult) {
                    latestSyncResult.setText(DateUtils.getRelativeTimeSpanString(
                        spartanSyncResult.getSyncEndTimestamp()));
                }
            }, new Action1<Throwable>() {
                @Override
                public void call(Throwable throwable) {
                    if (getContext() != null) {
                        Toast.makeText(getContext().getApplicationContext(), throwable.getMessage(), Toast.LENGTH_LONG).show();
                    }
                }
            });
        subscriptions.add(latestSyncSubscription);

        // Logbook button
        view.findViewById(R.id.btn_logbook).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                deviceFragmentListener.launchLogbook();
            }
        });

        // Settgins button
        view.findViewById(R.id.btn_settings).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                deviceFragmentListener.launchSettings();
            }
        });

        // NotificationsSettings button
        view.findViewById(R.id.btn_notifications).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                notificationsLauncher.launchNotifications();
            }
        });

        // Connection disconnect button
        view.findViewById(R.id.btn_disconnect).setOnClickListener(view1 -> spartan.disconnect().subscribe());

        // Connection reset button
        view.findViewById(R.id.btn_reset_connection).setOnClickListener(view1 -> spartan.resetConnection(10)
            .subscribe(resetConnectionResponse -> {
                if (getContext() != null) {
                    if (resetConnectionResponse.getSuccess()) {
                        Toast.makeText(getContext().getApplicationContext(), "Connection reset in "
                                + "" + resetConnectionResponse.getReconnectingInSeconds() +
                                " seconds", Toast
                                .LENGTH_LONG)
                            .show();
                    } else {
                        Toast.makeText(getContext().getApplicationContext(), "Connection reset failed", Toast
                                .LENGTH_LONG)
                            .show();
                    }
                }
            }));

        // Get service stability.
        view.findViewById(R.id.btn_get_service_stability)
            .setOnClickListener(view1 -> spartan.getServiceStability()
                .subscribe(serviceStabilityResponse -> {
                    String gpsStatus =
                        serviceStabilityResponse.getLatestAssistedGpsTrackingFailed() ? "Gps failed"
                            : "Gps ok";

                    if (serviceStabilityResponse.getServiceStartInformation() == null && getContext() != null) {
                        Toast.makeText(getContext().getApplicationContext(), "Service start information not found. "+gpsStatus, Toast
                            .LENGTH_LONG)
                            .show();
                    } else if (getContext() != null) {
                        ServiceStartInformation info =
                            serviceStabilityResponse.getServiceStartInformation();
                        Toast.makeText(getContext().getApplicationContext(),
                            "Service started " + info.getRestartCounter() + " times. "+gpsStatus, Toast
                                .LENGTH_LONG)
                            .show();
                    }
                }));

        getViewClicks(R.id.btn_sync, view).flatMap(
            new Func1<View, Observable<SpartanSyncResult>>() {
                @Override
                public Observable<SpartanSyncResult> call(View view) {
                    return spartan.synchronize(false).toObservable();
                }
            })
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(new Action1<SpartanSyncResult>() {
                @Override
                public void call(SpartanSyncResult syncResult) {
                    latestSyncResult.setText(
                        DateUtils.getRelativeTimeSpanString(syncResult.getSyncEndTimestamp()));
                }
            }, new Action1<Throwable>() {
                @Override
                public void call(Throwable throwable) {
                    if (getContext() != null) {
                        Toast.makeText(getContext().getApplicationContext(), throwable.getMessage(), Toast.LENGTH_LONG).show();
                    }
                }
            });

        return view;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        subscriptions.unsubscribe();
    }

    private Observable<View> getViewClicks(@IdRes int viewId, View root) {
        final View view = root.findViewById(viewId);

        return Observable.create(new Action1<Emitter<View>>() {
            @Override
            public void call(final Emitter<View> viewEmitter) {
                view.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        viewEmitter.onNext(view);
                    }
                });

                viewEmitter.setCancellation(new Cancellable() {
                    @Override
                    public void cancel() throws Exception {
                        view.setOnClickListener(null);
                    }
                });
            }
        }, Emitter.BackpressureMode.DROP);
    }

    private String watchStateToStr(WatchState watchState) {
        if (!watchState.isPaired()) {
            return "Not paired (" + watchState.getConnectionState().name() + ")";
        }
        final String connectStarted;
        if (watchState.getConnectionState() == WatchState.ConnectionState.CONNECTING ||
            watchState.getConnectionState() == WatchState.ConnectionState.RECONNECTING) {
            if (watchState.getLastConnectStarted() != null) {
                Calendar cal = Calendar.getInstance(Locale.ENGLISH);
                cal.setTimeInMillis(watchState.getLastConnectStarted());
                connectStarted = "Since "+ DateFormat.format("hh:mm:ss", cal).toString();
            } else {
                connectStarted = "";
            }
        } else {
            connectStarted = "";
        }
        return watchState.getConnectionState().name()
            + " ("
            + watchState.getSyncState().getState()
            + ") " + connectStarted;
    }

    interface SpartanProvider {
        Spartan getCurrentSpartan();
    }

    interface DeviceFragmentListener {
        void launchLogbook();

        void launchSettings();
    }

    interface NotificationsLauncher {
        void launchNotifications();
    }
}
