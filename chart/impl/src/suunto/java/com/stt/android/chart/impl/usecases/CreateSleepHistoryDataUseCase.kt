package com.stt.android.chart.impl.usecases

import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.data.SleepDataLoader
import com.stt.android.chart.impl.screen.SleepHistoryViewData
import com.stt.android.common.coroutines.CoroutinesDispatchers
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.flowOn
import java.time.LocalDate
import javax.inject.Inject

internal class CreateSleepHistoryDataUseCase @Inject constructor(
    private val sleepDataLoader: SleepDataLoader,
    private val dispatchers: CoroutinesDispatchers,
) {
    operator fun invoke(
        scope: CoroutineScope,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate
    ): SleepHistoryViewData {
        if (chartGranularity == ChartGranularity.DAILY) return SleepHistoryViewData.None

        return SleepHistoryViewData.Loaded(
            pagingData = sleepDataLoader.createSleepHistoryPager(scope, from, to)
                .flowOn(dispatchers.io),
        )
    }
}
