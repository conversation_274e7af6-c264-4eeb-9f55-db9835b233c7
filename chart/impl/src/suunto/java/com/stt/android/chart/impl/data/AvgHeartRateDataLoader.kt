package com.stt.android.chart.impl.data

import android.content.Context
import androidx.annotation.ColorInt
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.chart.impl.model.minutesSinceEpoch
import com.stt.android.controllers.UserSettingsController
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.averageOfDouble
import com.stt.android.utils.toEpochMilli
import com.suunto.algorithms.data.HeartRate
import com.suunto.algorithms.data.HeartRate.Companion.hz
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.time.Duration.Companion.minutes
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

internal class AvgHeartRateDataLoader @Inject constructor(
    @ApplicationContext appContext: Context,
    private val trendDataRepository: TrendDataRepository,
    private val userSettingsController: UserSettingsController,
) : BaseHeartRateDataLoader(appContext) {
    private interface HeartRateSeriesStrategy {
        fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            isLineChart: Boolean,
            heartRateStat: Number? = null,
        ): List<ChartData.Series>
    }

    private val seriesStrategies: Map<ChartGranularity, HeartRateSeriesStrategy> = mapOf(
        ChartGranularity.DAILY to DailyHeartRateSeriesStrategy(),
        ChartGranularity.WEEKLY to DayByDayHeartRateSeriesStrategy(),
        ChartGranularity.MONTHLY to DayByDayHeartRateSeriesStrategy(),
        ChartGranularity.YEARLY to MonthlyHeartRateSeriesStrategy(),
        ChartGranularity.SEVEN_DAYS to DayByDayHeartRateSeriesStrategy(),
        ChartGranularity.THIRTY_DAYS to DayByDayHeartRateSeriesStrategy(),
        ChartGranularity.SIX_WEEKS to DayByDayHeartRateSeriesStrategy(),
        ChartGranularity.SIX_MONTHS to WeeklyHeartRateSeriesStrategy(),
        ChartGranularity.EIGHT_YEARS to YearlyHeartRateSeriesStrategy(),
    )

    @ColorInt
    fun getChartColor(selectedHeartRateStatId: String? = null): Int = if (selectedHeartRateStatId != null) {
        appContext.getColor(BaseR.color.dive_set_active_mode_button_border_color)
    } else {
        getChartColor()
    }

    fun createChartSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        chartColor: Int,
        isLineChart: Boolean = false,
        heartRateStat: Number? = null,
    ): List<ChartData.Series> = seriesStrategies[chartGranularity]
        ?.createSeries(from, to, trendDataList, chartColor, isLineChart, heartRateStat)
        ?: throw IllegalArgumentException("Unsupported granularity: $chartGranularity")

    private inner class DailyHeartRateSeriesStrategy : HeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            isLineChart: Boolean,
            heartRateStat: Number?,
        ): List<ChartData.Series> {
            val allOriginalHeartRates = mutableListOf<HeartRate>()
            val allDataPoints = mutableListOf<ChartData.Entry>()
            val allCandlestickPoints = mutableListOf<ChartData.CandlestickEntry>()

            val timeWindowMap = groupTrendDataByMinutes(
                from = from,
                to = to,
                trendDataList = trendDataList,
                intervalMillis = 10.minutes.inWholeMilliseconds,
            )

            timeWindowMap.entries.sortedBy { it.key }.forEach { (_, trendsInWindow) ->
                val heartRatesInWindow = trendsInWindow.mapNotNull { trendData ->
                    trendData.hr
                        ?.takeIf { it > 0.0 }
                        ?.hz
                }

                if (heartRatesInWindow.isNotEmpty()) {
                    val avgHrInBpm = heartRatesInWindow.averageOfDouble(HeartRate::inBpm)
                    val minHrInBpm = heartRatesInWindow.minOrNull()?.inBpm ?: avgHrInBpm
                    val maxHrInBpm = heartRatesInWindow.maxOrNull()?.inBpm ?: avgHrInBpm
                    
                    allOriginalHeartRates.addAll(heartRatesInWindow)
                    
                    val firstTrend = trendsInWindow.minByOrNull { it.timestamp } ?: trendsInWindow.first()
                    val x = firstTrend.timeISO8601.minutesSinceEpoch
                    
                    allDataPoints.add(ChartData.Entry(x = x, y = avgHrInBpm))
                    
                    allCandlestickPoints.add(
                        ChartData.CandlestickEntry(
                            x = x,
                            open = minHrInBpm,
                            close = maxHrInBpm,
                            low = minHrInBpm,
                            high = maxHrInBpm,
                        )
                    )
                }
            }
            
            val minX = from.atStartOfDay().minutesSinceEpoch
            val maxX = to.atEndOfDay().minutesSinceEpoch - 10L
            
            var minBpm = allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0
            var maxBpm = allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
            
            heartRateStat?.let { stat ->
                val statValue = stat.toInt()
                minBpm = minOf(minBpm, statValue)
                maxBpm = maxOf(maxBpm, statValue)
            }
            
            val (minY, maxY) = adjustYAxisRangeForLineChart(minBpm, maxBpm)

            val dailyLineChartConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = false,
                showAreaFill = true,
                areaAlpha = 0.2f
            )

            val axisRange = ChartData.AxisRange(
                minX = minX.toDouble(),
                maxX = maxX.toDouble(),
                minY = minY,
                maxY = maxY,
            )

            val valueFormatted = formatHeartRateRangeValue(
                allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0,
                allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
            )
            
            if (allDataPoints.isEmpty()) {
                return listOf(ChartData.Series(
                    chartType = ChartType.LINE,
                    color = color,
                    axisRange = axisRange,
                    entries = persistentListOf(),
                    value = valueFormatted,
                    candlestickEntries = persistentListOf(),
                    lineConfig = dailyLineChartConfig,
                    backgroundRegion = null,
                    groupStackBarStyle = null,
                ))
            }

            val seriesList = mutableListOf<ChartData.Series>()
            val currentSeriesEntries = mutableListOf<ChartData.Entry>()
            val currentCandlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            
            val gapThresholdMinutes = 30L
            
            for (i in allDataPoints.indices) {
                val currentPoint = allDataPoints[i]
                val currentCandlestickPoint = allCandlestickPoints[i]
                
                currentSeriesEntries.add(currentPoint)
                currentCandlestickEntries.add(currentCandlestickPoint)
                
                val isLastPoint = i == allDataPoints.size - 1
                val hasGap = if (!isLastPoint) {
                    val nextPoint = allDataPoints[i + 1]
                    (nextPoint.x - currentPoint.x) > gapThresholdMinutes
                } else {
                    false
                }
                
                if (isLastPoint || hasGap) {
                    seriesList.add(ChartData.Series(
                        chartType = ChartType.LINE,
                        color = color,
                        axisRange = axisRange,
                        entries = currentSeriesEntries.toImmutableList(),
                        value = valueFormatted,
                        candlestickEntries = currentCandlestickEntries.toImmutableList(),
                        lineConfig = dailyLineChartConfig.copy(
                            showPoints = currentSeriesEntries.size == 1
                        ),
                        backgroundRegion = null,
                        groupStackBarStyle = null,
                    ))
                    
                    currentSeriesEntries.clear()
                    currentCandlestickEntries.clear()
                }
            }
            
            return seriesList
        }
    }

    private inner class DayByDayHeartRateSeriesStrategy : HeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            isLineChart: Boolean,
            heartRateStat: Number?,
        ): List<ChartData.Series> {
            val entries = mutableListOf<ChartData.Entry>()
            val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            val allOriginalHeartRates = mutableListOf<HeartRate>()
            
            val trendDataByDay = trendDataList.groupBy { it.timeISO8601.toLocalDate() }
            
            var currentDate = from
            while (currentDate <= to) {
                val dailyTrends = trendDataByDay[currentDate] ?: emptyList()
                val dailyHeartRates = dailyTrends.mapNotNull { it.hr?.hz }
                
                val x = currentDate.toEpochDay()
                
                if (dailyHeartRates.isNotEmpty()) {
                    allOriginalHeartRates.addAll(dailyHeartRates)
                    
                    val avgHrInBpm = dailyHeartRates.averageOfDouble(HeartRate::inBpm)
                    val minHr = dailyHeartRates.minOrNull()?.inBpm ?: avgHrInBpm
                    val maxHr = dailyHeartRates.maxOrNull()?.inBpm ?: avgHrInBpm
                    
                    entries.add(ChartData.Entry(x = x, y = avgHrInBpm))
                    
                    candlestickEntries.add(ChartData.CandlestickEntry(
                        x = x,
                        open = minHr,
                        close = maxHr,
                        low = minHr,
                        high = maxHr,
                    ))
                }
                
                currentDate = currentDate.plusDays(1)
            }
            
            var minBpm = allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0
            var maxBpm = allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
            
            heartRateStat?.let { stat ->
                val statValue = stat.toInt()
                minBpm = minOf(minBpm, statValue)
                maxBpm = maxOf(maxBpm, statValue)
            }
            
            val (minY, maxY) = if (isLineChart) {
                adjustYAxisRangeForLineChart(minBpm, maxBpm)
            } else {
                adjustYAxisRangeForCandleChart(minBpm, maxBpm)
            }

            val chartType = if (isLineChart) ChartType.LINE else ChartType.CANDLESTICK

            return listOf(ChartData.Series(
                chartType = chartType,
                color = if (isLineChart) appContext.getColor(BaseR.color.dashboard_widget_minimum_heart_rate) else color,
                axisRange = ChartData.AxisRange(
                    minX = from.toEpochDay().toDouble(),
                    maxX = to.toEpochDay().toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entries.toImmutableList(),
                value = formatHeartRateRangeValue(
                    allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0,
                    allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
                ),
                candlestickEntries = candlestickEntries.toImmutableList(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
            ))
        }
    }

    private inner class WeeklyHeartRateSeriesStrategy : HeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            isLineChart: Boolean,
            heartRateStat: Number?,
        ): List<ChartData.Series> {
            val entries = mutableListOf<ChartData.Entry>()
            val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            val allOriginalHeartRates = mutableListOf<HeartRate>()
            
            val trendDataByDay = trendDataList.groupBy { it.timeISO8601.toLocalDate() }
            
            val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
            val adjustedFrom = from.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
            
            var currentWeekStart = adjustedFrom
            while (currentWeekStart <= to) {
                val weekEnd = currentWeekStart.plusDays(6).coerceAtMost(to)
                val allHeartRatesInWeek = mutableListOf<HeartRate>()
                
                var currentDay = currentWeekStart
                while (currentDay <= weekEnd) {
                    val dailyHeartRates = trendDataByDay[currentDay]?.mapNotNull { it.hr?.hz } ?: emptyList()
                    allHeartRatesInWeek.addAll(dailyHeartRates)
                    allOriginalHeartRates.addAll(dailyHeartRates)
                    currentDay = currentDay.plusDays(1)
                }
                
                val x = currentWeekStart.toEpochDay()
                
                if (allHeartRatesInWeek.isNotEmpty()) {
                    val weeklyAvgHrInBpm = allHeartRatesInWeek.averageOfDouble(HeartRate::inBpm)
                    val minHrInBpm = allHeartRatesInWeek.minOrNull()?.inBpm ?: weeklyAvgHrInBpm
                    val maxHrInBpm = allHeartRatesInWeek.maxOrNull()?.inBpm ?: weeklyAvgHrInBpm
                    
                    entries.add(ChartData.Entry(x = x, y = weeklyAvgHrInBpm))
                    
                    candlestickEntries.add(ChartData.CandlestickEntry(
                        x = x,
                        open = minHrInBpm,
                        close = maxHrInBpm,
                        low = minHrInBpm,
                        high = maxHrInBpm,
                    ))
                }
                
                currentWeekStart = currentWeekStart.plusWeeks(1)
            }
            
            var minBpm = allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0
            var maxBpm = allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
            
            heartRateStat?.let { stat ->
                val statValue = stat.toInt()
                minBpm = minOf(minBpm, statValue)
                maxBpm = maxOf(maxBpm, statValue)
            }
            
            val (minY, maxY) = if (isLineChart) {
                adjustYAxisRangeForLineChart(minBpm, maxBpm)
            } else {
                adjustYAxisRangeForCandleChart(minBpm, maxBpm)
            }

            val chartType = if (isLineChart) ChartType.LINE else ChartType.CANDLESTICK

            return listOf(ChartData.Series(
                chartType = chartType,
                color = if (isLineChart) appContext.getColor(BaseR.color.dashboard_widget_minimum_heart_rate) else color,
                axisRange = ChartData.AxisRange(
                    minX = adjustedFrom.toEpochDay().toDouble(),
                    maxX = to.toEpochDay().toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entries.toImmutableList(),
                value = formatHeartRateRangeValue(
                    allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0,
                    allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
                ),
                candlestickEntries = candlestickEntries.toImmutableList(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
            ))
        }
    }

    private inner class MonthlyHeartRateSeriesStrategy : HeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            isLineChart: Boolean,
            heartRateStat: Number?,
        ): List<ChartData.Series> {
            val entries = mutableListOf<ChartData.Entry>()
            val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            val allOriginalHeartRates = mutableListOf<HeartRate>()
            
            val trendDataByDay = trendDataList.groupBy { it.timeISO8601.toLocalDate() }
            
            var currentMonth = from.withDayOfMonth(1)
            val endMonth = to.withDayOfMonth(1)
            
            while (currentMonth <= endMonth) {
                val lastDayOfMonth = currentMonth.plusMonths(1).minusDays(1).coerceAtMost(to)
                val allHeartRatesInMonth = mutableListOf<HeartRate>()
                
                var currentDay = currentMonth
                while (currentDay <= lastDayOfMonth) {
                    val dailyHeartRates = trendDataByDay[currentDay]?.mapNotNull { it.hr?.hz } ?: emptyList()
                    allHeartRatesInMonth.addAll(dailyHeartRates)
                    allOriginalHeartRates.addAll(dailyHeartRates)
                    currentDay = currentDay.plusDays(1)
                }
                
                val x = currentMonth.epochMonth.toLong()
                
                if (allHeartRatesInMonth.isNotEmpty()) {
                    val monthlyAvgHrInBpm = allHeartRatesInMonth.averageOfDouble(HeartRate::inBpm)
                    val minHrInBpm = allHeartRatesInMonth.minOrNull()?.inBpm ?: monthlyAvgHrInBpm
                    val maxHrInBpm = allHeartRatesInMonth.maxOrNull()?.inBpm ?: monthlyAvgHrInBpm
                    
                    entries.add(ChartData.Entry(x = x, y = monthlyAvgHrInBpm))
                    
                    candlestickEntries.add(ChartData.CandlestickEntry(
                        x = x,
                        open = minHrInBpm,
                        close = maxHrInBpm,
                        low = minHrInBpm,
                        high = maxHrInBpm,
                    ))
                }
                
                currentMonth = currentMonth.plusMonths(1)
            }
            
            var minBpm = allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0
            var maxBpm = allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
            
            heartRateStat?.let { stat ->
                val statValue = stat.toInt()
                minBpm = minOf(minBpm, statValue)
                maxBpm = maxOf(maxBpm, statValue)
            }
            
            val (minY, maxY) = if (isLineChart) {
                adjustYAxisRangeForLineChart(minBpm, maxBpm)
            } else {
                adjustYAxisRangeForCandleChart(minBpm, maxBpm)
            }

            val chartType = if (isLineChart) ChartType.LINE else ChartType.CANDLESTICK

            return listOf(ChartData.Series(
                chartType = chartType,
                color = if (isLineChart) appContext.getColor(BaseR.color.dashboard_widget_minimum_heart_rate) else color,
                axisRange = ChartData.AxisRange(
                    minX = from.withDayOfMonth(1).epochMonth.toDouble(),
                    maxX = to.withDayOfMonth(1).epochMonth.toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entries.toImmutableList(),
                value = formatHeartRateRangeValue(
                    allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0,
                    allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
                ),
                candlestickEntries = candlestickEntries.toImmutableList(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
            ))
        }
    }

    private inner class YearlyHeartRateSeriesStrategy : HeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            color: Int,
            isLineChart: Boolean,
            heartRateStat: Number?,
        ): List<ChartData.Series> {
            val entries = mutableListOf<ChartData.Entry>()
            val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
            val allOriginalHeartRates = mutableListOf<HeartRate>()
            
            val trendDataByDay = trendDataList.groupBy { it.timeISO8601.toLocalDate() }
            
            var currentYear = from.withDayOfYear(1)
            val endYear = to.withDayOfYear(1)
            
            while (currentYear.year <= endYear.year) {
                val lastDayOfYear = currentYear.withDayOfYear(currentYear.lengthOfYear()).coerceAtMost(to)
                val allHeartRatesInYear = mutableListOf<HeartRate>()
                
                var currentDay = if (currentYear.year == from.year) from else currentYear
                while (currentDay <= lastDayOfYear) {
                    val dailyHeartRates = trendDataByDay[currentDay]?.mapNotNull { it.hr?.hz } ?: emptyList()
                    allHeartRatesInYear.addAll(dailyHeartRates)
                    allOriginalHeartRates.addAll(dailyHeartRates)
                    currentDay = currentDay.plusDays(1)
                }
                
                val x = currentYear.year.toLong()
                
                if (allHeartRatesInYear.isNotEmpty()) {
                    val yearlyAvgHrInBpm = allHeartRatesInYear.averageOfDouble(HeartRate::inBpm)
                    val minHrInBpm = allHeartRatesInYear.minOrNull()?.inBpm ?: yearlyAvgHrInBpm
                    val maxHrInBpm = allHeartRatesInYear.maxOrNull()?.inBpm ?: yearlyAvgHrInBpm
                    
                    entries.add(ChartData.Entry(x = x, y = yearlyAvgHrInBpm))
                    
                    candlestickEntries.add(ChartData.CandlestickEntry(
                        x = x,
                        open = minHrInBpm,
                        close = maxHrInBpm,
                        low = minHrInBpm,
                        high = maxHrInBpm,
                    ))
                }
                
                currentYear = currentYear.plusYears(1)
            }
            
            var minBpm = allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0
            var maxBpm = allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
            
            heartRateStat?.let { stat ->
                val statValue = stat.toInt()
                minBpm = minOf(minBpm, statValue)
                maxBpm = maxOf(maxBpm, statValue)
            }
            
            val (minY, maxY) = if (isLineChart) {
                adjustYAxisRangeForLineChart(minBpm, maxBpm)
            } else {
                adjustYAxisRangeForCandleChart(minBpm, maxBpm)
            }

            val chartType = if (isLineChart) ChartType.LINE else ChartType.CANDLESTICK

            return listOf(ChartData.Series(
                chartType = chartType,
                color = if (isLineChart) appContext.getColor(BaseR.color.dashboard_widget_minimum_heart_rate) else color,
                axisRange = ChartData.AxisRange(
                    minX = from.year.toDouble(),
                    maxX = to.year.toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entries.toImmutableList(),
                value = formatHeartRateRangeValue(
                    allOriginalHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0,
                    allOriginalHeartRates.maxOrNull()?.inBpm?.roundToInt() ?: 0
                ),
                candlestickEntries = candlestickEntries.toImmutableList(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
            ))
        }
    }

    private fun formatHeartRateRangeValue(min: Int, max: Int): AnnotatedString {
        val unit = appContext.getString(CR.string.bpm)
        return when {
            min == NO_DATA_VALUE || max == NO_DATA_VALUE -> buildAnnotatedString {
                append(appContext.getString(BaseR.string.widget_no_data_title))
            }
            min == max && min == 0 -> buildAnnotatedString {
                append(appContext.getString(BaseR.string.widget_no_data_title))
            }
            min == max -> buildAnnotatedString {
                append("$min")
                withStyle(SpanStyle(fontSize = 12.sp)) {
                    append(" ")
                    append(unit)
                }
            }
            else -> buildAnnotatedString {
                append("$min-$max")
                withStyle(SpanStyle(fontSize = 12.sp)) {
                    append(" ")
                    append(unit)
                }
            }
        }
    }

    private fun groupTrendDataByMinutes(
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        intervalMillis: Long,
    ): Map<Long, List<TrendData>> {
        val fromMillis = from.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()
        val toMillis = to.atEndOfDay().toEpochMilli()
        val timeRange = fromMillis until toMillis

        val result = mutableMapOf<Long, MutableList<TrendData>>()
        trendDataList.forEach { trendData ->
            val timestamp = trendData.timeISO8601.toInstant().toEpochMilli()
            if (timestamp in timeRange) {
                val windowKey = (timestamp - fromMillis) / intervalMillis * intervalMillis + fromMillis
                result.getOrPut(windowKey) { mutableListOf() }.add(trendData)
            }
        }
        return result
    }

    suspend fun getAverageHeartRate(from: LocalDate, to: LocalDate): Int {
        val trendDataList = loadTrendData(from, to)
        return calculateAverageHeartRate(trendDataList, from, to)
    }

    suspend fun getAverageHeartRateRange(from: LocalDate, to: LocalDate): Pair<Int, Int> {
        val trendDataList = loadTrendData(from, to)
        return calculateAverageHeartRateRange(trendDataList, from, to)
    }

    private suspend fun loadTrendData(from: LocalDate, to: LocalDate): List<TrendData> {
        val fromMillis = from.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        val toMillis = to.plusDays(1).atStartOfDay(ZoneId.systemDefault()).minusNanos(1).toInstant().toEpochMilli()
        
        return trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = fromMillis,
            toTimestamp = toMillis,
            aggregated = false
        ).catch { emit(emptyList()) }.first()
    }

    private fun calculateAverageHeartRate(trendDataList: List<TrendData>, from: LocalDate, to: LocalDate): Int {
        val dateRange = from..to
        return trendDataList.mapNotNull { trendData ->
            if (trendData.timeISO8601.toLocalDate() !in dateRange) {
                return@mapNotNull null
            }

            trendData.hr
                ?.takeIf { it > 0.0 }
                ?.hz
        }
            .takeUnless(List<*>::isEmpty)
            ?.averageOfDouble(HeartRate::inBpm)
            ?.roundToInt()
            ?: NO_DATA_VALUE
    }

    private fun calculateAverageHeartRateRange(
        trendDataList: List<TrendData>,
        from: LocalDate,
        to: LocalDate,
    ): Pair<Int, Int> {
        val trendDataByDay = trendDataList.groupBy { it.timeISO8601.toLocalDate() }
        
        val dailyAverageHeartRates = mutableListOf<Double>()
        
        var currentDate = from
        while (currentDate <= to) {
            val dailyTrends = trendDataByDay[currentDate] ?: emptyList()
            val dailyHeartRates = dailyTrends.mapNotNull { trendData ->
                trendData.hr
                    ?.takeIf { it > 0.0 }
                    ?.hz
            }
            
            if (dailyHeartRates.isNotEmpty()) {
                val dailyAverage = dailyHeartRates.averageOfDouble(HeartRate::inBpm)
                dailyAverageHeartRates.add(dailyAverage)
            }
            
            currentDate = currentDate.plusDays(1)
        }
        
        val minHeartRate = dailyAverageHeartRates.minOrNull()?.roundToInt() ?: NO_DATA_VALUE
        val maxHeartRate = dailyAverageHeartRates.maxOrNull()?.roundToInt() ?: NO_DATA_VALUE
        
        return Pair(minHeartRate, maxHeartRate)
    }
} 
