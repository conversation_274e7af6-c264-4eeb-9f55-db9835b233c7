package com.stt.android.chart.impl.data

import android.annotation.SuppressLint
import android.content.Context
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.chart.impl.model.firstDayOfEpochMonth
import com.stt.android.chart.impl.screen.CommuteTagsViewData
import com.stt.android.chart.impl.screen.GoalEditorViewData
import com.stt.android.chart.impl.screen.GoalViewData
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.autoCommuteTaggingEnabled
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboardnew.commute.GetCommuteWorkoutHeadersUseCase
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.TemporalAdjusters
import java.util.Locale
import javax.inject.Inject
import kotlin.math.ceil
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt
import com.stt.android.R as BaseR
import com.stt.android.core.R as CoreR

internal class CommuteDataLoader @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val currentUserController: CurrentUserController,
    private val getCommuteWorkoutHeadersUseCase: GetCommuteWorkoutHeadersUseCase,
    private val userSettingsController: UserSettingsController,
) {
    fun loadCommuteTagsData(): CommuteTagsViewData = CommuteTagsViewData.CommuteTags(
        title = R.string.commute_tags_title,
        tagsOn = userSettingsController.settings.autoCommuteTaggingEnabled,
    )

    fun loadGoalData(): GoalViewData = GoalViewData.None
    fun loadGoalEditorData(): GoalEditorViewData = GoalEditorViewData.None

    fun loadChartData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
    ): Flow<ChartData> {
        return flow {
            val username = currentUserController.username

            val params = GetCommuteWorkoutHeadersUseCase.Params(
                username = username,
                fromDate = from,
                toDate = to
            )

            val commuteWorkouts = getCommuteWorkoutHeadersUseCase(params).first()

            val series = createSeries(chartGranularity, from, to, commuteWorkouts)

            emit(
                ChartData(
                    chartGranularity = chartGranularity,
                    series = persistentListOf(series),
                    currentValues = persistentListOf(),
                    highlightEnabled = true,
                    goal = null,
                    highlightDecorationLines = persistentMapOf(),
                    chartContent = ChartContent.COMMUTE,
                    colorIndicator = null,
                )
            )
        }
    }

    private fun createSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        workouts: List<WorkoutHeader>
    ): ChartData.Series {
        val entries = mutableListOf<ChartData.Entry>()
        var minX: Long = Long.MAX_VALUE
        var maxX: Long = Long.MIN_VALUE
        var maxY = 0.0

        when (chartGranularity) {
            ChartGranularity.WEEKLY -> {
                val weeklyData = workouts.groupBy { workout ->
                    val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                    val date = LocalDate.ofInstant(
                        Instant.ofEpochMilli(workout.startTime),
                        ZoneId.systemDefault()
                    )
                    date.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                }
                weeklyData.forEach { (weekStart, weekWorkouts) ->
                    (0..6).forEach { dayOffset ->
                        val currentDate = weekStart.plusDays(dayOffset.toLong())

                        val dailyWorkouts = weekWorkouts.filter { workout ->
                            val workoutDate = LocalDate.ofInstant(
                                Instant.ofEpochMilli(workout.startTime),
                                ZoneId.systemDefault()
                            )
                            workoutDate == currentDate
                        }

                        val dailyCO2Reduction = dailyWorkouts.sumOf { it.co2EmissionsReduced }

                        if (dailyCO2Reduction > 0) {
                            val entry = ChartData.Entry(
                                x = currentDate.toEpochDay(),
                                y = dailyCO2Reduction
                            )
                            entries.add(entry)
                            maxY = max(maxY, dailyCO2Reduction)
                        }
                        minX = minOf(minX, currentDate.toEpochDay())
                        maxX = maxOf(maxX, currentDate.toEpochDay())
                    }
                }
                if (minX == Long.MAX_VALUE) {
                    minX = from.toEpochDay()
                    maxX = to.toEpochDay()
                }
            }

            ChartGranularity.MONTHLY -> {
                val firstDayOfMonth = from.withDayOfMonth(1)
                val lastDayOfMonth = from.withDayOfMonth(from.lengthOfMonth())
                var currentDate = firstDayOfMonth
                while (!currentDate.isAfter(lastDayOfMonth)) {
                    val dailyWorkouts = workouts.filter { workout ->
                        val workoutDate = LocalDate.ofInstant(
                            Instant.ofEpochMilli(workout.startTime),
                            ZoneId.systemDefault()
                        )
                        workoutDate == currentDate
                    }

                    val dailyCO2Reduction = dailyWorkouts.sumOf { it.co2EmissionsReduced }

                    if (dailyCO2Reduction > 0) {
                        val entry = ChartData.Entry(
                            x = currentDate.toEpochDay(),
                            y = dailyCO2Reduction
                        )
                        entries.add(entry)
                        maxY = max(maxY, dailyCO2Reduction)
                    }

                    minX = min(minX, currentDate.toEpochDay())
                    maxX = max(maxX, currentDate.toEpochDay())
                    currentDate = currentDate.plusDays(1)
                }
            }

            ChartGranularity.THIRTY_DAYS -> {
                val firstDay = to.minusDays(29)
                var currentDate = firstDay
                while (!currentDate.isAfter(to)) {
                    val dailyWorkouts = workouts.filter { workout ->
                        val workoutDate = LocalDate.ofInstant(
                            Instant.ofEpochMilli(workout.startTime),
                            ZoneId.systemDefault()
                        )
                        workoutDate == currentDate
                    }

                    val dailyCO2Reduction = dailyWorkouts.sumOf { it.co2EmissionsReduced }

                    if (dailyCO2Reduction > 0) {
                        val entry = ChartData.Entry(
                            x = currentDate.toEpochDay(),
                            y = dailyCO2Reduction
                        )
                        entries.add(entry)
                        maxY = max(maxY, dailyCO2Reduction)
                    }

                    minX = minOf(minX, currentDate.toEpochDay())
                    maxX = maxOf(maxX, currentDate.toEpochDay())

                    currentDate = currentDate.plusDays(1)
                }

                if (minX == Long.MAX_VALUE) {
                    minX = firstDay.toEpochDay()
                    maxX = to.toEpochDay()
                }
            }

            ChartGranularity.YEARLY -> {
                val firstDayOfYear = from.withDayOfYear(1)
                val lastDayOfYear = from.withDayOfYear(from.lengthOfYear())

                (0..11).forEach { monthOffset ->
                    val currentMonth = firstDayOfYear.plusMonths(monthOffset.toLong())
                    val currentMonthEpoch = currentMonth.epochMonth
                    val firstDayOfMonth = firstDayOfEpochMonth(currentMonthEpoch)
                    val lastDayOfMonth = currentMonth.withDayOfMonth(currentMonth.lengthOfMonth())

                    val monthlyWorkouts = workouts.filter { workout ->
                        val workoutTime = workout.startTime
                        val workoutDate = Instant.ofEpochMilli(workoutTime)
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate()

                        workoutDate in firstDayOfMonth..lastDayOfMonth
                    }

                    val monthlyCO2Reduction = monthlyWorkouts.sumOf { it.co2EmissionsReduced }

                    if (monthlyCO2Reduction > 0) {
                        val entry = ChartData.Entry(
                            x = currentMonthEpoch.toLong(),
                            y = monthlyCO2Reduction
                        )
                        entries.add(entry)
                    }

                    minX = minOf(minX, currentMonthEpoch.toLong())
                    maxX = maxOf(maxX, currentMonthEpoch.toLong())
                    maxY = max(maxY, monthlyCO2Reduction)
                }

                if (minX == Long.MAX_VALUE) {
                    minX = firstDayOfYear.toEpochDay()
                    maxX = lastDayOfYear.toEpochDay()
                }
            }

            ChartGranularity.DAILY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.SIX_MONTHS,
            ChartGranularity.EIGHT_YEARS,
            ChartGranularity.SIXTY_DAYS,
            ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
            ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> {
                throw IllegalArgumentException("Unsupported granularity: $chartGranularity")
            }
        }

        val minY = 0.0

        val finalRange = if (entries.isEmpty() || entries.all { it.y == 0.0 }) {
            3.0
        } else {
            maxY
        }

        val totalCO2Reduction = workouts.sumOf { it.co2EmissionsReduced }

        val valueAnnotatedString = buildAnnotatedString {
            append(formatCO2Reduction(totalCO2Reduction))
            withStyle(SpanStyle(fontSize = 12.sp)) {
                append(" ")
                append(appContext.getString(CoreR.string.kilograms))
            }
        }

        return ChartData.Series(
            chartType = ChartType.BAR,
            color = appContext.getColor(BaseR.color.dashboard_widget_max_vo2),
            axisRange = ChartData.AxisRange(
                minX = minX.toDouble(),
                maxX = maxX.toDouble(),
                minY = minY,
                maxY = finalRange.roundUpToNearestMultipleOf3().toDouble(),
            ),
            entries = entries.toImmutableList(),
            value = valueAnnotatedString,
            candlestickEntries = null,
            lineConfig = null,
            backgroundRegion = null,
            groupStackBarStyle = null,
        )
    }

    private fun Double.roundUpToNearestMultipleOf3(): Int {
        val intValue = ceil(this).toInt()
        if (intValue % 3 == 0) return intValue
        return ((intValue + 2) / 3) * 3
    }

    @SuppressLint("DefaultLocale")
    private fun formatCO2Reduction(co2ReductionKg: Double): String = when {
        co2ReductionKg == 0.0 -> "0"
        co2ReductionKg < 10 -> underTenFormatter.format(co2ReductionKg)
        co2ReductionKg < 100 -> underHundredFormatter.format(co2ReductionKg)
        else -> co2ReductionKg.roundToInt().toString()
    }

    fun formatHighlightData(value: Number?): String {
        if (value == null) {
            return "-"
        }

        val co2Reduction = value.toDouble()
        val formattedValue = formatCO2Reduction(co2Reduction)
        val unit = appContext.getString(CoreR.string.kilograms)

        return when {
            co2Reduction == 0.0 -> "0 $unit"
            co2Reduction < 1.0 -> "$formattedValue $unit"
            co2Reduction % 1.0 == 0.0 -> "${co2Reduction.toInt()} $unit"
            else -> "$formattedValue $unit"
        }
    }

    private val underTenFormatter =
        DecimalFormat("0.00", DecimalFormatSymbols.getInstance(Locale.US))
    private val underHundredFormatter =
        DecimalFormat("#0.0", DecimalFormatSymbols.getInstance(Locale.US))
}
