package com.stt.android.chart.impl.screen

import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.chart.impl.model.WidgetInstruction as Instruction

internal sealed class ChartRenderItem {
    abstract val key: String

    data class DateSwitcher(
        val viewData: ChartViewData.Loaded,
        val onEvent: (ChartViewEvent) -> Unit,
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.DATE_SWITCHER
    }
    
    data class ChartSummaryAndPager(
        val viewData: ChartViewData.Loaded,
        val onEvent: (ChartViewEvent) -> Unit
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.CHART_SUMMARY_AND_PAGER
    }
    
    data class CurrentValues(
        val viewData: ChartViewData.Loaded
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.CURRENT_VALUES
    }
    
    data class CommuterTags(
        val viewData: ChartViewData.Loaded,
        val onEvent: (ChartViewEvent) -> Unit
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.COMMUTER_TAGS
    }
    
    data class SleepStages(
        val viewData: SleepViewData.Loaded
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.SLEEP_STAGES
    }

    data class SleepComparisonChart(
        val viewData: SleepViewData.Loaded,
        val chartGranularity: ChartGranularity,
        val chartTimeRange: String,
        val onEvent: (ChartViewEvent) -> Unit
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.SLEEP_COMPARISON_CHART
    }
    
    data class SleepQuality(
        val viewData: SleepViewData.Loaded,
        val chartTimeRange: String
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.SLEEP_QUALITY
    }
    
    data class SleepDuration(
        val viewData: SleepViewData.Loaded,
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.SLEEP_DURATION
    }
    
    data class SleepResources(
        val viewData: SleepViewData.Loaded,
        val chartTimeRange: String
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.SLEEP_RESOURCES
    }
    
    data class GoalSection(
        val viewData: ChartViewData.Loaded,
        val onEvent: (ChartViewEvent) -> Unit
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.GOAL_SECTION
    }
    
    data class HeartRateStatsSelector(
        val viewData: ChartViewData.Loaded,
        val onEvent: (ChartViewEvent) -> Unit
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.HEART_RATE_STATS_SELECTOR
    }
    
    data class WidgetInstruction(
        val instruction: Instruction,
        val index: Int,
        val onEvent: (ChartViewEvent) -> Unit
    ) : ChartRenderItem() {
        override val key: String = "instruction_${instruction.titleRes}"
    }
    
    data class WorkoutItem(
        val workout: WorkoutHeader,
        val index: Int,
        val isLastItem: Boolean,
        val canLoadMore: Boolean,
        val isLoading: Boolean,
        val onEvent: (ChartViewEvent) -> Unit
    ) : ChartRenderItem() {
        override val key: String = "${ChartItemKeys.WORKOUT_ITEM}_${workout.id}"
    }
    
    data class ActivityHistoryHeader(
        val availableActivityTypes: List<ActivityType>,
        val selectedActivityTypeId: Int?,
        val onEvent: (ChartViewEvent) -> Unit
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.ACTIVITY_HISTORY_HEADER
    }

    data class ChartFootnote(
        val text: String,
    ) : ChartRenderItem() {
        override val key: String = ChartItemKeys.CHART_FOOTNOTE
    }

    data class WorkoutListEnd(
        val isLoading: Boolean
    ) : ChartRenderItem() {
        override val key: String = "${ChartItemKeys.WORKOUT_LIST_END}_$isLoading"
    }

    data class WorkoutMonthHeader(
        val month: String,
        val activityCount: Int
    ) : ChartRenderItem() {
        override val key: String = "${ChartItemKeys.WORKOUT_MONTH_HEADER}_$month"
    }
}

// ChartItemKeys object for consistent key management
internal object ChartItemKeys {
    const val DATE_SWITCHER = "date_switcher"
    const val CHART_SUMMARY_AND_PAGER = "chart_summary_and_pager"
    const val CURRENT_VALUES = "current_values"
    const val COMMUTER_TAGS = "commuter_tags"
    const val GOAL_SECTION = "goal_section"
    const val HEART_RATE_STATS_SELECTOR = "heart_rate_stats_selector"
    const val SLEEP_STAGES = "sleep_stages"
    const val SLEEP_COMPARISON_CHART = "sleep_comparison_chart"
    const val SLEEP_QUALITY = "sleep_quality"
    const val SLEEP_DURATION = "sleep_duration"
    const val SLEEP_RESOURCES = "sleep_resources"
    const val WORKOUT_ITEM = "workout_item"
    const val ACTIVITY_HISTORY_HEADER = "activity_history_header"
    const val CHART_FOOTNOTE = "chart_footnote"
    const val WORKOUT_LIST_END = "workout_list_end"
    const val WORKOUT_MONTH_HEADER = "workout_month_header"

    fun mapToAnalyticsValue(key: String): String? {
        return when (key) {
            CHART_SUMMARY_AND_PAGER -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.CHART1
            SLEEP_STAGES -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.STAGES
            SLEEP_COMPARISON_CHART -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.COMPARISON_CHART
            GOAL_SECTION -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.GOAL
            SLEEP_RESOURCES -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.WAKEUP_RESOURCES
            SLEEP_QUALITY -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.SLEEP_QUALITY
            SLEEP_DURATION -> AnalyticsPropertyValue.WidgetDetailPageModuleNameProperty.SLEEP_TIME
            else -> null
        }
    }
}
