package com.stt.android.chart.impl.screen.components.sleep

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.DailySleepQualityData
import com.stt.android.chart.impl.model.DailySleepResourcesData
import com.stt.android.chart.impl.model.formatPercent
import com.stt.android.compose.theme.activityRecovery
import com.stt.android.compose.theme.activitySleep
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.material3.bodySmallBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.DualProgressBar
import kotlin.math.abs
import kotlin.math.roundToInt
import com.stt.android.R as BR
import com.stt.android.core.R as CR

@Composable
internal fun DailySleepQuality(
    data: DailySleepQualityData,
    modifier: Modifier = Modifier,
) {
    DailySleepQuality(
        qualityPercent = data.quality.times(100).formatPercent(),
        qualityDescription = data.qualityDesc,
        avgHr = data.avgHr?.inBpm?.roundToInt()?.toString(),
        minHr = data.minHr?.inBpm?.roundToInt()?.toString(),
        avgHrv = data.avgHrv?.toString(),
        maxSpO2 = data.maxSpO2?.times(100)?.formatPercent()?.trimEnd('%'),
        altitude = data.altitude?.roundToInt(),
        altitudeUnitRes = data.altitudeUnitRes,
        modifier = modifier,
    )
}

@Composable
internal fun DailySleepQuality(
    qualityPercent: String,
    qualityDescription: String?,
    modifier: Modifier = Modifier,
    qualityPercentColor: Color = MaterialTheme.colorScheme.activitySleep,
    qualityTitleColor: Color = MaterialTheme.colorScheme.onSurface,
    avgHr: String? = null,
    minHr: String? = null,
    avgHrv: String? = null,
    maxSpO2: String? = null,
    altitude: Int? = null,
    altitudeUnitRes: Int? = null,
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_sleep_quality),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
            )
            Text(
                text = qualityPercent,
                style = MaterialTheme.typography.bodyMegaBold,
                color = qualityPercentColor,
            )
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = stringResource(R.string.sleep_quality_title),
            style = MaterialTheme.typography.bodyXLargeBold,
            color = qualityTitleColor,
        )
        qualityDescription?.let {
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
            Text(
                text = it,
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.onSurface,
            )
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .defaultMinSize(minHeight = 45.dp),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            NameValueColumn(
                modifier = Modifier.weight(1f),
                painter = painterResource(BR.drawable.ic_hr_12),
                name = stringResource(BR.string.sleep_quality_arg_sleep_hr),
                value = avgHr ?: "--",
                unit = stringResource(CR.string.bpm),
            )
            NameValueColumn(
                modifier = Modifier.weight(1f),
                painter = painterResource(BR.drawable.ic_hr_12),
                name = stringResource(BR.string.sleep_quality_min_sleep_hr),
                value = minHr ?: "--",
                unit = stringResource(CR.string.bpm),
            )
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .defaultMinSize(minHeight = 45.dp),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            NameValueColumn(
                modifier = Modifier.weight(1f),
                painter = painterResource(BR.drawable.ic_hrv_12),
                name = stringResource(BR.string.sleep_quality_arg_sleep_hrv),
                value = avgHrv ?: "--",
                unit = stringResource(CR.string.ms),
            )
            NameValueColumn(
                modifier = Modifier.weight(1f),
                painter = painterResource(BR.drawable.ic_spo2_12),
                name = stringResource(BR.string.sleep_quality_max_sleep_spo2),
                text = buildAnnotatedString {
                    append(maxSpO2 ?: "--")
                    withStyle(MaterialTheme.typography.bodySmallBold.toSpanStyle()) {
                        append(" ")
                        append("%")
                        if (altitude != null && altitudeUnitRes != null) {
                            append(" ")
                            appendInlineContent(id = "altitude")
                            withStyle(SpanStyle(fontWeight = FontWeight.Normal)) {
                                append(
                                    stringResource(BR.string.sleep_quality_max_altitude, altitude)
                                )
                                append(stringResource(altitudeUnitRes))
                            }
                        }
                    }
                },
                inlineContent = mapOf(
                    "altitude" to InlineTextContent(
                        Placeholder(12.sp, 12.sp, PlaceholderVerticalAlign.TextCenter)
                    ) {
                        Icon(
                            painter = painterResource(BR.drawable.ic_altitude_12),
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                ),
            )
        }
    }
}

@Composable
private fun NameValueColumn(
    painter: Painter,
    name: String,
    value: String,
    unit: String,
    modifier: Modifier = Modifier,
) = NameValueColumn(
    modifier = modifier,
    painter = painter,
    name = name,
    text = buildAnnotatedString {
        append(value)
        withStyle(MaterialTheme.typography.bodySmallBold.toSpanStyle()) {
            append(" ")
            append(unit)
        }
    },
)

@Composable
private fun NameValueColumn(
    painter: Painter,
    name: String,
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    inlineContent: Map<String, InlineTextContent> = emptyMap(),
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyXLargeBold,
            color = MaterialTheme.colorScheme.onSurface,
            inlineContent = inlineContent,
        )
        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painter,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.secondary,
            )
            Text(
                text = name,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
    }
}

@Composable
internal fun DailySleepResources(
    data: DailySleepResourcesData,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                modifier = Modifier.weight(1f),
                text = data.wakeUpBalance.times(100).formatPercent(),
                style = MaterialTheme.typography.bodyLargeBold,
                color = MaterialTheme.colorScheme.onSurface,
            )
            data.gainedBalance?.let { gainedBalance ->
                val label = if (gainedBalance < 0) {
                    stringResource(BR.string.sleep_wake_up_resources_lost_during_sleep)
                } else {
                    stringResource(BR.string.sleep_wake_up_resources_gained_during_sleep)
                }
                Text(
                    text = "${abs(gainedBalance).times(100).formatPercent()} $label",
                    style = MaterialTheme.typography.bodyBold,
                    color = MaterialTheme.colorScheme.activityRecovery,
                )
            }
        }
        DualProgressBar(
            progress = data.wakeUpBalance,
            progressBarColor = MaterialTheme.colorScheme.activityRecovery,
            backgroundColor = MaterialTheme.colorScheme.secondaryContainer,
        )
        Text(
            text = stringResource(BR.string.sleep_wake_up_resources_title),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface,
        )
    }
}
