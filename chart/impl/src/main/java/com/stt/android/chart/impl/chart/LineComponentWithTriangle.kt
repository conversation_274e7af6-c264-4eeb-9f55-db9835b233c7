package com.stt.android.chart.impl.chart

import android.graphics.Paint
import android.graphics.Path
import com.patrykandpatrick.vico.core.common.DrawingContext
import com.patrykandpatrick.vico.core.common.Fill
import com.patrykandpatrick.vico.core.common.Insets
import com.patrykandpatrick.vico.core.common.component.LineComponent

import com.patrykandpatrick.vico.core.common.shape.Shape

class LineComponentWithTriangle(
    fill: Fill,
    thicknessDp: Float = 1f,
    shape: Shape = Shape.Rectangle,
    margins: Insets = Insets.Zero,
    strokeFill: Fill = Fill.Transparent,
    strokeThicknessDp: Float = 0f,
    private val triangleSize: Float = 4f,
    private val triangleFill: Fill? = null
) : LineComponent(fill, thicknessDp, shape, margins, strokeFill, strokeThicknessDp) {

    private val trianglePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
        color = (triangleFill ?: fill).color
    }

    private val trianglePath = Path()

    override fun drawVertical(
        context: DrawingContext,
        x: Float,
        top: Float,
        bottom: Float,
        thicknessFactor: Float
    ) {
        super.drawVertical(context, x, top, bottom, thicknessFactor)
        drawTriangleAtTop(context, x, top)
    }

    private fun drawTriangleAtTop(context: DrawingContext, x: Float, top: Float) {
        with(context) {
            val triangleSizePx = triangleSize.pixels
            val halfTriangleWidth = triangleSizePx / 2f
            val offsetDp = 0f
            val offsetPx = offsetDp.pixels
            val triangleTop = top + offsetPx

            trianglePath.reset()
            trianglePath.moveTo(x - halfTriangleWidth, triangleTop)
            trianglePath.lineTo(x + halfTriangleWidth, triangleTop)
            trianglePath.lineTo(x, triangleTop + triangleSizePx)
            trianglePath.close()

            canvas.drawPath(trianglePath, trianglePaint)
        }
    }
}
