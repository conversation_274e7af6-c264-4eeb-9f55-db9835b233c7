package com.stt.android.chart.impl.usecases

import android.content.Context
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.screen.WorkoutHeaderViewData
import com.stt.android.chart.impl.screen.WorkoutMonthGroup
import com.stt.android.domain.workouts.GetPagedWorkoutHeadersByTimeRangeUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.common.coroutines.CoroutinesDispatchers
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject

import com.stt.android.domain.featuretoggle.FeatureEnabledStateUseCase
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.shouldNotCountAscentForActivity
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_CHART_WORKOUT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_CHART_WORKOUT_DEFAULT
import kotlinx.coroutines.rx2.await
import java.time.Instant
import java.time.LocalDateTime
import dagger.hilt.android.qualifiers.ApplicationContext

internal interface WorkoutListUseCase {
    suspend operator fun invoke(
        username: String,
        chartContent: ChartContent,
        chartGranularity: ChartGranularity,
        fromDate: LocalDate,
        toDate: LocalDate,
        page: Int = 1,
        includeActivityTypeId: Int? = null,
        excludeActivityTypeIds: Set<Int> = emptySet(),
        firstPageSize: Int = 10,
        pageSize: Int = 20,
        zoneId: ZoneId = ZoneId.systemDefault()
    ): WorkoutHeaderViewData

    fun createGroupedWorkoutData(
        existingWorkouts: List<WorkoutHeader>,
        newWorkouts: List<WorkoutHeader>,
        chartGranularity: ChartGranularity,
        canLoadMore: Boolean,
        isLoading: Boolean = false
    ): WorkoutHeaderViewData.Loaded
}

internal class WorkoutListUseCaseImpl @Inject constructor(
    private val getPagedWorkoutHeadersByTimeRangeUseCase: GetPagedWorkoutHeadersByTimeRangeUseCase,
    private val dispatchers: CoroutinesDispatchers,
    private val featureEnabledStateUseCase: FeatureEnabledStateUseCase,
    @ApplicationContext private val context: Context,
) : WorkoutListUseCase {
    override suspend operator fun invoke(
        username: String,
        chartContent: ChartContent,
        chartGranularity: ChartGranularity,
        fromDate: LocalDate,
        toDate: LocalDate,
        page: Int,
        includeActivityTypeId: Int?,
        excludeActivityTypeIds: Set<Int>,
        firstPageSize: Int,
        pageSize: Int,
        zoneId: ZoneId
    ): WorkoutHeaderViewData {
        // Feature gate: if disabled, short-circuit
        val chartWorkoutEnabled = try {
            featureEnabledStateUseCase
                .fetchFeatureEnabledState(KEY_ENABLE_CHART_WORKOUT, KEY_ENABLE_CHART_WORKOUT_DEFAULT)
                .await()
        } catch (_: Exception) {
            // Fail-safe: default behavior when reading toggle fails
            KEY_ENABLE_CHART_WORKOUT_DEFAULT
        }

        if (!chartWorkoutEnabled) {
            return WorkoutHeaderViewData.None
        }

        if (chartContent !in listOf(ChartContent.DURATION, ChartContent.ASCENT, ChartContent.COMMUTE)) {
            return WorkoutHeaderViewData.None
        }
        
        return try {
            val isAscentChart = chartContent == ChartContent.ASCENT
            
            // For ascent charts, we need to exclude activity types that shouldn't count ascent
            val finalExcludeActivityTypeIds = if (isAscentChart) {
                excludeActivityTypeIds + ActivityType.values()
                    .filter { shouldNotCountAscentForActivity(it.id) }
                    .map { it.id }
                    .toSet()
            } else {
                excludeActivityTypeIds
            }
            
            val workouts = getPagedWorkoutHeadersByTimeRangeUseCase(
                GetPagedWorkoutHeadersByTimeRangeUseCase.Params(
                    username = username,
                    fromTimeMs = fromDate.atStartOfDay(zoneId).toInstant().toEpochMilli(),
                    toTimeMs = toDate.plusDays(1).atStartOfDay(zoneId).toInstant().toEpochMilli() - 1,
                    page = page,
                    includeActivityTypeId = includeActivityTypeId,
                    excludeActivityTypeIds = finalExcludeActivityTypeIds,
                    firstPageSize = firstPageSize,
                    pageSize = pageSize,
                    filterWithAscent = isAscentChart
                )
            ).sortedByDescending { it.startTime }
            
            // Check if we need to group workouts by month
            val needsMonthGrouping = shouldGroupByMonth(chartGranularity)
            
            val workoutGroups = if (needsMonthGrouping && workouts.isNotEmpty()) {
                createMonthGroups(workouts)
            } else null
            
            WorkoutHeaderViewData.Loaded(
                workoutHeaders = flowOf(workouts).flowOn(dispatchers.io),
                workoutGroups = workoutGroups?.let { flowOf(it).flowOn(dispatchers.io) },
                canLoadMore = workouts.size == if (page == 1) firstPageSize else pageSize,
                isLoading = false
            )
        } catch (e: Exception) {
            WorkoutHeaderViewData.Loaded(
                workoutHeaders = flowOf(emptyList<WorkoutHeader>()).flowOn(dispatchers.io),
                canLoadMore = false,
                isLoading = false
            )
        }
    }
    
    override fun createGroupedWorkoutData(
        existingWorkouts: List<WorkoutHeader>,
        newWorkouts: List<WorkoutHeader>,
        chartGranularity: ChartGranularity,
        canLoadMore: Boolean,
        isLoading: Boolean
    ): WorkoutHeaderViewData.Loaded {
        val mergedWorkouts = (existingWorkouts + newWorkouts).sortedByDescending { it.startTime }
        
        // Check if we need to group workouts by month
        val needsMonthGrouping = shouldGroupByMonth(chartGranularity)
        
        val workoutGroups = if (needsMonthGrouping && mergedWorkouts.isNotEmpty()) {
            createMonthGroups(mergedWorkouts)
        } else null
        
        return WorkoutHeaderViewData.Loaded(
            workoutHeaders = flowOf(mergedWorkouts).flowOn(dispatchers.io),
            workoutGroups = workoutGroups?.let { flowOf(it).flowOn(dispatchers.io) },
            canLoadMore = canLoadMore,
            isLoading = isLoading
        )
    }
    
    private fun createMonthGroups(workouts: List<WorkoutHeader>): List<WorkoutMonthGroup> {
        val resources = context.resources
        
        val workoutsByMonth = workouts.groupBy { workout ->
            val instant = Instant.ofEpochMilli(workout.startTime)
            val localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault())
            localDateTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0)
        }.toSortedMap(compareByDescending { it })
        
        return workoutsByMonth.map { (monthDateTime, monthWorkouts) ->
            val abbreviatedMonth = resources.getStringArray(com.stt.android.R.array.abbreviated_months)[monthDateTime.month.value - 1]
            val monthYearDisplay = resources.getString(
                com.stt.android.R.string.year_abbreviated_month,
                monthDateTime.year,
                abbreviatedMonth
            )
            
            val sortedMonthWorkouts = monthWorkouts.sortedByDescending { it.startTime }
            
            WorkoutMonthGroup(
                monthDisplay = monthYearDisplay,
                workouts = sortedMonthWorkouts
            )
        }
    }

    private fun shouldGroupByMonth(chartGranularity: ChartGranularity): Boolean =
        chartGranularity in listOf(
            ChartGranularity.SIX_MONTHS,
            ChartGranularity.YEARLY,
            ChartGranularity.EIGHT_YEARS,
        )
}
