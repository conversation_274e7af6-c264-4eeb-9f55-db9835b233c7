package com.stt.android.chart.impl.screen.components

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.material.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.impl.R
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing

@Composable
fun DailyTarget(
    chartContent: ChartContent?,
    hasGoal: Boolean,
    modifier: Modifier = Modifier,
) {
    if (!hasGoal) {
        val needSpacer = when (chartContent) {
            ChartContent.STEPS,
            ChartContent.CALORIES,
            ChartContent.SLEEP,
            ChartContent.DURATION -> true

            else -> false
        }
        if (needSpacer) {
            Spacer(
                Modifier.height(24.dp)
            )
        }
        return
    }

    Row(
        modifier = modifier.height(24.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(R.drawable.ic_dashline),
            contentDescription = null,
            tint = MaterialTheme.colorScheme.nearBlack,
        )

        Spacer(Modifier.width(MaterialTheme.spacing.xsmall))

        val goalRes = when (chartContent) {
            ChartContent.STEPS -> R.string.chart_daily_step_target
            ChartContent.CALORIES -> R.string.chart_daily_calorie_target
            ChartContent.SLEEP -> R.string.chart_daily_sleep_target
            ChartContent.DURATION -> R.string.chart_weekly_training_target
            else -> R.string.chart_daily_target
        }
        Text(
            text = stringResource(goalRes),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.nearBlack,
        )
    }
}
