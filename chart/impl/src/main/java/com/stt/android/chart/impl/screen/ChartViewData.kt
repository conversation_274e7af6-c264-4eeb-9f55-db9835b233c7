package com.stt.android.chart.impl.screen

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.ui.text.AnnotatedString
import androidx.paging.PagingData
import com.stt.android.chart.api.model.ChartComparison
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.DailySleepMetricsData
import com.stt.android.chart.impl.model.HeartRateStatItem
import com.stt.android.chart.impl.model.SleepComparisonChartData
import com.stt.android.chart.impl.model.SleepComparisonHighlightData
import com.stt.android.chart.impl.model.SleepHistoryItem
import com.stt.android.chart.impl.model.SleepStageSummary
import com.stt.android.chart.impl.model.WidgetInstruction
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

internal sealed interface ChartViewData {
    data object Initial : ChartViewData

    data class Loaded(
        val chartStyle: ChartStyleViewData,
        val chartGranularity: ChartGranularity,
        val chartComparison: ChartComparisonViewData,
        val mainChartGranularities: ImmutableList<ChartGranularity>,
        val extraChartGranularities: ImmutableList<ChartGranularity>,
        val showExtraChartGranularitySelection: Boolean,
        @StringRes val chartContentTitle: Int,
        @StringRes val chartValueType: Int,
        val chartTimeRange: String,
        val scrollToCurrentPage: Boolean,
        val currentChartPage: Int,
        val chartPageCount: Int,
        val chartData: Map<Int, StateFlow<ChartData>>, // page index to chart data
        val chartHighlight: ChartHighlightViewData,
        val goal: GoalViewData,
        val commuterTags: CommuteTagsViewData,
        val goalEditor: GoalEditorViewData,
        val isWatchConnected: Boolean,
        val instructions: ImmutableList<WidgetInstruction> = persistentListOf(),
        val sleepViewData: SleepViewData = SleepViewData.None,
        val heartRateStatsData: HeartRateStatsViewData = HeartRateStatsViewData.None,
        val workoutHeaderViewData: WorkoutHeaderViewData = WorkoutHeaderViewData.None,
        val availableActivityTypesViewData: AvailableActivityTypesViewData = AvailableActivityTypesViewData.None,
    ) : ChartViewData
}

internal data class ChartStyleViewData(
    val currentChartStyle: ChartStyle,
    val availableChartStyles: ImmutableList<ChartStyle>,
)

internal sealed interface ChartComparisonViewData {
    data object NotSupported : ChartComparisonViewData

    data class Off(
        @StringRes val titleRes: Int,
        val target: ChartComparison,
    ) : ChartComparisonViewData

    data class LastPeriod(
        @StringRes val titleRes: Int,
        @StringRes val chartValueType: Int,
        val chartTimeRange: String,
        @ColorRes val leftColorRes: Int?,
        @ColorRes val rightColorRes: Int?,
    ) : ChartComparisonViewData

    data class RightChart(
        @StringRes val titleRes: Int,
        @StringRes val chartValueType: Int,
        @StringRes val rightChartValueType: Int,
        @ColorRes val leftColorRes: Int?,
        @ColorRes val rightColorRes: Int?,
    ) : ChartComparisonViewData
}

internal sealed interface GoalViewData {
    data object None : GoalViewData

    data class Goal(
        @DrawableRes val icon: Int,
        @ColorRes val iconColor: Int,
        @StringRes val title: Int,
        val goal: Flow<String>,
    ) : GoalViewData
}

internal sealed interface CommuteTagsViewData {
    data object None : CommuteTagsViewData

    data class CommuteTags(
        @StringRes val title: Int,
        val tagsOn: Boolean,
    ) : CommuteTagsViewData
}

internal sealed interface GoalEditorViewData {
    data object None : GoalEditorViewData

    data class Editor(
        val chartContent: ChartContent,
        val requiresWatchConnection: Boolean,
        val currentGoal: Int,
    ) : GoalEditorViewData
}

data class CurrentValueData(
    val value: AnnotatedString,
    val explanation: String,
    @DrawableRes val icon: Int,
    @ColorRes val iconColor: Int,
)

internal sealed interface AvailableActivityTypesViewData {
    data object None : AvailableActivityTypesViewData

    data class Loaded(
        val availableActivityTypes: Flow<List<ActivityType>>,
        val selectedActivityTypeId: Int? = null
    ) : AvailableActivityTypesViewData
}

internal sealed interface WorkoutHeaderViewData {
    data object None : WorkoutHeaderViewData

    data class Loaded(
        val workoutHeaders: Flow<List<WorkoutHeader>>,
        val workoutGroups: Flow<List<WorkoutMonthGroup>>? = null,
        val canLoadMore: Boolean = false,
        val isLoading: Boolean = false
    ) : WorkoutHeaderViewData
}

internal data class WorkoutMonthGroup(
    val monthDisplay: String,
    val workouts: List<WorkoutHeader>
)

sealed interface ChartHighlightViewData {
    data object None : ChartHighlightViewData

    data class Highlight(
        @StringRes val valueType: Int,
        val primaryValue: Value,
        val comparisonValue: Value?,
    ) : ChartHighlightViewData

    data class Value(
        val value: String,
        val dateTime: String,
        val colorRes: Int? = null,
    )
}

internal sealed interface SleepViewData {
    data object None : SleepViewData

    data class Loaded(
        val availability: Flow<ChartAvailability>,
        val stagesViewData: SleepStagesViewData,
        val dailyMetricsViewData: DailySleepMetricsViewData,
        val comparisonViewData: SleepComparisonViewData,
        val historyViewData: SleepHistoryViewData,
        val showPrimaryComparisonGraphSelection: Boolean = false,
        val showSecondaryComparisonGraphSelection: Boolean = false,
        val comparisonHighlightViewData: SleepComparisonHighlightViewData = SleepComparisonHighlightViewData.None,
    ) : SleepViewData
}

internal data class ChartAvailability(
    val showChart: Boolean = true,
    val message: String? = null,
)

internal sealed interface SleepStagesViewData {
    data object None : SleepStagesViewData

    data class Loaded(
        val chartGranularity: ChartGranularity,
        val stages: Flow<Pair<Boolean, List<SleepStageSummary>>>,
    ) : SleepStagesViewData
}

internal sealed interface DailySleepMetricsViewData {
    data object None : DailySleepMetricsViewData

    data class Loaded(
        val dailySleepMetrics: Flow<DailySleepMetricsData>,
    ) : DailySleepMetricsViewData
}

internal sealed interface SleepComparisonViewData {
    data object None : SleepComparisonViewData

    data class Loaded(
        val chartData: Flow<SleepComparisonChartData>,
    ) : SleepComparisonViewData
}

internal sealed interface SleepComparisonHighlightViewData {
    data object None : SleepComparisonHighlightViewData

    data class Loaded(
        val highlightData: Flow<SleepComparisonHighlightData?>,
    ) : SleepComparisonHighlightViewData
}

internal sealed interface SleepHistoryViewData {
    data object None : SleepHistoryViewData

    data class Loaded(
        val pagingData: Flow<PagingData<SleepHistoryItem>>,
    ) : SleepHistoryViewData
}

internal sealed interface HeartRateStatsViewData {
    data object None : HeartRateStatsViewData
    data class Loaded(
        val stats: ImmutableList<HeartRateStatItem>,
        val selectedStatId: String?
    ) : HeartRateStatsViewData
}
