package com.stt.android.chart.impl.screen

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.screen.components.ChartHighlight
import com.stt.android.chart.impl.screen.components.ChartPager
import com.stt.android.chart.impl.screen.components.ChartSummary
import com.stt.android.chart.impl.screen.components.CommuterTags
import com.stt.android.chart.impl.screen.components.CurrentValues
import com.stt.android.chart.impl.screen.components.GoalSection
import com.stt.android.chart.impl.screen.components.HeartRateStatsSelector
import com.stt.android.chart.impl.screen.components.WorkoutItemCard
import com.stt.android.chart.impl.screen.components.WorkoutListEnd
import com.stt.android.chart.impl.screen.components.WorkoutMonthHeader
import com.stt.android.chart.impl.screen.components.ActivityHistoryHeader
import com.stt.android.chart.impl.screen.components.sleep.SleepStages
import com.stt.android.chart.impl.screen.components.sleep.DailySleepQuality
import com.stt.android.chart.impl.screen.components.sleep.DailySleepDuration
import com.stt.android.chart.impl.screen.components.sleep.DailySleepResources
import com.stt.android.chart.impl.chart.SleepComparisonChart
import com.stt.android.chart.impl.model.DailySleepResourcesData
import com.stt.android.chart.impl.screen.components.Instruction
import com.stt.android.compose.theme.activityCycling
import com.stt.android.compose.theme.spacing
import androidx.compose.ui.Alignment
import com.stt.android.chart.impl.screen.components.ChartFootnote
import com.stt.android.chart.impl.screen.components.DateSwitcher
import kotlin.time.Duration

@Composable
internal fun ChartRenderItem.DateSwitcher.Render(
    modifier: Modifier = Modifier
) {
    DateSwitcher(
        viewData = viewData,
        onEvent = onEvent,
        modifier = modifier,
    )
}

@Composable
internal fun ChartRenderItem.ChartFootnote.Render(
    modifier: Modifier = Modifier
) {
    ChartFootnote(
        message = text,
        modifier = modifier.padding(
            horizontal = MaterialTheme.spacing.medium,
            vertical = MaterialTheme.spacing.xsmall,
        ),
    )
}

@Composable
internal fun ChartRenderItem.ChartSummaryAndPager.Render(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.small,
            horizontal = MaterialTheme.spacing.medium,
        ),
    ) {
        Box {
            ChartSummary(
                viewData = viewData,
                onEvent = onEvent,
                modifier = Modifier
                    .padding(bottom = MaterialTheme.spacing.medium),
            )

            ChartHighlight(
                viewData = viewData.chartHighlight,
                modifier = Modifier
                    .align(Alignment.BottomCenter),
            )
        }

        ChartPager(
            viewData = viewData,
            onEvent = onEvent,
            modifier = Modifier
        )
    }
}

@Composable
internal fun ChartRenderItem.CurrentValues.Render(
    modifier: Modifier = Modifier
) {
    CurrentValues(
        viewData = viewData,
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.small,
            horizontal = MaterialTheme.spacing.medium,
        ),
    )
}

@Composable
internal fun ChartRenderItem.CommuterTags.Render(
    modifier: Modifier = Modifier
) {
    CommuterTags(
        viewData = viewData,
        onEvent = onEvent,
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.small,
            horizontal = MaterialTheme.spacing.medium,
        ),
    )
}

@Composable
internal fun ChartRenderItem.SleepStages.Render(
    modifier: Modifier = Modifier
) {
    SleepStages(
        viewData = viewData,
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.small,
            horizontal = MaterialTheme.spacing.medium,
        )
    )
}

@Composable
internal fun ChartRenderItem.SleepQuality.Render(
    modifier: Modifier = Modifier
) {
    val metricsViewData = viewData.dailyMetricsViewData
    if (metricsViewData !is DailySleepMetricsViewData.Loaded) return

    val data = metricsViewData.dailySleepMetrics.collectAsState(initial = null).value ?: return
    data.sleepQuality?.let { sleepQuality ->
        DailySleepQuality(
            data = sleepQuality,
            modifier = modifier.padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = MaterialTheme.spacing.medium,
            ),
        )
    } ?: run {
        DailySleepQuality(
            qualityPercent = "--",
            qualityDescription = stringResource(R.string.sleep_quality_description_default),
            qualityPercentColor = MaterialTheme.colorScheme.onSurface,
            qualityTitleColor = MaterialTheme.colorScheme.activityCycling,
            modifier = modifier.padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = MaterialTheme.spacing.medium,
            ),
        )
    }
}

@Composable
internal fun ChartRenderItem.SleepDuration.Render(
    modifier: Modifier = Modifier
) {
    val metricsViewData = viewData.dailyMetricsViewData
    if (metricsViewData !is DailySleepMetricsViewData.Loaded) return

    val data = metricsViewData.dailySleepMetrics.collectAsState(initial = null).value ?: return
    data.sleepDuration?.let { sleepDuration ->
        DailySleepDuration(
            longSleep = sleepDuration.longSleep,
            naps = sleepDuration.naps,
            longSleepDuration = sleepDuration.longSleepDuration,
            napDuration = sleepDuration.napDuration,
            totalDuration = sleepDuration.totalDuration,
            goalDuration = data.sleepGoal,
            date = sleepDuration.dateStr,
            modifier = modifier.padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = MaterialTheme.spacing.medium,
            ),
        )
    } ?: run {
        DailySleepDuration(
            longSleep = null,
            naps = emptyList(),
            longSleepDuration = Duration.ZERO,
            napDuration = Duration.ZERO,
            totalDuration = Duration.ZERO,
            goalDuration = data.sleepGoal,
            date = "",
            modifier = modifier.padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = MaterialTheme.spacing.medium,
            ),
        )
    }
}

@Composable
internal fun ChartRenderItem.SleepResources.Render(
    modifier: Modifier = Modifier
) {
    val metricsViewData = viewData.dailyMetricsViewData
    if (metricsViewData !is DailySleepMetricsViewData.Loaded) return

    val data = metricsViewData.dailySleepMetrics.collectAsState(initial = null).value ?: return
    data.resources?.let { resources ->
        DailySleepResources(
            data = resources,
            modifier = modifier.padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = MaterialTheme.spacing.medium,
            ),
        )
    } ?: run {
        DailySleepResources(
            data = DailySleepResourcesData(
                wakeUpBalance = 0f,
                gainedBalance = 0f,
            ),
            modifier = modifier.padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = MaterialTheme.spacing.medium,
            ),
        )
    }
}

@Composable
internal fun ChartRenderItem.SleepComparisonChart.Render(
    modifier: Modifier = Modifier
) {
    SleepComparisonChart(
        viewData = viewData,
        chartGranularity = chartGranularity,
        chartTimeRange = chartTimeRange,
        onEvent = onEvent,
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.small,
            horizontal = MaterialTheme.spacing.medium,
        )
    )
}

@Composable
internal fun ChartRenderItem.GoalSection.Render(
    modifier: Modifier = Modifier
) {
    GoalSection(
        viewData = viewData,
        onEvent = onEvent,
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.small,
            horizontal = MaterialTheme.spacing.medium,
        ),
    )
}

@Composable
internal fun ChartRenderItem.HeartRateStatsSelector.Render(
    modifier: Modifier = Modifier
) {
    HeartRateStatsSelector(
        viewData = viewData.heartRateStatsData,
        onEvent = onEvent,
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.small,
            horizontal = MaterialTheme.spacing.medium,
        ),
    )
}

@Composable
internal fun ChartRenderItem.WidgetInstruction.Render(
    modifier: Modifier = Modifier
) {
    Instruction(
        instruction = instruction,
        index = index,
        onEvent = onEvent,
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.small,
            horizontal = MaterialTheme.spacing.medium,
        ),
    )
}

@Composable
internal fun ChartRenderItem.WorkoutItem.Render(
    modifier: Modifier = Modifier
) {
    WorkoutItemCard(
        workout = workout,
        onEvent = onEvent,
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.small,
            horizontal = MaterialTheme.spacing.medium,
        ),
    )
    if (isLastItem && canLoadMore && !isLoading) {
        LaunchedEffect(workout.id) {
            onEvent(ChartViewEvent.LoadMoreWorkouts)
        }
    }
}

@Composable
internal fun ChartRenderItem.ActivityHistoryHeader.Render(
    modifier: Modifier = Modifier
) {
    ActivityHistoryHeader(
        selectedActivityType = availableActivityTypes.find { activityType -> activityType.id == selectedActivityTypeId },
        availableActivityTypes = availableActivityTypes,
        onActivityTypeSelected = { activityType ->
            onEvent(ChartViewEvent.ActivityTypeFilter(activityType?.id))
        },
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.small,
            horizontal = MaterialTheme.spacing.medium,
        )
    )
}

@Composable
internal fun ChartRenderItem.WorkoutListEnd.Render(
    modifier: Modifier = Modifier
) {
    WorkoutListEnd(
        isLoading = isLoading,
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.small,
            horizontal = MaterialTheme.spacing.medium,
        )
    )
}

@Composable
internal fun ChartRenderItem.WorkoutMonthHeader.Render(
    modifier: Modifier = Modifier
) {
    WorkoutMonthHeader(
        month = month,
        activityCount = activityCount,
        modifier = modifier
    )
}

@Composable
internal fun ChartRenderItem.RenderContent(
    modifier: Modifier = Modifier
) {
    when (this) {
        is ChartRenderItem.DateSwitcher -> Render(modifier)
        is ChartRenderItem.ChartFootnote -> Render(modifier)
        is ChartRenderItem.ChartSummaryAndPager -> Render(modifier)
        is ChartRenderItem.CurrentValues -> Render(modifier)
        is ChartRenderItem.CommuterTags -> Render(modifier)
        is ChartRenderItem.SleepStages -> Render(modifier)
        is ChartRenderItem.SleepQuality -> Render(modifier)
        is ChartRenderItem.SleepDuration -> Render(modifier)
        is ChartRenderItem.SleepResources -> Render(modifier)
        is ChartRenderItem.SleepComparisonChart -> Render(modifier)
        is ChartRenderItem.GoalSection -> Render(modifier)
        is ChartRenderItem.HeartRateStatsSelector -> Render(modifier)
        is ChartRenderItem.WidgetInstruction -> Render(modifier)
        is ChartRenderItem.WorkoutItem -> Render(modifier)
        is ChartRenderItem.ActivityHistoryHeader -> Render(modifier)
        is ChartRenderItem.WorkoutListEnd -> Render(modifier)
        is ChartRenderItem.WorkoutMonthHeader -> Render(modifier)
    }
}
