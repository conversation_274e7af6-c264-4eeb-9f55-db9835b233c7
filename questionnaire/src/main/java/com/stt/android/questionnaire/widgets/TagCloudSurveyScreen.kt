package com.stt.android.questionnaire.widgets

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.extensions.groupAndSort
import com.stt.android.questionnaire.QuestionnaireViewModel
import com.stt.android.ui.utils.WindowInfo
import com.stt.android.ui.utils.WindowSizeClass

@Composable
fun TagCloudSurveyScreen(
    header: String,
    state: TagCloudSurveyState,
    windowInfo: WindowInfo,
    onTagClick: (SurveyTag) -> Unit,
    onShowAllClick: (Boolean) -> Unit,
    onDone: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    subHeader: String? = null,
    @StringRes buttonText: Int = R.string.done,
) {
    val optimizeVerticalSpace = remember(windowInfo) {
        windowInfo.windowSize.height <= 480.dp
    }

    Box(
        modifier = modifier
            .background(MaterialTheme.colors.background)
            .fillMaxSize()
            .narrowContent()
    ) {
        Column(modifier = Modifier.background(MaterialTheme.colors.surface)) {
            if (optimizeVerticalSpace) {
                Box(modifier = Modifier.weight(1f)) {
                    TagCloudSurvey(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                top = MaterialTheme.spacing.medium,
                                start = MaterialTheme.spacing.medium,
                                end = MaterialTheme.spacing.medium
                            ),
                        state = state,
                        windowInfo = windowInfo,
                        header = header,
                        subHeader = subHeader,
                        onTagClick = onTagClick,
                        onShowAllClick = onShowAllClick
                    )
                    DismissButton(onDismiss, Modifier.align(Alignment.TopEnd))
                }
            } else {
                DismissButton(onDismiss, Modifier.align(Alignment.End))
                TagCloudSurvey(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            start = MaterialTheme.spacing.medium,
                            end = MaterialTheme.spacing.medium
                        )
                        .weight(1f),
                    state = state,
                    windowInfo = windowInfo,
                    header = header,
                    subHeader = subHeader,
                    onTagClick = onTagClick,
                    onShowAllClick = onShowAllClick
                )
            }

            PrimaryButton(
                text = stringResource(id = buttonText),
                onClick = onDone,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium),
                enabled = state.minSelectionsReached
            )
        }
    }
}

@Composable
private fun DismissButton(onDismiss: () -> Unit, modifier: Modifier = Modifier) {
    Icon(
        painter = painterResource(id = R.drawable.ic_close_dialog_icon),
        contentDescription = stringResource(R.string.close),
        modifier = modifier
            .padding(all = MaterialTheme.spacing.small)
            .clickable(
                onClick = onDismiss,
                interactionSource = remember { MutableInteractionSource() },
                indication = ripple(bounded = false),
                role = Role.Button
            )
            .padding(all = MaterialTheme.spacing.small) // expands touchable area
            .size(24.dp),
        tint = Color.Unspecified
    )
}

@Preview
@Composable
private fun TagCloudSurveyScreenPreview() {
    val grouped = ActivityType.values().groupAndSort(
        LocalContext.current,
        listOf(
            ActivityGroup.Running,
            ActivityGroup.Cycling,
            ActivityGroup.OutdoorAdventures,
            ActivityGroup.WinterSports,
            ActivityGroup.Watersports,
            ActivityGroup.Performance,
            ActivityGroup.IndoorSports,
            ActivityGroup.Diving,
            ActivityGroup.TeamAndRacketSports
        )
    )

    val previewHelper = TagCloudPreviewHelper(QuestionnaireViewModel.getDefaultSportState(grouped))

    AppTheme {
        TagCloudSurveyScreen(
            header = "What Sports you do?",
            subHeader = "Pick 1-5 most relevant.",
            state = previewHelper.state.value,
            windowInfo = WindowInfo(
                windowWidthSizeClass = WindowSizeClass.Medium,
                windowSize = DpSize(width = 1280.dp, 720.dp)
            ),
            buttonText = R.string.continue_str,
            onTagClick = previewHelper.onTagClick,
            onShowAllClick = previewHelper.onShowAllClick,
            onDone = {},
            onDismiss = {}
        )
    }
}

@Preview(device = Devices.DEFAULT, widthDp = 720, heightDp = 320)
@Composable
private fun TagCloudSurveyScreenLandscapePreview() {
    val grouped = ActivityType.values().groupAndSort(
        LocalContext.current,
        listOf(
            ActivityGroup.Running,
            ActivityGroup.Cycling,
            ActivityGroup.OutdoorAdventures,
            ActivityGroup.WinterSports,
            ActivityGroup.Watersports,
            ActivityGroup.Performance,
            ActivityGroup.IndoorSports,
            ActivityGroup.Diving,
            ActivityGroup.TeamAndRacketSports
        )
    )

    val previewHelper = TagCloudPreviewHelper(QuestionnaireViewModel.getDefaultSportState(grouped))

    AppTheme {
        TagCloudSurveyScreen(
            header = "What Sports you do?",
            subHeader = "Pick 1-5 most relevant.",
            state = previewHelper.state.value,
            windowInfo = WindowInfo(
                windowWidthSizeClass = WindowSizeClass.Medium,
                windowSize = DpSize(width = 720.dp, 320.dp)
            ),
            buttonText = R.string.continue_str,
            onTagClick = previewHelper.onTagClick,
            onShowAllClick = previewHelper.onShowAllClick,
            onDone = {},
            onDismiss = {}
        )
    }
}
