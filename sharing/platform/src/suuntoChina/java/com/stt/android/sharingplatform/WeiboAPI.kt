package com.stt.android.sharingplatform

import android.app.Activity
import android.app.Application
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import com.sina.weibo.sdk.api.MultiImageObject
import com.sina.weibo.sdk.api.TextObject
import com.sina.weibo.sdk.api.WebpageObject
import com.sina.weibo.sdk.api.WeiboMultiMessage
import com.sina.weibo.sdk.auth.AuthInfo
import com.sina.weibo.sdk.common.UiError
import com.sina.weibo.sdk.openapi.IWBAPI
import com.sina.weibo.sdk.openapi.SdkListener
import com.sina.weibo.sdk.openapi.WBAPIFactory
import com.sina.weibo.sdk.share.WbShareCallback
import com.stt.android.di.initializer.AppInitializerPostTermsApproval
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WeiboAPI @Inject constructor() : AppInitializerPostTermsApproval {

    lateinit var api: IWBAPI
    private var shareResultHandler: ShareResultHandler? = null

    fun setShareResultHandler(handler: ShareResultHandler?) {
        this.shareResultHandler = handler
    }

    fun appInstalled() = api.isWBAppInstalled

    private val wbShareCallback = object : WbShareCallback {
        override fun onComplete() {
            shareResultHandler?.invoke(SharingResultState.Success)
        }

        override fun onError(p0: UiError?) {
            Timber.w("share to weibo fail: ${p0?.errorMessage}")
            shareResultHandler?.invoke(SharingResultState.Success)
        }

        override fun onCancel() {
            shareResultHandler?.invoke(SharingResultState.Cancel)
        }
    }

    override fun init(app: Application) {
        val authInfo = AuthInfo(app, app.getString(R.string.weibo_app_key), "", "")
        api = WBAPIFactory.createWBAPI(app)
        api.registerApp(
            app,
            authInfo,
            object : SdkListener {
                override fun onInitSuccess() {
                    Timber.d("weibo sdk init success")
                }

                override fun onInitFailure(p0: Exception?) {
                    Timber.w(p0, "weibo sdk init fail: ${p0?.message}")
                }
            }
        )
    }

    fun shareImage(imageUris: List<Uri>, activity: Activity, hashTags: List<String> = emptyList()) {
        val message = WeiboMultiMessage()
        val imageObject = MultiImageObject()
        imageObject.imageList = ArrayList(imageUris)
        message.multiImageObject = imageObject
        if (hashTags.isNotEmpty()) {
            message.textObject = TextObject().apply {
                text = hashTags.joinToString(separator = "##", prefix = "#", postfix = "#")
            }
        }
        api.shareMessage(activity, message, false)
    }

    fun shareLink(
        title: String,
        description: String,
        linkUrl: String,
        activity: Activity,
        iconBitmap: Bitmap? = null,
        hashTags: List<String> = emptyList()
    ) {
        val message = WeiboMultiMessage()
        message.mediaObject = WebpageObject().apply {
            identify = UUID.randomUUID().toString()
            this.title = title
            this.description = description
            actionUrl = linkUrl
            iconBitmap?.let { icon ->
                thumbData = ByteArrayOutputStream().use {
                    icon.compress(Bitmap.CompressFormat.JPEG, 85, it)
                    it.toByteArray()
                }
            }
        }
        if (hashTags.isNotEmpty()) {
            message.textObject = TextObject().apply {
                text = hashTags.joinToString(separator = "##", prefix = "#", postfix = "#")
            }
        }
        api.shareMessage(activity, message, false)
    }

    fun onShareResult(intent: Intent, shareResultHandler: ShareResultHandler) {
        this.shareResultHandler = shareResultHandler
        api.doResultIntent(intent, wbShareCallback)
    }
}
