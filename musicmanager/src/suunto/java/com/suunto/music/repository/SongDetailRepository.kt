package com.suunto.music.repository

import com.suunto.music.datasource.SongDetailLocalDataSource
import com.suunto.music.localmapper.toLocalSongDetail
import com.suunto.music.localmapper.toSongDetail
import com.suunto.music.model.OfflineMusicSourceType
import com.suunto.music.model.SongDetail
import javax.inject.Inject

class SongDetailRepository @Inject constructor(
    private val songDetailLocalDataSource: SongDetailLocalDataSource
) {
    suspend fun saveSongDetail(songDetail: SongDetail, sourceType: OfflineMusicSourceType) {
        songDetailLocalDataSource.saveSongDetail(songDetail.toLocalSongDetail(sourceType))
    }

    suspend fun fetchSongDetailByKey(key: String): SongDetail? {
        return songDetailLocalDataSource.fetchSongDetailByKey(key)?.toSongDetail()
    }

    suspend fun fetchAllSongDetails(): List<SongDetail>? {
        return songDetailLocalDataSource.fetchAllSongDetails()?.map { it.toSongDetail() }
    }

    suspend fun updateSongDetail(songDetail: SongDetail, sourceType: OfflineMusicSourceType) {
        songDetailLocalDataSource.updateSongDetail(songDetail.toLocalSongDetail(sourceType))
    }

    suspend fun deleteSong(key: String) {
        songDetailLocalDataSource.deleteSong(key)
    }

    suspend fun deleteAllSongs() {
        songDetailLocalDataSource.deleteAllSongs()
    }
}
