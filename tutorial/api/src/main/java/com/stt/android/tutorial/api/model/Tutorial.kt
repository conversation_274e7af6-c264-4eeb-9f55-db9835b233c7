package com.stt.android.tutorial.api.model

import android.os.Parcelable
import androidx.annotation.DrawableRes
import androidx.annotation.RawRes
import androidx.annotation.StringRes
import kotlinx.parcelize.Parcelize

@Parcelize
data class Tutorial(
    @DrawableRes val image: Int?,
    @RawRes val video: Int?,
    @StringRes val title: Int,
    @StringRes val description: Int,
    @StringRes val primaryButton: Int?,
    @StringRes val secondaryButton: Int?,
) : Parcelable
