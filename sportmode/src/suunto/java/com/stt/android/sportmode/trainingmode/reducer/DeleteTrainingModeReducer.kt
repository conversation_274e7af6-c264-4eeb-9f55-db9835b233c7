package com.stt.android.sportmode.trainingmode.reducer

import com.stt.android.sportmode.datasource.RunSportModesApi
import com.stt.android.sportmode.datasource.mapper.DataScreenOutMapper
import com.stt.android.sportmode.trainingmode.TrainingMode
import com.stt.android.sportmode.trainingmode.TrainingModeReducer
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import javax.inject.Inject

class DeleteTrainingModeReducer @Inject constructor(
    private val runSportModesApi: RunSportModesApi,
    private val dataScreenOutMapper: DataScreenOutMapper,
) : TrainingModeReducer {
    override suspend fun invoke(trainingMode: TrainingMode): TrainingMode = coroutineScope {
        val deleteModeDeferred = async {
            runSportModesApi.deleteTrainingMode(trainingMode.sportId, trainingMode.sportTag, trainingMode.id)
        }
        val deleteScreensDeferred = async {
            val dataScreenResponse = dataScreenOutMapper(trainingMode.dataScreenList)
            runSportModesApi.deleteDataScreens(trainingMode.sportId, trainingMode.sportTag, trainingMode.id, dataScreenResponse)
        }
        deleteModeDeferred.await()
        deleteScreensDeferred.await()
        trainingMode.copy(deleted = true)
    }
}
