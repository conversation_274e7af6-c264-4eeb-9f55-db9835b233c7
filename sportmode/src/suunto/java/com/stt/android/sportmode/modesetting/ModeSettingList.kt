package com.stt.android.sportmode.modesetting

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.key
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.home.settings.wheel.WheelPickerColumn
import com.stt.android.home.settings.wheel.WheelPickerData
import com.stt.android.sportmode.modesetting.list.KeyValueItem
import com.stt.android.sportmode.modesetting.list.KeyValueItemView
import com.stt.android.sportmode.modesetting.list.RadioButtonFullItem
import com.stt.android.sportmode.modesetting.list.RadioButtonFullItemView
import com.stt.android.sportmode.modesetting.list.RadioButtonItem
import com.stt.android.sportmode.modesetting.list.RadioButtonItemView
import com.stt.android.sportmode.modesetting.list.RadioButtonTipsItem
import com.stt.android.sportmode.modesetting.list.RadioButtonTipsItemView
import com.stt.android.sportmode.modesetting.list.RadioButtonWheelItem
import com.stt.android.sportmode.modesetting.list.RadioButtonWheelItemView
import com.stt.android.sportmode.modesetting.list.SwitchItem
import com.stt.android.sportmode.modesetting.list.SwitchItemView
import com.stt.android.sportmode.modesetting.list.TipsItem
import com.stt.android.sportmode.modesetting.list.TipsItemView
import com.stt.android.sportmode.modesetting.list.TitleItem
import com.stt.android.sportmode.modesetting.list.TitleItemView

@SuppressLint("InflateParams")
@Composable
fun ModeSettingList(
    list: List<ModeSettingItem>,
    modifier: Modifier = Modifier,
) {
    LazyColumn(modifier = modifier) {
        items(
            count = list.size,
        ) { index ->
            val item = list[index]
            key(item.listKey()) {
                when (item) {
                    is SwitchItem -> SwitchItemView(item = item)
                    is KeyValueItem -> KeyValueItemView(item = item)
                    is TitleItem -> TitleItemView(item = item)
                    is TipsItem -> TipsItemView(item = item)
                    is RadioButtonItem -> RadioButtonItemView(
                        item = item,
                        modifier = Modifier.padding(start = 20.dp)
                    )

                    is RadioButtonWheelItem -> RadioButtonWheelItemView(item = item)
                    is RadioButtonTipsItem -> RadioButtonTipsItemView(item = item)
                    is RadioButtonFullItem -> RadioButtonFullItemView(item = item)
                }
            }
        }
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun ModeSettingItemListPreview() {
    val list = listOf(
        SwitchItem(
            title = "Switch Item",
            description = "Switch Item description1, Switch Item description2, Switch Item description3, Switch Item description4, ",
            checked = true,
            onCheck = {}
        ),
        SwitchItem(
            title = "Switch Item without description",
            description = "",
            checked = true,
            onCheck = {}
        ),
        KeyValueItem(
            key = "Key",
            value = "Value",
            onClick = {}
        ),
        TitleItem(
            index = 0,
            title = "Title Item 1"
        ),
        TipsItem(
            tips = "Tips Item"
        ),
        TitleItem(
            index = 1,
            title = "Title Item 2"
        ),
        RadioButtonItem(
            index = 0,
            title = "Radio Button Item",
            checked = true,
            onCheck = { _, _, _ -> }
        ),
        RadioButtonFullItem(
            index = 0,
            title = "Radio Button Item with tips",
            tips = "This is a tips, This is a tips, This is a tips, This is a tips, ",
            checked = true,
            onCheck = { _ -> },
        ),
        RadioButtonWheelItem(
            index = 0,
            title = "Radio Button Wheel Item",
            checked = true,
            wheelPickerData = WheelPickerData(
                columns = listOf(
                    WheelPickerColumn(
                        textList = (0..10).map { "$it" },
                        defaultIndex = 2,
                        unit = ":",
                        onSelect = {}
                    ),
                    WheelPickerColumn(
                        textList = (0..10).map { "$it" },
                        defaultIndex = 2,
                        unit = ":",
                        onSelect = {}
                    ),
                    WheelPickerColumn(
                        textList = (1..90).map { ".$it" },
                        defaultIndex = 10,
                        unit = "km",
                        onSelect = {}
                    )
                ),
            ),
            onCheck = {}
        ),
        RadioButtonTipsItem(
            index = 0,
            title = "Radio Button Tips Item",
            tips = "tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips tips ",
            checked = true,
            onCheck = {}
        )
    )

    AppTheme {
        ModeSettingList(list = list)
    }
}
