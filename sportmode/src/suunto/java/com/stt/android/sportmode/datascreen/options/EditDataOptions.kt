package com.stt.android.sportmode.datascreen.options

import android.os.Parcelable
import com.stt.android.infomodel.DataOptionMapping
import com.stt.android.sportmode.datascreen.DataScreenComponent
import kotlinx.parcelize.Parcelize

@Parcelize
data class EditDataOptions(
    val activityId: Int,
    val dataOptionId: String,
    val focusedHeaderIndex: Int = 0,
) : Parcelable {
    fun selectedMapping() = DataOptionMapping.entries.first { it.valueId == dataOptionId }

    fun mapComponent(isImperial: Boolean): DataScreenComponent {
        val mapping = DataOptionMapping.entries.first { it.valueId == dataOptionId }
        return DataScreenComponent.fromMapping(mapping, isImperial)
    }
}
