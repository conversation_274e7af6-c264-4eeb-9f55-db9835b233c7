package com.stt.android.sportmode.modesetting.intensitytarget.reducer

import android.content.Context
import android.view.LayoutInflater
import android.widget.RadioGroup
import androidx.appcompat.app.AlertDialog
import androidx.core.view.children
import androidx.core.view.isVisible
import com.stt.android.sportmode.R
import com.stt.android.sportmode.modesetting.intensitytarget.IntensityTarget
import com.stt.android.sportmode.modesetting.intensitytarget.IntensityTargetReducer
import com.stt.android.sportmode.modesetting.intensitytarget.ZoneType
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class IntensityTargetSelectTypeReducer(private val context: Context) : IntensityTargetReducer {
    override suspend fun invoke(target: IntensityTarget): IntensityTarget {
        val zoneType = suspendCoroutine { continuation ->
            val view = LayoutInflater.from(context)
                .inflate(R.layout.dialog_mode_setting_select_indensity_type, null)
            val group = view.findViewById<RadioGroup>(R.id.radioGroup)
            group.check(
                radioButtonIdMapping.keys.first { radioButtonIdMapping[it] == target.zoneType::class.java }
            )
            group.children.forEach { radioButton ->
                radioButton.isVisible =
                    radioButtonIdMapping[radioButton.id] in target.zoneTypes.map { it::class.java }
            }
            val dialog = AlertDialog.Builder(context)
                .setTitle(R.string.intensity_target_type)
                .setView(view)
                .setOnCancelListener {
                    continuation.resume(
                        target.zoneTypes.first { it::class.java == radioButtonIdMapping[group.checkedRadioButtonId] }
                    )
                }
                .create()
            dialog.show()
            group.setOnCheckedChangeListener { _, _ -> dialog.cancel() }
        }
        return target.copy(
            zoneTypes = target.zoneTypes.map {
                if (it.name == zoneType.name) {
                    when (it) {
                        is ZoneType.HeartRate -> it.copy(checked = true)
                        is ZoneType.Pace -> it.copy(checked = true)
                        is ZoneType.Speed -> it.copy(checked = true)
                        is ZoneType.Cadence -> it.copy(checked = true)
                        is ZoneType.Power -> it.copy(checked = true)
                    }
                } else if (it.checked) {
                    when (it) {
                        is ZoneType.HeartRate -> it.copy(checked = false)
                        is ZoneType.Pace -> it.copy(checked = false)
                        is ZoneType.Speed -> it.copy(checked = false)
                        is ZoneType.Cadence -> it.copy(checked = false)
                        is ZoneType.Power -> it.copy(checked = false)
                    }
                } else {
                    it
                }
            }
        )
    }

    companion object {
        val radioButtonIdMapping = mapOf(
            R.id.heartRate to ZoneType.HeartRate::class.java,
            R.id.pace to ZoneType.Pace::class.java,
            R.id.speed to ZoneType.Speed::class.java,
            R.id.cadence to ZoneType.Cadence::class.java,
            R.id.power to ZoneType.Power::class.java
        )
    }
}
