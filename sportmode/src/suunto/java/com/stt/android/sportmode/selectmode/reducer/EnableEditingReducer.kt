package com.stt.android.sportmode.selectmode.reducer

import com.stt.android.sportmode.selectmode.ModeHeaderListReducer
import com.stt.android.sportmode.selectmode.TrainingModeHeader
import com.stt.android.sportmode.selectmode.TrainingModeHeaderState
import javax.inject.Inject

class EnableEditingReducer @Inject constructor() : ModeHeaderListReducer {
    override suspend fun invoke(oldList: List<TrainingModeHeader>): List<TrainingModeHeader> {
        return oldList.map {
            if (it.state == TrainingModeHeaderState.IDLE) {
                it.copy(state = TrainingModeHeaderState.EDITING)
            } else {
                it
            }
        }
    }
}
