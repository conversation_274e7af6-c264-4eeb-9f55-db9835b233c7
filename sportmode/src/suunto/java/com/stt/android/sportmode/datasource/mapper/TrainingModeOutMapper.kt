package com.stt.android.sportmode.datasource.mapper

import com.amersports.formatter.Unit
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.domain.user.DISTANCE_PER_SECOND_TO_DISTANCE_PER_MINUTE
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.sportmode.entity.EntityMapperConstants.AUTOLAP_TYPE_DISTANCE
import com.stt.android.sportmode.entity.EntityMapperConstants.AUTOLAP_TYPE_DURATION
import com.stt.android.sportmode.entity.EntityMapperConstants.AUTOLAP_TYPE_LOCATION
import com.stt.android.sportmode.entity.EntityMapperConstants.PACE_RUNNER_STATUS_HIDE
import com.stt.android.sportmode.entity.EntityMapperConstants.PACE_RUNNER_STATUS_SHOW
import com.stt.android.sportmode.entity.EntityMapperConstants.STATUS_HIDE
import com.stt.android.sportmode.entity.EntityMapperConstants.STATUS_OFF
import com.stt.android.sportmode.entity.EntityMapperConstants.STATUS_ON
import com.stt.android.sportmode.entity.TrainingModeTarget
import com.stt.android.sportmode.modesetting.Mapper
import com.stt.android.sportmode.modesetting.autolap.Autolap
import com.stt.android.sportmode.modesetting.autolap.AutolapType
import com.stt.android.sportmode.modesetting.intensitytarget.IntensityTarget
import com.stt.android.sportmode.modesetting.pacerunner.PaceRunner as UiPaceRunner
import com.stt.android.sportmode.modesetting.reminder.Frequency
import com.stt.android.sportmode.modesetting.reminder.RemindInType
import com.stt.android.sportmode.modesetting.secondsPerMinute
import com.stt.android.sportmode.trainingmode.TrainingMode
import com.stt.android.sportmode.trainingmode.TrainingModeEditViewModel.Companion.MAX_RIVAL_NAME_BYTE_COUNT
import com.stt.android.sportmode.trainingmode.formatOutAscent
import com.stt.android.sportmode.trainingmode.formatOutDistance
import com.stt.android.sportmode.trainingmode.formatOutZoneTypeValue
import com.stt.android.utils.limitByteSizeAsUTF8
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.runsportmodes.TrainingModeResponse
import com.suunto.connectivity.runsportmodes.entities.AutoLap
import com.suunto.connectivity.runsportmodes.entities.ChallengeData
import com.suunto.connectivity.runsportmodes.entities.Intensity
import com.suunto.connectivity.runsportmodes.entities.IntensityZone
import com.suunto.connectivity.runsportmodes.entities.Metronome
import com.suunto.connectivity.runsportmodes.entities.PaceRunner
import com.suunto.connectivity.runsportmodes.entities.Reminder
import com.suunto.connectivity.runsportmodes.entities.StructTrain
import com.suunto.connectivity.runsportmodes.entities.TrainingModeData
import javax.inject.Inject

class TrainingModeOutMapper @Inject constructor(
    private val measurementUnit: MeasurementUnit,
    private val unitConverter: JScienceUnitConverter,
    private val suuntoWatchModel: SuuntoWatchModel,
) : Mapper<TrainingMode, TrainingModeResponse> {
    override suspend fun invoke(trainingMode: TrainingMode): TrainingModeResponse {
        val supportPaceRunnerSetting = suuntoWatchModel.supportsRunOta2(false)
        return buildTrainingModeResponse(trainingMode, supportPaceRunnerSetting)
    }

    private fun buildTrainingModeResponse(
        trainingMode: TrainingMode,
        supportPaceRunnerSetting: Boolean,
    ): TrainingModeResponse {
        return with(trainingMode) {
            TrainingModeResponse(
                trainingBaseMode = baseModeId,
                modeId = id,
                modeName = title,
                trainingModeData = TrainingModeData(
                    time = (modeTarget as? TrainingModeTarget.DurationTarget)?.defaultValue?.let { it / secondsPerMinute }
                        ?: 0,
                    distance = run {
                        val formattedMeter = (modeTarget as? TrainingModeTarget.DistanceTarget)?.defaultValue ?: 0f
                        formattedMeter.formatOutDistance(measurementUnit)
                    },
                    calorie = (modeTarget as? TrainingModeTarget.CalorieTarget)?.defaultValue ?: 0,
                    ascent = run {
                        val value = (modeTarget as? TrainingModeTarget.AscentTarget)?.defaultValue ?: 0f
                        value.formatOutAscent(measurementUnit)
                    },
                    pace = run {
                        val seconds = (modeTarget as? TrainingModeTarget.GhostPace)?.defaultValue ?: 0f
                        (seconds * measurementUnit.toDistanceUnit(1000.0)).toFloat()
                    },
                    challengeData = mapChallengeData(),
                    structTrain = StructTrain(),
                    intensity = mapIntensity(),
                    autoLap = mapAutoLap(),
                    reminder = mapReminder(),
                    metronome = mapMetronome(),
                    intensityZone = mapIntensityZone(),
                    paceRunner = if (!supportPaceRunnerSetting) null else mapPaceRunner(),
                    display = trainingMode.showDisplay,
                )
            )
        }
    }

    private fun TrainingMode.mapChallengeData(): ChallengeData =
        targetWorkoutCardInfo?.workoutHeader?.let {
            val distance = if (it.totalDistance > 0) {
                unitConverter.convert(it.totalDistance, Unit.M, Unit.KM)
            } else 0.0
            val pace = if (it.avgSpeed > 0) {
                unitConverter.convert(
                    it.avgSpeed,
                    Unit.M_PER_S,
                    Unit.S_PER_KM
                ) / DISTANCE_PER_SECOND_TO_DISTANCE_PER_MINUTE
            } else 0.0
            val rivalName = targetWorkoutCardInfo.user.realNameOrUsername
            ChallengeData(
                distance = distance,
                pace = pace,
                rivalName = rivalName.limitByteSizeAsUTF8(MAX_RIVAL_NAME_BYTE_COUNT).toString()
            )
        } ?: challengeData ?: ChallengeData()

    private fun TrainingMode.mapReminder(): Reminder {
        val reminder = modeSettingList
            .filterIsInstance<com.stt.android.sportmode.modesetting.reminder.Reminder>()
            .firstOrNull()
        if (reminder == null || reminder.types.size != 3) return Reminder()
        val visibleSubItem = reminder.types
            .filter { it.supported }
            .map { it.reminderType.toString() }
            .reduce { acc, s -> "$acc$s" }
        return Reminder(
            status = if (reminder.types.any { it.enable }) STATUS_ON else STATUS_OFF,
            bDrinkingOpen = reminder.types[0].enable,
            bDrinkRepeat = reminder.types[0].frequency == Frequency.REPEAT,
            drinkingTime = (reminder.types[0].remindInType as RemindInType.Duration).seconds,
            bSupplyOpen = reminder.types[1].enable,
            bSupplyRepeat = reminder.types[1].frequency == Frequency.REPEAT,
            supplyTime = (reminder.types[1].remindInType as RemindInType.Duration).seconds,
            bReturnOpen = reminder.types[2].enable,
            returnType = if (reminder.types[2].remindInType is RemindInType.Duration) 0 else 1,
            returnData = if (reminder.types[2].remindInType is RemindInType.Duration) {
                (reminder.types[2].remindInType as RemindInType.Duration).seconds.toFloat()
            } else {
                val value = (reminder.types[2].remindInType as RemindInType.Distance).value
                val meters = value.formatOutDistance(measurementUnit) * 1000
                meters / 100 // unit is 100m
            },
            visibleSubItem = visibleSubItem,
        )
    }

    private fun TrainingMode.mapIntensityZone(): IntensityZone {
        val intensityZone = modeSettingList
            .filterIsInstance<com.stt.android.sportmode.modesetting.intensityzones.IntensityZone>()
            .firstOrNull()
            ?: return IntensityZone()
        return IntensityZone(
            status = intensityZone.let { if (it.enable) STATUS_ON else STATUS_OFF },
            zone = if (intensityZone.enable) {
                intensityZone.zoneType.zoneId
            } else {
                0
            },
            visibleSubItem = intensityZone.visibleSubItem,
        )
    }

    private fun TrainingMode.mapMetronome(): Metronome {
        val metronome = modeSettingList
            .filterIsInstance<com.stt.android.sportmode.modesetting.metronome.Metronome>()
            .firstOrNull()
        return Metronome(
            status = metronome?.let { if (it.enable) STATUS_ON else STATUS_OFF } ?: STATUS_HIDE,
            beatType = metronome?.feedback?.ordinal ?: 0,
            beatCount = metronome?.tempo ?: 0,
        )
    }

    private fun TrainingMode.mapAutoLap(): AutoLap {
        val autolap = modeSettingList
            .filterIsInstance<Autolap>()
            .firstOrNull()
            ?: return AutoLap()
        val autolapType = autolap.autolapType
        var data = 0f
        val enum = when (autolapType) {
            is AutolapType.Distance -> {
                data = autolapType.meters / 100 // unit is 100m
                AUTOLAP_TYPE_DISTANCE
            }

            is AutolapType.Duration -> {
                data = autolapType.seconds.toFloat()
                AUTOLAP_TYPE_DURATION
            }

            is AutolapType.Location -> AUTOLAP_TYPE_LOCATION
        }
        val visibleSubItem = autolap.autolapTypes
            .map { it.autolapTypeId.toString() }
            .reduce { acc, s -> "$acc$s" }
        return AutoLap(
            status = if (autolap.enable) STATUS_ON else STATUS_OFF,
            autolapEnum = enum,
            data = data,
            visibleSubItem = visibleSubItem
        )
    }

    private fun TrainingMode.mapIntensity(): Intensity {
        val intensityTarget = modeSettingList
            .filterIsInstance<IntensityTarget>()
            .firstOrNull()
            ?: return Intensity()
        val zoneType = intensityTarget.zoneType
        val zone = zoneType.zones.getOrNull(zoneType.checkedZoneIndex) ?: return Intensity()
        val visibleSubItem = intensityTarget.zoneTypes
            .map { it.zoneTypeId.toString() }
            .reduce { acc, s -> "$acc$s" }
        return Intensity(
            status = intensityTarget.let { if (it.enable) STATUS_ON else STATUS_OFF },
            intensityEnum = zoneType.zoneTypeId,
            zoneIndex = zoneType.reverseZoneIndex(zoneType.checkedZoneIndex),
            min = (zone.range.first).formatOutZoneTypeValue(zoneType, measurementUnit),
            max = (zone.range.second).formatOutZoneTypeValue(zoneType, measurementUnit),
            visibleSubItem = visibleSubItem
        )
    }

    private fun TrainingMode.mapPaceRunner(): PaceRunner {
        val paceRunner = modeSettingList
            .filterIsInstance<UiPaceRunner>()
            .firstOrNull()
            ?: return PaceRunner(
                status = PACE_RUNNER_STATUS_HIDE,
                mode = 0,
            )
        return PaceRunner(
            status = PACE_RUNNER_STATUS_SHOW,
            mode = paceRunner.paceMode.ordinal,
        )
    }
}
