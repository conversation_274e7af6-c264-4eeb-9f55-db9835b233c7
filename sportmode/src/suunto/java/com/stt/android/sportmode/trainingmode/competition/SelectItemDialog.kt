package com.stt.android.sportmode.trainingmode.competition

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.AlertDialog
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyMegaBold
import com.stt.android.compose.theme.bodyXLarge
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.sportmode.R

@Composable
fun SelectItemDialog(
    title: String,
    items: List<String>,
    onDismissRequest: () -> Unit,
    selectedItem: String,
    onItemSelected: (Int) -> Unit,
) {
    AlertDialog(
        onDismissRequest = onDismissRequest,
        title = {
            Text(
                text = title,
                modifier = Modifier.padding(start = MaterialTheme.spacing.smaller),
                style = MaterialTheme.typography.bodyMegaBold
            )
        },
        text = {
            Column {
                items.forEachIndexed { index, item ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                onItemSelected(index)
                            },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = item == selectedItem,
                            onClick = { onItemSelected(index) },
                            colors = RadioButtonDefaults.colors(
                                selectedColor = MaterialTheme.colors.primary,
                                unselectedColor = MaterialTheme.colors.darkGrey
                            )
                        )
                        Text(
                            text = item,
                            modifier = Modifier.padding(start = MaterialTheme.spacing.small),
                            color = MaterialTheme.colors.nearBlack,
                            style = MaterialTheme.typography.bodyXLarge,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        },
        confirmButton = {},
        shape = RectangleShape
    )
}

@Preview
@Composable
private fun PreviewDistanceDialog() {
    AppTheme {
        Surface {
            SelectItemDialog(
                title = stringResource(id = com.stt.android.R.string.distance),
                items = listOf(
                    stringResource(id = R.string.competition_target_distance_all),
                    stringResource(id = R.string.competition_target_distance_filter_5),
                    stringResource(id = R.string.competition_target_distance_filter_5_10),
                    stringResource(id = R.string.competition_target_distance_filter_10_21),
                    stringResource(id = R.string.competition_target_distance_filter_21_45),
                    stringResource(id = R.string.competition_target_distance_filter_45)
                ),
                onDismissRequest = { },
                selectedItem = stringResource(id = R.string.competition_target_distance_all),
                onItemSelected = { index -> },
            )
        }
    }
}

@Preview
@Composable
private fun PreviewPostDialog() {
    AppTheme {
        Surface {
            SelectItemDialog(
                title = stringResource(id = R.string.competition_target_sort_by_title),
                items = listOf(
                    stringResource(id = R.string.competition_target_filter_sort_post_time),
                    stringResource(id = R.string.competition_target_filter_sort_pace),
                    stringResource(id = R.string.competition_target_filter_sort_duration),
                ),
                onDismissRequest = { },
                selectedItem = stringResource(id = R.string.competition_target_filter_sort_post_time),
                onItemSelected = { index -> },
            )
        }
    }
}
