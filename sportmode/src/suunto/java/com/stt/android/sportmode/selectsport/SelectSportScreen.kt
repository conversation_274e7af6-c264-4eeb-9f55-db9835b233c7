package com.stt.android.sportmode.selectsport

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.ListItem
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.SnackbarHost
import androidx.compose.material.SnackbarHostState
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.stt.android.R
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.sportmode.composables.WatchStateSnackbar
import com.stt.android.sportmode.home.SportHeader
import com.stt.android.sportmode.home.mapSportHeader
import com.stt.android.sportmode.modesetting.sportStr
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toPersistentList
import com.stt.android.R as BR

@Composable
fun SelectSportDialog(
    sportHeaderList: ImmutableList<SportHeader>,
    onDismiss: () -> Unit,
    onSportSelected: (SportHeader) -> Unit,
    viewModel: SelectSportViewModel = hiltViewModel(),
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false,
        )
    ) {
        LaunchedEffect(Unit) {
            viewModel.initialSportHeaderList = sportHeaderList
            viewModel.retrieveRecentSports()
        }

        val sportHeaderMap by viewModel.sportHeadersFlow.collectAsState()
        val unableToEditReason by viewModel.sportModeConnectionWrapper.unableToEditReasonFlow.collectAsState(null)

        SelectSportContent(
            sportHeaderMap = sportHeaderMap,
            onItemClick = { sportHeader ->
                onSportSelected(sportHeader)
            },
            onBackClick = {
                onDismiss()
            },
            unableToEditReason = unableToEditReason,
        )
    }
}

@Composable
fun SelectSportContent(
    sportHeaderMap: Map<Int, List<SportHeader>>,
    onBackClick: () -> Unit,
    onItemClick: (SportHeader) -> Unit,
    modifier: Modifier = Modifier,
    unableToEditReason: Int? = null,
) {
    val systemUiController = rememberSystemUiController()
    val statusColor = MaterialTheme.colors.secondary
    LaunchedEffect(Unit) {
        systemUiController.setStatusBarColor(
            color = statusColor,
            darkIcons = false,
        )
        systemUiController.setNavigationBarColor(
            color = statusColor,
            darkIcons = true,
        )
    }
    val snackBarHostState = remember { SnackbarHostState() }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = stringResource(BR.string.sport_modes_new_custom_mode_title).uppercase(),
                        color = MaterialTheme.colors.onSecondary
                    )
                },
                navigationIcon = {
                    SuuntoIconButton(
                        icon = SuuntoIcons.ActionClose,
                        onClick = onBackClick,
                        contentDescription = stringResource(R.string.back),
                        tint = Color.White,
                    )
                },
                backgroundColor = MaterialTheme.colors.secondary,
            )
        },
        snackbarHost = {
            SnackbarHost(hostState = snackBarHostState)
        },
        modifier = modifier,
    ) { paddingValues ->
        SportHeaderList(
            sportHeaderMap = sportHeaderMap,
            unableToEditReason = unableToEditReason,
            onItemClick = onItemClick,
            modifier = Modifier
                .padding(paddingValues)
                .background(MaterialTheme.colors.surface)
                .fillMaxSize()
                .narrowContent(),
        )

        WatchStateSnackbar(unableToEditReason, snackBarHostState)
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun SportHeaderList(
    sportHeaderMap: Map<Int, List<SportHeader>>,
    unableToEditReason: Int?,
    onItemClick: (SportHeader) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier,
    ) {
        sportHeaderMap.forEach { (title, list) ->

            if (list.any()) {
                item {
                    Text(
                        text = stringResource(title),
                        style = MaterialTheme.typography.bodyLargeBold,
                        modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    )
                    Divider()
                }
            }

            items(list.size) { index ->
                val sportItem = list[index]
                ListItem(
                    icon = {
                        SuuntoActivityIcon(
                            iconRes = sportItem.iconId,
                            tint = MaterialTheme.colors.onSurface,
                            background = Color.Transparent,
                            iconSize = MaterialTheme.iconSizes.large,
                        )
                    },
                    text = {
                        Text(
                            text = stringResource(id = sportItem.titleId),
                            style = MaterialTheme.typography.bodyLargeBold,
                        )
                    },
                    modifier = Modifier
                        .alpha(if (unableToEditReason == null) 1f else 0.5f)
                        .clickableThrottleFirst(enabled = unableToEditReason == null) { onItemClick(sportItem) },
                )
                Divider()
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun SelectSportItemDialogPreview() {
    AppTheme {
        val all = ActivityType.values().map { it.mapSportHeader() }.toPersistentList()
        SelectSportContent(
            sportHeaderMap = mapOf(
                sportStr.sport_mode_recent to all.take(4),
                sportStr.sport_mode_popular to all,
            ),
            onBackClick = {},
            onItemClick = {},
        )
    }
}
