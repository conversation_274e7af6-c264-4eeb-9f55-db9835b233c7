package com.stt.android.sportmode.modesetting.reminder.reducer

import android.content.Context
import android.view.LayoutInflater
import android.widget.RadioGroup
import androidx.appcompat.app.AlertDialog
import com.stt.android.sportmode.R
import com.stt.android.sportmode.modesetting.reminder.Frequency
import com.stt.android.sportmode.modesetting.reminder.Reminder
import com.stt.android.sportmode.modesetting.reminder.ReminderReducer
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class ReminderSelectFrequencyReducer(private val context: Context, private val index: Int) : ReminderReducer {
    override suspend fun invoke(reminder: <PERSON>mind<PERSON>): Reminder {
        val reminderType = reminder.types[index]
        if (reminderType.frequency == Frequency.DISABLE) return reminder
        val frequency = suspendCoroutine { continuation ->
            val view = LayoutInflater.from(context).inflate(R.layout.dialog_mode_setting_select_reminder_frequency, null)
            val group = view.findViewById<RadioGroup>(R.id.radioGroup)
            group.check(
                when (reminderType.frequency) {
                    Frequency.REPEAT -> R.id.repeat
                    else -> R.id.once
                }
            )
            val dialog = AlertDialog.Builder(context)
                .setTitle(R.string.reminder_frequency)
                .setView(view)
                .setOnCancelListener {
                    continuation.resume(
                        when (group.checkedRadioButtonId) {
                            R.id.repeat -> Frequency.REPEAT
                            else -> Frequency.ONCE
                        }
                    )
                }
                .create()
            dialog.show()
            group.setOnCheckedChangeListener { _, _ -> dialog.cancel() }
        }
        return reminder.copy(
            types = reminder.types.map {
                if (it == reminderType) {
                    it.copy(
                        frequency = frequency
                    )
                } else {
                    it
                }
            }
        )
    }
}
