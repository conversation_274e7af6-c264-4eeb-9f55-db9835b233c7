package com.stt.android.sportmode.modesetting.list

import android.content.Context
import com.stt.android.sportmode.modesetting.ModeSettingItem
import com.stt.android.home.settings.wheel.WheelPickerData

data class SwitchItem(
    val title: String,
    val description: String,
    val checked: <PERSON>olean,
    val onCheck: () -> Unit,
) : ModeSettingItem {
    override fun listKey(): Any {
        return "SwitchItem $title"
    }
}

data class KeyValueItem(
    val key: String,
    val value: String,
    val onClick: (context: Context) -> Unit,
) : ModeSettingItem

data class TitleItem(
    val index: Int = 0,
    val title: String,
) : ModeSettingItem

data class TipsItem(
    val tips: String,
) : ModeSettingItem

data class RadioButtonItem(
    val index: Int,
    val title: String,
    val checked: Boolean,
    val onCheck: (context: Context, index: Int, duplicated: Boolean) -> Unit,
) : ModeSettingItem {
    override fun listKey(): Any {
        return "RadioButtonItem $title"
    }
}

data class RadioButtonWheelItem(
    val index: Int,
    val title: String,
    val checked: Boolean,
    val wheelPickerData: WheelPickerData,
    val onCheck: (index: Int) -> Unit,
) : ModeSettingItem {
    override fun listKey(): Any {
        return "RadioButtonWheelItem $title"
    }
}

data class RadioButtonTipsItem(
    val index: Int,
    val title: String,
    val tips: String,
    val checked: Boolean,
    val onCheck: (index: Int) -> Unit,
) : ModeSettingItem {
    override fun listKey(): Any {
        return "RadioButtonTipsItem $title"
    }
}

data class RadioButtonFullItem(
    val index: Int,
    val title: String,
    val tips: String,
    val checked: Boolean,
    val onCheck: (index: Int) -> Unit,
) : ModeSettingItem {
    override fun listKey(): Any {
        return "RadioButtonFullItem $title"
    }
}
