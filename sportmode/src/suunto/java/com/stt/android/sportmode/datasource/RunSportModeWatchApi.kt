package com.stt.android.sportmode.datasource

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.await
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.infomodel.getMcIdForStId
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.mdsapi.apis.RunSportModesMdsApiV2Consumer
import com.suunto.connectivity.runsportmodes.GetDataScreenResponse
import com.suunto.connectivity.runsportmodes.RunSportModesConsumer
import com.suunto.connectivity.runsportmodes.TrainingModeResponse
import com.suunto.connectivity.runsportmodes.entities.Challenge
import com.suunto.connectivity.runsportmodes.entities.SportHeaderEntity
import com.suunto.connectivity.runsportmodes.entities.TrainingModeHeaderEntity
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.UUID
import javax.inject.Inject

class RunSportModeWatchApi @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : RunSportModesApi {

    override suspend fun fetchRecentSportIdTagPairs(): List<Pair<Int, Int?>> {
        val ota2Enabled = suuntoWatchModel.supportsRunOta2(false)
        return invokeApiV2 { consumer, macAddress ->
            consumer.getAllSports(macAddress, ota2Enabled).data?.arrayData?: emptyList()
        }.mapNotNull { entity ->
            getStIdForMcId(entity.sportId)?.let { id ->
                id to entity.sportTag?.takeIfOta2Enabled()
            }
        }
    }

    override suspend fun fetchAllSportItems(): List<SportHeaderEntity> {
        val ota2Enabled = suuntoWatchModel.supportsRunOta2(false)
        return invokeApi { consumer, macAddress ->
            consumer.getAllSportHeaders(macAddress, ota2Enabled)
        }.mapNotNull { entity ->
            getStIdForMcId(entity.sportId)?.let { entity.copy(sportId = it) }
        }.filter {
            it.sportTag == null || ota2Enabled
        }
    }

    override suspend fun fetchDefaultTrainingModes(sportId: Int, sportTag: Int?): List<TrainingModeHeaderEntity> {
        return invokeApi { consumer, macAddress ->
            consumer.getDefaultTrainingModeList(
                macAddress,
                getMcIdForStId(sportId),
                sportTag,
            )
        }
    }

    override suspend fun fetchTrainingModes(sportId: Int, sportTag: Int?, lastModeId: Int?): List<TrainingModeHeaderEntity> {
        return invokeApi { consumer, macAddress ->
            consumer.getTrainingModeList(
                macAddress,
                getMcIdForStId(sportId),
                lastModeId,
                sportTag,
            )
        }
    }

    override suspend fun fetchDefaultTrainingMode(sportId: Int, sportTag: Int?, modeId: Int): TrainingModeResponse {
        return invokeApi { consumer, macAddress ->
            consumer.getDefaultTrainingMode(
                macAddress,
                getMcIdForStId(sportId),
                modeId,
                sportTag,
            )
        }
    }

    override suspend fun fetchTrainingMode(sportId: Int, sportTag: Int?, modeId: Int): TrainingModeResponse {
        return invokeApi { consumer, macAddress ->
            consumer.getTrainingMode(
                macAddress,
                getMcIdForStId(sportId),
                modeId,
                sportTag,
            )
        }
    }

    override suspend fun createTrainingMode(sportId: Int, sportTag: Int?, trainingMode: TrainingModeResponse): Int {
        val id = UUID.randomUUID().toString().hashCode() and 0x7FFFFFFF
        invokeApi { consumer, macAddress ->
            consumer.postTrainingMode(
                macAddress,
                getMcIdForStId(sportId),
                trainingMode.copy(modeId = id),
                sportTag,
            )
        }
        return id
    }

    override suspend fun updateTrainingMode(sportId: Int, sportTag: Int?, trainingMode: TrainingModeResponse) {
        invokeApi { consumer, macAddress ->
            consumer.putTrainingMode(
                macAddress,
                getMcIdForStId(sportId),
                trainingMode,
                sportTag,
            )
        }
    }

    override suspend fun deleteTrainingMode(sportId: Int, sportTag: Int?, modeId: Int) {
        invokeApi { consumer, macAddress ->
            consumer.deleteTrainingMode(
                macAddress,
                getMcIdForStId(sportId),
                modeId,
                sportTag,
            )
        }
    }

    override suspend fun fetchDefaultDataScreens(
        sportId: Int,
        sportTag: Int?,
        modeBaseId: Int,
    ): GetDataScreenResponse {
        return invokeApi { consumer, macAddress ->
            consumer.getDefaultDataScreenList(
                macAddress,
                getMcIdForStId(sportId),
                modeBaseId,
                sportTag,
            )
        }
    }

    override suspend fun fetchDataScreens(sportId: Int, sportTag: Int?, modeId: Int): GetDataScreenResponse {
        return invokeApi { consumer, macAddress ->
            consumer.getDataScreenList(
                macAddress,
                getMcIdForStId(sportId),
                modeId,
                sportTag,
            )
        }
    }

    override suspend fun createDataScreens(
        sportId: Int,
        sportTag: Int?,
        modeId: Int,
        dataScreens: GetDataScreenResponse
    ) {
        invokeApi { consumer, macAddress ->
            consumer.postDataScreen(
                macAddress,
                getMcIdForStId(sportId),
                modeId,
                dataScreens,
                sportTag,
            )
        }
    }

    override suspend fun updateDataScreens(
        sportId: Int,
        sportTag: Int?,
        modeId: Int,
        dataScreens: GetDataScreenResponse
    ) {
        invokeApi { consumer, macAddress ->
            consumer.putDataScreen(
                macAddress,
                getMcIdForStId(sportId),
                modeId,
                dataScreens,
                sportTag,
            )
        }
    }

    override suspend fun deleteDataScreens(
        sportId: Int,
        sportTag: Int?,
        modeId: Int,
        dataScreens: GetDataScreenResponse
    ) {
        invokeApi { consumer, macAddress ->
            consumer.deleteDataScreen(
                macAddress,
                getMcIdForStId(sportId),
                modeId,
                dataScreens,
                sportTag,
            )
        }
    }

    override suspend fun saveCompetitionInfoTarget(challenge: Challenge) {
        invokeApi { consumer, macAddress ->
            consumer.putCompetitionTarget(
                macAddress,
                challenge
            )
        }
    }

    override suspend fun saveCompetitionSamplesTarget(speedSamples: List<Int>, distanceIncluded: Boolean) {
        // The amount of data may be large, do not apply timeout
        invokeApi { consumer, macAddress ->
            consumer.putCompetitionSamplesTarget(
                macAddress,
                speedSamples,
                distanceIncluded
            )
        }
    }

    override suspend fun supportsCustomizeLapDataScreen(): Boolean {
        return suuntoWatchModel.supportsCustomizeLapDataScreen(false)
    }

    private suspend fun getMacAddressAndConsumer(): Pair<String, RunSportModesConsumer> {
        return suuntoWatchModel.currentWatch.await().let {
            val macAddress = it.suuntoBtDevice.macAddress
            val consumer = it.suuntoRepositoryClient.runSportModesConsumer
            macAddress to consumer
        }
    }

    private suspend inline fun <reified T> invokeApi(
        crossinline apiBlock: suspend (consumer: RunSportModesConsumer, macAddress: String) -> T,
    ): T = withContext(coroutinesDispatchers.io) {
        val (macAddress, consumer) = getMacAddressAndConsumer()
        apiBlock(consumer, macAddress)
    }

    private suspend fun getMacAddressAndConsumerV2(): Pair<String, RunSportModesMdsApiV2Consumer> {
        return suuntoWatchModel.currentWatch.await().let {
            val macAddress = it.suuntoBtDevice.macAddress
            val consumer = it.suuntoRepositoryClient.runSportModesMdsApiV2Consumer
            macAddress to consumer
        }
    }

    private suspend inline fun <reified T> invokeApiV2(
        crossinline apiBlock: suspend (consumer: RunSportModesMdsApiV2Consumer, macAddress: String) -> T,
    ): T = withContext(coroutinesDispatchers.io) {
        val (macAddress, consumer) = getMacAddressAndConsumerV2()
        apiBlock(consumer, macAddress)
    }

    private fun getStIdForMcId(mcId: Int): Int? = ActivityMapping.entries
        .firstOrNull { it.mcId == mcId }
        ?.stId
        ?: run {
            Timber.d("Not supported mcId: $mcId")
            null
        }

    private suspend fun Int.takeIfOta2Enabled() = if (suuntoWatchModel.supportsRunOta2(false)) this else null
}
