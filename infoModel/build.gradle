plugins {
    alias libs.plugins.kotlin.jvm
}

sourceSets {
    kPoetInfoModel {
        kotlin.srcDirs += 'src/kotlinPoet/kotlin'
    }
    kPoetSummaries {
        kotlin.srcDirs += "src/main/java"
        kotlin.srcDirs += "${buildDir}/generated-sources-infomodel/"
    }
    main {
        kotlin.srcDirs += "${buildDir}/generated-sources-infomodel/"
        kotlin.srcDirs += "${buildDir}/generated-sources-summaries/"
    }
}

dependencies {
    kPoetInfoModelImplementation libs.kotlin.stdlib
    kPoetInfoModelImplementation libs.kotlin.poet
    kPoetInfoModelImplementation libs.activitymodes
    kPoetInfoModelImplementation libs.moshi
    kPoetInfoModelImplementation libs.moshi.kotlin
    kPoetInfoModelImplementation libs.moshi.adapters
    kPoetInfoModelImplementation libs.gson

    kPoetSummariesImplementation libs.kotlin.stdlib
    kPoetSummariesImplementation libs.kotlin.poet
    kPoetSummariesImplementation libs.activitymodes
    kPoetSummariesImplementation libs.moshi
    kPoetSummariesImplementation libs.moshi.kotlin
    kPoetSummariesImplementation libs.moshi.adapters
    kPoetSummariesImplementation libs.gson


    //add these only while developing this module to resolve dependencies in generators
//    implementation libs.moshi
//    implementation libs.moshi.adapters
//    implementation libs.kotlin.poet

    implementation libs.gson
    implementation libs.moshi.kotlin
    implementation libs.kotlin.stdlib
    api libs.activitymodes

    testImplementation libs.junit
    testImplementation libs.assertj
}

tasks.register('generateInfoModel', JavaExec) {
    getMainClass().set('com.stt.android.infomodel.InfoModelGenerator')
    classpath = sourceSets.kPoetInfoModel.runtimeClasspath
    def buildDirectory = getLayout().getBuildDirectory().get()
    args "${buildDirectory}/generated-sources-infomodel/"
    jvmArgs += '-noverify'
    def libraryJar = file("${buildDirectory}/libs/infoModel.jar")
    def resources = file("${projectDir}/src/kPoetInfoModel/resources/")
    outputs.upToDateWhen {
        if (!libraryJar.exists()) {
            println("Rebuilding: Missing jar output")
            return false
        } else {
            for (File resourceFile : resources.listFiles()) {
                if (resourceFile.lastModified() > libraryJar.lastModified()) {
                    println("Rebuilding: Json newer than jar.")
                    return false
                }
            }
        }
        return true
    }
}

tasks.register('generateSummaries', JavaExec) {
    getMainClass().set('com.stt.android.infomodel.SummariesGenerator')
    classpath = sourceSets.kPoetSummaries.runtimeClasspath
    def buildDirectory = getLayout().getBuildDirectory().get()
    args "${buildDirectory}/generated-sources-summaries/"
    jvmArgs += '-noverify'
    def libraryJar = file("${buildDirectory}/libs/infoModel.jar")
    def resourcesInfoModel = file("${projectDir}/src/kPoetInfoModel/resources/")
    def resourcesSummaries = file("${projectDir}/src/kPoetSummaries/resources/")
    def resourceFiles = resourcesInfoModel.listFiles() + resourcesSummaries.listFiles()
    outputs.upToDateWhen {
        if (!libraryJar.exists()) {
            println("Rebuilding: Missing jar output")
            return false
        } else {
            for (File resourceFile : resourceFiles) {
                if (resourceFile.lastModified() > libraryJar.lastModified()) {
                    println("Rebuilding: Json newer than jar.")
                    return false
                }
            }
        }
        return true
    }
}

// we have dependency also at compile-level between these tasks
compileKPoetSummariesKotlin.dependsOn(generateInfoModel)
generateSummaries.dependsOn(generateInfoModel)

compileTestJava.dependsOn(generateSummaries)
compileKotlin.dependsOn(generateSummaries)

