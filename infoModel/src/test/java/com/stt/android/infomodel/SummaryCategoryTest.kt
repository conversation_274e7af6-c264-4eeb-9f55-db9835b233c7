package com.stt.android.infomodel

import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class SummaryCategoryTest {

    @Test
    fun `summary items have only one category`() {
        SummaryItem.entries
            .filterNot { it == SummaryItem.NONE }
            .forEach { summaryItem ->
                assertThat(
                    categoriesEntryCountForSummaryItem(summaryItem)
                ).withFailMessage(
                    "Error with summaryItem: $summaryItem" +
                        " has ${categoriesEntryCountForSummaryItem(summaryItem)} entry count"
                ).isEqualTo(1)
            }
    }

    private fun categoriesEntryCountForSummaryItem(summaryItem: SummaryItem): Int {
        return SummaryCategoryMap.entries
            .filter { it.value.contains(summaryItem) }
            .size
    }
}
