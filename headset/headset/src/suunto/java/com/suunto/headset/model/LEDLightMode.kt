package com.suunto.headset.model

import androidx.annotation.StringRes
import com.suunto.soa.ble.control.attr.SportsMode

enum class LEDLightMode {
    CONSTANT_LIGHT,
    BLINKING,
    SOS,
    CADENCE_SYNC,
    TURN_BRAKE,
}

fun LEDLightMode.toSoaSportMode(): SportsMode {
    return when (this) {
        LEDLightMode.CONSTANT_LIGHT -> SportsMode.TEAM // SOA doesn't have a direct mapping for CONSTANT_LIGHT
        LEDLightMode.BLINKING -> SportsMode.LEADER // SOA doesn't have a direct mapping for FLASHING
        LEDLightMode.SOS -> SportsMode.SOS // SOA doesn't have a direct mapping for SOS
        LEDLightMode.CADENCE_SYNC -> SportsMode.RUNNING
        LEDLightMode.TURN_BRAKE -> SportsMode.CYCLING
    }
}

data class LEDLightItem(
    val type: LEDLightMode,
    @StringRes val titleRes: Int,
    val selected: Boolean = false,
    @StringRes val descriptionRes: Int? = null,
)
