package com.suunto.headset.viewmodel

import android.annotation.SuppressLint
import androidx.annotation.StringRes
import com.stt.android.coroutines.runSuspendCatching
import com.suunto.headset.R
import com.suunto.headset.capability.BaseHeadsetFeature
import com.suunto.headset.capability.SU09HeadsetFeature
import com.suunto.headset.capability.SupportedHeadsetDevices
import com.suunto.headset.contract.IndicatorContract
import com.suunto.headset.model.LEDLightItem
import com.suunto.headset.model.toSoaSportMode
import com.suunto.headset.mvi.BaseStateViewModel
import com.suunto.headset.repository.SetUserHeadsetConfigDataSource
import com.suunto.soa.ble.control.attr.LedStatus
import com.suunto.soa.ble.control.attr.SportsMode
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import timber.log.Timber
import javax.inject.Inject
import kotlin.reflect.KClass

@SuppressLint("CheckResult")
@HiltViewModel
class IndicatorViewModel @Inject constructor(
    private val setUserHeadsetConfigDataSource: SetUserHeadsetConfigDataSource,
    private val baseHeadsetFeature: BaseHeadsetFeature,
    private val supportedHeadsetDevices: SupportedHeadsetDevices,
    private val deviceStatusNotifyUseCase: DeviceStatusNotifyUseCase,
    private val analyticsUtils: AnalyticsUtils
) : BaseStateViewModel<IndicatorContract.UIIntent, IndicatorContract.UIState>() {

    private val _lightModeOptions =
        MutableStateFlow<ImmutableList<LEDLightItem>>(persistentListOf())
    val lightModeOptions: StateFlow<ImmutableList<LEDLightItem>> = _lightModeOptions.asStateFlow()

    private val _lightModelClosed = MutableStateFlow(true)
    val lightModelClosed: StateFlow<Boolean> = _lightModelClosed.asStateFlow()

    override val stateClass: KClass<IndicatorContract.UIState>
        get() = IndicatorContract.UIState::class

    init {
        val supportGetDeviceLedStatus =
            supportedHeadsetDevices.supportGetDeviceLedStatus(baseHeadsetFeature)
        if (supportGetDeviceLedStatus) {
            notifyDeviceStatus()
        }
    }

    private fun notifyDeviceStatus() {
        launchOnIO {
            runSuspendCatching {
                deviceStatusNotifyUseCase.getDeviceStatusFlow().collect { deviceStatus ->
                    deviceStatus.getLedState()?.let {
                        _lightModelClosed.value = it == LedStatus.CLOSE
                    }
                    deviceStatus.getLightMode()?.let { mode ->
                        updateLightModeOptions(mode)
                    }
                }
            }
        }
    }

    override suspend fun dispatchIntentOnIO(intent: IndicatorContract.UIIntent) {
        when (intent) {
            is IndicatorContract.UIIntent.LoadSportsMode -> loadSportsMode(intent.currentMode)
            is IndicatorContract.UIIntent.SetSportsMode -> setSportsMode(intent.sportsMode)
            is IndicatorContract.UIIntent.SportsModeSwitchState -> setSportsModeSwitchState(intent.enabled)
        }
    }

    @StringRes
    fun getLEDOffAnimationFileNameRes(): Int {
        return when (baseHeadsetFeature) {
            is SU09HeadsetFeature -> R.string.headphone_su09_led_off
            else -> R.string.headphone_SU03_light_close
        }
    }

    private fun currentLedLightMode() =
        _lightModeOptions.value.firstOrNull { it.selected }?.type?.toSoaSportMode()

    private suspend fun loadSportsMode(
        currentSportsMode: SportsMode?,
        sendAnalytics: Boolean = false
    ) {
        val sportsMode = currentSportsMode
            ?: try {
                val sportsMode = baseHeadsetFeature.getSportsMode()
                val headsetConfig = setUserHeadsetConfigDataSource.getHeadsetConfig()
                headsetConfig.sportsMode = sportsMode
                setUserHeadsetConfigDataSource.saveHeadsetConfig(headsetConfig)
                headsetConfig.sportsMode
            } catch (thr: Throwable) {
                Timber.w(thr, "get sports mode and sports custom mode failed.")
                val headsetConfig = setUserHeadsetConfigDataSource.getHeadsetConfig()
                headsetConfig.sportsMode
            }
        if (sendAnalytics) {
            analyticsUtils.sendHeadphonesSettingsChangedAmplitudeEvent(
                HeadsetSetting.LEDlights(
                    sportsModeToLEDStatus(
                        currentLedLightMode() ?: SportsMode.CLOSE
                    ) to AnalyticsLEDLightMode.Off
                )
            )
        }
        if (sportsMode == SportsMode.CLOSE) {
            _lightModeOptions.emit(persistentListOf())
            _lightModelClosed.emit(true)
        } else {
            val lightModeList = supportedHeadsetDevices.getLightModeList(baseHeadsetFeature)
                .map {
                    it.copy(selected = it.type.toSoaSportMode() == sportsMode)
                }
                .toImmutableList()
            _lightModeOptions.emit(lightModeList)
            _lightModelClosed.emit(false)
        }
    }

    private suspend fun setSportsModeSwitchState(enabled: Boolean) {
        runSuspendCatching {
            baseHeadsetFeature.setLedLightSwitchState(enabled)
        }.onSuccess {
            loadSportsMode(null, true)
        }.onFailure { thr ->
            Timber.e(thr, "setSportsModeSwitchState error: ${thr.message}")
            showErrorSnackBar(thr)
        }
    }

    private suspend fun setSportsMode(sportsMode: SportsMode) {
        try {
            val result = baseHeadsetFeature.setSportsMode(sportsMode)
            Timber.i("setSportsMode: $result")
            if (result) {
                updateLightModeOptions(sportsMode)
            }
            currentLedLightMode()?.let {
                analyticsUtils.sendHeadphonesSettingsChangedAmplitudeEvent(
                    HeadsetSetting.LEDlights(
                        sportsModeToLEDStatus(sportsMode) to sportsModeToLEDStatus(
                            it
                        )
                    )
                )
            }
        } catch (thr: Throwable) {
            Timber.e(thr, "setSportsMode error: ${thr.message}")
            showErrorSnackBar(thr)
        }
    }

    private suspend fun updateLightModeOptions(sportsMode: SportsMode) {
        if (_lightModeOptions.value.isNotEmpty()) {
            _lightModeOptions.update { modeOptions ->
                modeOptions.map { it.copy(selected = it.type.toSoaSportMode() == sportsMode) }
                    .toImmutableList()
            }
        } else {
            _lightModeOptions.emit(
                supportedHeadsetDevices.getLightModeList(baseHeadsetFeature)
                    .map { it.copy(selected = it.type.toSoaSportMode() == sportsMode) }
                    .toImmutableList()
            )
        }
    }

    private fun sportsModeToLEDStatus(sportsMode: SportsMode): AnalyticsLEDLightMode {
        return when (sportsMode) {
            SportsMode.LEADER -> AnalyticsLEDLightMode.Flashing
            SportsMode.TEAM -> AnalyticsLEDLightMode.ConstantLight
            SportsMode.SOS -> AnalyticsLEDLightMode.SOS
            SportsMode.RUNNING -> AnalyticsLEDLightMode.Running
            SportsMode.CYCLING -> AnalyticsLEDLightMode.Cycling
            else -> AnalyticsLEDLightMode.Off
        }
    }
}
