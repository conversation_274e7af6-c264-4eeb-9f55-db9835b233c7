package com.suunto.headset.viewmodel

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.smartdeviceota.LatestVersionInfo
import com.suunto.headset.repository.SmartDeviceOtaRepository
import javax.inject.Inject

class FetchOTAInfoUseCase @Inject constructor(
    private val smartDeviceOtaRepository: SmartDeviceOtaRepository
) {
    suspend fun invoke(currentVersion: String, productName: String) = runSuspendCatching {
        val otaInfo =
            smartDeviceOtaRepository.getLatestOTAInfo(currentVersion, productName)
                ?: LatestVersionInfo()
        val versionLog = otaInfo.configId?.let {
            runSuspendCatching {
                smartDeviceOtaRepository.getVersionLog(configId = it.toString())
            }.getOrNull()?.content ?: ""
        } ?: runSuspendCatching {
            smartDeviceOtaRepository.getVersionLog(
                currentVersion = currentVersion,
                productModel = productName
            )
        }.getOrNull()?.content ?: ""
        otaInfo.copy(versionLog = versionLog)
    }
}
