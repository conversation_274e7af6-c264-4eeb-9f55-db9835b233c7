package com.suunto.headset.ui.controller

import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.suunto.headset.ui.adapter.deviceButtonSettingMode
import com.suunto.headset.ui.data.HeadsetButtonSettingViewState
import javax.inject.Inject

class HeadsetButtonSettingController @Inject constructor() :
    ViewStateEpoxyController<HeadsetButtonSettingViewState>() {

    override fun buildModels(viewState: ViewState<HeadsetButtonSettingViewState?>) {
        super.buildModels(viewState)
        val settingViewState = viewState.data ?: return
        settingViewState.shortcutKeyList.forEach {
            val settingButton = settingViewState.button
            deviceButtonSettingMode {
                id("buttonSetting_${it.name}")
                shortcutKey(it)
                isSelected(it == settingButton.buttonShortcutKey)
                onButtonClick(viewState.data?.onClickHandler)
            }
        }
    }
}
