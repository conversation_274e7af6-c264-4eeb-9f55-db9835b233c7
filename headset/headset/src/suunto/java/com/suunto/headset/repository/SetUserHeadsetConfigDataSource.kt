package com.suunto.headset.repository

import com.suunto.headset.model.HeadsetConfig
import kotlinx.coroutines.flow.Flow

interface SetUserHeadsetConfigDataSource {

    fun saveHeadsetConfig(headsetConfig: HeadsetConfig)

    fun getHeadsetConfig(mac: String = ""): HeadsetConfig

    fun markFirstEntry(first: Boolean)

    fun isFirstEntry(): Boolean

    fun getLastDeviceMac(): String

    fun saveLastDeviceMac(mac: String)

    fun observeLastDeviceMac(): Flow<String>
}
