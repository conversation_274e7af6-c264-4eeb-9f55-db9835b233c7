package com.suunto.headset.model

import androidx.compose.ui.graphics.Color
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

data class AssessmentReportDetail(
    val name: String,
    val healthState: NeckHealthState,
    val assessmentItems: List<AssessmentItemDetail>,
    val rotationSymmetry: Boolean = true,
    val lateralFlexionSymmetry: Boolean = true,
    val viewHistoryData: Boolean = false
)

data class AssessmentItemDetail(
    val type: NeckAssessmentType,
    val clockwiseAngle: Int,
    val clockwiseAngleStandardValue: Int,
    val clockwiseAngleWaringValue: Int,
    val anticlockwiseAngle: Int,
    val anticlockwiseAngleStandardValue: Int,
    val anticlockwiseAngleWaringValue: Int,
    val clockwiseAngelHistoryValues: ImmutableList<AssessmentItemValueWithTime> = persistentListOf(),
    val anticlockwiseAngleHistoryValues: ImmutableList<AssessmentItemValueWithTime> = persistentListOf(),
    val symmetry: <PERSON>olean = true,
)

data class AssessmentItemValueWithTime(val time: Long, val angleValue: Int)

enum class AssessmentAnglePosition(val color: Color) {
    EXCEED_STANDARD(Color(0xFF01AD3A)),
    MIDDLE_STANDARD_WARING(Color(0xFFFFC221)),
    UNDER_WARING(Color(0xFFDE0101))
}

enum class AssessmentLevelLineValue(val color: Color) {
    STANDARD(Color(0xFF01AD3A)),
    WARNING(Color(0xFFDE0101))
}
