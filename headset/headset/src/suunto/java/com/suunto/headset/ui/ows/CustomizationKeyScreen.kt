package com.suunto.headset.ui.ows

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.selection.selectable
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SecondaryTabRow
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ActionBottomSheet
import com.stt.android.compose.widgets.LoadingContent
import com.suunto.headset.R
import com.suunto.headset.model.CustomizationKeyInfo
import com.suunto.headset.model.CustomizationKeyValue
import com.suunto.headset.model.FunctionType
import com.suunto.headset.model.HeadphoneSide
import com.suunto.headset.model.KeyMethod
import com.suunto.headset.model.getDescriptionResId
import com.suunto.headset.model.keyMethodWithFunctionTypes
import com.suunto.headset.ui.components.CommonTopAppBar
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomizationKeyScreen(
    loading: Boolean,
    customizationKeyValues: ImmutableList<CustomizationKeyValue>,
    onSetCustomizationKey: (HeadphoneSide, CustomizationKeyInfo) -> Unit,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val snackbarState = remember { SnackbarHostState() }
    Scaffold(
        modifier = modifier,
        topBar = {
            CommonTopAppBar(
                title = stringResource(R.string.setting_button_customization_title),
                onBackClick = onBackClick
            )
        },
        snackbarHost = {
            SnackbarHost(snackbarState)
        },
        containerColor = MaterialTheme.colorScheme.surface
    ) { paddingValues ->
        val context = LocalContext.current
        LaunchedEffect(customizationKeyValues) {
            customizationKeyValues.find { !it.enabled }?.let {
                snackbarState.showSnackbar(
                    when (it.side) {
                        HeadphoneSide.LEFT -> context.getString(R.string.left_headphone_disconnected)
                        HeadphoneSide.RIGHT -> context.getString(R.string.right_headphone_disconnected)
                    }
                )
            }
        }
        var showSettingFunctionType by remember { mutableStateOf(false) }
        var customizationKeyInfo by remember {
            mutableStateOf<Pair<HeadphoneSide, CustomizationKeyInfo>?>(null)
        }
        Column(modifier = Modifier.padding(paddingValues)) {
            LoadingContent(loading)
            if (!loading && customizationKeyValues.isNotEmpty()) {
                var selectedIndex by remember {
                    mutableIntStateOf(
                        customizationKeyValues.indexOfFirst { it.selected }
                    )
                }
                val pagerState =
                    rememberPagerState(initialPage = selectedIndex) { customizationKeyValues.size }
                val coroutineScope = rememberCoroutineScope()
                SecondaryTabRow(
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
                    selectedTabIndex = pagerState.currentPage
                ) {
                    customizationKeyValues.forEachIndexed { index, customizationKeyValue ->
                        Tab(
                            selected = selectedIndex == index,
                            onClick = {
                                selectedIndex = index
                                coroutineScope.launch { pagerState.scrollToPage(index) }
                            },
                            enabled = customizationKeyValue.enabled,
                            text = {
                                Row(modifier = Modifier.alpha(if (customizationKeyValue.enabled) 1f else 0.5f)) {
                                    if (!customizationKeyValue.enabled) {
                                        Icon(
                                            painter = painterResource(R.drawable.icon_warning_circle),
                                            contentDescription = null,
                                            tint = Color.Unspecified
                                        )
                                    }
                                    Text(
                                        modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                                        text = stringResource(id = customizationKeyValue.titleRes),
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                            },
                            selectedContentColor = MaterialTheme.colorScheme.primary,
                            unselectedContentColor = MaterialTheme.colorScheme.darkGrey
                        )
                    }
                }
                HorizontalPager(
                    pagerState,
                    userScrollEnabled = customizationKeyValues.all { it.enabled }
                ) { index ->
                    CustomizationKeyItem(
                        customizationKeyValues[index],
                        customizationKeyValues.map { it.side to it.enabled }.toImmutableList(),
                        onClickKeyItem = { side, it ->
                            customizationKeyInfo = side to it
                            showSettingFunctionType = true
                        }
                    )
                }
            }
        }
        if (showSettingFunctionType) {
            customizationKeyInfo?.let {
                CustomizationFunctionChooser(
                    customizationKeyInfo = it.second,
                    onDismiss = { showSettingFunctionType = false },
                    onSelectFunctionType = { functionType ->
                        onSetCustomizationKey(it.first, it.second.copy(functionType = functionType))
                        showSettingFunctionType = false
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
private fun CustomizationKeyItem(
    value: CustomizationKeyValue,
    headphonesState: ImmutableList<Pair<HeadphoneSide, Boolean>>,
    onClickKeyItem: (HeadphoneSide, CustomizationKeyInfo) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(modifier = modifier.fillMaxWidth()) {
        item {
            Column {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(40.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    headphonesState.forEach {
                        Image(
                            painter = painterResource(
                                when (it.first) {
                                    HeadphoneSide.LEFT -> R.drawable.icon_left_headphone
                                    HeadphoneSide.RIGHT -> R.drawable.icon_right_headphone
                                }
                            ),
                            contentDescription = null,
                            modifier = Modifier
                                .alpha(if (it.second) 1f else 0.5f)
                        )
                    }
                }

                Text(
                    modifier = Modifier
                        .background(MaterialTheme.colorScheme.nearWhite)
                        .padding(MaterialTheme.spacing.medium),
                    text = stringResource(id = R.string.customization_key_tips),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
        items(value.keyInfos, key = { it.keyMethod }) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(onClick = { onClickKeyItem(value.side, it) })
                    .padding(MaterialTheme.spacing.medium),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = stringResource(id = it.keyMethod.nameRes),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                        text = stringResource(id = it.functionType.nameRes),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.darkGrey
                    )
                }
                Icon(
                    painter = painterResource(com.stt.android.R.drawable.ic_right_arrow),
                    contentDescription = null
                )
            }
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        }
        item {
            Text(
                modifier = Modifier
                    .background(MaterialTheme.colorScheme.nearWhite)
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium),
                text = stringResource(id = R.string.no_customizable),
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface
            )
        }

        items(value.nonCustomizationKeyInfo, key = { "${it.keyMethod}_non" }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium)
            ) {
                Text(
                    text = stringResource(id = it.functionType.nameRes),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                    text = stringResource(id = it.keyMethod.nameRes),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.darkGrey
                )
            }
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        }
    }
}

@Composable
private fun CustomizationFunctionChooser(
    customizationKeyInfo: CustomizationKeyInfo,
    onDismiss: () -> Unit,
    onSelectFunctionType: (FunctionType) -> Unit,
    modifier: Modifier = Modifier,
) {
    ActionBottomSheet(
        onDismiss = onDismiss,
        actionTitle = stringResource(R.string.cancel),
        onActionClick = onDismiss,
        modifier = modifier,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = MaterialTheme.spacing.medium)
        ) {
            Column(
                modifier = Modifier.padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = 18.dp
                )
            ) {
                Text(
                    text = stringResource(id = customizationKeyInfo.keyMethod.nameRes),
                    style = MaterialTheme.typography.bodyXLargeBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                    text = stringResource(id = R.string.select_function_hint),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.darkGrey
                )
            }
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
            keyMethodWithFunctionTypes[customizationKeyInfo.keyMethod]?.let { functionTypes ->
                functionTypes.forEach {
                    val selected = customizationKeyInfo.functionType == it
                    Column {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = selected,
                                    onClick = {
                                        onSelectFunctionType(it)
                                    },
                                    role = Role.RadioButton
                                )
                                .padding(MaterialTheme.spacing.medium),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = stringResource(id = it.nameRes),
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                                it.getDescriptionResId()?.let { descriptionResId ->
                                    Text(
                                        text = stringResource(id = descriptionResId),
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.darkGrey
                                    )
                                }
                            }
                            RadioButton(
                                selected = selected,
                                onClick = null,
                                colors = RadioButtonDefaults.colors().copy(
                                    selectedColor = MaterialTheme.colorScheme.primary,
                                    unselectedColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                                )
                            )
                        }
                        HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun CustomizationKeyScreenPreview() {
    val fakeValue = CustomizationKeyValue(
        side = HeadphoneSide.LEFT,
        enabled = true,
        selected = true,
        titleRes = R.string.left,
        keyInfos = persistentListOf(
            CustomizationKeyInfo(
                keyMethod = KeyMethod.SINGLE_TAP,
                functionType = FunctionType.LAST_SONG
            ),
            CustomizationKeyInfo(
                keyMethod = KeyMethod.DOUBLE_TAP,
                functionType = FunctionType.NEXT_SONG
            ),
            CustomizationKeyInfo(
                keyMethod = KeyMethod.LONG_PRESS_AND_HOLD,
                functionType = FunctionType.VOLUME_UP
            )
        ),
        nonCustomizationKeyInfo = persistentListOf(
            CustomizationKeyInfo(KeyMethod.SINGLE_TAP, FunctionType.ANSWER_CALL),
            CustomizationKeyInfo(KeyMethod.DOUBLE_TAP, FunctionType.REJECT_CALL)
        )
    )
    M3AppTheme {
        CustomizationKeyScreen(
            loading = false,
            customizationKeyValues = persistentListOf(
                fakeValue,
                fakeValue.copy(
                    titleRes = R.string.right,
                    side = HeadphoneSide.RIGHT,
                    selected = false,
                    enabled = false
                )
            ),
            onSetCustomizationKey = { _, _ -> },
            onBackClick = {}
        )
    }
}
