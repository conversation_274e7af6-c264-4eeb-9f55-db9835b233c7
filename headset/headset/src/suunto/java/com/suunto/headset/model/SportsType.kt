package com.suunto.headset.model

import androidx.annotation.StringRes
import com.stt.android.core.R
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList

data class SportsTypeOption(
    val sportType: SportType,
    val enabled: Boolean,
    val poolLength: PoolLength = PoolLength()
)

data class PoolLength(
    val value: Int = DEFAULT_POOL_LENGTH,
    val unit: PoolLengthUnit = PoolLengthUnit(R.string.meters, PoolLengthUnitType.METRIC),
    val options: PersistentList<Int> = poolLengthOptions,
    val unitResOptions: PersistentList<PoolLengthUnit> = poolLengthUnitOptions
) {
    companion object {
        private const val DEFAULT_POOL_LENGTH = 25
        private val topLengthList = persistentListOf(25, 50)

        fun create(value: Int, unit: PoolLengthUnit): PoolLength = PoolLength(value, unit)

        private val poolLengthUnitOptions = persistentListOf(
            PoolLengthUnit(R.string.meters, PoolLengthUnitType.METRIC),
            PoolLengthUnit(R.string.TXT_YD, PoolLengthUnitType.IMPERIAL)
        )

        private val poolLengthOptions = buildList {
            addAll(topLengthList)
            addAll(15..330)
        }.toPersistentList()
    }
}

data class PoolLengthUnit(@StringRes val valueRes: Int, val unitType: PoolLengthUnitType)

enum class PoolLengthUnitType {
    METRIC,
    IMPERIAL
}

enum class SportType {
    POOL_SWIMMING,
    OPEN_WATER_SWIMMING,
    JUMP_ROPE,
    RUNNING,
}
