package com.suunto.headset.capability

import com.suunto.headset.R
import com.suunto.headset.model.ButtonType
import com.suunto.headset.model.HeadsetButton
import com.suunto.headset.ui.HeadsetDeviceType
import com.suunto.headset.ui.onboarding.HeadsetOnBoardingPage
import com.suunto.headset.ui.onboarding.SU08OnBoardingPages
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.SportsMode
import com.suunto.soa.ble.response.ButtonFunction
import com.suunto.soa.ble.response.ButtonShortcutKey

class SU08HeadsetCapability : BaseHeadsetCapability() {
    override val headsetDeviceType: HeadsetDeviceType = HeadsetDeviceType.SU08

    override fun supportEqMode(): Boolean = true

    override fun supportBodySense(): Boolean = false

    override fun supportDualDeviceConnect(): Boolean = true

    override fun supportGetSerial(): Boolean = false

    override fun supportLowLatency(): Boolean = false

    override fun supportGetConnectedDevices(): Boolean = true

    override fun supportLEDLightMode(): Boolean = false

    override fun supportLanguageSetting(): Boolean = true

    override fun supportNotifyBatteryChanged(): Boolean = true

    override fun supportOfflineMusic(): Boolean = true

    override fun supportButtonCustomization(): Boolean = true

    override fun supportSwimFeature(): Boolean = false

    override fun getFunctionIntroduction(): List<HeadsetOnBoardingPage> {
        return SU08OnBoardingPages
    }

    override fun getUserGuideUrl(): Int {
        return R.string.user_guide_url_su08
    }

    override fun getMoreInformationUrl(): Int {
        return R.string.more_information_url_su08
    }

    override fun getSportsModeList(): List<SportsMode> {
        return mutableListOf(SportsMode.CLOSE, SportsMode.TEAM, SportsMode.LEADER, SportsMode.SOS)
    }

    override fun getSoundModeNormalPhotoResIds(): Int {
        // not used
        return 0
    }

    override fun getSoundModeOutdoorPhotoResIds(): Int {
        // not used
        return 0
    }

    override fun getSoundModeList(): List<EqMode> {
        return listOf(EqMode.NORMAL, EqMode.OUTDOOR, EqMode.UNDERWATER)
    }

    override fun getButtonList(): List<HeadsetButton> {
        return getSU08ButtonFunctions().map { function ->
            HeadsetButton(
                buttonType = getHeadsetButtonType(function),
                buttonFunction = function,
                buttonShortcutKey = getHeadsetButtonDefaultShortcutKey(function),
                isSupportCustomized = isSupportCustomized(function)
            )
        }
    }

    override fun getCustomizationShortcutKeyList(): List<ButtonShortcutKey> {
        return ButtonShortcutKey.entries
            .filterNot {
                it == ButtonShortcutKey.EMPTY ||
                    it == ButtonShortcutKey.HOLD_DOWN_PLUS_3S ||
                    it == ButtonShortcutKey.PRESS_PLUS_OR_REDUCE ||
                    it == ButtonShortcutKey.HOLD_DOWN_MFB_AND_PLUS_3S ||
                    it == ButtonShortcutKey.PRESS_MFB_ONCE ||
                    it == ButtonShortcutKey.PRESS_AND_HOLD_MFB ||
                    it == ButtonShortcutKey.HOLD_DOWN_MFB_5S ||
                    it == ButtonShortcutKey.HOLD_DOWN_PLUS_AND_REDUCE_3S
            } + ButtonShortcutKey.EMPTY
    }

    override fun isCheckDeviceAvailableBeforeOTA(): Boolean = true

    override fun getHeadsetButtonType(function: ButtonFunction): ButtonType {
        return when (function) {
            ButtonFunction.ACTIVE_VOICE_ASSISTANT -> ButtonType.ANY_CIRCUMSTANCES

            ButtonFunction.SWITCH_PLAYBACK_ORDER,
            ButtonFunction.NEXT_SONG,
            ButtonFunction.LAST_SONG,
            ButtonFunction.CHANGE_INTERNAL_PLAYLIST,
            ButtonFunction.SWITCH_MUSIC_MODE -> ButtonType.WHILE_PLAYING_MUSIC

            ButtonFunction.POWER_ON_OFF,
            ButtonFunction.VOLUME_ADJUSTMENT,
            ButtonFunction.MUSIC_PLAY_PAUSE,
            ButtonFunction.SWITCH_SOUND_MODE,
            ButtonFunction.START_BLE_PAIR,
            ButtonFunction.PHONE_CALL_CONTROL,
            ButtonFunction.REJECT_CALL,
            ButtonFunction.DUAL_DEVICE_CONNECTION,
            ButtonFunction.REST_SETTING -> ButtonType.NON_CUSTOMIZABLE

            ButtonFunction.HEAD_MOVE_CONTROL,
            ButtonFunction.ENABLE_SPORT_TRACKING,
            ButtonFunction.NECK_FATIGUE_ALERT_ON_OFF,
            ButtonFunction.LED_MODE_ON_OFF,
            ButtonFunction.EMPTY -> ButtonType.INVALID
        }
    }

    private fun getHeadsetButtonDefaultShortcutKey(function: ButtonFunction): ButtonShortcutKey {
        return when (function) {
            ButtonFunction.ACTIVE_VOICE_ASSISTANT -> ButtonShortcutKey.HOLD_DOWN_MFB_3S
            ButtonFunction.NEXT_SONG -> ButtonShortcutKey.DOUBLE_PRESS_MFB
            ButtonFunction.LAST_SONG -> ButtonShortcutKey.TRIPE_PRESS_MFB
            ButtonFunction.SWITCH_MUSIC_MODE -> ButtonShortcutKey.HOLD_DOWN_REDUCE_3S
            ButtonFunction.POWER_ON_OFF -> ButtonShortcutKey.HOLD_DOWN_PLUS_3S
            ButtonFunction.VOLUME_ADJUSTMENT -> ButtonShortcutKey.PRESS_PLUS_OR_REDUCE
            ButtonFunction.SWITCH_SOUND_MODE,
            ButtonFunction.DUAL_DEVICE_CONNECTION -> ButtonShortcutKey.HOLD_DOWN_MFB_AND_PLUS_3S

            ButtonFunction.MUSIC_PLAY_PAUSE,
            ButtonFunction.PHONE_CALL_CONTROL -> ButtonShortcutKey.PRESS_MFB_ONCE

            ButtonFunction.START_BLE_PAIR -> ButtonShortcutKey.HOLD_DOWN_PLUS_AND_REDUCE_3S
            ButtonFunction.REJECT_CALL -> ButtonShortcutKey.PRESS_AND_HOLD_MFB
            ButtonFunction.REST_SETTING -> ButtonShortcutKey.HOLD_DOWN_MFB_5S
            ButtonFunction.SWITCH_PLAYBACK_ORDER -> ButtonShortcutKey.HOLD_DOWN_MFB_AND_REDUCE_3S

            ButtonFunction.HEAD_MOVE_CONTROL,
            ButtonFunction.ENABLE_SPORT_TRACKING,
            ButtonFunction.CHANGE_INTERNAL_PLAYLIST,
            ButtonFunction.NECK_FATIGUE_ALERT_ON_OFF,
            ButtonFunction.LED_MODE_ON_OFF,
            ButtonFunction.EMPTY -> ButtonShortcutKey.EMPTY
        }
    }

    private fun isSupportCustomized(function: ButtonFunction): Boolean {
        return when (function) {
            ButtonFunction.ACTIVE_VOICE_ASSISTANT,
            ButtonFunction.SWITCH_PLAYBACK_ORDER,
            ButtonFunction.NEXT_SONG,
            ButtonFunction.LAST_SONG,
            ButtonFunction.CHANGE_INTERNAL_PLAYLIST,
            ButtonFunction.SWITCH_MUSIC_MODE -> true

            else -> false
        }
    }

    private fun getSU08ButtonFunctions(): List<ButtonFunction> {
        return listOf(
            ButtonFunction.POWER_ON_OFF,
            ButtonFunction.VOLUME_ADJUSTMENT,
            ButtonFunction.ACTIVE_VOICE_ASSISTANT,
            ButtonFunction.MUSIC_PLAY_PAUSE,
            ButtonFunction.SWITCH_SOUND_MODE,
            ButtonFunction.START_BLE_PAIR,
            ButtonFunction.SWITCH_PLAYBACK_ORDER,
            ButtonFunction.NEXT_SONG,
            ButtonFunction.LAST_SONG,
            ButtonFunction.CHANGE_INTERNAL_PLAYLIST,
            ButtonFunction.SWITCH_MUSIC_MODE,
            ButtonFunction.PHONE_CALL_CONTROL,
            ButtonFunction.REJECT_CALL,
            ButtonFunction.DUAL_DEVICE_CONNECTION,
            ButtonFunction.REST_SETTING
        )
    }

    override fun supportNeckMovementAssessment(): Boolean = false

    override fun supportMusicModeFeature(): Boolean = true

    override fun supportCallStatusFeature(): Boolean = false

    override fun supportJumpAssessment(): Boolean = false

    override fun getHeadControlInstructions(): List<HeadControlInstruction> {
        return listOf(
            HeadControlInstruction(
                R.string.head_control_instructions_call,
                listOf(
                    HeadControlInstructionItem(
                        R.drawable.su07_nod,
                        R.string.head_control_item_answer_call_title,
                        R.string.head_control_item_answer_call_content
                    ),
                    HeadControlInstructionItem(
                        R.drawable.su07_shake,
                        R.string.head_control_item_answer_reject_title,
                        R.string.head_control_item_shake_head
                    )
                )
            ),
            HeadControlInstruction(
                R.string.head_control_instructions_media,
                listOf(
                    HeadControlInstructionItem(
                        R.drawable.su07_shake,
                        R.string.head_control_item_skip_song_title,
                        R.string.head_control_item_shake_head
                    )
                )
            )
        )
    }
}
