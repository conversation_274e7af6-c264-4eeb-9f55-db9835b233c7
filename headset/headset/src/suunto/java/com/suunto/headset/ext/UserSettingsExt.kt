package com.suunto.headset.ext

import com.stt.android.domain.user.Sex
import com.stt.android.domain.user.UserSettings
import com.suunto.soa.data.SoaUserInfo
import java.util.zip.CRC32

fun UserSettings.toSoaUserInfo(): SoaUserInfo {
    return SoaUserInfo(
        userId = CRC32().run {
            this.update(analyticsUUID.toByteArray())
            this.value.toInt()
        },
        heightInCentimeter = this.height,
        weightInGrams = this.weight,
        sex = if (this.gender == Sex.MALE) 0 else 1,
        birth = this.birthDate
    )
}
