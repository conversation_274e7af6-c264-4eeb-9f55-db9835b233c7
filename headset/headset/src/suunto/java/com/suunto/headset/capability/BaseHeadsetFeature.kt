package com.suunto.headset.capability

import com.stt.android.domain.user.UserSettings
import com.suunto.headset.R
import com.suunto.headset.model.DeviceCapability
import com.suunto.headset.model.HeadsetButton
import com.suunto.headset.model.SportSupports
import com.suunto.headset.model.SportType
import com.suunto.soa.ble.control.attr.BodySenseOperation
import com.suunto.soa.ble.control.attr.CallStatus
import com.suunto.soa.ble.control.attr.CommonSwitchState
import com.suunto.soa.ble.control.attr.DevicesConnectSupport
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.LowLatencyMode
import com.suunto.soa.ble.control.attr.MusicMode
import com.suunto.soa.ble.control.attr.OwsSoundModeData
import com.suunto.soa.ble.control.attr.SportsMode
import com.suunto.soa.ble.control.attr.SwimStatus
import com.suunto.soa.ble.response.OwsCustomizationKey
import com.suunto.soa.ble.response.attr.AttrBluetoothStatus
import com.suunto.soa.ble.response.attr.OwsConnectionState
import com.suunto.soa.data.MetronomeData
import com.suunto.soa.data.NeckIntervalData
import com.suunto.soa.data.SoaPoolDistance
import com.suunto.soa.data.SoaRopeJumpData
import com.suunto.soa.data.SportStatus

open class BaseHeadsetFeature constructor(private val headsetBtDevice: HeadsetBtDevice) {

    open suspend fun getSerial(): String {
        throw UnsupportedOperationException()
    }

    open suspend fun setDualDeviceConnect(support: DevicesConnectSupport): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun getDualDeviceConnect(): DevicesConnectSupport {
        throw UnsupportedOperationException()
    }

    open suspend fun getConnectedDevices(): List<AttrBluetoothStatus.ConnectedDevice> {
        throw UnsupportedOperationException()
    }

    open suspend fun getBodySense(): BodySenseOperation {
        throw UnsupportedOperationException()
    }

    open suspend fun setBodySense(bodySenseOperation: BodySenseOperation): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun setEqMode(eqMode: EqMode): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun getEqMode(): EqMode {
        throw UnsupportedOperationException()
    }

    open suspend fun setLowLatencyMode(lowLatencyMode: LowLatencyMode): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun getLowLatencyMode(): LowLatencyMode {
        throw UnsupportedOperationException()
    }

    open suspend fun getSportsMode(): SportsMode {
        throw UnsupportedOperationException()
    }

    open suspend fun setSportsMode(sportsMode: SportsMode): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun setAppLanguage(language: String): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun getSwimStatus(): SwimStatus? {
        throw UnsupportedOperationException()
    }

    open suspend fun getButtonList(): List<HeadsetButton> {
        throw UnsupportedOperationException()
    }

    open suspend fun updateButtonList(buttons: List<HeadsetButton>): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun getMusicMode(): MusicMode? {
        throw UnsupportedOperationException()
    }

    open suspend fun setMusicMode(musicMode: MusicMode): Boolean {
        throw UnsupportedOperationException()
    }

    fun getHeadsetBtDevice(): HeadsetBtDevice {
        return headsetBtDevice
    }

    open suspend fun getCallingStatus(): CallStatus? {
        throw UnsupportedOperationException()
    }

    open suspend fun getNeckReminderInterval(): NeckIntervalData {
        throw UnsupportedOperationException()
    }

    open suspend fun updateSportType(sportType: SportType): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun getSportType(): SportType? {
        throw UnsupportedOperationException()
    }

    open suspend fun getSportStatus(sportSupports: SportSupports): SportStatus {
        throw UnsupportedOperationException()
    }

    open suspend fun getDeviceCapability(): DeviceCapability {
        throw UnsupportedOperationException()
    }

    open suspend fun setUserInfoToDevice(userSettings: UserSettings): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun getPoolDistance(): SoaPoolDistance? {
        throw UnsupportedOperationException()
    }

    open suspend fun setPoolDistance(poolDistance: SoaPoolDistance): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun syncRopeJumpData(): List<SoaRopeJumpData> {
        throw UnsupportedOperationException()
    }

    open suspend fun setLedLightSwitchState(enabled: Boolean): Boolean {
        throw UnsupportedOperationException()
    }

    open fun getHeadsetIconRes(): Int = R.drawable.headset_suunto_sound_pro

    open suspend fun getOwsCustomizationKeys(): List<OwsCustomizationKey> {
        throw UnsupportedOperationException()
    }

    open suspend fun setOwsCustomizationKeys(keys: List<OwsCustomizationKey>): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun getMetronomeData(): MetronomeData {
        throw UnsupportedOperationException()
    }

    open suspend fun getImmersiveMode(): CommonSwitchState {
        throw UnsupportedOperationException()
    }

    open suspend fun getSpatialAudioState(): CommonSwitchState {
        throw UnsupportedOperationException()
    }

    open suspend fun getOwsSoundMode(): OwsSoundModeData {
        throw UnsupportedOperationException()
    }

    open suspend fun setOwsSoundMode(owsSoundMode: OwsSoundModeData): Boolean {
        throw UnsupportedOperationException()
    }

    open suspend fun getOwsConnectionState(): OwsConnectionState {
        throw UnsupportedOperationException()
    }
}
