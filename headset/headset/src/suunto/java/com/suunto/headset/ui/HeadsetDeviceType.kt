package com.suunto.headset.ui

// The su03 has two Pids, the previous product is the default 0x01, named Suunto Sound Pro, and the new production is 0x03, named Suunto Wing
const val SUUNTO_SOUND_PRO_PID = 0x01

enum class HeadsetDeviceType(val isReleased: Boolean, val displayName: String, val pid: Int) {
    SU03(isReleased = true, displayName = "SUUNTO SOUND PRO", 0x03),
    SU05(isReleased = true, displayName = "SUUNTO SOUND", 0x05),
    SU07(isReleased = true, displayName = "SUUNTO AQUA", 0x07),
    SU08(isReleased = true, displayName = "SUUNTO AQUA LIGHT", 0x08),
    SU09(isReleased = true, displayName = "SUUNTO WING 2", 0x09),

    // TODO use actual name and pid
    SU10(isReleased = false, displayName = "SUUNTO SU10", 0x0A),
    Unrecognized(isReleased = false, displayName = "Unrecognized", 0xFF),
}
