package com.suunto.headset.ui

import android.annotation.SuppressLint
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import by.kirich1409.viewbindingdelegate.viewBinding
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.CustomTabsUtils
import com.stt.android.watch.watchupdates.setVersionInfoLog
import com.suunto.extension.lazyArgument
import com.suunto.extension.navigate
import com.suunto.extension.popBackStack
import com.suunto.headset.R
import com.suunto.headset.capability.BaseHeadsetFeature
import com.suunto.headset.capability.SupportedHeadsetDevices
import com.suunto.headset.contract.HeadsetContract
import com.suunto.headset.databinding.FragmentOtaVersionLatestBinding
import com.suunto.headset.mvi.BaseStateFragment
import com.suunto.headset.viewmodel.HeadsetViewModel
import com.suunto.soa.ble.service.BleState
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.reflect.KClass

/**
 * OTA headset upgrade latest version UI.
 */
@AndroidEntryPoint
class OTALatestFragment : BaseStateFragment<HeadsetContract.UIIntent, HeadsetContract.UIState, HeadsetViewModel>() {

    private val binding by viewBinding(FragmentOtaVersionLatestBinding::bind, R.id.container)
    private val rebootWait by lazyArgument(REBOOT_WAIT, false)
    private val versionLog by lazyArgument(VERSION_LOG, "")
    private val formattedVersion by lazyArgument(FORMATTED_VERSION, "")
    override fun getLayoutResId() = R.layout.fragment_ota_version_latest

    override val viewModelClass: KClass<HeadsetViewModel>
        get() = HeadsetViewModel::class

    override fun getTitleResId() = R.string.headphone_update

    @Inject
    lateinit var baseHeadsetFeature: BaseHeadsetFeature

    @Inject
    lateinit var supportedHeadsetDevices: SupportedHeadsetDevices

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()
        handleObserveConnectState()
        binding.tvTips.text = if (rebootWait) {
            getString(R.string.reboot_wait)
        } else {
            getString(
                R.string.software_up_to_date
            )
        }
        binding.tvVersion.text = getString(com.stt.android.R.string.watch_updates_version, formattedVersion)
        binding.versionLogLayout.root.isVisible = versionLog.isNotBlank()
        setVersionInfoLog(
            binding.versionLogLayout.versionLog,
            requireActivity(),
            versionLog
        )
        binding.learnMoreLayout.root.setOnClickListenerThrottled {
            supportedHeadsetDevices.getMoreInformationUrl(baseHeadsetFeature)?.let {
                CustomTabsUtils.launchCustomTab(
                    requireContext(),
                    getString(it)
                )
            }
        }
    }

    override fun dispatchConnectStatus(state: BleState) {
        super.dispatchConnectStatus(state)
        val disconnected = !state.isReady
        if (!disconnected && rebootWait) { // check latest version when waiting reboot and current headphone is connected
            HeadsetContract.UIIntent.GetSoftwareVersion.sendIntent()
        }
    }

    override fun bindState() {
        super.bindState()
        HeadsetContract.UIState::formattedVersion.bindStateNotNull {
            popBackStack()
            navigate(
                R.id.fragment_ota_version_check,
                bundleOf(FORMATTED_VERSION to it)
            )
        }
    }
}

const val REBOOT_WAIT = "REBOOT_WAIT"
const val VERSION_LOG = "VERSION_LOG"
