<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?suuntoItemBackgroundColor">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_text_title"
            style="@style/Body.Large.Bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/dual_device_text_top"
            android:textColor="@color/near_black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_text_top"
            style="@style/Body.Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:lineSpacingExtra="10dp"
            android:text="@string/tv_text_top"
            android:textColor="@color/near_black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_text_title" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_dual_device_left"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_dual_device_left"
            app:layout_constraintDimensionRatio="1.9:1"
            app:layout_constraintEnd_toStartOf="@id/iv_dual_device_right"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_text_top"
            app:layout_constraintWidth_percent="0.4" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_dual_device_right"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="16dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_dual_device_right"
            app:layout_constraintBottom_toBottomOf="@id/iv_dual_device_left"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_dual_device_left"
            app:layout_constraintTop_toBottomOf="@id/tv_text_top"
            app:layout_constraintWidth_percent="0.6" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_text_center"
            style="@style/Body.Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:lineSpacingExtra="10dp"
            android:text="@string/dual_device_center"
            android:textColor="@color/near_black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_dual_device_right" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_dual_device_bottom"
            android:layout_width="0dp"
            android:layout_height="203dp"
            android:layout_marginTop="16dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_dual_device_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_text_center" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_text_bottom"
            style="@style/Body.Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:lineSpacingExtra="10dp"
            android:text="@string/dual_device_bottom"
            android:textColor="@color/near_black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_dual_device_bottom" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
