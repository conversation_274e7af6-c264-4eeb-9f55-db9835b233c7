<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_neck_mobility_assessment"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/neck_mobility_assessment_label"
        style="@style/Body.Larger.Bold"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/size_spacing_medium"
        android:paddingTop="@dimen/size_spacing_medium"
        android:paddingEnd="@dimen/size_spacing_medium"
        android:text="@string/neck_mobility_assessment"
        android:textColor="@color/color_body_text_disabled"
        app:layout_constraintEnd_toStartOf="@+id/neck_mobility_assessment_next"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/neck_mobility_introduction"
        style="@style/Body.Medium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/size_spacing_medium"
        android:paddingEnd="@dimen/size_spacing_medium"
        android:paddingBottom="@dimen/size_spacing_medium"
        android:text="@string/neck_mobility_assessment_introduce"
        android:textColor="@color/color_body_text_disabled"
        app:layout_constraintEnd_toStartOf="@+id/neck_mobility_assessment_next"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/neck_mobility_assessment_label" />

    <ImageView
        android:id="@+id/neck_mobility_assessment_next"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:src="@drawable/icon_arrow_right"
        android:padding="@dimen/size_spacing_xsmall"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/near_black" />

    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:background="?suuntoDividerColor"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/neck_mobility_introduction" />

</androidx.constraintlayout.widget.ConstraintLayout>
