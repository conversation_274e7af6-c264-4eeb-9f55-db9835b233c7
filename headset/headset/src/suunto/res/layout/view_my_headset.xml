<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?suuntoItemBackgroundColor"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/headset_image"
            android:layout_width="match_parent"
            android:layout_height="@dimen/device_image_height"
            android:contentDescription="@null"
            android:src="@drawable/headset_suunto_sound_pro"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/device_connect_animation"
            android:layout_width="66dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="30dp"
            app:layout_constraintBottom_toBottomOf="@+id/headset_image"
            app:layout_constraintEnd_toEndOf="@+id/headset_image"
            app:layout_constraintStart_toStartOf="@+id/headset_image"
            app:layout_constraintTop_toTopOf="@+id/headset_image"
            app:lottie_autoPlay="true"
            app:lottie_fileName="@string/all_anim_done_green_tick"
            app:lottie_loop="true" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_headset_disconnected"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="30dp"
            android:src="@drawable/ic_watch_state_not_connected"
            android:tint="@color/list_item_divider"
            app:layout_constraintBottom_toBottomOf="@+id/headset_image"
            app:layout_constraintEnd_toEndOf="@+id/headset_image"
            app:layout_constraintStart_toStartOf="@+id/headset_image"
            app:layout_constraintTop_toTopOf="@+id/headset_image" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_headset_sport"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="30dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/headset_image"
            app:layout_constraintEnd_toEndOf="@+id/headset_image"
            app:layout_constraintStart_toStartOf="@+id/headset_image"
            app:layout_constraintTop_toTopOf="@+id/headset_image" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/iv_headset_data_sync"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="30dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@+id/headset_image"
            app:layout_constraintEnd_toEndOf="@+id/headset_image"
            app:layout_constraintStart_toStartOf="@+id/headset_image"
            app:layout_constraintTop_toTopOf="@+id/headset_image"
            app:lottie_autoPlay="true"
            app:lottie_fileName="@string/headphone_SU07_syncing"
            app:lottie_loop="true"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>

