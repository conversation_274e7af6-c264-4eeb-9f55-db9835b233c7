package com.suunto.heartbelt.ui

import android.os.Build
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.stt.android.compose.component.SuuntoNavHost
import com.stt.android.data.smartdeviceota.LatestVersionInfo
import com.stt.android.utils.CustomTabsUtils
import com.suunto.heartbelt.R
import com.suunto.heartbelt.model.HeartBeltSettingItemType
import com.suunto.heartbelt.model.NavigationType
import com.suunto.heartbelt.viewmodel.HeartBeltViewModel
import com.suunto.smartdevice.model.SmartDeviceEvent
import com.suunto.smartdevice.ui.PairDeviceFailedScreen
import com.suunto.smartdevice.ui.PairDeviceScreen
import com.suunto.smartdevice.ui.ScanDeviceScreen
import com.suunto.soa.ble.permission.BlePermission
import kotlinx.coroutines.launch

object NavDestinations {
    const val BLUETOOTH_PERMISSION = "BLUETOOTH_PERMISSION"
    const val FIRST_PAIR_GUIDE = "FIRST_PAIR_GUIDE"
    const val SCAN_DEVICE = "SCAN_DEVICE"
    const val PAIRING = "DEVICE_PAIRING"
    const val PAIR_FAILED = "DEVICE_PAIR_FAILED"
    const val SETTINGS = "SETTINGS"
    const val BATTER_MODE_SETTINGS = "BATTER_MODE_SETTINGS"
    const val RUNNING_FORM_MONITORING_SETTINGS = "RUNNING_FORM_MONITORING_SETTINGS"
    const val DEVICE_VERSION = "DEVICE_VERSION"
    const val DEVICE_OTA = "DEVICE_OTA"
}

@Composable
fun HeartBeltNavGraph(
    viewModel: HeartBeltViewModel,
    onBack: () -> Unit,
    onRequestPermissions: (Array<String>) -> Unit,
    onOpenLocationSettings: () -> Unit,
    onOpenBluetoothSettings: () -> Unit,
    modifier: Modifier = Modifier,
    startDestination: String = NavDestinations.SETTINGS,
    navController: NavHostController = rememberNavController()
) {
    val coroutineScope = rememberCoroutineScope()
    val permissionState by viewModel.permissionState.collectAsState()

    val allPermissionsGranted = permissionState.allowLocation &&
        permissionState.turnOnLocation &&
        permissionState.allowNearbyDevices &&
        permissionState.turnOnBluetooth

    LaunchedEffect(allPermissionsGranted, navController) {
        val currentRoute = navController.currentDestination?.route

        if (!allPermissionsGranted) {
            navController.navigate(NavDestinations.BLUETOOTH_PERMISSION) {
                launchSingleTop = true
            }
        } else if (currentRoute == NavDestinations.BLUETOOTH_PERMISSION) {
            navController.popBackStack()
        }
    }

    SuuntoNavHost(
        navController = navController,
        modifier = modifier,
        startDestination = if (allPermissionsGranted) startDestination else NavDestinations.BLUETOOTH_PERMISSION,
    ) {
        coroutineScope.launch {
            viewModel.navigationType.collect { type ->
                when (type) {
                    NavigationType.PAIR_SUCCESS ->
                        navController.navigate(NavDestinations.SETTINGS) {
                            popUpTo(NavDestinations.PAIRING) { inclusive = true }
                        }

                    NavigationType.PAIR_FAILED ->
                        navController.navigate(NavDestinations.PAIR_FAILED) {
                            popUpTo(NavDestinations.PAIRING) { inclusive = true }
                        }

                    NavigationType.UNKNOWN -> {
                        // do nothing
                    }
                }
            }
        }
        composable(NavDestinations.BLUETOOTH_PERMISSION) {
            BluetoothPermissionScreen(
                onBackClick = onBack,
                onAllowLocationClick = {
                    onRequestPermissions(BlePermission.LOCATION_PERMISSIONS)
                },
                onTurnOnLocationClick = onOpenLocationSettings,
                onAllowNearbyDevicesClick = {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        onRequestPermissions(BlePermission.SCAN_AND_CONNECT_PERMISSIONS)
                    }
                },
                onTurnOnBluetoothClick = onOpenBluetoothSettings,
                permissionState = permissionState
            )
        }

        composable(NavDestinations.FIRST_PAIR_GUIDE) {
            // TODO Implement first pair guide screen
        }

        composable(NavDestinations.SCAN_DEVICE) {
            ScanDeviceScreen(
                viewModel.scanDeviceInfo.collectAsState().value,
                onBack = {
                    onBack()
                },
                onPairDevice = {
                    viewModel.connectNewDevice(it.macAddress)
                    navController.navigate(NavDestinations.PAIRING) {
                        popUpTo(NavDestinations.SCAN_DEVICE) { inclusive = true }
                    }
                }
            )
        }
        composable(NavDestinations.PAIRING) {
            PairDeviceScreen(
                onBack = {
                    viewModel.handDeviceEvent(SmartDeviceEvent.StopPair)
                    onBack()
                },
                onCancelPairEvent = {
                    viewModel.handDeviceEvent(SmartDeviceEvent.StopPair)
                    onBack()
                }
            )
        }
        composable(NavDestinations.PAIR_FAILED) {
            PairDeviceFailedScreen(
                restartDevice = {
                    viewModel.restartConnectDevice()
                    navController.popBackStack()
                    navController.navigate(NavDestinations.PAIRING)
                },
                onHandleEvent = {
                    viewModel.handDeviceEvent(it)
                },
                onBack = onBack,
            )
        }
        composable(NavDestinations.SETTINGS) {
            HeartBeltSettingScreen(
                heartBeltSetting = viewModel.heartBeltSettings.collectAsState().value,
                onHandleClickEvent = {
                    when (it) {
                        HeartBeltSettingItemType.FORGET_DEVICE -> {
                            viewModel.forgetDevice()
                            onBack()
                        }

                        HeartBeltSettingItemType.BATTERY_MODE -> {
                            navController.navigate(NavDestinations.BATTER_MODE_SETTINGS)
                        }

                        HeartBeltSettingItemType.RUNNING_FORM_MONITORING -> {
                            navController.navigate(NavDestinations.RUNNING_FORM_MONITORING_SETTINGS)
                        }

                        HeartBeltSettingItemType.PAIR_ANOTHER_DEVICE -> {
                            viewModel.pairAnotherDevice()
                            navController.popBackStack()
                            navController.navigate(NavDestinations.SCAN_DEVICE)
                        }

                        else -> {
                        }
                    }
                },
                onBack = onBack,
                onRightActionClick = {
                    viewModel.getOtaInfoFromDevice()
                    navController.navigate(NavDestinations.DEVICE_VERSION)
                }
            )
        }

        composable(NavDestinations.DEVICE_VERSION) {
            val latestVersionInfo = viewModel.latestVersionInfo.collectAsState().value
            DeviceVersionScreen(
                deviceName = viewModel.heartBeltSettings.collectAsState().value.smartDevice.name,
                currentVersion = viewModel.currentVersion.collectAsState().value,
                haveNewVersion = latestVersionInfo?.configId != null,
                onBack = {
                    navController.popBackStack()
                },
                onVersionClick = {
                    navController.navigate(NavDestinations.DEVICE_OTA)
                }
            )
        }

        composable(NavDestinations.DEVICE_OTA) {
            val context = LocalContext.current
            DeviceOtaScreen(
                deviceName = viewModel.heartBeltSettings.collectAsState().value.smartDevice.name,
                currentVersion = viewModel.currentVersion.collectAsState().value,
                latestVersionInfo = viewModel.latestVersionInfo.collectAsState().value
                    ?: LatestVersionInfo(),
                otaProgressValue = viewModel.otaProgressValue.collectAsState().value,
                otaInProgress = viewModel.otaInProgress.collectAsState().value,
                onUpdateNow = {
                    viewModel.startOta()
                },
                onBack = {
                    navController.popBackStack()
                },
                onOldVersionClick = {
                    CustomTabsUtils.launchCustomTab(
                        context,
                        context.getString(R.string.software_update_url)
                    )
                }
            )
        }

        composable(NavDestinations.BATTER_MODE_SETTINGS) {
            BatteryModeSettingScreen(
                currentBatteryMode = viewModel.batteryMode.collectAsState().value,
                onBack = {
                    navController.popBackStack()
                },
                onClickBatteryModeItem = {
                    viewModel.updateBatteryMode(it)
                }
            )
        }
        composable(NavDestinations.RUNNING_FORM_MONITORING_SETTINGS) {
            RunningFormMonitoringSettingScreen(
                enabled = viewModel.runningFormMonitoringEnabled.collectAsState().value,
                onStateChange = {
                    viewModel.updateRunningFormMonitoringEnabled(it)
                },
                onBack = {
                    navController.popBackStack()
                },
            )
        }
    }
}
