package com.suunto.soa.ble.control

import com.suunto.soa.ble.device.DeviceConfig
import com.suunto.soa.ble.workqueue.OperationHandler
import io.reactivex.Single
import io.reactivex.subjects.PublishSubject

internal interface InternalController {

    fun initDevice(): Single<Boolean>

    fun disconnect(): Single<Boolean>

    fun destroy()

    fun enableNotifications(serviceUuid: String, characteristicUuid: String): Single<Boolean>

    fun disableNotifications(serviceUuid: String, characteristicUuid: String): Single<Boolean>

    fun modifyMtu(mtu: Int): Single<Boolean>

    fun addHandler(key: Int, handler: OperationHandler)

    fun removeHandler(key: Int)

    fun getHandler(key: Int): OperationHandler?

    fun addNotifyObservable(key: Byte, observable: PublishSubject<*>)

    fun removeNotifyObservable(key: Byte)

    fun getNotifyObservable(key: Byte): PublishSubject<*>?

    fun getDeviceConfig(): DeviceConfig
}
