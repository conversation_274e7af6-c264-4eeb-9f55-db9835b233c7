package com.suunto.soa.ble.operation

import com.suunto.soa.ble.control.InternalController
import com.suunto.soa.ble.response.RunningInfoResponse
import com.suunto.soa.ble.service.BleManager
import com.suunto.soa.command.Request
import com.suunto.soa.state.DeviceRunning

internal class RunningInfoOperation(
    internalController: InternalController,
    bleManager: BleManager,
    request: Request,
    deviceRunning: DeviceRunning
) : RequestOperation<RunningInfoResponse>(internalController, bleManager, request, deviceRunning) {

    override fun buildResponse(bytes: ByteArray): RunningInfoResponse {
        return RunningInfoResponse(bytes)
    }
}
