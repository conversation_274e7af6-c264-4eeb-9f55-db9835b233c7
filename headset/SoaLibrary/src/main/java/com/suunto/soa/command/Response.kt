package com.suunto.soa.command

import com.suunto.soa.DataConstants.CMD_RESPONSE_NO_RESPONSE
import com.suunto.soa.DataConstants.CMD_RESPONSE_RESPONSE
import com.suunto.soa.DataConstants.CMD_SIZE
import com.suunto.soa.DataConstants.CONTENT_SIZE_SIZE
import com.suunto.soa.DataConstants.END_MASK
import com.suunto.soa.DataConstants.OPCODE_SIZE
import com.suunto.soa.DataConstants.OPCODE_SN_SIZE
import com.suunto.soa.DataConstants.START_MASK
import com.suunto.soa.DataConstants.STATUS_SIZE
import com.suunto.soa.DataConstants.STATUS_SUCCESS
import com.suunto.soa.ResponseStatus
import com.suunto.soa.toUInt
import com.suunto.soa.utils.DataUtils.isResponse

abstract class Response<T>(bytes: ByteArray) {

    companion object {

        private fun splitData(bytes: ByteArray): ResponseParamsData {
            if (!isResponse(bytes)) {
                throw IllegalStateException("Invalid data.")
            }

            var valueIndex = START_MASK.size
            val cmd = bytes[valueIndex]

            valueIndex += CMD_SIZE
            val opCode = bytes[valueIndex]

            valueIndex += OPCODE_SIZE
            val contentSizeBytes = byteArrayOf(bytes[valueIndex], bytes[valueIndex + 1])
            val contentSize = toUInt(contentSizeBytes[0], contentSizeBytes[1])
            if (valueIndex + CONTENT_SIZE_SIZE + contentSize + END_MASK.size != bytes.size) {
                throw IllegalStateException("Invalid data.")
            }

            valueIndex += CONTENT_SIZE_SIZE
            val opCodeSn = bytes[valueIndex]

            valueIndex += OPCODE_SN_SIZE
            val status = bytes[valueIndex]

            valueIndex += STATUS_SIZE
            val dataBytes = bytes.copyOfRange(
                valueIndex,
                valueIndex + (contentSize - OPCODE_SN_SIZE - STATUS_SIZE)
            )
            return ResponseParamsData(cmd, opCode, contentSize, opCodeSn, status, dataBytes)
        }
    }

    private val bytes: ByteArray
    val cmd: Byte
    private val opCode: Byte
    private val contentSize: Int
    val opCodeSn: Byte
    val status: Byte

    protected val dataBytes: ByteArray
    abstract val data: T

    init {
        this.bytes = bytes
        val splitData = splitData(bytes)
        this.cmd = splitData.cmd
        this.opCode = splitData.opCode
        this.contentSize = splitData.contentSize
        this.opCodeSn = splitData.opCodeSn
        this.status = splitData.status
        this.dataBytes = splitData.data
    }

    fun isResponseCmd() = cmd == CMD_RESPONSE_RESPONSE

    fun isNoResponseCmd() = cmd == CMD_RESPONSE_NO_RESPONSE

    fun isSuccess() = status == STATUS_SUCCESS

    fun getStatus() = ResponseStatus.entries.firstOrNull { it.code == status.toUInt() }

    fun toByteArray() = bytes.clone()
}
