package com.suunto.soa.ble.response

import com.suunto.soa.ble.exception.ParseException
import com.suunto.soa.command.Response
import com.suunto.soa.data.NeckCheckoutResult
import com.suunto.soa.toUInt

class NeckCheckoutResponse(bytesInfo: ByteArray) : Response<NeckCheckoutResult>(bytesInfo) {

    override val data: NeckCheckoutResult = parseData()

    fun getOriginalData(): ByteArray = dataBytes

    private fun parseData(): NeckCheckoutResult {
        if (dataBytes.size < 13) {
            throw ParseException("parse neck assessment result error.")
        }
        return NeckCheckoutResult(
            leftRotationDegree = toUInt(dataBytes[1], dataBytes[2]),
            rightRotationDegree = toUInt(dataBytes[3], dataBytes[4]),
            forwardDegree = toUInt(dataBytes[5], dataBytes[6]),
            backwardDegree = toUInt(dataBytes[7], dataBytes[8]),
            leftBendDegree = toUInt(dataBytes[9], dataBytes[10]),
            rightBendDegree = toUInt(dataBytes[11], dataBytes[12]),
        )
    }
}
