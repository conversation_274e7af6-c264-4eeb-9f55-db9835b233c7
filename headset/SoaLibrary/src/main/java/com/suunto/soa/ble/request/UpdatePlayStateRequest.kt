package com.suunto.soa.ble.request

import com.suunto.soa.DataConstants.CMD_REQUEST_RESPONSE
import com.suunto.soa.ble.response.OfflineMusicPlayState
import com.suunto.soa.command.OpCodeSn
import com.suunto.soa.command.Request
import com.suunto.soa.to2Bytes
import com.suunto.soa.utils.ext.toBytes
import com.suunto.soa.utils.toU16Long
import java.nio.ByteBuffer

internal class UpdatePlayStateRequest(playState: OfflineMusicPlayState) :
    Request(
        CMD_REQUEST_RESPONSE,
        UPDATE_PLAY_STATE_REQUEST,
        OpCodeSn.nextOpCodeSn(),
        getRequestData(playState)
    ) {

    companion object {
        const val UPDATE_PLAY_STATE_REQUEST = 0x2A.toByte()

        private fun getRequestData(playState: OfflineMusicPlayState): ByteArray {
            val byteBuffer = ByteBuffer.allocate(16)
            byteBuffer.put(playState.playState.state.toByte())
            byteBuffer.put(playState.playMode.mode.toByte())
            byteBuffer.put(playState.playlistId.toU16Long().toBytes())
            byteBuffer.put(playState.musicIndex.toU16Long().toBytes())
            byteBuffer.put(playState.musicId.toU16Long().toBytes())
            byteBuffer.put(playState.musicSortId.to2Bytes())
            return byteBuffer.array()
        }
    }
}
