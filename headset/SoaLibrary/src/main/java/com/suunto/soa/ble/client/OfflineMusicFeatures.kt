package com.suunto.soa.ble.client

import com.suunto.soa.ble.response.OfflineMusicInfo
import com.suunto.soa.ble.response.OfflineMusicPlayState
import com.suunto.soa.ble.response.DevicePlayDetail
import com.suunto.soa.data.FileInfo
import com.suunto.soa.data.HeadsetFile
import com.suunto.soa.data.PlayMode
import io.reactivex.Observable
import java.io.File

interface OfflineMusicFeatures {

    suspend fun getPlayRecord(): DevicePlayDetail

    suspend fun subscribeDeviceMusicPlayState(): Observable<DevicePlayDetail>

    suspend fun unsubscribeDeviceMusicPlayState(): Boolean

    suspend fun deletePlayList(id: String): Boolean

    suspend fun getOfflineMusicInfo(musicIndex: String): OfflineMusicInfo

    suspend fun updatePlayState(playState: OfflineMusicPlayState): Boolean

    suspend fun updatePlayMode(playMode: PlayMode): Boolean

    suspend fun getFileInfoFromDevice(headsetFile: HeadsetFile, fileName: String): FileInfo

    suspend fun getFileFromDevice(fileInfo: FileInfo): File?

    suspend fun sendFileToDevice(fileType: Int, file: File): Boolean
}
