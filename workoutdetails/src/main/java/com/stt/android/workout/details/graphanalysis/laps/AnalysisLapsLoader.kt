package com.stt.android.workout.details.graphanalysis.laps

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.domain.advancedlaps.LapsTable
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.advancedlaps.WindowType
import com.stt.android.domain.user.KILOMETERS_TO_METERS
import com.stt.android.domain.user.MILES_TO_KILOMETERS
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.isMultisport
import com.stt.android.laps.CompleteLap
import com.stt.android.laps.Laps
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.mapping.getSummaryItemListByStId
import com.stt.android.workout.details.LapsData
import com.stt.android.workout.details.advancedlaps.AdvancedLapsDataLoader
import com.stt.android.workout.details.laps.LapsDataLoader
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.suunto.algorithms.data.HeartRate.Companion.bpm
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Loads and returns laps in the advanced laps' [LapsTable] format for all workouts that
 * have meaningful data for laps
 *
 * For workouts that have SML passes through results from [AdvancedLapsDataLoader], for
 * others tries to convert data from [LapsDataLoader] to [LapsTable] format
 */
class AnalysisLapsLoader @Inject constructor(
    private val advancedLapsDataLoader: AdvancedLapsDataLoader,
    private val oldLapsLoader: LapsDataLoader,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val infoModelFormatter: InfoModelFormatter,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) {
    val lapsTableFlow: StateFlow<ViewState<List<LapsTable>?>>
        get() = _lapsTableStateFlow.asStateFlow()

    private val _lapsTableStateFlow: MutableStateFlow<ViewState<List<LapsTable>?>> =
        MutableStateFlow(loading())

    fun loadLapsTables(
        workoutHeader: WorkoutHeader
    ): Flow<ViewState<List<LapsTable>?>> {
        activityRetainedCoroutineScope.launch(IO) {
            combine(
                advancedLapsDataLoader.loadLapsTables(workoutHeader),
                oldLapsLoader.loadLapsData(workoutHeader),
                multisportPartActivityLoader.multisportPartActivityFlow,
                ::Triple
            ).collect { (advancedLapsState, oldLapsState, multisportPartActivityState) ->
                if (workoutHeader.isMultisport && multisportPartActivityState.data == null) {
                    _lapsTableStateFlow.value = loaded(null)
                    return@collect
                }

                if (advancedLapsState.data != null) {
                    _lapsTableStateFlow.value = advancedLapsState
                    return@collect
                }

                if (advancedLapsState.isLoaded() && oldLapsState.data != null) {
                    val stId =
                        multisportPartActivityState.data?.activityType ?: workoutHeader.activityTypeId
                    _lapsTableStateFlow.value =
                        loaded(convertOldLapsToLapsTables(oldLapsState.data!!, stId))
                }
            }
        }

        return _lapsTableStateFlow
    }

    private fun convertOldLapsToLapsTables(lapsData: LapsData, stId: Int): List<LapsTable>? {
        val dataTypes = getSummaryItemListByStId(stId).map { LapsTableDataType.Summary(it) }.toSet()
        val unit = infoModelFormatter.unit
        val oneUnitLength = if (unit == MeasurementUnit.IMPERIAL) {
            MILES_TO_KILOMETERS * KILOMETERS_TO_METERS
        } else {
            KILOMETERS_TO_METERS
        }

        return buildList {
            if (lapsData.hasManualLaps) {
                lapsData.manualLaps?.let { manualLaps ->
                    add(
                        LapsTable(
                            lapsType = LapsTableType.MANUAL,
                            lapsTableRows = completeLapsToLapsTableRow(manualLaps.completeLaps),
                            dataTypes = dataTypes,
                        )
                    )
                }
            }

            if (lapsData.hasLaps) {
                lapsData.automaticLaps?.let { automaticLaps ->
                    val oneUnitAutoLaps = automaticLaps.getLaps(Laps.Type.ONE).completeLaps
                    if (oneUnitAutoLaps.isNotEmpty()) {
                        unit.distanceUnit
                        add(
                            LapsTable(
                                lapsType = if (unit == MeasurementUnit.IMPERIAL) LapsTableType.ONE_MILE_AUTO_LAP else LapsTableType.ONE_KM_AUTO_LAP,
                                lapsTableRows = completeLapsToLapsTableRow(oneUnitAutoLaps),
                                dataTypes = dataTypes,
                                autoLapLength = oneUnitLength.toFloat()
                            )
                        )
                    }

                    val fiveUnitsAutoLaps = automaticLaps.getLaps(Laps.Type.FIVE).completeLaps
                    if (fiveUnitsAutoLaps.isNotEmpty()) {
                        add(
                            LapsTable(
                                lapsType = if (unit == MeasurementUnit.IMPERIAL) LapsTableType.FIVE_MILE_AUTO_LAP else LapsTableType.FIVE_KM_AUTO_LAP,
                                lapsTableRows = completeLapsToLapsTableRow(fiveUnitsAutoLaps),
                                dataTypes = dataTypes,
                                autoLapLength = 5 * oneUnitLength.toFloat()
                            )
                        )
                    }

                    val tenUnitsAutoLaps = automaticLaps.getLaps(Laps.Type.TEN).completeLaps
                    if (tenUnitsAutoLaps.isNotEmpty()) {
                        add(
                            LapsTable(
                                lapsType = if (unit == MeasurementUnit.IMPERIAL) LapsTableType.TEN_MILE_AUTO_LAP else LapsTableType.TEN_KM_AUTO_LAP,
                                lapsTableRows = completeLapsToLapsTableRow(tenUnitsAutoLaps),
                                dataTypes = dataTypes,
                                autoLapLength = 10 * oneUnitLength.toFloat()
                            )
                        )
                    }
                }
            }
        }.takeIf { it.isNotEmpty() }
    }

    private fun completeLapsToLapsTableRow(completeLaps: List<CompleteLap>): List<LapsTableRow> =
        completeLaps.mapIndexed { index, completeLap ->
            val cumulatedDuration = completeLap.workoutDurationOnEnd.toFloat() / 1000f
            LapsTableRow(
                rowid = index + 1,
                lapNumber = index + 1,
                minAltitude = completeLap.minAltitude.toFloat(),
                maxAltitude = completeLap.maxAltitude.toFloat(),
                avgAltitude = null,
                ascent = completeLap.totalAscent.toFloat(),
                ascentTime = null,
                descent = completeLap.totalDescent.toFloat(),
                descentTime = null,
                maxDescent = null,
                minCadence = null,
                maxCadence = null,
                avgCadence = null,
                distance = completeLap.distance.toFloat(),
                distanceMax = null,
                minDownhillGrade = null,
                maxDownhillGrade = null,
                avgDownhillGrade = null,
                duration = completeLap.duration.toFloat() / 1000f,
                energy = null,
                minHR = null,
                maxHR = null,
                avgHR = completeLap.averageHeartRate.bpm.inHz.toFloat(),
                minPower = null,
                maxPower = null,
                avgPower = null,
                recoveryTime = null,
                minSpeed = null,
                maxSpeed = null,
                avgSpeed = completeLap.averageSpeed.toFloat(),
                minStrokeRate = null,
                maxStrokeRate = null,
                avgStrokeRate = null,
                minStrokes = null,
                maxStrokes = null,
                avgStrokes = null,
                swimStyle = null,
                minSwolf = null,
                maxSwolf = null,
                avgSwolf = null,
                minTemperature = null,
                maxTemperature = null,
                avgTemperature = null,
                type = WindowType.LAP,
                minVerticalSpeed = null,
                maxVerticalSpeed = null,
                avgVerticalSpeed = null,
                minDepth = null,
                maxDepth = null,
                avgDepth = null,
                diveTime = null,
                diveRecoveryTime = null,
                diveTimeMax = null,
                diveInWorkout = null,
                cumulatedDistance = completeLap.workoutDistanceOnEnd.toFloat(),
                cumulatedDuration = cumulatedDuration,
                suuntoPlusData = null,
                repetitionCount = null,
                isIntervalRecoveryLap = false,
                aerobicHrThreshold = null,
                anaerobicHrThreshold = null,
                aerobicPowerThreshold = null,
                anaerobicPowerThreshold = null,
                aerobicPaceThreshold = null,
                anaerobicPaceThreshold = null,
                aerobicDuration = null,
                anaerobicDuration = null,
                vo2MaxDuration = null,
                avgStrideLength = null,
                minStrideLength = null,
                maxStrideLength = null,
                fatConsumption = null,
                carbohydrateConsumption = null,
                avgGroundContactTime = null,
                minGroundContactTime = null,
                maxGroundContactTime = null,
                avgVerticalOscillation = null,
                minVerticalOscillation = null,
                maxVerticalOscillation = null,
                avgLeftGroundContactBalance = null,
                minLeftGroundContactBalance = null,
                maxLeftGroundContactBalance = null,
                avgRightGroundContactBalance = null,
                minRightGroundContactBalance = null,
                maxRightGroundContactBalance = null,
                avgAscentSpeed = null,
                minAscentSpeed = null,
                maxAscentSpeed = null,
                avgDescentSpeed = null,
                minDescentSpeed = null,
                maxDescentSpeed = null,
                avgDistancePerStroke = null,
                minDistancePerStroke = null,
                maxDistancePerStroke = null,
            )
        }
}
