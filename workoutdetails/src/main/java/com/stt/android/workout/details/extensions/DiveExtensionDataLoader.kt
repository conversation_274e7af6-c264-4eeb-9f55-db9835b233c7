package com.stt.android.workout.details.extensions

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.DiveExtensionDataModel
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.extensions.loadExtension
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

interface DiveExtensionDataLoader {
    val diveExtensionStateFlow: StateFlow<ViewState<DiveExtension?>>
    suspend fun loadDiveExtension(workoutHeader: WorkoutHeader): StateFlow<ViewState<DiveExtension?>>
}

@ActivityRetainedScoped
class DefaultDiveExtensionDataLoader
@Inject constructor(
    private val diveExtensionDataModel: DiveExtensionDataModel,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) : DiveExtensionDataLoader {
    override val diveExtensionStateFlow: MutableStateFlow<ViewState<DiveExtension?>> =
        MutableStateFlow(loading())

    override suspend fun loadDiveExtension(workoutHeader: WorkoutHeader): StateFlow<ViewState<DiveExtension?>> {
        val supportsDiveProfile = workoutHeader.activityType.supportsDiveProfile
        if (supportsDiveProfile) {
            activityRetainedCoroutineScope.launch {
                getDiveExtension(workoutHeader)
            }
        } else {
            diveExtensionStateFlow.value = loaded()
        }
        return diveExtensionStateFlow
    }

    private suspend fun getDiveExtension(workoutHeader: WorkoutHeader) {
        val diveExtension = diveExtensionDataModel.loadExtension(workoutHeader)
        diveExtensionStateFlow.value = ViewState.Loaded(diveExtension)
    }
}
