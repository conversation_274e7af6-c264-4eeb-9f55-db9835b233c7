package com.stt.android.workout.details.graphanalysis.charts

import android.graphics.Canvas
import android.graphics.Paint
import androidx.core.graphics.withClip
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.renderer.YAxisRenderer
import com.github.mikephil.charting.utils.Transformer
import com.github.mikephil.charting.utils.ViewPortHandler
import com.stt.android.core.domain.GraphType
import com.stt.android.intensityzone.ZoneRangeWithColor
import com.stt.android.workout.details.intensity.IntensityZoneUtils
import kotlin.math.abs

class GraphAnalysisYAxisRenderer(
    viewPortHandler: ViewPortHandler,
    yAxis: YAxis,
    transformer: Transformer
) : YAxisRenderer(viewPortHandler, yAxis, transformer) {
    var numInBetweenGridLines: Int = 4
        set(value) {
            field = maxOf(value, 0)
        }

    var drawOnlyTickMarks = false

    private val inBetweenGridLinePaint = Paint(mGridPaint)

    // Unlike in XAxisRenderer, the original buffer for drawing grid lines is also used to draw
    // the labels so we define our own
    private var renderGridLinesBuffer = FloatArray(0)
    private var isInverted = false
    private lateinit var zoneRangesAndColors: List<ZoneRangeWithColor>
    private lateinit var graphType: GraphType

    private fun getTransformedGridPositions(): FloatArray {
        val numGridLinesPerLabel = numInBetweenGridLines + 1
        val bufferItemsPerLabel = 2 * numGridLinesPerLabel

        // We insert the in between lines below the actual label lines in the loop below,
        // to make sure that the top of the chart always has the in between lines we pretend
        // that there's one more label
        val drawnEntryCount = if (mYAxis.mEntryCount > 0) mYAxis.mEntryCount + 1 else 0
        val drawnEntries =
            if (drawnEntryCount > 0) floatArrayOf(*mYAxis.mEntries, 0f) else floatArrayOf()
        // If there's any labels to draw, we always draw 2 due to the inserted fake label so count 0-1 can be ignored
        if (drawnEntryCount == 2) {
            // Only one real label that is expected to be centered, draw the fake line above
            drawnEntries[1] = drawnEntries[0] * 2
        } else if (drawnEntryCount > 2) {
            // Take the distance between the last two labels, and insert the fake the same distance above
            drawnEntries[drawnEntryCount - 1] = drawnEntries[drawnEntryCount - 2] +
                (drawnEntries[drawnEntryCount - 2] - drawnEntries[drawnEntryCount - 3])
        }

        if (renderGridLinesBuffer.size != drawnEntryCount * bufferItemsPerLabel) {
            renderGridLinesBuffer = FloatArray(drawnEntryCount * bufferItemsPerLabel)
        }

        for (i in renderGridLinesBuffer.indices step bufferItemsPerLabel) {
            val entryPosition = drawnEntries[i / bufferItemsPerLabel]
            // For the first entry estimate distance to potential previous label with the next label
            val distanceComparisonEntryPosition = if (i == 0) {
                drawnEntries[1]
            } else {
                drawnEntries[(i - bufferItemsPerLabel) / bufferItemsPerLabel]
            }

            val entryDistance = abs(entryPosition - distanceComparisonEntryPosition)
            val prevEntryPosition = entryPosition - entryDistance
            val distanceBetweenLines = entryDistance / numGridLinesPerLabel
            // Starting from where the previous label was, move forwards one distance between lines
            // and insert position to array. So the last line added is the actual label line
            for (j in 0 until numGridLinesPerLabel) {
                val bufferIndex = i + (j * 2)
                val bufferValue = prevEntryPosition + (distanceBetweenLines * (j + 1))
                renderGridLinesBuffer[bufferIndex] = bufferValue
                renderGridLinesBuffer[bufferIndex + 1] = bufferValue
            }
        }

        mTrans.pointValuesToPixel(renderGridLinesBuffer)
        return renderGridLinesBuffer
    }

    override fun renderGridLines(c: Canvas) {
        // Original YAxisRenderer had hardcoded the amount & positions of grid lines to match the
        // label entries, so we had to copy and slightly modify the code form MPAndroidChart 3.1.0
        // The only modification here is that we get the positions array from out custom method,
        // and using a separate paint for the custom lines
        if (!mYAxis.isEnabled || drawOnlyTickMarks) return

        if (mYAxis.isDrawGridLinesEnabled) {
            c.withClip(gridClippingRect) {
                val positions = getTransformedGridPositions()
                mGridPaint.color = mYAxis.gridColor
                inBetweenGridLinePaint.color = mYAxis.gridColor
                mGridPaint.strokeWidth = mYAxis.gridLineWidth
                inBetweenGridLinePaint.strokeWidth = mYAxis.gridLineWidth
                mGridPaint.pathEffect = mYAxis.gridDashPathEffect
                inBetweenGridLinePaint.pathEffect = mYAxis.gridDashPathEffect
                inBetweenGridLinePaint.alpha = 64
                val gridLinePath = mRenderGridLinesPath
                gridLinePath.reset()

                // draw the grid
                val numEntriesPerLabel = (numInBetweenGridLines + 1) * 2
                val moduloForLabelLines = numEntriesPerLabel - 2
                for (i in positions.indices step 2) {
                    val paint = if (i != 0 && i % numEntriesPerLabel == moduloForLabelLines) {
                        mGridPaint
                    } else {
                        inBetweenGridLinePaint
                    }

                    // draw a path because lines don't support dashing on lower android versions
                    c.drawPath(linePath(gridLinePath, i, positions), paint)
                    gridLinePath.reset()
                }
            }
        }

        if (mYAxis.isDrawZeroLineEnabled) {
            drawZeroLine(c)
        }
    }

    override fun renderAxisLabels(c: Canvas) {
        super.renderAxisLabels(c)
        // labels are drawn on top of the ticks if they are drawn with the grid itself,
        // so we need to draw them separately after the labels
        renderTickMarks(c)
    }

    private fun renderTickMarks(c: Canvas) {
        if (!mYAxis.isDrawGridLinesEnabled || !mYAxis.isEnabled) return
        val positions = getTransformedGridPositions()
        val leftXStart = mViewPortHandler.contentLeft()
        val rightXStart = mViewPortHandler.contentRight()
        val length = mYAxis.xOffset * 0.75f
        for (i in positions.indices step 2) {
            val yPos = positions[i + 1]
            if (yPos in mViewPortHandler.contentTop()..mViewPortHandler.contentBottom()) {
                c.drawLine(leftXStart, yPos, leftXStart - length, yPos, mGridPaint)
                c.drawLine(rightXStart, yPos, rightXStart + length, yPos, mGridPaint)
            }
        }
    }

    override fun drawYLabels(
        c: Canvas,
        fixedPosition: Float,
        positions: FloatArray,
        offset: Float
    ) {
        if (!::zoneRangesAndColors.isInitialized || zoneRangesAndColors.isEmpty()) {
            super.drawYLabels(c, fixedPosition, positions, offset)
        } else {
            val from = if (mYAxis.isDrawBottomYLabelEntryEnabled) 0 else 1
            val to =
                if (mYAxis.isDrawTopYLabelEntryEnabled) mYAxis.mEntryCount else mYAxis.mEntryCount - 1

            for (i in from..<to) {
                val text = mYAxis.getFormattedLabel(i)
                val value = mYAxis.mEntries.getOrNull(i)
                value?.let {
                    getColorForValue(it)?.let { color ->
                        mAxisLabelPaint.color = color
                    }
                }
                c.drawText(text, fixedPosition, positions[i * 2 + 1] + offset, mAxisLabelPaint)
            }
        }
    }

    fun reset() {
        zoneRangesAndColors = emptyList()
    }

    private fun getColorForValue(value: Float): Int? {
        if (!::zoneRangesAndColors.isInitialized) {
            return null
        }
        return IntensityZoneUtils.getColorForValue(
            value,
            isInverted,
            zoneRangesAndColors,
            graphType
        )
    }

    fun setZones(isInverted: Boolean, zoneRangesAndColors: List<ZoneRangeWithColor>, graphType: GraphType) {
        this.isInverted = isInverted
        this.zoneRangesAndColors = zoneRangesAndColors
        this.graphType = graphType
    }
}
