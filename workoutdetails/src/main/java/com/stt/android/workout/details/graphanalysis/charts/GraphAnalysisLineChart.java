package com.stt.android.workout.details.graphanalysis.charts;

import android.content.Context;
import android.graphics.Canvas;
import android.text.TextUtils;
import android.util.AttributeSet;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.renderer.YAxisRenderer;
import com.github.mikephil.charting.utils.MPPointF;
import com.github.mikephil.charting.utils.Transformer;
import com.github.mikephil.charting.utils.Utils;
import java.util.List;

/**
 * Adds second right Y axis support. Use [GraphAnalysisLineDataSets] and set the graphAnalysisYAxisDependency
 * to RIGHT_SECOND to put data to the second right Y axis. The second right axis doesn't draw vertical grid lines,
 * it draws only its labels and data that is set to depend on it aligns correctly with it.
 *
 * Uses the [GraphAnalysisChartXAXisRenderer] by default that doubles the vertical grid lines
 * and adds little ticks at the bottom of those lines.
 *
 * Some of the overwrites are simply doing same calculations on the second Y axis as the super class
 * does for default axis. Others are bit trickier and they swap the contents of mData field so that we
 * can do things with just the default axis and the custom axis data separately. Otherwise more internals
 * of the chart and its renderer would need to know about the existence of the second Y axis
 */
public class GraphAnalysisLineChart extends LineChart {
    protected YAxis mAxisSecondRight;
    protected YAxisRenderer mAxisRendererSecondRight;
    protected Transformer mSecondRightAxisTransformer;
    protected Highlight[] mSecondRightAxisIndicesToHighlight;

    // These 2 are private in Chart.java, but our customizations need access to them
    // so they are copied here
    private String mNoDataText;
    private boolean mOffsetsCalculated = false;

    private LineData mDefaultAxisData;
    private LineData mSecondYAxisData;

    public GraphAnalysisLineChart(Context context) {
        super(context);
    }

    public GraphAnalysisLineChart(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public GraphAnalysisLineChart(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    // The axis member names and the names of constructor params for various renderers don't match,
    // causing lint warnings
    @SuppressWarnings("SuspiciousNameCombination")
    @Override
    protected void init() {
        super.init();

        mRenderer = new GraphAnalysisLineChartRenderer(this, mAnimator, mViewPortHandler);

        mAxisSecondRight = new YAxis(YAxis.AxisDependency.RIGHT);
        mSecondRightAxisTransformer = new Transformer(mViewPortHandler);
        mAxisRendererSecondRight = new YAxisRenderer(mViewPortHandler, mAxisSecondRight, mSecondRightAxisTransformer);

        mXAxisRenderer = new GraphAnalysisChartXAxisRenderer(mViewPortHandler, mXAxis, mLeftAxisTransformer);
        mAxisRendererLeft = new GraphAnalysisYAxisRenderer(mViewPortHandler, mAxisLeft, mLeftAxisTransformer);
        mAxisRendererRight = new GraphAnalysisYAxisRenderer(mViewPortHandler, mAxisRight, mRightAxisTransformer);
        mAxisRendererSecondRight= new GraphAnalysisYAxisRenderer(mViewPortHandler, mAxisSecondRight, mSecondRightAxisTransformer);

        setHighlighter(new GraphAnalysisChartHighlighter(this));
    }

    public YAxis getAxisSecondRight() {
        return mAxisSecondRight;
    }

    public Transformer getSecondRightAxisTransformer() {
        return mSecondRightAxisTransformer;
    }

    public LineData getSecondYAxisData() {
        return mSecondYAxisData;
    }

    /**
     * Get all candidates the Highlighter considered as the one that best represents the touch position.
     * Should contain one Highlight per Y-axis.
     */
    public List<GraphAnalysisChartHighlight> getLatestHighlightBuffer() {
        return ((GraphAnalysisChartHighlighter)mHighlighter).getLatestHighlightBuffer();
    }

    public GraphAnalysisChartXAxisRenderer getGraphAnalysisXAxisRenderer() {
        return (GraphAnalysisChartXAxisRenderer)mXAxisRenderer;
    }

    public GraphAnalysisYAxisRenderer getGraphAnalysisYAxisRendererLeft() {
        return (GraphAnalysisYAxisRenderer)mAxisRendererLeft;
    }

    public GraphAnalysisYAxisRenderer getGraphAnalysisYAxisRendererRight() {
        return (GraphAnalysisYAxisRenderer)mAxisRendererRight;
    }

    public GraphAnalysisYAxisRenderer getGraphAnalysisYAxisRendererSecondRight() {
        return (GraphAnalysisYAxisRenderer)mAxisRendererSecondRight;
    }

    public GraphAnalysisChartHighlighter getGraphAnalysisChartHighlighter() {
        return (GraphAnalysisChartHighlighter)getHighlighter();
    }

    public void setHighlightLineTopOffset(float offset) {
        ((GraphAnalysisLineChartRenderer)mRenderer).setHighlightLineTopOffset(offset);
    }

    @Override
    public Transformer getTransformer(YAxis.AxisDependency which) {
        if (which == YAxis.AxisDependency.LEFT) {
            return mLeftAxisTransformer;
        } else {
            if (mData == mSecondYAxisData) {
                return mSecondRightAxisTransformer;
            } else {
                return mRightAxisTransformer;
            }
        }
    }

    @Override
    protected void prepareOffsetMatrix() {
        super.prepareOffsetMatrix();
        mSecondRightAxisTransformer.prepareMatrixOffset(mAxisSecondRight.isInverted());
    }

    @Override
    protected void prepareValuePxMatrix() {
        super.prepareValuePxMatrix();
        mSecondRightAxisTransformer.prepareMatrixValuePx(
            mXAxis.mAxisMinimum,
            mXAxis.mAxisRange,
            mAxisSecondRight.mAxisRange,
            mAxisSecondRight.mAxisMinimum
        );
    }

    @Override
    public void notifyDataSetChanged() {
        mAxisRendererSecondRight.computeAxis(
            mAxisSecondRight.mAxisMinimum,
            mAxisSecondRight.mAxisMaximum,
            mAxisSecondRight.isInverted()
        );
        super.notifyDataSetChanged();
    }

    @Override
    protected void autoScale() {
        if (mAxisSecondRight.isEnabled()) {
            final float fromX = getLowestVisibleX();
            final float toX = getHighestVisibleX();
            mAxisSecondRight.calculate(fromX, toX);
        }

        super.autoScale();

        if (mDefaultAxisData.getDataSetCount() == 0 && mSecondYAxisData.getDataSetCount() > 0) {
            mXAxis.calculate(mSecondYAxisData.getXMin(), mSecondYAxisData.getXMax());
        }
    }

    @Override
    public void calculateOffsets() {
        // Make the labels show on top of each other if both right Y axis are shown,
        // the defaults are shown above the line and custom ones below it
        if (mAxisSecondRight.isEnabled() && mAxisRight.isEnabled()) {
            float offsetMultiplier = 0.75f;
            mAxisRight.setYOffset(-Utils.convertPixelsToDp(mAxisRight.getTextSize()) * offsetMultiplier);
            mAxisSecondRight.setYOffset(Utils.convertPixelsToDp(mAxisSecondRight.getTextSize()) * offsetMultiplier);
        } else {
            mAxisRight.setYOffset(0f);
            mAxisSecondRight.setYOffset(0f);
        }

        // Make sure the horizontal area for labels always fits no matter which Y axis' labels are wider
        float axisRightWidth = mAxisRight.isEnabled()
            ? mAxisRight.getRequiredWidthSpace(mAxisRendererRight.getPaintAxisLabels())
            : 0f;
        float axisSecondRightWidth = mAxisSecondRight.isEnabled()
            ? mAxisSecondRight.getRequiredWidthSpace(mAxisRendererSecondRight.getPaintAxisLabels())
            : 0f;
        if (axisSecondRightWidth > axisRightWidth) {
            setExtraRightOffset(Utils.convertPixelsToDp(axisSecondRightWidth - axisRightWidth));
        } else {
            setExtraRightOffset(0f);
        }

        super.calculateOffsets();
    }

    @Override
    protected void calcMinMax() {
        super.calcMinMax();

        if (mDefaultAxisData.getDataSetCount() == 0 && mSecondYAxisData.getDataSetCount() > 0) {
            mXAxis.calculate(mSecondYAxisData.getXMin(), mSecondYAxisData.getXMax());
        }

        mAxisSecondRight.calculate(mSecondYAxisData.getYMin(), mSecondYAxisData.getYMax());
    }

    @Override
    public void clear() {
        super.clear();
        mOffsetsCalculated = false;
    }

    @Override
    public void setData(LineData data) {
        // TODO - check that highlighting works properly even with this approach where
        // the second Y-Axis data isn't part of the dataset for the chart and is basically
        // only rendered. Keeping it in the dataset messes with min-max ranges of the first right Y-xis
        mOffsetsCalculated = false;
        mDefaultAxisData = new LineData();
        mSecondYAxisData = new LineData();
        for (ILineDataSet set: data.getDataSets()) {
            if (set instanceof GraphAnalysisLineDataSet &&
                ((GraphAnalysisLineDataSet)set).getGraphAnalysisYAxisDependency() == GraphAnalysisYAxisDependency.RIGHT_SECOND
            ) {
                mSecondYAxisData.addDataSet(set);
            } else {
                mDefaultAxisData.addDataSet(set);
            }
        }

        if (mDefaultAxisData.getDataSetCount() == 0 && mSecondYAxisData.getDataSetCount() > 0) {
            getGraphAnalysisXAxisRenderer().setTransformer(mSecondRightAxisTransformer);
        } else {
            getGraphAnalysisXAxisRenderer().setTransformer(mLeftAxisTransformer);
        }

        super.setData(mDefaultAxisData);
    }

    private void useDefaultAxisData() {
        mData = mDefaultAxisData;
    }

    private void useSecondYAxisData() {
        mData = mSecondYAxisData;
    }

    @Override
    public void setNoDataText(String text) {
        mNoDataText = text;
        super.setNoDataText(text);
    }

    @Override
    public float getLowestVisibleX() {
        float defaultAxisLowestX = super.getLowestVisibleX();

        if (mSecondYAxisData.getEntryCount() > 0) {
            mSecondRightAxisTransformer.getValuesByTouchPoint(mViewPortHandler.contentLeft(),
                mViewPortHandler.contentBottom(), posForGetLowestVisibleX);
            float secondRightAxisLowestX = (float)Math.max(mXAxis.mAxisMinimum, posForGetLowestVisibleX.x);

            return Math.min(defaultAxisLowestX, secondRightAxisLowestX);
        } else {
            return defaultAxisLowestX;
        }
    }

    @Override
    public float getHighestVisibleX() {
        float defaultAxisHighestX = super.getHighestVisibleX();

        if (mSecondYAxisData.getEntryCount() > 0) {
            mSecondRightAxisTransformer.getValuesByTouchPoint(mViewPortHandler.contentRight(),
                mViewPortHandler.contentBottom(), posForGetHighestVisibleX);
            float secondRightAxisHighestX = (float)Math.min(mXAxis.mAxisMaximum, posForGetHighestVisibleX.x);

            return Math.max(defaultAxisHighestX, secondRightAxisHighestX);
        } else {
            return defaultAxisHighestX;
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        // This method has been copied and slightly modified from the MPAndroidChart 3.1.0
        // BarLineChartBase and Chart classes to add support for drawing the second right Y-axis.
        // The modifications consist of:
        // - removing logging
        // - adding several places where the contents of mData are temporarily swapped to use data
        //   with the second right Y-axis data and calling a renderer's draw method again.
        // - removing clipping rect from the canvas earlier than usual to allow the customized highlight line
        //   drawing to work
        // So everything between calls to [useSecondYAxisData] and [useDefaultAxisData]
        // or lines that mention mAxisSecondRight / mAxisRendererSecondRight are added, other
        // changes to original source apart from adding braces to if / elses where needed are
        // commented separately.

        // Begin Chart.java
        if (mData == null) {
            boolean hasText = !TextUtils.isEmpty(mNoDataText);

            if (hasText) {
                MPPointF c = getCenter();
                canvas.drawText(mNoDataText, c.x, c.y, mInfoPaint);
            }

            return;
        }

        if (!mOffsetsCalculated) {
            calculateOffsets();
            mOffsetsCalculated = true;
        }
        // End Chart.java


        // Begin BarLineChartBase.java
        if (mData == null)
            return;

        // execute all drawing commands
        drawGridBackground(canvas);

        if (mAutoScaleMinMaxEnabled) {
            autoScale();
        }

        if (mAxisLeft.isEnabled())
            mAxisRendererLeft.computeAxis(mAxisLeft.mAxisMinimum, mAxisLeft.mAxisMaximum, mAxisLeft.isInverted());

        if (mAxisRight.isEnabled())
            mAxisRendererRight.computeAxis(mAxisRight.mAxisMinimum, mAxisRight.mAxisMaximum, mAxisRight.isInverted());

        useSecondYAxisData();
        if (mAxisSecondRight.isEnabled())
            mAxisRendererSecondRight.computeAxis(mAxisSecondRight.mAxisMinimum, mAxisSecondRight.mAxisMaximum, mAxisSecondRight.isInverted());

        useDefaultAxisData();
        if (mXAxis.isEnabled())
            mXAxisRenderer.computeAxis(mXAxis.mAxisMinimum, mXAxis.mAxisMaximum, false);

        mXAxisRenderer.renderAxisLine(canvas);
        mAxisRendererLeft.renderAxisLine(canvas);
        mAxisRendererRight.renderAxisLine(canvas);

        if (mXAxis.isDrawGridLinesBehindDataEnabled())
            mXAxisRenderer.renderGridLines(canvas);

        if (mAxisLeft.isDrawGridLinesBehindDataEnabled())
            mAxisRendererLeft.renderGridLines(canvas);

        if (mAxisRight.isDrawGridLinesBehindDataEnabled())
            mAxisRendererRight.renderGridLines(canvas);

        if (mXAxis.isEnabled() && mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (mAxisLeft.isEnabled() && mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (mAxisRight.isEnabled() && mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        // make sure the data cannot be drawn outside the content-rect
        int clipRestoreCount = canvas.save();
        canvas.clipRect(mViewPortHandler.getContentRect());

        useSecondYAxisData();
        mRenderer.drawData(canvas);
        useDefaultAxisData();
        mRenderer.drawData(canvas);

        if (!mXAxis.isDrawGridLinesBehindDataEnabled())
            mXAxisRenderer.renderGridLines(canvas);

        if (!mAxisLeft.isDrawGridLinesBehindDataEnabled())
            mAxisRendererLeft.renderGridLines(canvas);

        if (!mAxisRight.isDrawGridLinesBehindDataEnabled())
            mAxisRendererRight.renderGridLines(canvas);

        // STT CUSTOMIZED - ORIGINALLY BETWEEN DRAWING HIGHLIGHTS AND EXTAS
        // Removes clipping rectangle
        canvas.restoreToCount(clipRestoreCount);

        // if highlighting is enabled
        if (valuesToHighlight()) {
            mRenderer.drawHighlighted(canvas, mIndicesToHighlight);
        }

        if (secondYAxisValuesToHighlight()) {
            useSecondYAxisData();
            mRenderer.drawHighlighted(canvas, mSecondRightAxisIndicesToHighlight);
            useDefaultAxisData();
        }

        // STT CUSTOMIZED - MOVED BEFORE DRAWING HIGHLIGHTS
        //canvas.restoreToCount(clipRestoreCount);

        mRenderer.drawExtras(canvas);
        useSecondYAxisData();
        mRenderer.drawExtras(canvas);
        useDefaultAxisData();

        if (mXAxis.isEnabled() && !mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (mAxisLeft.isEnabled() && !mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (mAxisRight.isEnabled() && !mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        mXAxisRenderer.renderAxisLabels(canvas);
        mAxisRendererLeft.renderAxisLabels(canvas);
        mAxisRendererRight.renderAxisLabels(canvas);
        mAxisRendererSecondRight.renderAxisLabels(canvas); // Added line for second right Y-Axis

        if (isClipValuesToContentEnabled()) {
            clipRestoreCount = canvas.save();
            canvas.clipRect(mViewPortHandler.getContentRect());
            mRenderer.drawValues(canvas);
            useSecondYAxisData();
            mRenderer.drawValues(canvas);
            useDefaultAxisData();

            canvas.restoreToCount(clipRestoreCount);
        } else {
            mRenderer.drawValues(canvas);
            useSecondYAxisData();
            mRenderer.drawValues(canvas);
            useDefaultAxisData();
        }

        mLegendRenderer.renderLegend(canvas);

        drawDescription(canvas);

        drawMarkers(canvas);
        // STT CUSTOMIZED - The second right axis data swap has extra step of also
        // swapping contents of mIndicesToHighlight
        Highlight[] defaultAxisHighlights = mIndicesToHighlight;
        useSecondYAxisData();
        mIndicesToHighlight = mSecondRightAxisIndicesToHighlight;
        drawMarkers(canvas);
        useDefaultAxisData();
        mIndicesToHighlight = defaultAxisHighlights;

        // End BarLineChartBase.java
    }

    @Override
    public void highlightValue(Highlight high, boolean callListener) {
        // This method has been copied and slightly modified from the MPAndroidChart 3.1.0
        // The original version in Chart.java nulls out the Highlight if it can't find the Entry
        // for it in mData, which in our case can happen if the Highlight points to mSecondYAxisData.
        // To fix this and some issues with rendering, we check if the Highlight has our custom Y-Axis
        // dependency data, and store it separately if it belongs to the second right Y-Axis.

        Entry e = null;

        if (high == null) {
            mIndicesToHighlight = null;
            mSecondRightAxisIndicesToHighlight = null;
        }
        else {
            // STT CUSTOMIZATIONS START - CHECK IF DATA IS FROM SECOND RIGHT Y-AXIS
            boolean isSecondYAxisHighlight = false;
            if (high instanceof GraphAnalysisChartHighlight) {
                GraphAnalysisChartHighlight gacHigh = (GraphAnalysisChartHighlight)high;
                e = gacHigh.getEntry();
                if (gacHigh.getGraphAnalysisAxis() == GraphAnalysisYAxisDependency.RIGHT_SECOND) {
                    isSecondYAxisHighlight = true;
                }
            } else {
                e = mData.getEntryForHighlight(high);
            }
            // STT CUSTOMIZATIONS END

            if (e == null) {
                mIndicesToHighlight = null;
                mSecondRightAxisIndicesToHighlight = null;
                high = null;
            } else {
                // set the indices to highlight

                // STT CUSTOMIZATIONS START - STORE IN OUR CUSTOM ARRAY IF FROM SECOND RIGHT Y-AXIS
                Highlight[] highlightsArray = new Highlight[]{
                    high
                };
                if (isSecondYAxisHighlight) {
                    mIndicesToHighlight = null;
                    mSecondRightAxisIndicesToHighlight = highlightsArray;
                } else {
                    mIndicesToHighlight = highlightsArray;
                    mSecondRightAxisIndicesToHighlight = null;
                }
            }
        }

        setLastHighlighted(mIndicesToHighlight);

        if (callListener && mSelectionListener != null) {

            if (!valuesToHighlight() && !secondYAxisValuesToHighlight())
                mSelectionListener.onNothingSelected();
            else {
                // notify the listener
                mSelectionListener.onValueSelected(e, high);
            }
        }

        // redraw the chart
        invalidate();
    }

    /**
     * Counterpart for the default [valuesToHighlight] that checks our own custom array of highlights
     * in second right Y-axis instead of the default highlights
     */
    public boolean secondYAxisValuesToHighlight() {
        return mSecondRightAxisIndicesToHighlight != null
            && mSecondRightAxisIndicesToHighlight.length > 0
            && mSecondRightAxisIndicesToHighlight[0] != null;
    }

    /**
     * Counterpart for the default [getHighlighted] that checks our own custom array of highlights
     * in second right Y-axis instead of the default highlights
     */
    public Highlight[] getSecondRightAxisHighlighted() {
        return mSecondRightAxisIndicesToHighlight;
    }
}

