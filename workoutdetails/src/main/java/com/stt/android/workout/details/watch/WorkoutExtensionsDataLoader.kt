package com.stt.android.workout.details.watch

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.GetExtensionsUseCase
import com.stt.android.workout.details.WorkoutExtensionsData
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

interface WorkoutExtensionsDataLoader {
    val workoutExtensionsStateFlow: StateFlow<ViewState<WorkoutExtensionsData?>>
    suspend fun loadWorkoutExtensions(workoutHeader: WorkoutHeader): StateFlow<ViewState<WorkoutExtensionsData?>>
}

@ActivityRetainedScoped
class DefaultWorkoutExtensionsDataLoader @Inject constructor(
    private val getExtensionsUseCase: GetExtensionsUseCase,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) : WorkoutExtensionsDataLoader {

    override val workoutExtensionsStateFlow: MutableStateFlow<ViewState<WorkoutExtensionsData?>> =
        MutableStateFlow(ViewState.Loading(null))

    override suspend fun loadWorkoutExtensions(workoutHeader: WorkoutHeader): StateFlow<ViewState<WorkoutExtensionsData?>> {
        activityRetainedCoroutineScope.launch {
            val data = withContext(IO) {
                loaded(
                    WorkoutExtensionsData(
                        workoutExtensions = getExtensionsUseCase.getExtensions(workoutHeader)
                    )
                )
            }
            workoutExtensionsStateFlow.value = data
        }

        return workoutExtensionsStateFlow
    }
}
