package com.stt.android.workout.details.divetrack

import androidx.annotation.IntRange
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.DatahubAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.ViewState
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.divetrack.DiveTrack
import com.stt.android.divetrack.DiveTrackSettings
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.logbook.NgDiveRouteGyroBias
import com.stt.android.logbook.NgDiveRouteOrigin
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workoutdetail.divetrack.DiveTrackUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.dropWhile
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class FullscreenDiveTrackViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val smlDataLoader: SmlDataLoader,
    private val datahubAnalyticsTracker: DatahubAnalyticsTracker,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    data class ViewData(
        val diveTrackSettings: DiveTrackSettings,
        val diveTrack: DiveTrack,
        val showResetCamera: Boolean,
        val diveRouteOrigin: NgDiveRouteOrigin?,
        val diveRouteQuality: Double?,
        val diveRouteGyroBias: NgDiveRouteGyroBias?,
    )

    private val workoutHeader =
        FullscreenDiveTrackActivityArgs.fromSavedStateHandle(savedStateHandle).workoutHeader

    private val _viewState: MutableStateFlow<ViewState<ViewData>> =
        MutableStateFlow(ViewState.Loading())
    val viewState: StateFlow<ViewState<ViewData>> = _viewState.asStateFlow()

    fun loadDiveTrack() {
        viewModelScope.launch(coroutinesDispatchers.io) { loadDiveTrackInternal(workoutHeader) }
    }

    private suspend fun loadDiveTrackInternal(workoutHeader: WorkoutHeader) = runSuspendCatching {
        val sml = smlDataLoader.loadSml(workoutHeader).dropWhile { it.isLoading() }.firstOrNull()?.data
        DiveTrackUtil.diveTrackFromSml(sml)?.let { diveTrack ->
            _viewState.update { currentViewState ->
                val settings = (currentViewState as? ViewState.Loaded)?.data?.diveTrackSettings
                    ?: DiveTrackSettings(
                        supportGestures = true,
                        shouldDestroyWhenViewDetached = true,
                        cameraSetting = DiveTrackSettings.CameraSetting.FAR,
                        showBottomPlane = true,
                    )
                val viewData = ViewData(
                    diveTrackSettings = settings,
                    diveTrack = diveTrack,
                    showResetCamera = false,
                    diveRouteOrigin = sml?.streamData?.diveTrackOrigin?.firstOrNull(),
                    diveRouteQuality = sml?.streamData?.diveTrackQuality?.firstOrNull(),
                    diveRouteGyroBias = sml?.summary?.diveFooter?.diveRouteGyroBias,
                )
                ViewState.Loaded(viewData)
            }
        } ?: ViewState.Loaded(null)
    }.onFailure { e ->
        Timber.w(e, "Failed to load dive track")
        _viewState.value = ViewState.Error(ErrorEvent.get(e::class))
    }

    private fun updateDiveTrackSettings(block: (currentSettings: DiveTrackSettings) -> DiveTrackSettings) {
        _viewState.update { currentViewState ->
            (currentViewState as? ViewState.Loaded)
                ?.data
                ?.let { viewData ->
                    val updatedDiveTrackSettings = block(viewData.diveTrackSettings)
                    ViewState.Loaded(
                        viewData.copy(
                            diveTrackSettings = updatedDiveTrackSettings,
                        )
                    )
                } ?: currentViewState
        }
    }

    fun setShowBottomWall(show: Boolean) {
        updateDiveTrackSettings { it.copy(showBottomWall = show) }
    }

    fun resetCamera() {
        _viewState.update { currentViewState ->
            (currentViewState as? ViewState.Loaded)
                ?.data
                ?.let { viewData ->
                    ViewState.Loaded(
                        viewData.copy(
                            diveTrackSettings = viewData.diveTrackSettings.copy(resetCamera = true),
                            showResetCamera = false,
                        )
                    )
                } ?: currentViewState
        }

        updateDiveTrackSettings { it.copy(resetCamera = true) }
    }

    fun onCameraResetCompleted() {
        updateDiveTrackSettings { it.copy(resetCamera = false) }
    }

    fun onCameraMoved() {
        _viewState.update { currentViewState ->
            (currentViewState as? ViewState.Loaded)
                ?.data
                ?.let { viewData ->
                    ViewState.Loaded(
                        viewData.copy(
                            showResetCamera = true,
                        )
                    )
                } ?: currentViewState
        }
    }

    fun sendUserRating(@IntRange(from = 1, to = 5) rating: Int) {
        (_viewState.value as? ViewState.Loaded<ViewData>)?.data
            ?.let { viewData ->
                AnalyticsProperties().apply {
                    put(AnalyticsEventProperty.WORKOUT_KEY, workoutHeader.key)
                    put(AnalyticsEventProperty.DIVE_ROUTE_USER_FEEDBACK_RATING, rating)
                    put(AnalyticsEventProperty.DIVE_ROUTE_USER_FEEDBACK_QUALITY, viewData.diveRouteQuality)
                    put(AnalyticsEventProperty.DIVE_ROUTE_USER_FEEDBACK_ORIGIN_ALTITUDE, viewData.diveRouteOrigin?.altitude)
                    put(AnalyticsEventProperty.DIVE_ROUTE_USER_FEEDBACK_ORIGIN_LATITUDE, viewData.diveRouteOrigin?.latitude)
                    put(AnalyticsEventProperty.DIVE_ROUTE_USER_FEEDBACK_ORIGIN_LONGITUDE, viewData.diveRouteOrigin?.longitude)
                    put(AnalyticsEventProperty.DIVE_ROUTE_USER_FEEDBACK_FIGURE_OF_MERIT, viewData.diveRouteGyroBias?.figureOfMerig)
                    put(AnalyticsEventProperty.DIVE_ROUTE_USER_FEEDBACK_GYRO_BIAS_X, viewData.diveRouteGyroBias?.x)
                    put(AnalyticsEventProperty.DIVE_ROUTE_USER_FEEDBACK_GYRO_BIAS_Y, viewData.diveRouteGyroBias?.y)
                    put(AnalyticsEventProperty.DIVE_ROUTE_USER_FEEDBACK_GYRO_BIAS_Z, viewData.diveRouteGyroBias?.z)
                }.let { datahubAnalyticsTracker.trackEvent(AnalyticsEvent.DIVE_ROUTE_USER_FEEDBACK, it) }
            }
    }
}
