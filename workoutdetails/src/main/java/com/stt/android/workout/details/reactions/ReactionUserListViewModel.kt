package com.stt.android.workout.details.reactions

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AnalyticsPropertyValue.FollowSourceProperty
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.failure
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.ReactionModel
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.session.CurrentUser
import com.stt.android.domain.user.ReactionSummary
import com.stt.android.follow.FollowStatus
import com.stt.android.follow.UserFollowStatus
import com.stt.android.home.people.PeopleController
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.toV2
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class ReactionUserListViewModel @Inject constructor(
    private val peopleController: PeopleController,
    private val reactionModel: ReactionModel,
    private val currentUser: CurrentUser,
) : ViewModel() {
    private val _navigationEvent = SingleLiveEvent<String>()
    val navigationEvent: LiveData<String> = _navigationEvent

    private val _viewState = MutableLiveData<ViewState<ReactionUserListViewState?>>()
    val viewState: LiveData<ViewState<ReactionUserListViewState?>> = _viewState

    fun loadData(workoutKey: String) {
        viewModelScope.launch {
            Timber.d("Loading reactions user list")
            _viewState.value = loading(_viewState.value?.data)

            runSuspendCatching {
                // loadReactions emits data from local DB first and then data fetched from backend
                reactionModel.loadReactions(workoutKey, ReactionSummary.REACTION_LIKE)
                    .map { reactions ->
                        peopleController.loadUfsListFromDbForReactedUsers(reactions)
                            .sortedWith { o1, o2 ->
                                o1.realNameOrUsername.compareTo(
                                    o2.realNameOrUsername,
                                    true
                                )
                            }
                    }
                    .catch { Timber.w(it, "loadReactions failed") }
                    .flowOn(IO)
                    .collect { userFollowStatusList ->
                        _viewState.value = loaded(
                            ReactionUserListViewState(
                                currentUsername = currentUser.getUsername(),
                                workoutKey = workoutKey,
                                onFollowButtonClicked = ::onFollowButtonClick,
                                onUserClicked = ::onUserClicked,
                                userFollowStatusList = userFollowStatusList
                            )
                        )
                    }
            }.onFailure { e ->
                Timber.w(e, "Failed to load reaction users list")
                _viewState.value = failure(ErrorEvent.get(e::class))
            }
        }
    }

    private fun onUserClicked(workoutKey: String, userFollowStatus: UserFollowStatus) {
        Timber.d("Navigating to user profile [username = ${userFollowStatus.username}] from workout key: $workoutKey")
        _navigationEvent.value = userFollowStatus.username
    }

    private fun onFollowButtonClick(workoutKey: String, userFollowStatus: UserFollowStatus) {
        viewModelScope.launch {
            try {
                when (userFollowStatus.status) {
                    FollowStatus.FOLLOWING -> unfollow(userFollowStatus)
                    FollowStatus.PENDING -> unfollow(userFollowStatus)
                    FollowStatus.REJECTED -> unfollow(userFollowStatus)
                    FollowStatus.UNFOLLOWING -> follow(userFollowStatus)
                    FollowStatus.FAILED -> follow(userFollowStatus)
                    FollowStatus.FRIENDS -> unfollow(userFollowStatus)
                }
                loadData(workoutKey)
            } catch (e: Exception) {
                val previousState = _viewState.value?.data
                if (previousState != null) {
                    Timber.w(e, "Error attempting to follow user")
                    _viewState.value = failure(ErrorEvent.get(e::class), previousState)
                }
            }
        }
    }

    private suspend fun follow(userFollowStatus: UserFollowStatus) = withContext(IO) {
        Timber.d("Following user")
        peopleController.follow(userFollowStatus, FollowSourceProperty.LIKES_LIST)
            .toV2()
            .await()
    }

    private suspend fun unfollow(userFollowStatus: UserFollowStatus) = withContext(IO) {
        peopleController.unfollow(userFollowStatus).toV2().await()
    }
}
