package com.stt.android.workout.details.graphanalysis.charts

import android.graphics.Canvas
import android.graphics.Path
import com.github.mikephil.charting.animation.ChartAnimator
import com.github.mikephil.charting.interfaces.dataprovider.LineDataProvider
import com.github.mikephil.charting.interfaces.datasets.ILineScatterCandleRadarDataSet
import com.github.mikephil.charting.utils.ViewPortHandler
import com.stt.android.ui.components.charts.ZoneLineChartRenderer

/**
 * Customizes default LineChartRenderer by allowing the highlight line to extend outside of the chart
 * content area
 */
class GraphAnalysisLineChartRenderer(
    chart: LineDataProvider,
    animator: ChartA<PERSON>mator,
    viewPortHandler: ViewPortHandler
) : ZoneLineChartRenderer(chart, animator, viewPortHandler) {
    var highlightLineTopOffset: Float = 0f

    private val highlightLinePath = Path()

    /**
     * Taken from MPAndroidChart 3.1.0 LineChartterCandleRaderRenderer, the starting point of the line
     * can be customized with the [highlightLineTopOffset]
     */
    override fun drawHighlightLines(
        c: Canvas,
        x: Float,
        y: Float,
        set: ILineScatterCandleRadarDataSet<*>
    ) {
        if (x !in mViewPortHandler.contentLeft()..mViewPortHandler.contentRight()) {
            return
        }

        // set color and stroke-width
        mHighlightPaint.color = set.highLightColor
        mHighlightPaint.strokeWidth = set.highlightLineWidth

        // draw highlighted lines (if enabled)
        mHighlightPaint.pathEffect = set.dashPathEffectHighlight

        // draw vertical highlight lines
        if (set.isVerticalHighlightIndicatorEnabled) {
            // create vertical path
            highlightLinePath.reset()

            // STT CUSTOMIZED LINE, ORIGINALLY Y WAS JUST mViewPortHandler.contentTop()
            highlightLinePath.moveTo(x, mViewPortHandler.contentTop() - highlightLineTopOffset)
            highlightLinePath.lineTo(x, mViewPortHandler.contentBottom())
            c.drawPath(highlightLinePath, mHighlightPaint)
        }

        // draw horizontal highlight lines
        if (set.isHorizontalHighlightIndicatorEnabled) {
            // create horizontal path
            highlightLinePath.reset()
            highlightLinePath.moveTo(mViewPortHandler.contentLeft(), y)
            highlightLinePath.lineTo(mViewPortHandler.contentRight(), y)
            c.drawPath(highlightLinePath, mHighlightPaint)
        }
    }
}
