package com.stt.android.workout.details.trend

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import android.widget.AdapterView
import android.widget.Button
import android.widget.ImageView
import android.widget.Spinner
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.viewpager.widget.ViewPager
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.domain.localization.Localizable
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummaryData
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.activities.SimilarWorkoutsActivity
import com.stt.android.ui.adapters.RecentWorkoutTrendPagerAdapter
import com.stt.android.ui.fragments.SimilarWorkoutsListFragment
import com.stt.android.ui.fragments.summaries.CustomDropdownArrayAdapter
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.workout.details.R
import com.stt.android.workout.details.RecentTrendData
import com.stt.android.workout.details.competition.setCompetitionIcon
import com.stt.android.workout.details.competition.setCompetitionSubtitleText
import com.stt.android.workout.details.competition.setCompetitionTitleColor
import com.stt.android.workout.details.competition.setCompetitionTitleText
import com.stt.android.workoutcomparison.WorkoutComparisonActivity
import com.stt.android.workoutdetail.trend.RouteSelection
import me.relex.circleindicator.CircleIndicator
import java.lang.ref.WeakReference
import kotlin.math.roundToLong
import com.stt.android.R as BaseR
import com.stt.android.core.R as CoreR

@EpoxyModelClass
abstract class RecentTrendDataModel :
    EpoxyModelWithHolder<RecentWorkoutTrendViewHolder>(),
    AdapterView.OnItemSelectedListener,
    ViewPager.OnPageChangeListener {
    private lateinit var viewHolder: WeakReference<RecentWorkoutTrendViewHolder>
    private lateinit var recentWorkoutTrendPagerAdapter: RecentWorkoutTrendPagerAdapter

    @EpoxyAttribute
    lateinit var workoutHeader: WorkoutHeader

    @EpoxyAttribute
    var competitionWorkoutSummaryData: CompetitionWorkoutSummaryData? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onWorkoutCompetitionClick: View.OnClickListener? = null

    @EpoxyAttribute
    lateinit var recentTrendData: RecentTrendData

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var infoModelFormatter: InfoModelFormatter

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var currentPage: Int = 0

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onPageSelected: ((Int) -> Unit)? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var unitConverter: JScienceUnitConverter

    override fun getDefaultLayout() = R.layout.model_recent_workout_trend

    override fun bind(holder: RecentWorkoutTrendViewHolder) {
        if (::viewHolder.isInitialized) viewHolder.clear()
        viewHolder = WeakReference(holder)
        val context = holder.pager.context
        setupRanking(context, holder)
        setupPager(context, holder)
        setupSpinner(context, holder)
        setupCompareButton(holder)
        competitionWorkoutSummaryData?.let {
            setCompetitionSummary(holder, it)
            holder.workoutCompetitionContainer.isVisible = true
        } ?: run { holder.workoutCompetitionContainer.isVisible = false }
    }

    override fun unbind(holder: RecentWorkoutTrendViewHolder) {
        holder.pager.removeOnPageChangeListener(this)
        viewHolder.clear()
    }

    private fun setCompetitionSummary(
        holder: RecentWorkoutTrendViewHolder,
        competitionWorkoutSummaryData: CompetitionWorkoutSummaryData
    ) {
        val context = holder.workoutCompetitionTitle.context
        val competition = competitionWorkoutSummaryData.summary.competition
        holder.workoutCompetitionIcon.setCompetitionIcon(competition?.result)
        holder.workoutCompetitionTitle.setCompetitionTitleText(
            (competition?.finishDuration ?: 0) - (competition?.targetDuration ?: 0),
            infoModelFormatter,
            SummaryItem.DURATION
        )
        holder.workoutCompetitionTitle.setCompetitionTitleText(
            workoutHeader.totalDistance.toLong() - (competition?.distance ?: 0).toLong(),
            infoModelFormatter,
            SummaryItem.DISTANCE
        )
        holder.workoutCompetitionTitle.setCompetitionTitleColor(context, competition?.result)
        holder.workoutCompetitionSubtitle.setCompetitionSubtitleText(competition?.result, context)
        onWorkoutCompetitionClick?.let { holder.workoutCompetitionContainer.setOnClickListener(it) }
    }

    @SuppressLint("SetTextI18n")
    private fun setupRanking(context: Context, holder: RecentWorkoutTrendViewHolder) {
        recentTrendData.recentWorkoutTrend
            .rankingOnSimilarRoute
            .takeIf { it > 0 }
            ?.let { rankingOnSimilarRoute ->
                holder.rankingTouchArea.setOnClickListenerThrottled {
                    context.startActivity(
                        SimilarWorkoutsActivity.newStartIntent(
                            context,
                            recentTrendData.recentWorkoutTrend.currentWorkout,
                            SimilarWorkoutsListFragment.SimilarTag.BY_ROUTE,
                        )
                    )
                }

                holder.ranking.text = rankingOnSimilarRoute.toString()
            }
            ?: with(holder) {
                rankingIcon.isVisible = false
                ranking.isVisible = false
                rankingDescription.isVisible = false
                rankingDivider.isVisible = false
            }

        holder.rankingOfSimilarDistance.text = recentTrendData.recentWorkoutTrend
            .rankingOfSimilarDistance
            .toString()
        holder.rankingOfSimilarTouchArea.setOnClickListenerThrottled {
            context.startActivity(
                SimilarWorkoutsActivity.newStartIntent(
                    context,
                    recentTrendData.recentWorkoutTrend.currentWorkout,
                    SimilarWorkoutsListFragment.SimilarTag.BY_DISTANCE,
                )
            )
        }

        recentTrendData.recentWorkoutTrend
            .bestWorkoutOnSimilarRoute
            ?.let { targetWorkout ->
                holder.comparedToBestTouchArea.setOnClickListenerThrottled {
                    context.startActivity(
                        WorkoutComparisonActivity.newStartIntent(
                            context,
                            recentTrendData.recentWorkoutTrend.currentWorkout,
                            targetWorkout,
                            AnalyticsPropertyValue.CompareWorkoutScreenSource.SUMMARY,
                        )
                    )
                }
                showWorkoutComparison(
                    textView = holder.comparedToBest,
                    referenceWorkout = recentTrendData.recentWorkoutTrend.currentWorkout,
                    targetWorkout = targetWorkout,
                    infoModelFormatter = infoModelFormatter,
                )
            } ?: with(holder) {
            comparedToBestIcon.isVisible = false
            comparedToBest.isVisible = false
            comparedToBestDescription.isVisible = false
            comparedToBestDivider.isVisible = false
        }

        recentTrendData.recentWorkoutTrend
            .previousWorkoutOnSimilarRoute
            ?.let { targetWorkout ->
                holder.comparedToPreviousTouchArea.setOnClickListenerThrottled {
                    context.startActivity(
                        WorkoutComparisonActivity.newStartIntent(
                            context,
                            recentTrendData.recentWorkoutTrend.currentWorkout,
                            targetWorkout,
                            AnalyticsPropertyValue.CompareWorkoutScreenSource.SUMMARY,
                        )
                    )
                }
                showWorkoutComparison(
                    textView = holder.comparedToPrevious,
                    referenceWorkout = recentTrendData.recentWorkoutTrend.currentWorkout,
                    targetWorkout = targetWorkout,
                    infoModelFormatter = infoModelFormatter,
                )
            } ?: with(holder) {
            comparedToPreviousIcon.isVisible = false
            comparedToPrevious.isVisible = false
            comparedToPreviousDescription.isVisible = false
            comparedToPreviousDivider.isVisible = false
        }
    }

    private fun setupPager(
        context: Context,
        holder: RecentWorkoutTrendViewHolder
    ) {
        recentWorkoutTrendPagerAdapter = createAdapter(context)
        holder.pager.adapter = recentWorkoutTrendPagerAdapter
        holder.pager.currentItem = currentPage
        holder.dataType.text = recentWorkoutTrendPagerAdapter.getPageTitle(holder.pager.currentItem)
        holder.pagerIndicator.setViewPager(holder.pager)
        recentWorkoutTrendPagerAdapter.registerDataSetObserver(holder.pagerIndicator.dataSetObserver)
        setupPageOnChangedListener(holder)
    }

    private fun setupPageOnChangedListener(holder: RecentWorkoutTrendViewHolder) {
        holder.pager.removeOnPageChangeListener(this)
        holder.pager.addOnPageChangeListener(this)
    }

    private fun setupSpinner(
        context: Context,
        holder: RecentWorkoutTrendViewHolder
    ) {
        if (recentTrendData.hideSelectionSpinner) {
            holder.selectionSpinner.isEnabled = false
            val defaultTitle =
                Localizable { resources ->
                    resources.getString(
                        BaseR.string.previous_on_all_route_capital
                    )
                }
            holder.selectionSpinner.adapter =
                object : CustomDropdownArrayAdapter<Localizable>(context, arrayOf(defaultTitle)) {
                    override val headerResourceId = R.layout.recent_workouts_spinner_header
                    override val dropDownIconResourceId: Int? = null
                }
        } else {
            holder.selectionSpinner.isEnabled = true
            holder.selectionSpinner.adapter =
                object : CustomDropdownArrayAdapter<RouteSelection>(
                    context,
                    RouteSelection.entries.toTypedArray()
                ) {
                    override val headerResourceId = R.layout.recent_workouts_spinner_header
                    override val dropDownIconResourceId: Int = BaseR.drawable.ic_dropdown_arrow_fill
                }

            holder.selectionSpinner.onItemSelectedListener = this
            holder.selectionSpinner.setSelection(
                if (recentTrendData.routeSelection == RouteSelection.ON_THIS_ROUTE) 0 else 1
            )
        }
    }

    private fun createAdapter(
        context: Context
    ): RecentWorkoutTrendPagerAdapter {
        val isSimilar = recentTrendData.routeSelection == RouteSelection.ON_THIS_ROUTE &&
            recentTrendData.recentWorkoutTrend.previousWorkout != null

        return RecentWorkoutTrendPagerAdapter(
            context,
            recentTrendData.recentWorkoutTrend.toOldModel(context),
            isSimilar,
            infoModelFormatter,
            unitConverter,
        )
    }

    private fun setupCompareButton(
        holder: RecentWorkoutTrendViewHolder
    ) {
        holder.compareButton.setOnClickListener {
            recentTrendData.onCompareClicked(
                recentTrendData.recentWorkoutTrend,
                recentTrendData.routeSelection
            )
        }
    }

    override fun onItemSelected(
        parent: AdapterView<*>?,
        view: View?,
        position: Int,
        id: Long
    ) {
        val routeSelection =
            if (position == 0) {
                RouteSelection.ON_THIS_ROUTE
            } else {
                RouteSelection.ON_ALL_ROUTE
            }
        if (routeSelection == recentTrendData.routeSelection) {
            return
        }

        recentTrendData.onRouteSelection(
            routeSelection,
            currentPage,
        )
    }

    override fun onNothingSelected(parent: AdapterView<*>?) {
        // do nothing
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
        // do nothing
    }

    override fun onPageSelected(position: Int) {
        viewHolder.get()?.run {
            dataType.text = recentWorkoutTrendPagerAdapter.getPageTitle(position)
        }
        onPageSelected?.invoke(position)
    }

    override fun onPageScrollStateChanged(state: Int) {
        // do nothing
    }

    private companion object {
        @ColorRes
        private val AHEAD_COLOR: Int = CoreR.color.comparison_color_increase

        @ColorRes
        private val BEHIND_COLOR: Int = CoreR.color.comparison_color_decrease

        @SuppressLint("SetTextI18n")
        fun showWorkoutComparison(
            textView: TextView,
            referenceWorkout: WorkoutHeader,
            targetWorkout: WorkoutHeader,
            infoModelFormatter: InfoModelFormatter,
        ) {
            val totalTimeDiffInSeconds =
                (referenceWorkout.totalTime - targetWorkout.totalTime).roundToLong()
            if (totalTimeDiffInSeconds <= 0L) {
                textView.text = "-${
                    infoModelFormatter.formatValue(
                        SummaryItem.DURATION,
                        -totalTimeDiffInSeconds
                    ).value
                }"
                textView.setTextColor(ContextCompat.getColor(textView.context, AHEAD_COLOR))
            } else {
                textView.text = "+${
                    infoModelFormatter.formatValue(
                        SummaryItem.DURATION,
                        totalTimeDiffInSeconds
                    ).value
                }"
                textView.setTextColor(ContextCompat.getColor(textView.context, BEHIND_COLOR))
            }
        }
    }
}

class RecentWorkoutTrendViewHolder : KotlinEpoxyHolder() {
    val rankingTouchArea by bind<View>(R.id.ranking_touch_area)
    val rankingIcon by bind<ImageView>(R.id.ranking_icon)
    val ranking by bind<TextView>(R.id.ranking)
    val rankingDescription by bind<TextView>(R.id.ranking_description)
    val rankingDivider by bind<View>(R.id.ranking_divider)
    val rankingOfSimilarTouchArea by bind<View>(R.id.ranking_on_similar_distance_touch_area)
    val rankingOfSimilarDistance by bind<TextView>(R.id.ranking_on_similar_distance)
    val comparedToBestTouchArea by bind<View>(R.id.compared_to_best_touch_area)
    val comparedToBestIcon by bind<ImageView>(R.id.compared_to_best_icon)
    val comparedToBest by bind<TextView>(R.id.compared_to_best)
    val comparedToBestDescription by bind<TextView>(R.id.compared_to_best_description)
    val comparedToBestDivider by bind<View>(R.id.compared_to_best_divider)
    val comparedToPreviousTouchArea by bind<View>(R.id.compared_to_previous_touch_area)
    val comparedToPreviousIcon by bind<ImageView>(R.id.compared_to_previous_icon)
    val comparedToPrevious by bind<TextView>(R.id.compared_to_previous)
    val comparedToPreviousDescription by bind<TextView>(R.id.compared_to_previous_description)
    val comparedToPreviousDivider by bind<View>(R.id.compared_to_previous_divider)
    val selectionSpinner by bind<Spinner>(R.id.routeSelection)
    val dataType by bind<TextView>(R.id.dataType)
    val pager by bind<ViewPager>(R.id.trendViewPager)
    val pagerIndicator by bind<CircleIndicator>(R.id.pagerIndicator)
    val compareButton by bind<Button>(R.id.compareButton)
    val workoutCompetitionTitle by bind<TextView>(R.id.workoutCompetitionTitle)
    val workoutCompetitionSubtitle by bind<TextView>(R.id.workoutCompetitionSubtitle)
    val workoutCompetitionContainer by bind<ConstraintLayout>(R.id.workoutCompetitionContainer)
    val workoutCompetitionIcon by bind<ImageView>(R.id.icon_trophy)
}
