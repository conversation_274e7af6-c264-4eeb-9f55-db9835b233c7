package com.stt.android.workout.details.trend

import android.content.SharedPreferences
import androidx.annotation.ColorRes
import androidx.core.content.edit
import com.github.mikephil.charting.data.Entry
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.models.SimilarWorkoutModel
import com.stt.android.utils.STTConstants
import com.stt.android.utils.traceSuspend
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.RecentTrendData
import com.stt.android.workout.details.RecentWorkoutTrendNew
import com.stt.android.workout.details.WorkoutDetailsRecentWorkoutTrendActivityNavEvent
import com.stt.android.workout.details.WorkoutDetailsWorkoutComparisonActivityNavEvent
import com.stt.android.workoutdetail.trend.RouteSelection
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

interface RecentTrendDataLoader {
    val recentTrendDataStateFlow: StateFlow<ViewState<RecentTrendData?>>
    suspend fun loadRecentTrendData(
        workoutHeader: WorkoutHeader,
        currentPage: Int
    ): StateFlow<ViewState<RecentTrendData?>>
}

@ActivityRetainedScoped
class DefaultRecentTrendDataLoader @Inject constructor(
    private val defaultPrefs: SharedPreferences,
    private val similarWorkoutModel: SimilarWorkoutModel,
    private val infoModelFormatter: InfoModelFormatter,
    private val currentUserController: CurrentUserController,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : RecentTrendDataLoader {
    private var hideRouteSelection = false

    private lateinit var referenceWorkout: WorkoutHeader

    override val recentTrendDataStateFlow: MutableStateFlow<ViewState<RecentTrendData?>> =
        MutableStateFlow(loading())

    private var routeSelection: RouteSelection
        get() = defaultPrefs.getString(
            STTConstants.DefaultPreferences.KEY_WORKOUT_TREND_ROUTE_SELECTION,
            RouteSelection.DEFAULT.name
        )?.let {
            RouteSelection.valueOf(it)
        } ?: RouteSelection.ON_ALL_ROUTE
        set(value) {
            defaultPrefs.edit {
                putString(STTConstants.DefaultPreferences.KEY_WORKOUT_TREND_ROUTE_SELECTION, value.name)
            }
        }

    private fun onRouteSelection(routeSelection: RouteSelection, currentPage: Int) {
        this.routeSelection = routeSelection
        activityRetainedCoroutineScope.launch {
            getTrendData(referenceWorkout, currentPage)
        }
    }

    private fun onCompareClicked(trend: RecentWorkoutTrendNew, routeSelection: RouteSelection) {
        navigationEventDispatcher.dispatchEvent(
            if (routeSelection == RouteSelection.ON_THIS_ROUTE) {
                WorkoutDetailsWorkoutComparisonActivityNavEvent(
                    current = trend.currentWorkout,
                    currentRank = 0,
                    target = trend.previousWorkout,
                    targetRank = 0,
                    analyticsSource = AnalyticsPropertyValue.CompareWorkoutScreenSource.RANKINGLIST
                )
            } else {
                WorkoutDetailsRecentWorkoutTrendActivityNavEvent(trend.currentWorkout)
            }
        )
    }

    override suspend fun loadRecentTrendData(
        workoutHeader: WorkoutHeader,
        currentPage: Int
    ): StateFlow<ViewState<RecentTrendData?>> {
        this.referenceWorkout = workoutHeader
        if (referenceWorkout.activityType.isDiving || referenceWorkout.activityType.isIndoor) {
            // Trend data not needed for dives or indoor workouts
            recentTrendDataStateFlow.value = loaded()
        } else {
            activityRetainedCoroutineScope.launch {
                traceSuspend("loadRecentTrendData") {
                    getTrendData(referenceWorkout, currentPage)
                }
            }
        }
        return recentTrendDataStateFlow
    }

    private suspend fun getTrendData(referenceWorkout: WorkoutHeader, currentPage: Int) {
        withContext(coroutinesDispatchers.io) {
            recentTrendDataStateFlow.value = runSuspendCatching {
                val (onSimilarRoute, ofSimilarDistance) = loadRecentWorkouts(referenceWorkout)
                if (ofSimilarDistance.size <= 1) {
                    return@runSuspendCatching loaded()
                }

                val trend = buildRecentTrend(
                    referenceWorkout = referenceWorkout,
                    onSimilarRoute = onSimilarRoute,
                    ofSimilarDistance = ofSimilarDistance,
                )
                RecentTrendData(
                    recentWorkoutTrend = trend,
                    routeSelection = routeSelection,
                    hideSelectionSpinner = shouldHideSelectionSpinner(trend),
                    currentPage = currentPage,
                    onRouteSelection = ::onRouteSelection,
                    onCompareClicked = ::onCompareClicked,
                ).let(::loaded)
            }.getOrElse { e ->
                Timber.w(e, "Loading recent trend data failed for workoutId ${referenceWorkout.id}")
                loaded<RecentTrendData>(null)
            }
        }
    }

    private fun shouldHideSelectionSpinner(trend: RecentWorkoutTrendNew): Boolean {
        return hideRouteSelection ||
            // don't show it, if no data or only a single workout for another user
            (currentUserController.username != referenceWorkout.username && trend.previousWorkout == null)
    }

    /**
     * Returns a pair of recent workouts on this route, and recent workouts of similar distance, both
     * sorted by start time in descending order.
     */
    private suspend fun loadRecentWorkouts(
        referenceWorkout: WorkoutHeader,
    ): Pair<List<WorkoutHeader>, List<WorkoutHeader>> = withContext(coroutinesDispatchers.io) {
        val onSimilarRoute = findRecentWorkoutOnSimilarRoute(referenceWorkout)
        hideRouteSelection = onSimilarRoute.isEmpty()

        val ofSimilarDistance = findRecentWorkoutOfSimilarDistance(referenceWorkout)

        onSimilarRoute.sortedByDescending { it.startTime } to ofSimilarDistance.sortedByDescending { it.startTime }
    }

    private suspend fun buildRecentTrend(
        referenceWorkout: WorkoutHeader,
        onSimilarRoute: List<WorkoutHeader>,
        ofSimilarDistance: List<WorkoutHeader>,
    ): RecentWorkoutTrendNew = withContext(coroutinesDispatchers.computation) {
        val activityType: ActivityType = referenceWorkout.activityType
        val shouldAddCadence = ActivityType.CYCLING == activityType ||
            ActivityType.MOUNTAIN_BIKING == activityType ||
            ActivityType.GRAVEL_CYCLING == activityType

        // if asked to load workouts on all routes, just do that without any fallbacks
        // if asked to load workouts on same route, do it; falls back to backend if no
        // match is found, and reference workout is not own; and eventually falls back to all routes
        val recentWorkouts = if (routeSelection == RouteSelection.ON_ALL_ROUTE) {
            ofSimilarDistance
        } else {
            onSimilarRoute.ifEmpty {
                routeSelection = RouteSelection.ON_ALL_ROUTE
                ofSimilarDistance
            }
        }.take(RECENT_WORKOUT_LIMIT)

        val colors = ArrayList<Int>(RECENT_WORKOUT_LIMIT)
        val durationEntries = ArrayList<Entry>(RECENT_WORKOUT_LIMIT)
        val distanceEntries = ArrayList<Entry>(RECENT_WORKOUT_LIMIT)
        val speedEntries = ArrayList<Entry>(RECENT_WORKOUT_LIMIT)
        val paceEntries = ArrayList<Entry>(RECENT_WORKOUT_LIMIT)
        val energyEntries = ArrayList<Entry>(RECENT_WORKOUT_LIMIT)
        val averageHeartRateEntries = ArrayList<Entry>(RECENT_WORKOUT_LIMIT)
        val averageCadenceEntries = if (shouldAddCadence) ArrayList<Entry>(RECENT_WORKOUT_LIMIT) else null
        buildEntries(
            recentWorkouts = recentWorkouts,
            referenceTime = referenceWorkout.startTime,
            measurementUnit = infoModelFormatter.unit,
            colors = colors,
            durationEntries = durationEntries,
            distanceEntries = distanceEntries,
            speedEntries = speedEntries,
            paceEntries = paceEntries,
            energyEntries = energyEntries,
            averageHeartRateEntries = averageHeartRateEntries,
            averageCadenceEntries = averageCadenceEntries,
        )

        val bestWorkoutOnSimilarRoute = if (onSimilarRoute.size > 1) {
            onSimilarRoute.subList(1, onSimilarRoute.size)
                .minBy(WorkoutHeader::totalTime)
        } else {
            null
        }

        val referenceWorkoutId = referenceWorkout.id
        val rankingOfSimilarDistance = ofSimilarDistance.sortedBy(WorkoutHeader::totalTime)
            .indexOfFirst { it.id == referenceWorkoutId } + 1
        val rankingOnSimilarRoute = onSimilarRoute.sortedBy(WorkoutHeader::totalTime)
            .indexOfFirst { it.id == referenceWorkoutId } + 1

        RecentWorkoutTrendNew(
            currentWorkout = referenceWorkout,
            previousWorkout = recentWorkouts.getOrNull(1),
            bestWorkoutOnSimilarRoute = bestWorkoutOnSimilarRoute,
            previousWorkoutOnSimilarRoute = onSimilarRoute.getOrNull(1),
            rankingOfSimilarDistance = rankingOfSimilarDistance,
            rankingOnSimilarRoute = rankingOnSimilarRoute,
            durationEntries = durationEntries,
            distanceEntries = distanceEntries,
            speedEntries = speedEntries,
            paceEntries = paceEntries,
            energyEntries = energyEntries,
            averageHeartRateEntries = averageHeartRateEntries,
            averageCadenceEntries = averageCadenceEntries,
            dataSetColorRes = RECENT_TREND_COLOR_RES,
            dataColorResList = colors,
        )
    }

    private suspend fun findRecentWorkoutOnSimilarRoute(
        referenceWorkout: WorkoutHeader,
    ): List<WorkoutHeader> = withContext(coroutinesDispatchers.io) {
        similarWorkoutModel.findRecentWorkoutsOnSimilarRoute(referenceWorkout)
    }

    private suspend fun findRecentWorkoutOfSimilarDistance(
        referenceWorkout: WorkoutHeader,
    ): List<WorkoutHeader> = withContext(coroutinesDispatchers.io) {
        similarWorkoutModel.findRecentWorkoutsOfSimilarDistance(referenceWorkout)
    }

    private companion object {
        private const val RECENT_WORKOUT_LIMIT = 7

        @ColorRes
        val RECENT_TREND_COLOR_RES = BaseR.color.blue

        @ColorRes
        private val RECENT_TREND_HIGHLIGHT_COLOR_RES = CR.color.accent

        fun buildEntries(
            recentWorkouts: List<WorkoutHeader>,
            referenceTime: Long,
            measurementUnit: MeasurementUnit,
            colors: ArrayList<Int>,
            durationEntries: ArrayList<Entry>,
            distanceEntries: ArrayList<Entry>,
            speedEntries: ArrayList<Entry>,
            paceEntries: ArrayList<Entry>,
            energyEntries: ArrayList<Entry>,
            averageHeartRateEntries: ArrayList<Entry>,
            averageCadenceEntries: ArrayList<Entry>?,
        ) {
            recentWorkouts.reversed().take(RECENT_WORKOUT_LIMIT).forEachIndexed { index, recentWorkoutHeader ->
                val workoutStartTime = recentWorkoutHeader.startTime
                val speed = recentWorkoutHeader.avgSpeed.toFloat()
                colors.add(
                    if (referenceTime == workoutStartTime) {
                        RECENT_TREND_HIGHLIGHT_COLOR_RES
                    } else {
                        RECENT_TREND_COLOR_RES
                    }
                )

                val xIndex = index.toFloat()
                durationEntries.add(
                    Entry(
                        xIndex,
                        recentWorkoutHeader.totalTime.toFloat(),
                        recentWorkoutHeader
                    )
                )
                distanceEntries.add(
                    Entry(
                        xIndex,
                        recentWorkoutHeader.totalDistance.toFloat(),
                        recentWorkoutHeader,
                    )
                )
                speedEntries.add(
                    Entry(
                        xIndex,
                        speed,
                        recentWorkoutHeader,
                    )
                )
                paceEntries.add(
                    Entry(
                        xIndex,
                        measurementUnit.toPaceUnit(speed.toDouble()).toFloat(),
                        recentWorkoutHeader,
                    )
                )
                energyEntries.add(
                    Entry(
                        xIndex,
                        recentWorkoutHeader.energyConsumption.toFloat(),
                        recentWorkoutHeader,
                    )
                )
                averageHeartRateEntries.add(
                    Entry(
                        xIndex,
                        recentWorkoutHeader.heartRateAverage.toFloat(),
                        recentWorkoutHeader,
                    )
                )
                averageCadenceEntries?.add(
                    Entry(
                        xIndex,
                        recentWorkoutHeader.averageCadence.toFloat(),
                        recentWorkoutHeader,
                    )
                )
            }
        }
    }
}
