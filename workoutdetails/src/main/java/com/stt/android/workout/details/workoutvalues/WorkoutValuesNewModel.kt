package com.stt.android.workout.details.workoutvalues

import android.content.res.Resources
import android.view.View
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.compose.theme.AppTheme
import com.stt.android.workout.details.R
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridData
import com.stt.android.workoutdetail.workoutvalues.composables.WorkoutValuesContainer

@EpoxyModelClass
abstract class WorkoutValuesNewModel : EpoxyModelWithHolder<WorkoutValuesViewHolder>() {

    override fun getDefaultLayout() = R.layout.model_workout_values

    @EpoxyAttribute
    lateinit var workoutValuesGridData: WorkoutValuesGridData

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var enableWorkoutValueGroups: Boolean = false

    override fun bind(holder: WorkoutValuesViewHolder) {
        with(holder) {
            val resources = holder.workoutValuesContainer.resources
            setTopMargin(resources)
            workoutValuesView.setContent {
                AppTheme {
                    WorkoutValuesContainer(
                        showHeader = workoutValuesGridData.showHeader,
                        activityName = workoutValuesGridData.activityName,
                        activityIcon = workoutValuesGridData.activityIcon,
                        workoutValues = workoutValuesGridData.workoutValues,
                        workoutValueGroups = workoutValuesGridData.workoutValueGroups,
                        workoutValuesGridType = workoutValuesGridData.workoutValuesGridType,
                        showDetailsButton = workoutValuesGridData.showDetailsButton,
                        onValueClicked = workoutValuesGridData.onValueClicked,
                        onMultisportDetailsClicked = workoutValuesGridData.onMultisportDetailsClicked,
                        onViewMoreClicked = workoutValuesGridData.onViewMoreClicked,
                        enableWorkoutValueGroups = enableWorkoutValueGroups,
                    )
                }
            }
        }
    }

    private fun WorkoutValuesViewHolder.setTopMargin(resources: Resources) {
        workoutValuesContainer.layoutParams =
            (workoutValuesContainer.layoutParams as ViewGroup.MarginLayoutParams).apply {
                topMargin = if (workoutValuesGridData.addTopMargin) {
                    resources.getDimensionPixelSize(com.stt.android.R.dimen.size_spacing_medium)
                } else {
                    0
                }
            }
    }
}

class WorkoutValuesViewHolder : KotlinEpoxyHolder() {
    val workoutValuesContainer by bind<View>(R.id.workout_values_root)
    val workoutValuesView by bind<ComposeView>(R.id.workout_values_view)
}
