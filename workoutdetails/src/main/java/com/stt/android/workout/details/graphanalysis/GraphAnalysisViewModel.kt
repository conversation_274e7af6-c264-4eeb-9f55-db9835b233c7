package com.stt.android.workout.details.graphanalysis

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.github.mikephil.charting.data.Entry
import com.google.maps.android.PolyUtil
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.common.viewstate.ViewState
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.advancedlaps.LapsTable
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlStreamData
import com.stt.android.domain.user.MILES_TO_METERS
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.supportsLaps
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.infomodel.SummaryItem
import com.stt.android.logbook.SuuntoLogbookWindow
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.mapping.getActivitySummaryForActivityId
import com.stt.android.tracker.event.Event
import com.stt.android.ui.extensions.supportWorkoutAnalysisOnMap
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.ui.utils.ThrottledLiveData
import com.stt.android.utils.firstOfType
import com.stt.android.workout.details.AerobicIqGraphData
import com.stt.android.workout.details.DefaultHeartRateLoader
import com.stt.android.workout.details.HeartRateData
import com.stt.android.workout.details.HeartRateInThreeMinsLoader
import com.stt.android.workout.details.HrGraphData
import com.stt.android.workout.details.WorkoutAnalysisData
import com.stt.android.workout.details.WorkoutExtensionsData
import com.stt.android.workout.details.WorkoutValuesContainer
import com.stt.android.workout.details.analysis.WorkoutAnalysisDataLoader
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.charts.AnalysisGraphXValueType
import com.stt.android.workout.details.charts.WorkoutLineChartData
import com.stt.android.workout.details.charts.WorkoutLineEntry
import com.stt.android.workout.details.graphanalysis.laps.AnalysisLapsLoader
import com.stt.android.workout.details.graphanalysis.laps.GenerateAnalysisWorkoutValuesContainerUseCase
import com.stt.android.workout.details.graphanalysis.laps.LapMarkerModel
import com.stt.android.workout.details.graphanalysis.playback.PlaybackProgressionReason
import com.stt.android.workout.details.graphanalysis.playback.PlaybackStateModel
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackGeopointLoader
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackPauseReason
import com.stt.android.workout.details.graphanalysis.typeselection.WorkoutGraphAnalysisInfo
import com.stt.android.workout.details.graphanalysis.typeselection.WorkoutGraphAnalysisInfoLoader
import com.stt.android.workout.details.heartrate.HeartRateDataLoader
import com.stt.android.workout.details.intensity.GetAerobicIqGraphDataUseCase
import com.stt.android.workout.details.intensity.IntensityZoneUtils
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.watch.WorkoutExtensionsDataLoader
import com.stt.android.workout.details.workoutheader.WorkoutHeaderLoader
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.Job
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.dropWhile
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import timber.log.Timber
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt
import kotlin.math.roundToLong
import com.stt.android.coroutines.combine as combineMulti

@HiltViewModel
class GraphAnalysisViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val workoutHeaderLoader: WorkoutHeaderLoader,
    private val workoutAnalysisDataLoader: WorkoutAnalysisDataLoader,
    @DefaultHeartRateLoader private val heartRateDataLoader: HeartRateDataLoader,
    @HeartRateInThreeMinsLoader private val recoveryHeartRateInThreeMinsDataLoader: HeartRateDataLoader,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val smlDataLoader: SmlDataLoader,
    private val graphAnalysisInfoLoader: WorkoutGraphAnalysisInfoLoader,
    private val workoutPlaybackGeopointLoader: WorkoutPlaybackGeopointLoader,
    private val analysisLapsLoader: AnalysisLapsLoader,
    private val workoutExtensionsDataLoader: WorkoutExtensionsDataLoader,
    private val playbackStateModel: PlaybackStateModel,
    private val infoModelFormatter: InfoModelFormatter,
    private val generateAnalysisWorkoutValuesContainerUseCase: GenerateAnalysisWorkoutValuesContainerUseCase,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val getAerobicIqGraphDataUseCase: GetAerobicIqGraphDataUseCase,
    private val lapMarkerModel: LapMarkerModel,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers,
) : LoadingStateViewModel<GraphAnalysisData>(
    ioThread,
    mainThread,
    coroutinesDispatchers
) {
    val highlightedSecond: LiveData<Float>
        get() = _highlightedSecond
    private val _highlightedSecond = ThrottledLiveData<Float>(
        SELECTED_SECOND_THROTTLE_MIN.toLong(),
        viewModelScope
    )
        .apply {
            value = 0f
        }

    val playbackResumed: LiveData<Boolean>
        get() = _playbackResumed
    private val _playbackResumed = MutableLiveData(false)

    val lapsData: LiveData<AnalysisLapsData>
        get() = _lapsData
    private val _lapsData: MutableLiveData<AnalysisLapsData> = MutableLiveData()

    val workoutValuesContainer: LiveData<WorkoutValuesContainer>
        get() = _workoutValuesContainer
    private val _workoutValuesContainer: MutableLiveData<WorkoutValuesContainer> = MutableLiveData()

    val workoutValuesAreOutdated: LiveData<Boolean>
        get() = _workoutValuesAreOutdated
    private val _workoutValuesAreOutdated = SingleLiveEvent<Boolean>()

    val playbackProgressMillisInWorkout: Long
        get() = playbackStateModel.playbackProgress.timeInWorkoutMillis

    private var loadJob: Job? = null
    private var loadLapsJob: Job? = null
    private var loadWorkoutValuesJob: Job? = null

    init {
        viewModelScope.launch {
            launch {
                playbackStateModel.playbackProgressFlow.collect {
                    val seconds =
                        TimeUnit.MILLISECONDS.toSeconds(it.timeInWorkoutMillis).toFloat()
                    _highlightedSecond.postValue(seconds)
                }
            }

            launch {
                playbackStateModel.playbackStateFlow.collect {
                    _playbackResumed.value = it.resumed
                }
            }

            launch(io) {
                combine(
                    viewState.asFlow().filterNotNull(),
                    lapsData.asFlow().filterNotNull(),
                ) { state, data ->
                    when (val header = state.data?.workoutHeader) {
                        null -> null
                        else -> Pair(header, data)
                    }
                }.filterNotNull().collectLatest { (header, data) ->
                    lapMarkerModel.update(
                        header,
                        data,
                        ::getLapStartAndEndsSecondsInWorkout,
                        ::setSelectedLap,
                    )
                }
            }
        }

        graphAnalysisInfoLoader.setForcedMainGraphType(savedStateHandle[ARG_INITIAL_MAIN_GRAPH_TYPE])
        loadData()
    }

    @Suppress("UNCHECKED_CAST")
    private fun loadData() {
        notifyLoading(viewState.value?.data)

        val initialSelections =
            savedStateHandle.get<GraphAnalysisSelections?>(ARG_INITIAL_SELECTIONS)
        savedStateHandle.remove<GraphAnalysisSelections?>(ARG_INITIAL_SELECTIONS)

        val dataLoadResultsFlow =
            workoutHeaderLoader.workoutHeaderFlow.flatMapLatest { headerState ->
                val header = headerState.data ?: return@flatMapLatest emptyFlow()

                combineMulti(
                    workoutAnalysisDataLoader.loadWorkoutAnalysisData(
                        header,
                        AnalysisGraphXValueType.DURATION,
                        viewModelScope
                    ).dropWhile { it.isLoading() },
                    heartRateDataLoader.loadHeartRateData(
                        header,
                        viewModelScope
                    ).dropWhile { it.isLoading() },
                    recoveryHeartRateInThreeMinsDataLoader.loadHeartRateData(
                        header,
                        viewModelScope
                    ).dropWhile { it.isLoading() },
                    smlDataLoader.smlStateFlow.dropWhile { it.isLoading() },
                    multisportPartActivityLoader.multisportPartActivityFlow.dropWhile { it.isLoading() },
                    workoutPlaybackGeopointLoader.observeGeoPointForPlayback(),
                    workoutExtensionsDataLoader.workoutExtensionsStateFlow.dropWhile { it.isLoading() }
                ) { dataState, hrState, recoveryHRInThreeMinsState, smlState, multisportPartState, geoPoints, extensionData ->
                    listOf(
                        dataState,
                        hrState,
                        recoveryHRInThreeMinsState,
                        smlState,
                        multisportPartState,
                        geoPoints,
                        extensionData
                    )
                }
            }
                .flatMapLatest { loadedData ->
                    val analysisDataViewState = loadedData[0] as ViewState<WorkoutAnalysisData?>
                    val hrState = loadedData[1] as ViewState<HeartRateData>
                    val recoveryHRInThreeMinsState = loadedData[2] as ViewState<HeartRateData>
                    val smlState = loadedData[3] as ViewState<Sml?>
                    val multisportPartState = loadedData[4] as ViewState<MultisportPartActivity?>
                    val geoPoints = loadedData[5] as List<WorkoutGeoPoint>
                    val extensionDataState = loadedData[6] as ViewState<WorkoutExtensionsData?>

                    val fullAnalysisData = analysisDataViewState.data
                    if (fullAnalysisData != null) {
                        val fullHrData = hrState.data
                        val sml = smlState.data
                        val multisportPart = multisportPartState.data
                        val multisportWindow = sml?.getActivityWindow(multisportPart)

                        val data =
                            multisportPart?.let { fullAnalysisData.multisportPartAnalysisData[it] }
                                ?: fullAnalysisData
                        val hrGraphData = fullHrData?.let {
                            if (multisportPart != null) {
                                it.multisportPartGraphData[multisportPart]
                            } else {
                                it.graphData
                            }
                        }?.takeIf { hr -> hr.hasValidData() }

                        val aerobicIqGraphData = getAerobicIqGraphDataUseCase(
                            workoutId = fullAnalysisData.pagerData.workoutHeader.id,
                            sml = sml,
                            multisportPartActivity = multisportPart,
                        )

                        val analysisInfoFlow =
                            graphAnalysisInfoLoader.observeGraphAnalysisInfoForWorkoutAnalysisData(
                                data,
                                hrGraphData != null,
                                hasAerobicIqGraphData = aerobicIqGraphData?.hasValidData() == true,
                                hasHrInThreeMinsGraphData = recoveryHRInThreeMinsState.data?.graphData?.hasValidData() == true,
                            )

                        val intensityExtension: IntensityExtension? =
                            extensionDataState.data?.workoutExtensions?.firstOfType()
                        val activityType = multisportPart?.activityType
                            ?: fullAnalysisData.pagerData.workoutHeader.activityTypeId
                        val summaryItems = getActivitySummaryForActivityId(activityType).items
                        val showDistance = summaryItems.contains(SummaryItem.DISTANCE) ||
                            summaryItems.contains(SummaryItem.SWIMDISTANCE) ||
                            summaryItems.contains(SummaryItem.NAUTICALDISTANCE)

                        analysisInfoFlow.map { analysisInfo ->
                            DataLoadResults(
                                fullAnalysisData,
                                data,
                                hrGraphData,
                                aerobicIqGraphData,
                                recoveryHRInThreeMinsState.data?.graphData,
                                multisportPart,
                                sml,
                                multisportWindow,
                                analysisInfo,
                                geoPoints,
                                sml?.streamData?.takeIf {
                                    canBeUsedAsDistanceData(it)
                                },
                                intensityExtension,
                                showDistance
                            )
                        }
                    } else {
                        Timber.w("No workout analysis or multisport part data, can't load graph analysis info")
                        emptyFlow()
                    }
                }.shareIn(viewModelScope, SharingStarted.Lazily)

        loadJob?.cancel()
        var initialSelectedMillisInWorkoutSet = false
        loadJob = launch(io) {
            dataLoadResultsFlow.collect { result ->
                val workoutHeader = result.fullAnalysisData.pagerData.workoutHeader
                val multisportPart = result.multisportPart
                val graphTypeInfo = result.graphTypeInfo
                val lineChartDataList = result.partAnalysisDataData.pagerData.graphData
                val hrData = result.hrGraphData
                val recoveryHRInThreeMinsData = result.recoveryHRInThreeMinsGraphData
                val aerobicIqGraphData = result.aerobicIqGraphData
                val intensityExtension = result.intensityExtension
                val isSwimming = workoutHeader.activityType.isSwimming
                val usesNauticalUnits = workoutHeader.activityType.usesNauticalUnits
                val durationMillis = result.durationMillis

                workoutDetailsAnalytics.setWorkoutAnalysisGraphTypes(
                    graphTypeInfo.mainGraphType,
                    graphTypeInfo.comparisonGraphType,
                    graphTypeInfo.backgroundGraphType
                )
                playbackStateModel.setWorkoutDuration(durationMillis)
                if (initialSelections != null && !initialSelectedMillisInWorkoutSet) {
                    playbackStateModel.seekToTimeInWorkout(
                        initialSelections.workoutTimeInMillis,
                        PlaybackProgressionReason.USER_SCRUB_TIMELINE
                    )
                    initialSelectedMillisInWorkoutSet = true
                }

                val mainGraphData = getAnalysisData(
                    graphTypeInfo.mainGraphType,
                    lineChartDataList,
                    hrData,
                    recoveryHRInThreeMinsData,
                    aerobicIqGraphData,
                )
                val mainGraphZoneLimits = mainGraphData?.let { data ->
                    IntensityZoneUtils.getMainGraphZoneLimits(
                        mainGraphType = data.graphType,
                        graphDataMaxYValue = data.dataMaxYValue,
                        graphDataMinYValue = data.dataMinYValue,
                        measurementUnit = infoModelFormatter.unit,
                        hrGraphData = hrData,
                        intensityExtension = intensityExtension
                    )
                }

                fun GraphAnalysisChartData.addOriginEntry(): GraphAnalysisChartData =
                    if ((graphType as? GraphType.Summary)?.summaryGraph == SummaryGraph.AEROBICZONE) {
                        // TODO Is there any other graph types that should not change? Also, this is
                        //  just too fragile, need to find a better way to handle the "add 0" thingy.
                        this
                    } else {
                        copy(
                            data = data.map { entry ->
                                when {
                                    entry.entries.isEmpty() || entry.entries.first().x == 0f -> entry
                                    else -> entry.copy(entries = buildList {
                                        add(Entry(0f, entry.entries.first().y))
                                        addAll(entry.entries)
                                    })
                                }
                            },
                        )
                    }

                val data = GraphAnalysisData(
                    mainGraphData = mainGraphData?.addOriginEntry(),
                    comparisonGraphData = getAnalysisData(
                        graphTypeInfo.comparisonGraphType,
                        lineChartDataList,
                        hrData,
                        recoveryHRInThreeMinsData,
                        aerobicIqGraphData
                    )?.addOriginEntry(),
                    backgroundGraphData = getAnalysisData(
                        graphTypeInfo.backgroundGraphType,
                        lineChartDataList,
                        hrData,
                        recoveryHRInThreeMinsData,
                        aerobicIqGraphData
                    )?.addOriginEntry(),
                    graphTypeInfo = graphTypeInfo,
                    workoutHeader = workoutHeader,
                    multisportPartActivity = multisportPart,
                    mainGraphZoneLimits = mainGraphZoneLimits,
                    isSwimming = isSwimming,
                    usesNauticalUnits = usesNauticalUnits,
                    durationSeconds = if (graphTypeInfo.mainGraphType == GraphType.Summary(
                            SummaryGraph.RECOVERYHRINTHREEMINS
                        )
                    ) mainGraphData?.dataMaxXValue ?: 0f
                    else result.durationMillis / 1000f,
                    timeInWorkoutToDistance = { time ->
                        if (result.showDistance) {
                            timeInWorkoutToDistance(
                                time,
                                result.distanceSmlStreamData,
                                result.geoPoints
                            )
                        } else {
                            null
                        }
                    },
                    canSelectLap = workoutHeader.canSelectLap(multisportPart),
                )

                notifyDataLoaded(data)
                _highlightedSecond.setInterval(getHighlightedSecondThrottleInterval(data))
            }
        }

        // Load laps separately and take only 1, the data shouldn't change per sml & multisport part
        // combo and the multisport part can't be changed while analysis is visible.
        // This makes sure that graph types or something else changing in the other loading
        // flow won't trigger lapsData to emit and possibly interfere with ongoing chart manipulations
        loadLapsJob?.cancel()
        loadLapsJob = launch(io) {
            dataLoadResultsFlow
                .take(1)
                .flatMapLatest { dataLoadResults ->
                    analysisLapsLoader.loadLapsTables(dataLoadResults.fullAnalysisData.pagerData.workoutHeader)
                        .dropWhile { it.isLoading() }
                        .take(1)
                        .map {
                            Triple(
                                it,
                                dataLoadResults.fullAnalysisData.pagerData.workoutHeader,
                                dataLoadResults.multisportPart
                            )
                        }
                }
                .collect { (lapsTablesState, workoutHeader, multisportPart) ->
                    val lapsTables = lapsTablesState.data ?: emptyList()
                    var curLapsData = _lapsData.value

                    // Apply initial lap selection when laps data is first loaded
                    // to both analysis and playback
                    if (curLapsData == null && initialSelections?.lapSelection != null) {
                        val (lapsTableType, lapsTableRow) = when (initialSelections.lapSelection) {
                            is GraphAnalysisSelections.LapsTableRowSelection -> {
                                initialSelections.lapSelection.lapsTableType to initialSelections.lapSelection.lapsTableRow
                            }

                            is GraphAnalysisSelections.TimeWindowLapSelection -> {
                                null to null
                            }
                        }
                        val (lapStart, lapEnd) = when (initialSelections.lapSelection) {
                            is GraphAnalysisSelections.LapsTableRowSelection -> getLapStartAndEndsSecondsInWorkout(
                                workoutHeader,
                                initialSelections.lapSelection.lapsTableRow
                            )

                            is GraphAnalysisSelections.TimeWindowLapSelection -> {
                                initialSelections.lapSelection.startSeconds to initialSelections.lapSelection.endSeconds
                            }
                        }

                        curLapsData = AnalysisLapsData(
                            lapsTables = lapsTables,
                            lapsTableType = lapsTableType,
                            selectedLap = lapsTableRow,
                            lapStartSecondsInWorkout = lapStart,
                            lapEndSecondsInWorkout = lapEnd
                        )

                        if (lapStart != null && lapEnd != null) {
                            playbackStateModel.setLapTimeWindow(
                                (lapStart * 1000).toLong(),
                                (lapEnd * 1000).toLong()
                            )
                            _workoutValuesAreOutdated.postValue(true)
                        } else {
                            playbackStateModel.removeLapTimeWindow()
                        }
                    }

                    val realLapsData = if (curLapsData != null) {
                        curLapsData.copy(
                            selectedLap = lapsTables
                                .firstOrNull { it.lapsType == curLapsData.lapsTableType }
                                ?.lapsTableRows
                                ?.firstOrNull { it.lapNumber == curLapsData.selectedLap?.lapNumber }
                        )
                    } else {
                        val lapsTableType = when {
                            !workoutHeader.canSelectLap(multisportPart) -> null

                            lapsTables.hasType(LapsTableType.MANUAL) -> LapsTableType.MANUAL

                            lapsTables.hasType(LapsTableType.DURATION_AUTO_LAP) -> LapsTableType.DURATION_AUTO_LAP

                            lapsTables.hasType(LapsTableType.DISTANCE_AUTO_LAP) -> LapsTableType.DISTANCE_AUTO_LAP

                            else -> {
                                val type = when (infoModelFormatter.unit) {
                                    MeasurementUnit.METRIC -> when {
                                        workoutHeader.totalDistance > 100_000 -> LapsTableType.TEN_KM_AUTO_LAP
                                        workoutHeader.totalDistance > 20_000 -> LapsTableType.FIVE_KM_AUTO_LAP
                                        else -> LapsTableType.ONE_KM_AUTO_LAP
                                    }

                                    MeasurementUnit.IMPERIAL -> when {
                                        workoutHeader.totalDistance > 100_000 * MILES_TO_METERS -> LapsTableType.TEN_MILE_AUTO_LAP
                                        workoutHeader.totalDistance > 20_000 * MILES_TO_METERS -> LapsTableType.FIVE_MILE_AUTO_LAP
                                        else -> LapsTableType.ONE_MILE_AUTO_LAP
                                    }
                                }
                                if (lapsTables.hasType(type)) type else null
                            }
                        }
                        AnalysisLapsData(
                            lapsTables = lapsTables,
                            lapsTableType = lapsTableType,
                            selectedLap = null,
                            lapStartSecondsInWorkout = null,
                            lapEndSecondsInWorkout = null
                        )
                    }
                    _lapsData.postValue(realLapsData)
                }
        }

        loadWorkoutValuesJob?.cancel()
        loadWorkoutValuesJob = launch(io) {
            combine(
                dataLoadResultsFlow.distinctUntilChanged { old, new ->
                    old.fullAnalysisData == new.fullAnalysisData && old.partAnalysisDataData == new.partAnalysisDataData
                },
                _lapsData
                    .asFlow()
                    .debounce {
                        // Update values for custom laps only after chart stops moving
                        if (it.isCustomLapSelected) 300L else 0L
                    }
                    .map<AnalysisLapsData, AnalysisLapsData?> { it } // Allow nulls
                    .onStart {
                        // Start with null laps data to get values updated ASAP even when laps aren't loaded.
                        // This initial null is the only one in this Flow, and further down the
                        // stream all non-null AnalysisLapsData's are treated as user initiated lap selections.
                        // When the data eventually gets loaded, the distinctUntilChangedBy below
                        // treats its full workout selection as equal to this null, so the event
                        // when data gets loaded isn't treated as lap selection.
                        emit(null)
                    }
                    .distinctUntilChanged { old, new ->
                        val areEquivalent =
                            old?.lapStartSecondsInWorkout == new?.lapStartSecondsInWorkout &&
                                old?.lapEndSecondsInWorkout == new?.lapEndSecondsInWorkout
                        if (areEquivalent) {
                            _workoutValuesAreOutdated.postValue(false)
                        }

                        areEquivalent
                    },
                ::Pair
            ).collect { (dataLoadResult, analysisData) ->
                val activityType = dataLoadResult.multisportPart?.activityType
                    ?: dataLoadResult.fullAnalysisData.pagerData.workoutHeader.activityTypeId
                val windowStart = analysisData?.lapStartSecondsInWorkout
                val windowEnd = analysisData?.lapEndSecondsInWorkout
                val hasDistanceData = dataLoadResult.distanceSmlStreamData != null ||
                    (dataLoadResult.geoPoints.lastOrNull()?.totalDistance ?: 0.0) > 0.0

                _workoutValuesContainer.postValue(
                    generateAnalysisWorkoutValuesContainerUseCase(
                        activityType = activityType,
                        hrGraphData = dataLoadResult.hrGraphData,
                        recoveryHRInThreeMinsData = dataLoadResult.recoveryHRInThreeMinsGraphData,
                        geoPoints = dataLoadResult.geoPoints,
                        workoutDurationMillis = dataLoadResult.durationMillis,
                        lapsTableRow = analysisData?.selectedLap,
                        sml = dataLoadResult.sml,
                        timeWindowStartSeconds = windowStart,
                        timeWindowEndSeconds = windowEnd,
                        multisportPartActivity = dataLoadResult.multisportPart,
                        hasDistanceData = hasDistanceData,
                        // Pad the number of values so that it never shrinks to make sure
                        // the amount of space the workout value UI takes doesn't go down.
                        // The amount is allowed to up, but for most workouts with proper data
                        // it can be assumed that the initial workout values for the whole workout
                        // atleast matches the maximum amount available for any lap.
                        padValueCountToAtleast = _workoutValuesContainer.value?.workoutValues?.size
                    )
                )
                _workoutValuesAreOutdated.postValue(false)

                if (analysisData != null) {
                    val lapsTables = analysisLapsLoader.lapsTableFlow.value.data
                    val lapCount = lapsTables
                        ?.firstOrNull { it.lapsType == analysisData.lapsTableType }
                        ?.lapsTableRows?.size ?: 0

                    val lapDistance = if (windowStart != null && windowEnd != null) {
                        val startDistance = timeInWorkoutToDistance(
                            windowStart,
                            dataLoadResult.distanceSmlStreamData,
                            dataLoadResult.geoPoints
                        )
                        val endDistance = timeInWorkoutToDistance(
                            windowEnd,
                            dataLoadResult.distanceSmlStreamData,
                            dataLoadResult.geoPoints
                        )
                        if (startDistance != null && endDistance != null) {
                            (endDistance - startDistance).toDouble()
                        } else {
                            0.0
                        }
                    } else {
                        dataLoadResult.fullAnalysisData.pagerData.workoutHeader.totalDistance
                    }

                    workoutDetailsAnalytics.trackAnalysisLapSelectionChanged(
                        analysisData,
                        lapCount,
                        lapDistance
                    )
                }
            }
        }
    }

    fun setSelectedGraphTypes(
        activityTypeId: Int,
        main: GraphType,
        comparison: GraphType,
        background: GraphType
    ) {
        val initialMainType = savedStateHandle.get(ARG_INITIAL_MAIN_GRAPH_TYPE) as? GraphType
        if (main != initialMainType) {
            savedStateHandle.remove<GraphType?>(ARG_INITIAL_MAIN_GRAPH_TYPE)
            graphAnalysisInfoLoader.setForcedMainGraphType(null)
        }

        graphAnalysisInfoLoader.saveGraphTypesForActivityType(
            activityTypeId,
            main,
            comparison,
            background
        )

        viewModelScope.launch(NonCancellable) {
            workoutDetailsAnalytics.trackAnalysisCustomizeGraphs(
                main,
                comparison,
                background
            )
        }
    }

    fun clearSelectedLap() {
        val selectedTableType = _lapsData.value?.lapsTableType
        setSelectedTimeWindow(null, null, selectedTableType, null)
    }

    fun setSelectNextLap() {
        selectLap(1)
    }

    fun setSelectPreviousLap() {
        selectLap(-1)
    }

    private fun selectLap(offset: Int) {
        val selectLaps =
            lapsData.value?.lapsTables?.firstOrNull { it.lapsType == lapsData.value?.lapsTableType }
                ?: return
        val index = selectLaps.lapsTableRows.indexOf(lapsData.value?.selectedLap)
        val newIndex = index + offset
        if (newIndex in selectLaps.lapsTableRows.indices) {
            val lap = selectLaps.lapsTableRows[newIndex]
            setSelectedLap(selectLaps.lapsType, lap)
        }
    }

    fun setSelectedLapsTableType(lapsTableType: LapsTableType) {
        setSelectedTimeWindow(null, null, lapsTableType, null)
    }

    fun setSelectedLap(lapsTableType: LapsTableType, lap: LapsTableRow) {
        val curHeader = viewState.value?.data?.workoutHeader ?: return
        val curLapsData = _lapsData.value ?: return
        if (lapsTableType == curLapsData.lapsTableType && curLapsData.selectedLap == lap) {
            return
        }

        val (lapStart, lapEnd) = getLapStartAndEndsSecondsInWorkout(curHeader, lap)
        setSelectedTimeWindow(lapStart, lapEnd, lapsTableType, lap)
    }

    private fun getLapStartAndEndsSecondsInWorkout(
        workoutHeader: WorkoutHeader,
        lap: LapsTableRow
    ): Pair<Float?, Float?> {
        val lapEndTime = lap.cumulatedDuration
        val lapDuration = lap.duration
        return if (lapEndTime != null && lapDuration != null) {
            // Last lap's data can sometimes show it lasts couple seconds past end of workout
            // or due to some snorkeling ESW issues the lapDuration can be more than the lapEndtime
            val workoutTotalTime = workoutHeader.totalTime.toFloat()
            (lapEndTime - lapDuration).coerceIn(0f, workoutTotalTime) to
                lapEndTime.coerceAtMost(workoutTotalTime)
        } else {
            null to null
        }
    }

    fun setSelectedTimeWindow(startSeconds: Float, endSeconds: Float) {
        val curData = viewState.value?.data ?: return
        val curLapsData = _lapsData.value ?: return
        if (curLapsData.lapsTableType == null &&
            curLapsData.lapStartSecondsInWorkout == startSeconds &&
            curLapsData.lapEndSecondsInWorkout == endSeconds
        ) {
            return
        }

        // If fully zoomed out, remove lap selection with nulls
        val selectedTableType = curLapsData.lapsTableType
        if (startSeconds <= 0f && endSeconds >= curData.durationSeconds) {
            setSelectedTimeWindow(null, null, selectedTableType, null)
        } else {
            setSelectedTimeWindow(startSeconds, endSeconds, selectedTableType, null)
        }
    }

    private fun setSelectedTimeWindow(
        startSeconds: Float?,
        endSeconds: Float?,
        lapsTableType: LapsTableType?,
        lap: LapsTableRow?
    ) {
        val curLapsData = _lapsData.value ?: return

        if (startSeconds != null && endSeconds != null) {
            playbackStateModel.setLapTimeWindow(
                (startSeconds * 1000).toLong(),
                (endSeconds * 1000).toLong()
            )
        } else {
            playbackStateModel.removeLapTimeWindow()
        }

        _lapsData.value = curLapsData.copy(
            lapsTableType = lapsTableType,
            selectedLap = lap,
            lapStartSecondsInWorkout = startSeconds,
            lapEndSecondsInWorkout = endSeconds
        )
        _workoutValuesAreOutdated.value = true
    }

    fun onChartTouchXValueHighlighted(xValue: Float) {
        val workoutMillis = (xValue * 1000).toLong()
        playbackStateModel.seekToTimeInWorkout(
            workoutMillis,
            PlaybackProgressionReason.USER_SCRUB_TIMELINE
        )

        // Sync seeker and chart ASAP ignoring the throttling used with postValue
        _highlightedSecond.value = xValue
    }

    fun onPlaybackStateTogglePressed() {
        if (playbackStateModel.playbackState.resumed) {
            playbackStateModel.pausePlayback(WorkoutPlaybackPauseReason.PauseButton)
        } else {
            playbackStateModel.setTimeInWorkoutAndAnimationInterpolator(
                checkIfPlaybackAtDataEndInterpolator
            )
            playbackStateModel.resumePlayback(
                AnalyticsPropertyValue.WorkoutPlaybackInitiatedFrom.WORKOUT_ANALYSIS_SCREEN
            )
        }
    }

    private fun getAnalysisData(
        graphType: GraphType,
        lineChartData: List<WorkoutLineChartData>,
        hrGraphData: HrGraphData?,
        recoveryHRInThreeMinsGraphData: HrGraphData?,
        aerobicIqGraphData: AerobicIqGraphData?
    ): GraphAnalysisChartData? = when (graphType) {
        GraphType.Summary(SummaryGraph.HEARTRATE) -> {
            hrGraphData?.let {
                hrGraphDataToGraphAnalysisChartData(
                    it,
                    GraphType.Summary(SummaryGraph.HEARTRATE)
                )
            }
        }

        GraphType.Summary(SummaryGraph.AEROBICZONE) -> {
            aerobicIqGraphData?.let { generateAerobicIqZoneAnalysisChartData(it) }
        }

        GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS) -> {
            recoveryHRInThreeMinsGraphData?.let {
                hrGraphDataToGraphAnalysisChartData(
                    it,
                    GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS)
                )
            }
        }

        else -> {
            lineChartData.firstOrNull { it.graphType == graphType }
                ?.let {
                    workoutLineChartDataToGraphAnalysisChartData(it)
                }
        }
    }

    override fun retryLoading() {
        loadData()
    }

    private fun workoutLineChartDataToGraphAnalysisChartData(workoutLineChartData: WorkoutLineChartData): GraphAnalysisChartData {
        var minXValue = Float.MAX_VALUE
        var maxXValue = Float.MIN_VALUE
        var minYValue = Float.MAX_VALUE
        var maxYValue = Float.MIN_VALUE

        workoutLineChartData.data.forEach { lineEntry ->
            lineEntry.entries.forEach { entry ->
                minXValue = min(entry.x, minXValue)
                maxXValue = max(entry.x, maxXValue)

                minYValue = min(entry.y, minYValue)
                maxYValue = max(entry.y, maxYValue)
            }
        }

        return GraphAnalysisChartData(
            graphType = workoutLineChartData.graphType,
            data = workoutLineChartData.data,
            dataMinXValue = minXValue,
            dataMaxXValue = maxXValue,
            dataMinYValue = minYValue,
            dataMaxYValue = maxYValue,
            graphMinValueStrict = workoutLineChartData.minValueStrict,
            graphMinAllowedValue = workoutLineChartData.minAllowedValue,
            graphMaxValueStrict = workoutLineChartData.maxValueStrict,
            graphMinRange = workoutLineChartData.minRange,
            isGraphInverted = workoutLineChartData.isInverted,
            yValueFormatter = workoutLineChartData.formatter::formatConvertedYValue,
            measurementUnit = workoutLineChartData.infoModelFormatter.unit
        )
    }

    private fun hrGraphDataToGraphAnalysisChartData(
        hrGraphData: HrGraphData,
        graphType: GraphType
    ): GraphAnalysisChartData {
        val entries = hrGraphData.entries

        var minXValue = Float.MAX_VALUE
        var maxXValue = Float.MIN_VALUE
        var minYValue = Float.MAX_VALUE
        var maxYValue = Float.MIN_VALUE

        entries.forEach { entry ->
            minXValue = min(entry.x, minXValue)
            maxXValue = max(entry.x, maxXValue)

            minYValue = min(entry.y, minYValue)
            maxYValue = max(entry.y, maxYValue)
        }

        return GraphAnalysisChartData(
            graphType = graphType,
            data = listOf(WorkoutLineEntry(entries)),
            dataMinXValue = minXValue,
            dataMaxXValue = maxXValue,
            dataMinYValue = minYValue,
            dataMaxYValue = maxYValue,
            graphMinAllowedValue = 0f,
            yValueFormatter = { value -> value.roundToInt().toString() },
            measurementUnit = infoModelFormatter.unit
        )
    }

    private fun generateAerobicIqZoneAnalysisChartData(aerobicIqGraphData: AerobicIqGraphData): GraphAnalysisChartData {
        var minXValue = Float.MAX_VALUE
        var maxXValue = Float.MIN_VALUE
        var minYValue = Float.MAX_VALUE
        var maxYValue = Float.MIN_VALUE

        aerobicIqGraphData.entries.forEach { entry ->
            minXValue = min(entry.x, minXValue)
            maxXValue = max(entry.x, maxXValue)
            minYValue = min(entry.y, minYValue)
            maxYValue = max(entry.y, maxYValue)
        }

        return GraphAnalysisChartData(
            graphType = GraphType.Summary(SummaryGraph.AEROBICZONE),
            data = listOf(WorkoutLineEntry(aerobicIqGraphData.entries)),
            dataMinXValue = minXValue,
            dataMaxXValue = maxXValue,
            dataMinYValue = minYValue,
            dataMaxYValue = maxYValue,

            yValueFormatter = { value ->
                DecimalFormat(
                    "#.#",
                    DecimalFormatSymbols.getInstance(Locale.US)
                ).format(value)
            },
            highlightedInfoValueFormatter = { value ->
                DecimalFormat(
                    "#.##",
                    DecimalFormatSymbols.getInstance(Locale.US)
                ).format(value)
            },
            measurementUnit = infoModelFormatter.unit,
            isGraphInverted = true
        )
    }

    private fun getHighlightedSecondThrottleInterval(graphAnalysisData: GraphAnalysisData): Long {
        val maxEntryCount = maxOf(
            graphAnalysisData.mainGraphData?.data?.maxOf { it.entries.size } ?: 0,
            graphAnalysisData.comparisonGraphData?.data?.maxOf { it.entries.size } ?: 0,
            graphAnalysisData.backgroundGraphData?.data?.maxOf { it.entries.size } ?: 0
        )

        return (SELECTED_SECOND_THROTTLE_FACTOR * maxEntryCount)
            .coerceIn(SELECTED_SECOND_THROTTLE_MIN..SELECTED_SECOND_THROTTLE_MAX).toLong()
    }

    /**
     * When moving the chart highlight manually to the end, it often leaves the PlaybackStateModel's
     * time in workout bit short of reaching the end due to the timestamp of data being recorded not
     * matching the workout's end timestamp. This TimeInWorkoutAndAnimationInterpolator checks
     * for those situations and tells the PlaybackStateModel to wrap the animation to start early
     * if needed.
     */
    private val checkIfPlaybackAtDataEndInterpolator =
        object : PlaybackStateModel.LinearTimeInWorkoutAndAnimationInterpolator() {
            override fun timeInAnimationAfterInterpolatorChange(
                timeInWorkoutMillis: Long,
                timeInAnimationMillis: Long,
                animationDurationMillis: Long,
                workoutDurationMillis: Long
            ): Long {
                val curData = viewState.value?.data
                if (curData != null) {
                    val maxMillisInData = maxOf(
                        curData.mainGraphData?.dataMaxXValue ?: 0f,
                        curData.comparisonGraphData?.dataMaxXValue ?: 0f,
                        curData.backgroundGraphData?.dataMaxXValue ?: 0f
                    ).times(1000).toLong()

                    if (timeInWorkoutMillis >= maxMillisInData) {
                        return 0L
                    }
                }

                return super.timeInAnimationAfterInterpolatorChange(
                    timeInWorkoutMillis,
                    timeInAnimationMillis,
                    animationDurationMillis,
                    workoutDurationMillis
                )
            }
        }

    private fun WorkoutHeader.canSelectLap(multisportPart: MultisportPartActivity?): Boolean {
        val route = polyline
            ?.takeUnless(String::isEmpty)
            ?.let(PolyUtil::decode)
            ?: emptyList()
        return activityType != ActivityType.MULTISPORT &&
            activityType.supportsLaps() &&
            supportWorkoutAnalysisOnMap(route, multisportPart)
    }

    private fun List<LapsTable>.hasType(type: LapsTableType): Boolean = any { it.lapsType == type }

    private data class DataLoadResults(
        val fullAnalysisData: WorkoutAnalysisData,
        val partAnalysisDataData: WorkoutAnalysisData,
        val hrGraphData: HrGraphData?,
        val aerobicIqGraphData: AerobicIqGraphData?,
        val recoveryHRInThreeMinsGraphData: HrGraphData?,
        val multisportPart: MultisportPartActivity?,
        val sml: Sml?,
        val multisportPartWindow: SuuntoLogbookWindow?,
        val graphTypeInfo: WorkoutGraphAnalysisInfo,
        val geoPoints: List<WorkoutGeoPoint>,
        /**
         * Non-null if workout has SML with distance data in sample points
         */
        val distanceSmlStreamData: SmlStreamData?,
        val intensityExtension: IntensityExtension?,
        val showDistance: Boolean
    ) {
        val durationMillis: Long
            get() {
                if (multisportPart != null) {
                    val windowDuration = multisportPartWindow?.duration
                    return if (windowDuration != null) {
                        (windowDuration * 1000).roundToLong()
                    } else {
                        multisportPart.stopTime - multisportPart.startTime
                    }
                }

                val smlHeaderDuration = sml?.summary?.header?.duration
                val smlHeaderPauseDuration = sml?.summary?.header?.pauseDuration
                if (smlHeaderDuration != null && smlHeaderPauseDuration != null) {
                    return ((smlHeaderDuration - smlHeaderPauseDuration) * 1000).roundToLong()
                }

                val wholeWorkoutWindowDuration = sml?.getActivityWindow(null)?.duration
                if (wholeWorkoutWindowDuration != null) {
                    return (wholeWorkoutWindowDuration * 1000).roundToLong()
                }

                val lastStopEvent = fullAnalysisData.pagerData.workoutData?.events?.lastOrNull {
                    it.type == Event.EventType.PAUSE || it.type == Event.EventType.STOP
                }

                return when {
                    lastStopEvent != null -> {
                        lastStopEvent.timeWhenEventHappened.roundToLong()
                    }

                    geoPoints.isNotEmpty() -> {
                        geoPoints.last().millisecondsInWorkout.toLong()
                    }

                    else -> {
                        // WorkoutHeader's totalTime can be edited by the user, use it only
                        // if there's nothing else to use
                        (fullAnalysisData.pagerData.workoutHeader.totalTime * 1000).roundToLong()
                    }
                }
            }
    }

    companion object {
        internal const val ARG_INITIAL_MAIN_GRAPH_TYPE = "ARG_INITIAL_MAIN_GRAPH_TYPE"
        internal const val ARG_INITIAL_SELECTIONS = "ARG_INITIAL_SELECTIONS"

        private const val SELECTED_SECOND_THROTTLE_FACTOR = 0.018
        private const val SELECTED_SECOND_THROTTLE_MIN = 100.0
        private const val SELECTED_SECOND_THROTTLE_MAX = 1000.0
    }
}
