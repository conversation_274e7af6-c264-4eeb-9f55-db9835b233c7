package com.stt.android.workout.details.photopager

import android.widget.ImageView
import androidx.databinding.BindingAdapter
import coil3.load
import com.github.chrisbanes.photoview.PhotoView
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.workouts.pictures.Picture

@BindingAdapter(value = ["src"])
fun loadPicture(photoView: PhotoView, picture: Picture?) {
    if (picture == null) return
    photoView.scaleType = ImageView.ScaleType.FIT_CENTER
    photoView.load(ImageInformation.fromPicture(picture).getHighResUri(photoView.context))
}
