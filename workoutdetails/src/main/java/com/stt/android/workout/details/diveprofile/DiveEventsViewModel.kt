package com.stt.android.workout.details.diveprofile

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.distinctUntilChanged
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.StateEvent
import com.stt.android.domain.sml.StateMarkType
import com.stt.android.ui.extensions.depth
import com.stt.android.ui.fragments.workout.dive.DiveProfileShowEventsContainer
import com.stt.android.ui.fragments.workout.dive.EventItem
import com.stt.android.workout.details.DiveProfileData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers.Default
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@HiltViewModel
class DiveEventsViewModel @Inject constructor() : ViewModel() {
    private val _viewState = MutableLiveData<ViewState<DiveProfileData?>>()
    val viewState: LiveData<ViewState<DiveProfileData?>> = _viewState

    private val _diveEvents = MutableLiveData<ViewState<DiveProfileShowEventsContainer>>()
    val diveEvents: LiveData<ViewState<DiveProfileShowEventsContainer>> = _diveEvents

    private val _showDivider = MutableLiveData<Boolean>()
    val showDivider: LiveData<Boolean> = _showDivider.distinctUntilChanged()

    fun loadData(data: DiveProfileData?) {
        _viewState.value = loaded(data)
    }

    suspend fun createEvents(
        sml: Sml,
        dataSet: ILineDataSet
    ) = withContext(Default) {
        val events = kotlin.runCatching {
            sml.streamData.getDiveEvents(sml.summary.isDiveAfterTissueReset)
                .mapIndexedNotNull { index, event ->
                    val elapsed: Long = when {
                        event.data.elapsed != null -> event.data.elapsed
                        // Seal may log dive active-event before start-event
                        index == 0 && event is StateEvent && event.type == StateMarkType.DIVE_ACTIVE -> 0L
                        else -> null
                    } ?: return@mapIndexedNotNull null
                    EventItem(
                        event,
                        elapsed,
                        event.depth ?: dataSet.getEntryForXValue(
                            TimeUnit.MILLISECONDS.toSeconds(elapsed).toFloat(),
                            0f
                        ).y
                    )
                }
        }.onFailure {
            Timber.w(it, "Creating dive events failed.d")
        }.getOrDefault(emptyList())

        _diveEvents.postValue(loaded(DiveProfileShowEventsContainer(events)))
    }

    fun setDividerVisibility(value: Boolean) {
        _showDivider.value = value
    }
}
