package com.stt.android.workout.details.heartrate

import com.google.android.gms.maps.model.LatLng
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.failure
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.IntensityExtensionDataModel
import com.stt.android.controllers.UserSettingsController
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workout.filterEventsAndHrEvents
import com.stt.android.domain.workout.filterPausesAndAdjustMillisecondsInWorkout
import com.stt.android.domain.workout.heartRateEventsWithoutPauses
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.loadExtension
import com.stt.android.extensions.multisportRoutes
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.utils.averageOfInt
import com.stt.android.utils.takeIfNotNaN
import com.stt.android.workout.details.HeartRateData
import com.stt.android.workout.details.HrGraphData
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.workoutdata.WorkoutDataLoader
import com.suunto.algorithms.data.HeartRate.Companion.hz
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.Default
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt

interface HeartRateDataLoader {
    suspend fun loadHeartRateData(
        workoutHeader: WorkoutHeader,
        scope: CoroutineScope
    ): Flow<ViewState<HeartRateData>>

    suspend fun update(workoutHeader: WorkoutHeader, scope: CoroutineScope)
    val hrGraphDataStateFlow: StateFlow<ViewState<HeartRateData>>
}

@FlowPreview
@ActivityRetainedScoped
class DefaultHeartRateDataLoader
@Inject constructor(
    userSettingsController: UserSettingsController,
    navigationEventDispatcher: NavigationEventDispatcher,
    intensityExtensionDataModel: IntensityExtensionDataModel,
    smlDataLoader: SmlDataLoader,
    private val workoutDataLoader: WorkoutDataLoader,
    workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    multisportPartActivityLoader: MultisportPartActivityLoader
) : BaseHeartRateDataLoader(
    userSettingsController,
    navigationEventDispatcher,
    intensityExtensionDataModel,
    smlDataLoader,
    workoutDetailsAnalytics,
    multisportPartActivityLoader
) {
    private fun buildHrEventsForMultisportPartActivity(
        workoutData: WorkoutData,
        activity: MultisportPartActivity
    ): List<WorkoutHrEvent> {
        val (activityEvents, activityHrEvents) = workoutData.filterEventsAndHrEvents(activity)
        return filterPausesAndAdjustMillisecondsInWorkout(
            activityEvents,
            activityHrEvents,
            activity.startTime
        )
    }

    override suspend fun getHeartRateData(workoutHeader: WorkoutHeader) = withContext(IO) {
        workoutDataLoader.workoutDataStateFlow
            .onEach {
                when (it) {
                    is ViewState.Error -> hrGraphDataStateFlow.value = failure(
                        it.errorEvent,
                        HeartRateData(
                            null,
                            emptyMap(),
                            ::graphClickListener,
                            ::viewOnMapClickListener
                        )
                    )

                    is ViewState.Loading ->
                        hrGraphDataStateFlow.value =
                            loading(
                                HeartRateData(
                                    null,
                                    emptyMap(),
                                    ::graphClickListener,
                                    ::viewOnMapClickListener
                                )
                            )

                    is ViewState.Loaded -> {
                        val workoutData = it.data
                        if (workoutData != null) {
                            val intensityExtension =
                                intensityExtensionDataModel.loadExtension(workoutHeader)
                            val graphData = createGraphData(
                                workoutHeader,
                                workoutData.heartRateEventsWithoutPauses,
                                intensityExtension
                            )
                            hrGraphDataStateFlow.value = loaded(
                                HeartRateData(
                                    graphData,
                                    emptyMap(),
                                    ::graphClickListener,
                                    ::viewOnMapClickListener
                                )
                            )
                            launch {
                                getMultisportPartActivityHeartRateData(
                                    workoutData,
                                    intensityExtension
                                )
                            }
                        } else {
                            // Since WorkoutDataLoader emits a loaded state with null instead of an error,
                            // we use the same approach here to prevent downstream consumers from getting stuck on a loading state
                            hrGraphDataStateFlow.value = loaded(null)
                        }
                    }
                }
            }
            .catch { Timber.w(it, "Getting heart rate data failed.") }
            .collect()
    }

    private suspend fun getMultisportPartActivityHeartRateData(
        workoutData: WorkoutData,
        intensityExtension: IntensityExtension?
    ) = withContext(Default) {
        smlDataLoader.smlStateFlow.onEach {
            when (it) {
                is ViewState.Error -> Timber.w("Getting multisport heart rate data failed.")
                is ViewState.Loading -> { // do nothing
                }

                is ViewState.Loaded -> {
                    val multisportRoutes = it.data?.multisportRoutes
                    it.data?.streamData?.multisportPartActivities?.let { activities ->
                        val data = hrGraphDataStateFlow.value.data
                        val map = activities.toHrGraphDataMap(
                            workoutData,
                            intensityExtension,
                            it.data,
                            multisportRoutes
                        )
                        data?.copy(multisportPartGraphData = map)?.run {
                            hrGraphDataStateFlow.value = loaded(this)
                        }
                    }
                }
            }
        }
            .catch { Timber.w(it, "Getting multisport heart rate data failed.") }
            .collect()
    }

    private fun List<MultisportPartActivity>.toHrGraphDataMap(
        workoutData: WorkoutData,
        intensityExtension: IntensityExtension?,
        sml: Sml?,
        multisportRoutes: List<List<LatLng>>?
    ): Map<MultisportPartActivity, HrGraphData> = associate { activity ->
        activity to createGraphData(
            workoutHeader = workoutHeader,
            hrEvents = buildHrEventsForMultisportPartActivity(workoutData, activity),
            intensityExtension = intensityExtension,
            multisportPartActivity = activity,
            sml = sml,
            multisportRoutes = multisportRoutes
        )
    }

    override fun getGraphType(): GraphType {
        return GraphType.Summary(SummaryGraph.HEARTRATE)
    }

    override fun getAvgHr(
        multisportPartActivity: MultisportPartActivity?,
        sml: Sml?,
        workoutHeader: WorkoutHeader,
        hrEvents: List<WorkoutHrEvent>
    ): Int = if (multisportPartActivity != null) {
        // Get avg hr from the multisport part activity move window
        sml?.getActivityWindow(multisportPartActivity)?.hr
            ?.firstOrNull()?.avg?.toDouble()
            ?.run { this * 60 }
            ?.roundToInt()
    } else {
        null
    } ?: workoutHeader.heartRateAverage.roundToInt()
}

@FlowPreview
@ActivityRetainedScoped
class RecoveryHeartRateInThreeMinsDataLoader
@Inject constructor(
    userSettingsController: UserSettingsController,
    navigationEventDispatcher: NavigationEventDispatcher,
    intensityExtensionDataModel: IntensityExtensionDataModel,
    smlDataLoader: SmlDataLoader,
    workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val multisportPartActivityLoader: MultisportPartActivityLoader
) : BaseHeartRateDataLoader(
    userSettingsController,
    navigationEventDispatcher,
    intensityExtensionDataModel,
    smlDataLoader,
    workoutDetailsAnalytics,
    multisportPartActivityLoader
) {

    companion object {
        private const val THREE_MINS_IN_MILLIS = 3 * 60 * 1000
    }

    override suspend fun getHeartRateData(workoutHeader: WorkoutHeader) = withContext(Default) {
        smlDataLoader.smlStateFlow.onEach {
            when (it) {
                is ViewState.Error -> hrGraphDataStateFlow.value = failure(
                    it.errorEvent,
                    HeartRateData(
                        null,
                        emptyMap(),
                        ::graphClickListener,
                        ::viewOnMapClickListener
                    )
                )

                is ViewState.Loading ->
                    hrGraphDataStateFlow.value =
                        loading(
                            HeartRateData(
                                null,
                                emptyMap(),
                                ::graphClickListener,
                                ::viewOnMapClickListener
                            )
                        )

                is ViewState.Loaded -> {
                    val isMultisportPart =
                        multisportPartActivityLoader.multisportPartActivityFlow.value.data != null

                    val recoveryHeartRateData = it.data?.streamData?.recoveryHeartInThreeMins
                    if (recoveryHeartRateData != null && !isMultisportPart) {
                        val intensityExtension =
                            intensityExtensionDataModel.loadExtension(workoutHeader)
                        val firstRecoveryHeartRateTimestamp =
                            recoveryHeartRateData.firstOrNull()?.timestamp ?: workoutHeader.stopTime
                        val graphData = createGraphData(
                            workoutHeader,
                            recoveryHeartRateData.mapNotNull { streamPoint ->
                                if (streamPoint.timestamp - firstRecoveryHeartRateTimestamp <= THREE_MINS_IN_MILLIS) {
                                    WorkoutHrEvent(
                                        streamPoint.timestamp,
                                        streamPoint.value.hz.inBpm.roundToInt(),
                                        (streamPoint.timestamp - firstRecoveryHeartRateTimestamp) % THREE_MINS_IN_MILLIS
                                    )
                                } else {
                                    null
                                }
                            },
                            intensityExtension
                        )
                        Timber.d("RecoveryHeartRateInThreeMinsDataLoader createGraphData: ${graphData.entries.joinToString { "${it.x}, ${it.y}, ${it.data}" }}")
                        hrGraphDataStateFlow.value = loaded(
                            HeartRateData(
                                graphData,
                                emptyMap(),
                                ::graphClickListener,
                                ::viewOnMapClickListener
                            )
                        )
                        launch {
                            getMultisportPartActivityHeartRateData(it.data)
                        }
                    } else {
                        // Since WorkoutDataLoader emits a loaded state with null instead of an error,
                        // we use the same approach here to prevent downstream consumers from getting stuck on a loading state
                        hrGraphDataStateFlow.value = loaded(null)
                    }
                }
            }
        }
            .catch { Timber.w(it, "Getting heart rate data failed.") }
            .collect()
    }

    private fun getMultisportPartActivityHeartRateData(sml: Sml?) {
        sml?.streamData?.multisportPartActivities?.let { activities ->
            val data = hrGraphDataStateFlow.value.data
            val map = emptyMap<MultisportPartActivity, HrGraphData>()
            data?.copy(multisportPartGraphData = map)?.run {
                hrGraphDataStateFlow.value = loaded(this)
            }
        }
    }

    override fun getGraphType(): GraphType {
        return GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS)
    }

    override fun getAvgHr(
        multisportPartActivity: MultisportPartActivity?,
        sml: Sml?,
        workoutHeader: WorkoutHeader,
        hrEvents: List<WorkoutHrEvent>
    ): Int = hrEvents.averageOfInt(WorkoutHrEvent::getHeartRate)
        .takeIfNotNaN()
        ?.roundToInt()
        ?: 0
}
