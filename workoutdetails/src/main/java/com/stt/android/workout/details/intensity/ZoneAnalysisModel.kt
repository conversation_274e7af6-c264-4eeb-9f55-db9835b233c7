package com.stt.android.workout.details.intensity

import androidx.compose.ui.platform.ComposeView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.aerobiczone.AerobicZonesInfoSheet
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.compose.theme.AppTheme
import com.stt.android.workout.details.R
import com.stt.android.workout.details.ZoneAnalysisData
import com.stt.android.workout.details.ZoneAnalysisGraphType
import com.stt.android.workout.details.intensity.composables.ZoneAnalysisScreen

@EpoxyModelClass
abstract class ZoneAnalysisModel : EpoxyModelWithHolder<ZoneAnalysisViewHolder>() {

    @EpoxyAttribute
    lateinit var zoneAnalysisData: ZoneAnalysisData

    @EpoxyAttribute
    lateinit var mainGraphType: ZoneAnalysisGraphType

    @EpoxyAttribute
    lateinit var secondaryGraphType: ZoneAnalysisGraphType

    @EpoxyAttribute
    lateinit var zoneAnalysisGraphTypes: Set<ZoneAnalysisGraphType>

    @EpoxyAttribute
    var showFullscreenButton: Boolean = true

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onAerobicZoneInfoClick: ((dest: AerobicZonesInfoSheet) -> Unit)? = null

    override fun getDefaultLayout() = R.layout.model_zone_analysis

    override fun bind(holder: ZoneAnalysisViewHolder) {
        holder.zoneAnalysisView.setContent {
            AppTheme {
                ZoneAnalysisScreen(
                    availableGraphTypes = zoneAnalysisGraphTypes,
                    mainGraphType = mainGraphType,
                    secondaryGraphType = secondaryGraphType,
                    zoneAnalysisData = zoneAnalysisData,
                    showFullscreenButton = showFullscreenButton,
                    onAerobicZoneInfoClick = onAerobicZoneInfoClick
                )
            }
        }
    }
}

class ZoneAnalysisViewHolder : KotlinEpoxyHolder() {
    val zoneAnalysisView by bind<ComposeView>(R.id.zone_analysis_view)
}
