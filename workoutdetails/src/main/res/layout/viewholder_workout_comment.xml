<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="commentText"
            type="String" />

        <variable
            name="realNameOrUsername"
            type="String" />

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />

    </data>

    <TextView
        style="@style/WorkoutDetailCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:foreground="?attr/selectableItemBackground"
        android:lineSpacingExtra="@dimen/size_spacing_small"
        android:lineSpacingMultiplier="1"
        android:maxLines="20"
        android:onClick="@{clickListener}"
        android:paddingStart="@dimen/size_spacing_medium"
        android:paddingTop="@dimen/size_spacing_xsmall"
        android:paddingEnd="@dimen/size_spacing_medium"
        android:paddingBottom="@dimen/size_spacing_xsmall"
        android:textAppearance="@style/Body3"
        app:commentText="@{commentText}"
        app:realNameOrUsername="@{realNameOrUsername}"
        tools:ignore="UnusedAttribute" />
</layout>
