<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?suuntoBackground"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            style="@style/Toolbar.Native"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:theme="@style/Toolbar.Native" />

    </com.google.android.material.appbar.AppBarLayout>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/workout_share_tab"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appbar"
        app:layout_constraintWidth_max="@dimen/content_max_width"
        app:tabSelectedTextColor="@color/primary_blue"
        app:tabTextAppearance="@style/TabTextStyle"
        android:background="@color/white"
        app:tabTextColor="@color/medium_grey" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/share_view_pager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintWidth_max="@dimen/content_max_width"
        app:layout_constraintTop_toBottomOf="@+id/workout_share_tab" />


</androidx.constraintlayout.widget.ConstraintLayout>

