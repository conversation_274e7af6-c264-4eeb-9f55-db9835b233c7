package com.stt.android.workout.details.share.recentsummary

import android.content.Context
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.domain.user.workout.RecentWorkoutSummary
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workoutdetail.recentsummary.RecentWorkoutItemHelper
import javax.inject.Inject

class LongScreenshotRecentWorkoutSummaryController @Inject constructor(
    private val infoModelFormatter: InfoModelFormatter,
    private val unitConverter: JScienceUnitConverter,
) : ViewStateEpoxyController<RecentWorkoutSummary>() {

    var workoutHeader: WorkoutHeader? = null

    private var titles: List<String> = arrayListOf()

    private var context: Context? = null

    private var startDateText = ""
    private var endDateText = ""

    fun initData(
        context: Context,
        workoutHeader: WorkoutHeader,
        startDateText: String,
        endDateText: String
    ) {
        this.workoutHeader = workoutHeader
        this.context = context
        this.startDateText = startDateText
        this.endDateText = endDateText
        titles = RecentWorkoutItemHelper.getTitle(context.resources, workoutHeader)
    }

    override fun buildModels(viewState: ViewState<RecentWorkoutSummary?>) {
        super.buildModels(viewState)
        if (viewState.isLoaded()) {
            viewState.data?.let {
                for (index in titles.indices) {
                    longScreenshotRecentWorkoutSummaryItem {
                        id("recentWorkoutSummaryItem$index")
                        referenceWorkout(workoutHeader)
                        position(index)
                        context(context)
                        recentWorkoutSummary(it)
                        startDateText(startDateText)
                        endDateText(endDateText)
                        dateType(titles[index])
                        infoModelFormatter(infoModelFormatter)
                        unitConverter(unitConverter)
                    }
                }
            }
        }
    }
}
