package com.stt.android.workout.details.share.brief

import android.content.Context
import android.content.SharedPreferences
import androidx.fragment.app.FragmentManager
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.common.viewstate.ViewState
import com.stt.android.di.ActivityFragmentManager
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummaryData
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workout.details.AdvancedLapsData
import com.stt.android.workout.details.BaseWorkoutDetailsController
import com.stt.android.workout.details.OnPageSelected
import com.stt.android.workout.details.RecentTrendData
import com.stt.android.workout.details.WorkoutDetailsViewState
import dagger.hilt.android.qualifiers.ActivityContext
import javax.inject.Inject

class BriefShareWorkoutDetailsController @Inject constructor(
    @ActivityContext context: Context,
    @ActivityFragmentManager fragmentManager: FragmentManager,
    infoModelFormatter: InfoModelFormatter,
    unitConverter: JScienceUnitConverter,
    @FeatureTogglePreferences featureTogglePreferences: SharedPreferences,
) : BaseWorkoutDetailsController(
    context,
    fragmentManager,
    infoModelFormatter,
    unitConverter,
    featureTogglePreferences
) {
    override fun addTopModel(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        // do nothing
    }

    override fun addDiveLocation(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        // do nothing
    }

    override fun addShareActivity(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        // do nothing
    }

    override fun addRecentTrendData(
        competitionWorkoutSummaryData: CompetitionWorkoutSummaryData?,
        workoutHeader: WorkoutHeader,
        recentTrendData: RecentTrendData,
        onPageSelected: OnPageSelected?
    ) {
        // do nothing
    }

    override fun addRecentWorkoutSummary(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        // do nothing
    }

    override fun addZoneAnalysisView(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        // do nothing
    }

    override fun addLaps(
        workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>,
        advancedLapsDataViewState: ViewState<AdvancedLapsData?>
    ) {
        // do nothing
    }

    override fun addHrBeltAd(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        // do nothing
    }

    override fun addDiveProfile(
        workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>,
        supportsDiveEvents: Boolean
    ) {
        // do nothing
    }

    override fun longScreenshotLayout(): Boolean = true

    override fun briefSharing(): Boolean = true
}
