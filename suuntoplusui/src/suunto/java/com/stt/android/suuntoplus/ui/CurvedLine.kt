package com.stt.android.suuntoplus.ui

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing

enum class CurveDirection {
    LEFT_DOWN,
    RIGHT_DOWN,
    LEFT_UP,
    RIGHT_UP;

    val startAngle: Float
        get() = when (this) {
            LEFT_DOWN -> 180f
            RIGHT_DOWN -> 270f
            LEFT_UP -> 90f
            RIGHT_UP -> 0f
        }

    val sweepAngle = 90f

    val isUp: Boolean
        get() = this == LEFT_UP || this == RIGHT_UP

    val isRight: Boolean
        get() = this == RIGHT_UP || this == RIGHT_DOWN
}

@Composable
fun CurvedLine(
    direction: CurveDirection,
    modifier: Modifier = Modifier,
    curveRadius: Dp = 16.dp,
    thickness: Dp = 1.dp,
    color: Color = MaterialTheme.colors.lightGrey,
) {
    Canvas(modifier = modifier) {
        val radiusPx = curveRadius.toPx()
        val centerY = size.height / 2.0f
        val curveTopY = if (direction.isUp) centerY - radiusPx * 2f else centerY
        val curveLeftX = if (direction.isRight) size.width - radiusPx * 2f else 0f
        val lineLeftX = if (direction.isRight) 0f else radiusPx
        val lineRightX = if (direction.isRight) size.width - radiusPx else size.width
        val verticalLineX = if (direction.isRight) size.width else 0f
        val verticalLineTopY = if (direction.isUp) 0f else centerY + radiusPx
        val verticalLineBottomY = if (direction.isUp) centerY - radiusPx else size.height

        drawArc(
            color = color,
            startAngle = direction.startAngle,
            sweepAngle = direction.sweepAngle,
            useCenter = false,
            topLeft = Offset(curveLeftX, curveTopY),
            size = Size(radiusPx * 2f, radiusPx * 2f),
            style = Stroke(width = thickness.toPx())
        )

        drawLine(
            color,
            Offset(lineLeftX, centerY),
            Offset(lineRightX, centerY),
            strokeWidth = thickness.toPx()
        )

        drawLine(
            color,
            Offset(verticalLineX, verticalLineTopY),
            Offset(verticalLineX, verticalLineBottomY),
            strokeWidth = thickness.toPx()
        )
    }
}

@Preview
@Composable
private fun CurvedLineDownwardsPreview() {
    AppTheme {
        Surface {
            Row(
                Modifier
                    .padding(MaterialTheme.spacing.medium)
                    .height(MaterialTheme.spacing.large)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                CurvedLine(
                    modifier = Modifier
                        .weight(1f)
                        .height(MaterialTheme.spacing.large),
                    direction = CurveDirection.LEFT_DOWN
                )

                Text(
                    "Start repeat block",
                    color = MaterialTheme.colors.mediumGrey,
                    fontSize = 12.sp
                )

                CurvedLine(
                    modifier = Modifier
                        .weight(1f)
                        .height(MaterialTheme.spacing.large),
                    direction = CurveDirection.RIGHT_DOWN
                )
            }
        }
    }
}

@Preview
@Composable
private fun CurvedLineUpwardsPreview() {
    AppTheme {
        Surface {
            Row(
                Modifier
                    .padding(MaterialTheme.spacing.medium)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                CurvedLine(
                    modifier = Modifier
                        .weight(1f)
                        .height(MaterialTheme.spacing.large),

                    direction = CurveDirection.LEFT_UP
                )

                Text(
                    "End repeat block",
                    color = MaterialTheme.colors.mediumGrey,
                    fontSize = 12.sp
                )

                CurvedLine(
                    modifier = Modifier
                        .weight(1f)
                        .height(MaterialTheme.spacing.large),
                    direction = CurveDirection.RIGHT_UP
                )
            }
        }
    }
}
