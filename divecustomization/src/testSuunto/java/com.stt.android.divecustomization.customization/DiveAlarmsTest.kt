package com.stt.android.divecustomization.customization

import com.soy.algorithms.divemodecustomization.entities.Mode
import com.stt.android.divecustomization.customization.entities.DiveTimeValue
import com.stt.android.divecustomization.customization.logic.DiveAlarms
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions
import org.assertj.core.api.Assertions.within
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.times
import org.mockito.kotlin.verify

@RunWith(MockitoJUnitRunner::class)
class DiveAlarmsTest : DiveCustomizationTestBase() {

    private lateinit var diveAlarms: DiveAlarms

    @Before
    override fun setup() = runTest {
        super.setup()
        diveAlarms = viewModel
    }

    @Test
    fun `check if alarms flow returns correct values`() = runTest {
        val diveAlarmsFlow = diveAlarms.getDiveAlarmsFlow().first()

        // Test the toggle states
        // Only the tank pressure is enabled in the config
        Assertions.assertThat(diveAlarmsFlow.data?.maxDepth?.enabled).isEqualTo(false)
        Assertions.assertThat(diveAlarmsFlow.data?.diveTime?.enabled).isEqualTo(false)
        Assertions.assertThat(diveAlarmsFlow.data?.gasTime?.enabled).isEqualTo(false)
        Assertions.assertThat(diveAlarmsFlow.data?.tankPressure?.enabled).isEqualTo(true)

        Assertions.assertThat(diveAlarmsFlow.data?.maxDepth?.option?.selectedValue).isEqualTo(
            30.0,
            within(PRECISION)
        )
        // Config returns 45min as the default dive time
        Assertions.assertThat(diveAlarmsFlow.data?.diveTime?.selectedValue).isEqualTo(
            DiveTimeValue(
                seconds = 0,
                minutes = 45,
                hours = 0
            )
        )

        Assertions.assertThat(diveAlarmsFlow.data?.gasTime?.selectedValue).isEqualTo(
            DiveTimeValue(
                seconds = 0,
                minutes = 10,
                hours = 0
            )
        )

        // Pressure unit in the config is bar
        // config has 7500000.0f = 75 bar as selected value but available list has values in increments of ten
        // so the nearest value will be selected and preference is given to higher number i.e 80 bar
        Assertions.assertThat(diveAlarmsFlow.data?.tankPressure?.option?.selectedValue).isEqualTo(
            80.0,
            within(PRECISION)
        )
        // Not testing list of available values here, that is tested separately in DiveUtilsTest
    }

    @Test
    fun `setting depth alarm should call validation with correct change`() =
        runTest {
            diveAlarms.enableDepthAlarm(true)
            diveAlarms.setDepthAlarm(40.0)

            val argumentCaptor = argumentCaptor<Mode>()
            verify(diveDeviceModeUseCase, times(2)).validateCustomizationMode(
                any(),
                argumentCaptor.capture(),
                any()
            )

            // verify that the conversion back to pascal happens
            Assertions.assertThat(argumentCaptor.firstValue.alarms.maxDepth.enabled.value).isTrue
            Assertions.assertThat(argumentCaptor.secondValue.alarms.maxDepth.depth.value)
                .isEqualTo(40.0, within(PRECISION))
        }
}
