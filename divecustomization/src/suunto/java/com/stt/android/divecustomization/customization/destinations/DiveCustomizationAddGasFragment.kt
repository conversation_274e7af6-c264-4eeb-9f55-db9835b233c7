package com.stt.android.divecustomization.customization.destinations

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Scaffold
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.common.viewstate.ViewState.Loading
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.divecustomization.R
import com.stt.android.divecustomization.customization.logic.DiveCustomizationViewModel
import com.stt.android.divecustomization.customization.ui.common.DiveAlertDialog
import com.stt.android.divecustomization.customization.ui.gases.addgas.AddGasTopBar
import com.stt.android.divecustomization.customization.ui.gases.addgas.DiveAddGasMainContentView
import com.stt.android.divecustomization.customization.ui.theme.DiveMaterialTheme
import dagger.hilt.android.AndroidEntryPoint
import com.stt.android.R as BaseR

@AndroidEntryPoint
class DiveCustomizationAddGasFragment : Fragment() {

    private val viewModel: DiveCustomizationViewModel by activityViewModels()
    private val args: DiveCustomizationAddGasFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val id = args.diveGasId
        val state = args.diveGasState
        viewModel.setGas(id, state)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setContent {
            var deleteClicked by rememberSaveable { mutableStateOf(false) }
            val addGasFlow by viewModel.getDiveAddGasFlow().collectAsState(Loading())
            val addGasTitleFlow by viewModel.getAddGasTitleFlow().collectAsState(Loading())
            DiveMaterialTheme {
                Scaffold(
                    topBar = {
                        AddGasTopBar(
                            content = addGasTitleFlow,
                            onCancel = {
                                findNavController().navigateUp()
                            },
                            onSave = {
                                viewModel.saveGas(args.diveGasId)
                                findNavController().navigateUp()
                            }
                        )
                    }
                ) { internalPadding ->
                    ContentCenteringColumn(
                        modifier = Modifier
                            .padding(internalPadding)
                            .verticalScroll(rememberScrollState())
                    ) {
                        DiveAddGasMainContentView(
                            content = addGasFlow,
                            controller = viewModel,
                            onDelete = {
                                deleteClicked = true
                            }
                        )

                        DiveAlertDialog(
                            showDialog = deleteClicked,
                            contentText = stringResource(R.string.dive_modes_gas_delete),
                            confirmText = stringResource(BaseR.string.ok),
                            dismissText = stringResource(BaseR.string.cancel),
                            onDone = {
                                deleteClicked = false
                                viewModel.deleteGas(args.diveGasId)
                                findNavController().navigateUp()
                            },
                            onDismiss = {
                                deleteClicked = false
                            }
                        )
                    }
                }
            }
        }
    }
}
