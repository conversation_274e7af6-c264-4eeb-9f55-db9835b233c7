package com.stt.android.divecustomization.customization.entities

import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.ui.res.stringResource
import kotlinx.collections.immutable.ImmutableList

@Immutable
data class DiveRangeSelectionOption<T>(
    val values: ImmutableList<T>,
    val selectedValue: T,
    @StringRes val units: Int
) where T : Comparable<T> {
    val selectedOptionText: String
        @Composable get() {
            return "$selectedValue " + stringResource(units)
        }
}
