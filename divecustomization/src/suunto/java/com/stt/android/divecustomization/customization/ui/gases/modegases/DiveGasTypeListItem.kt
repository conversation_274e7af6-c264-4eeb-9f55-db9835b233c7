package com.stt.android.divecustomization.customization.ui.gases.modegases

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.soy.algorithms.divemodecustomization.entities.GasType
import com.stt.android.divecustomization.R
import com.stt.android.divecustomization.customization.entities.gases.DiveGas
import com.stt.android.divecustomization.customization.ui.gases.GasListInfoItem
import com.stt.android.R as BaseR

@Composable
fun DiveGasTypeListItem(
    diveGas: DiveGas,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .clickable(onClick = onClick)
            .padding(dimensionResource(BaseR.dimen.size_spacing_medium))
            .fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            GasTypeIcon(painterResource(R.drawable.ic_dive_gas))
            Text(
                modifier = Modifier.padding(horizontal = dimensionResource(BaseR.dimen.size_spacing_medium)),
                text = diveGas.gasName
            )
        }

        Row(
            modifier = Modifier
                .padding(
                    top = dimensionResource(BaseR.dimen.size_spacing_medium),
                    bottom = dimensionResource(BaseR.dimen.size_spacing_medium)
                )
        ) {
            GasListInfoItem(
                modifier = Modifier.weight(weight = 0.25f),
                title = diveGas.gasTypeText,
                subtitle = stringResource(R.string.dive_modes_gas_type_title)
            )

            diveGas.endValueText?.let {
                GasListInfoItem(
                    modifier = Modifier.weight(weight = 0.25f),
                    title = it,
                    subtitle = stringResource(BaseR.string.dive_modes_gas_end)
                )
            }

            diveGas.modValueText?.let {
                GasListInfoItem(
                    modifier = Modifier.weight(weight = 0.25f),
                    title = it,
                    subtitle = stringResource(BaseR.string.dive_modes_gas_mod)
                )
            }

            GasListInfoItem(
                modifier = Modifier.weight(weight = 0.25f),
                title = diveGas.po2ValueText,
                subtitle = if (diveGas.gasType == GasType.CC) {
                    stringResource(R.string.dive_modes_gases_setpoint_item_title)
                } else {
                    stringResource(R.string.dive_modes_gas_po2)
                }
            )
        }

        diveGas.tankPodId?.run {
            Text(
                text = stringResource(R.string.dive_modes_gases_pod_id, this),
                fontSize = 12.sp,
                color = colorResource(BaseR.color.medium_grey)
            )
        }
    }
}

@Composable
private fun GasTypeIcon(icon: Painter) {
    Image(
        painter = icon,
        contentDescription = null,
        contentScale = ContentScale.Crop,
        modifier = Modifier
            .size(24.dp)
    )
}
