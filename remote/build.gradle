plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.protobuf"
    id "stt.android.plugin.moshi"
    id "kotlin-parcelize"
}

android {
    namespace 'com.stt.android.remote'
    buildFeatures.buildConfig = true
}

dependencies {
    api project(Deps.remoteBase)

    implementation libs.androidx.corektx

    implementation libs.rxjava2

    // Retrofit
    implementation libs.retrofit
    implementation libs.retrofit.scalars
    implementation libs.retrofit.moshi
    implementation libs.retrofit.rxjava
    implementation libs.okhttp.logging
    implementation libs.okhttp

    implementation libs.java.otp
    implementation project(Deps.timeline)
}
