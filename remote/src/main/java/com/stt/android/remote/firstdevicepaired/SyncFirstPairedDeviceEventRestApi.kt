package com.stt.android.remote.firstdevicepaired

import com.stt.android.remote.response.AskoResponse
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface SyncFirstPairedDeviceEventRestApi {
    @GET("device/firstpaired/{serialNumber}")
    suspend fun setFirstPairedDeviceEvent(
        @Path("serialNumber") serial: String,
        @Query("name") deviceName: String,
        @Query("type") type: String,
        @Query("country") country: String? = null,
        @Query("region") region: String? = null,
        @Query("city") city: String? = null
    ): AskoResponse<Boolean>
}
