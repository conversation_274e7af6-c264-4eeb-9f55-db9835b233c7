package com.stt.android.remote.session

import com.stt.android.remote.BaseRemoteApi
import com.stt.android.remote.exceptions.BypassExceptionMapping
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Query

interface AccountRequestsRestApi {
    @GET("resetPassword")
    suspend fun sendPasswordResetEmail(
        @Query("brand") brand: String,
        @Query("username") username: String
    ): Response<Any>

    @POST("account/initdelete")
    suspend fun sendDeleteAccountRequest(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
    ): Response<Any>

    @BypassExceptionMapping
    @POST("user/gdprexportrequest")
    suspend fun sendGdprExportRequest(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
    ): Response<Unit>
}
