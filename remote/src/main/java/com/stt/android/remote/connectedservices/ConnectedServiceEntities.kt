package com.stt.android.remote.connectedservices

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class RemotePartnerConnectionResponse(
    @<PERSON>son(name = "oauth1") val oauth1: List<RemoteServiceMetadata> = listOf(),
    @<PERSON><PERSON>(name = "oauth2") val oauth2: List<RemoteServiceMetadata> = listOf(),
    @<PERSON><PERSON>(name = "openApi") val openApi: List<RemoteServiceMetadata> = listOf(),
    @<PERSON><PERSON>(name = "partnerTags") val partnerTags: List<RemotePartnerCategory> = listOf()
)

@JsonClass(generateAdapter = true)
data class RemoteServiceMetadata(
    @Json(name = "name") val name: String,
    @<PERSON><PERSON>(name = "partnerUrl") val partnerUrl: String,
    @J<PERSON>(name = "interceptUrl") val interceptUrl: String?,
    @<PERSON><PERSON>(name = "iconImageUrl") val iconImageUrl: String?,
    @<PERSON>son(name = "additionalImageUrls") val additionalImageUrls: List<String>,
    @<PERSON><PERSON>(name = "connectImageUrl") val connectImageUrl: String?,
    @<PERSON><PERSON>(name = "readMoreUrl") val readMoreUrl: String,
    @Json(name = "isConnected") val isConnected: Boolean,
    @Json(name = "localization") val localization: RemoteLocalizedMetadata,
    @Json(name = "clientTags") val clientTags: List<String>,
    @Json(name = "clientScopes") val clientScopes: List<String>,
    @Json(name = "reconnectRecommendation") val reconnectRecommendation: RemoteServiceReconnectRecommendation?
)

@JsonClass(generateAdapter = true)
data class RemoteServiceReconnectRecommendation(
    @Json(name = "message") val message: String
)

@JsonClass(generateAdapter = true)
data class RemoteLocalizedMetadata(
    @Json(name = "viewTitle") val viewTitle: String,
    @Json(name = "descriptionTitle") val descriptionTitle: String?,
    @Json(name = "descriptionBody") val descriptionBody: String,
    @Json(name = "connectedTitle") val connectedTitle: String?,
    @Json(name = "connectedBody") val connectedBody: String?,
    @Json(name = "instructionText") val instructionText: String?
)

@JsonClass(generateAdapter = true)
data class RemotePartnerCategory(
    @Json(name = "tag") val tag: String,
    @Json(name = "tagImage") val tagImage: String?,
    @Json(name = "localizedInfo") val localizedInfo: RemoteLocalizedPartnerCategory
)

@JsonClass(generateAdapter = true)
data class RemoteLocalizedPartnerCategory(
    @Json(name = "localeCode") val localeCode: String?,
    @Json(name = "name") val name: String,
    @Json(name = "description") val description: String?
)
