package com.stt.android.nfc.impl.screen.components

import androidx.activity.compose.BackHandler
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.nfc.impl.R
import kotlin.math.roundToInt
import com.stt.android.R as BR

@Composable
internal fun TransitCardTopUpBar(
    topUpAmount: String,
    totalAmount: String,
    label: String,
    enabled: Boolean,
    onClick: () -> Unit,
    onBarHeightChanged: (Int) -> Unit,
    modifier: Modifier = Modifier,
    activationFee: String? = null,
) {
    var expanded by rememberSaveable { mutableStateOf(false) }
    val rotation by animateFloatAsState(if (expanded) 180f else 0f)
    val topPaddingPx = with(LocalDensity.current) {
        MaterialTheme.spacing.small.toPx().roundToInt()
    }
    Surface(
        modifier = modifier,
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 20.dp,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = MaterialTheme.spacing.small),
        ) {
            Column(
                modifier = Modifier
                    .animateContentSize()
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.spacing.medium),
            ) {
                if (expanded) {
                    activationFee?.let {
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
                        FeeItem(
                            modifier = Modifier.fillMaxWidth(),
                            label = stringResource(R.string.transit_card_activation_fee),
                            amount = it,
                        )
                    }
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
                    FeeItem(
                        modifier = Modifier.fillMaxWidth(),
                        label = stringResource(R.string.transit_card_top_up_amount),
                        amount = topUpAmount,
                    )
                }
            }
            Row(
                modifier = Modifier
                    .onSizeChanged { onBarHeightChanged(it.height + topPaddingPx) }
                    .fillMaxWidth()
                    .padding(
                        top = MaterialTheme.spacing.small,
                        bottom = MaterialTheme.spacing.medium,
                        start = MaterialTheme.spacing.small,
                        end = MaterialTheme.spacing.medium,
                    )
                    .windowInsetsPadding(WindowInsets.systemBars.only(WindowInsetsSides.Bottom)),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                TextButton(
                    onClick = { expanded = !expanded },
                    colors = ButtonDefaults.textButtonColors(contentColor = MaterialTheme.colorScheme.onSurface),
                    shape = MaterialTheme.shapes.small,
                ) {
                    Text(
                        text = totalAmount,
                        style = Suunto.typography.headerM,
                        color = MaterialTheme.colorScheme.primary,
                    )
                    Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
                    Text(
                        text = stringResource(R.string.transit_card_fee_details),
                        style = Suunto.typography.bodyM,
                        color = MaterialTheme.colorScheme.secondary,
                    )
                    Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
                    Icon(
                        modifier = Modifier.rotate(rotation),
                        painter = painterResource(BR.drawable.ic_chevron_up_24),
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.secondary,
                    )
                }
                Spacer(modifier = Modifier.weight(1f))
                PlainPrimaryButton(
                    label = label,
                    onClick = onClick,
                    enabled = enabled,
                    minHeight = PLAIN_BUTTON_HEIGHT_SMALL,
                )
            }
        }
    }

    if (expanded) {
        BackHandler {
            expanded = false
        }
    }
}

@Composable
private fun FeeItem(
    label: String,
    amount: String,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.padding(vertical = MaterialTheme.spacing.small),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        ) {
            Text(
                modifier = Modifier
                    .weight(1f)
                    .alignByBaseline(),
                text = amount,
                style = Suunto.typography.bodyL,
                color = MaterialTheme.colorScheme.onSurface,
            )
            Text(
                modifier = Modifier.alignByBaseline(),
                text = label,
                style = Suunto.typography.bodyM,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
        HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
    }
}
