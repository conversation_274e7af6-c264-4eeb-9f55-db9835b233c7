package com.stt.android.nfc.impl.screen.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Checkbox
import androidx.compose.material3.LocalMinimumInteractiveComponentSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.Dp
import com.stt.android.compose.theme.Suunto
import com.stt.android.compose.theme.spacing
import com.stt.android.nfc.impl.R

@Composable
internal fun TransitCardActivationAgreement(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    onAgreementClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.padding(MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        CompositionLocalProvider(LocalMinimumInteractiveComponentSize provides Dp.Unspecified) {
            Checkbox(
                checked = checked,
                onCheckedChange = onCheckedChange,
            )
        }
        Text(
            text = buildAnnotatedString {
                val linkText = stringResource(R.string.transit_card_activation_agreement_link_text)
                val agreement = stringResource(R.string.transit_card_activation_agreement, linkText)
                append(agreement)
                val linkAnnotation = LinkAnnotation.Clickable(
                    tag = "agreement",
                    styles = TextLinkStyles(
                        style = Suunto.typography.bodyL.toSpanStyle()
                            .copy(color = MaterialTheme.colorScheme.primary),
                    ),
                    linkInteractionListener = { onAgreementClick() },
                )
                val start = agreement.indexOf(linkText)
                addLink(linkAnnotation, start, start + linkText.length)
            },
            style = Suunto.typography.bodyL,
            color = MaterialTheme.colorScheme.onSurface,
        )
    }
}
